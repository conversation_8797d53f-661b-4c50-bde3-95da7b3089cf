import React from "react";
import WidgetIcon from "./WidgetIcon";

const DragPreview = ({ widget, isDragging }) => {
  if (!isDragging || !widget) return null;

  return (
    <div className="fixed pointer-events-none z-[9999] opacity-80 transform -translate-x-1/2 -translate-y-1/2">
      <div className="bg-white border border-blue-300 rounded-lg shadow-2xl p-3 flex flex-col items-center gap-2">
        <WidgetIcon type={widget.type} size={32} className="text-blue-600" />
        <span className="text-xs font-medium text-gray-700">{widget.name}</span>
      </div>
    </div>
  );
};

export default DragPreview;
