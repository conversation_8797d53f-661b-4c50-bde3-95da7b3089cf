import mongoose from "mongoose";

/**
 * Voice Script Model - Represents script templates created by agencies
 * for voice recording assignments
 *
 * Enhanced with:
 * - Comprehensive validation
 * - Performance optimizations
 * - Advanced indexing strategies
 * - Better error handling
 */
const voiceScriptSchema = new mongoose.Schema(
  {
    // Basic script information with enhanced validation
    title: {
      type: String,
      required: [true, "Script title is required"],
      trim: true,
      minlength: [3, "Title must be at least 3 characters"],
      maxlength: [200, "Title cannot exceed 200 characters"],
      validate: [
        {
          validator: function (value) {
            // Prevent duplicate titles within the same agency
            return mongoose
              .model("VoiceScript")
              .findOne({
                agencyId: this.agencyId,
                title: value,
                _id: { $ne: this._id },
                isDeleted: false,
              })
              .then((doc) => !doc);
          },
          message: "Script title must be unique within your agency",
        },
        {
          validator: function (value) {
            // Prevent inappropriate content in titles
            const inappropriateWords = ["spam", "fake", "scam", "illegal"];
            return !inappropriateWords.some((word) =>
              value.toLowerCase().includes(word.toLowerCase()),
            );
          },
          message: "Title contains inappropriate content",
        },
        {
          validator: function (value) {
            // Ensure title has meaningful content (not just special characters)
            return /[a-zA-Z0-9]/.test(value);
          },
          message: "Title must contain at least one alphanumeric character",
        },
      ],
    },
    description: {
      type: String,
      required: [true, "Script description is required"],
      trim: true,
      minlength: [10, "Description must be at least 10 characters"],
      maxlength: [2000, "Description cannot exceed 2000 characters"],
      validate: {
        validator: function (value) {
          // Ensure description has meaningful content
          const wordCount = value.trim().split(/\s+/).length;
          return wordCount >= 3;
        },
        message: "Description must contain at least 3 words",
      },
    },
    scriptText: {
      type: String,
      required: function () {
        return this.scriptType !== "single_line";
      },
      trim: true,
      minlength: [10, "Script text must be at least 10 characters"],
      maxlength: [10000, "Script text cannot exceed 10000 characters"],
      validate: [
        {
          validator: function (value) {
            if (this.scriptType === "single_line") return true;
            return value && value.trim().length >= 10;
          },
          message:
            "Script text is required for detailed scripts and must be at least 10 characters",
        },
        {
          validator: function (value) {
            if (!value || this.scriptType === "single_line") return true;
            // Check for reasonable word count
            const wordCount = value.trim().split(/\s+/).length;
            return wordCount >= 5 && wordCount <= 2000;
          },
          message: "Script text must contain between 5 and 2000 words",
        },
      ],
    },

    // Script type and single-line support
    scriptType: {
      type: String,
      enum: ["detailed", "single_line"],
      default: "detailed",
    },
    singleLinePrompt: {
      type: String,
      required: function () {
        return this.scriptType === "single_line";
      },
      trim: true,
      minlength: [5, "Single line prompt must be at least 5 characters"],
      maxlength: [500, "Single line prompt cannot exceed 500 characters"],
      validate: {
        validator: function (value) {
          if (this.scriptType !== "single_line") return true;
          return value && value.trim().length >= 5;
        },
        message:
          "Single line prompt is required for single-line scripts and must be at least 5 characters",
      },
    },

    // Recording mode and sentence support
    recordingMode: {
      type: String,
      enum: ["full", "sentence"],
      default: "full",
    },
    sentences: [
      {
        id: {
          type: String,
          required: function () {
            return this.parent().sentences.length > 0;
          },
        },
        text: {
          type: String,
          required: function () {
            return this.parent().sentences.length > 0;
          },
          trim: true,
        },
        order: {
          type: Number,
          required: function () {
            return this.parent().sentences.length > 0;
          },
        },
        agencyComments: {
          type: String,
          trim: true,
          maxlength: [500, "Agency comments cannot exceed 500 characters"],
        },
      },
    ],

    // Agency and ownership
    agencyId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      required: [true, "Agency ID is required"],
      index: true,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      required: [true, "Creator ID is required"],
      validate: {
        validator: function (value) {
          return value.toString() === this.agencyId.toString();
        },
        message: "Creator must match agency ID",
      },
    },

    // Enhanced script categorization and metadata
    tags: {
      type: [
        {
          type: String,
          trim: true,
          enum: [
            "NSFW",
            "Welcome",
            "Whisper",
            "Moaning",
            "Promo",
            "Sensual",
            "Funny",
            "Romantic",
            "Aggressive",
            "Soft",
            "Intro",
            "Outro",
            "Custom",
            "Educational",
            "Commercial",
            "Narrative",
            "Conversational",
            "Dramatic",
            "Casual",
            "Professional",
          ],
        },
      ],
      validate: [
        {
          validator: function (tags) {
            return tags.length <= 15;
          },
          message: "Cannot have more than 15 tags",
        },
        {
          validator: function (tags) {
            // Ensure no duplicate tags
            return new Set(tags).size === tags.length;
          },
          message: "Duplicate tags are not allowed",
        },
      ],
      default: [],
    },
    tone: {
      type: String,
      enum: [
        "neutral",
        "seductive",
        "playful",
        "romantic",
        "dominant",
        "submissive",
        "professional",
        "casual",
        "energetic",
        "calm",
        "mysterious",
        "confident",
        "gentle",
        "authoritative",
      ],
      default: "neutral",
      validate: {
        validator: function (value) {
          // Ensure tone matches category appropriately
          if (
            this.category === "welcome_message" &&
            ["dominant", "submissive"].includes(value)
          ) {
            return false;
          }
          return true;
        },
        message: "Tone must be appropriate for the selected category",
      },
    },
    category: {
      type: String,
      required: [true, "Category is required"],
      enum: [
        "moaning_pack",
        "welcome_message",
        "promo_content",
        "custom_script",
        "educational",
        "commercial",
        "narrative",
        "interview",
        "announcement",
      ],
      validate: {
        validator: function (value) {
          // Validate category-specific requirements
          if (
            value === "educational" &&
            this.expectedDuration &&
            this.expectedDuration < 60
          ) {
            return false;
          }
          return true;
        },
        message:
          "Category requirements not met (e.g., educational content should be at least 1 minute)",
      },
    },

    // Enhanced priority and scheduling with business logic
    priority: {
      type: String,
      enum: ["low", "medium", "high", "urgent", "critical"],
      default: "medium",
      validate: {
        validator: function (value) {
          // Critical priority requires deadline
          if (value === "critical" && !this.deadline) {
            return false;
          }
          return true;
        },
        message: "Critical priority requires a deadline to be set",
      },
    },
    deadline: {
      type: Date,
      validate: [
        {
          validator: function (value) {
            if (!value) return true;
            const now = new Date();
            const minDeadline = new Date(now.getTime() + 30 * 60 * 1000); // 30 minutes from now
            return value > minDeadline;
          },
          message: "Deadline must be at least 30 minutes in the future",
        },
        {
          validator: function (value) {
            if (!value) return true;
            const now = new Date();
            const maxDeadline = new Date(
              now.getTime() + 365 * 24 * 60 * 60 * 1000,
            ); // 1 year from now
            return value <= maxDeadline;
          },
          message: "Deadline cannot be more than 1 year in the future",
        },
        {
          validator: function (value) {
            // Urgent/Critical priority should have shorter deadlines
            if (!value || !["urgent", "critical"].includes(this.priority))
              return true;
            const now = new Date();
            const maxUrgentDeadline = new Date(
              now.getTime() + 7 * 24 * 60 * 60 * 1000,
            ); // 7 days
            return value <= maxUrgentDeadline;
          },
          message:
            "Urgent/Critical priority scripts should have deadlines within 7 days",
        },
      ],
    },

    // New field: Deadline reminders
    deadlineReminders: {
      enabled: {
        type: Boolean,
        default: true,
      },
      intervals: [
        {
          type: String,
          enum: ["24h", "12h", "6h", "3h", "1h", "30m"],
          default: ["24h", "6h", "1h"],
        },
      ],
      lastSent: {
        type: Date,
        default: null,
      },
    },

    // Reference audio and guidelines
    referenceAudioUrl: {
      type: String,
      trim: true,
      validate: {
        validator: function (value) {
          if (!value) return true;
          return /^https?:\/\/.+/.test(value) || /^\/.+/.test(value);
        },
        message: "Reference audio URL must be a valid URL or file path",
      },
    },
    recordingGuidelines: {
      type: String,
      trim: true,
      maxlength: [1000, "Guidelines cannot exceed 1000 characters"],
    },
    expectedDuration: {
      type: Number, // in seconds
      min: [5, "Expected duration must be at least 5 seconds"],
      max: [7200, "Expected duration cannot exceed 2 hours"],
      validate: {
        validator: function (value) {
          if (!value) return true;
          // Validate duration based on script type and content
          if (this.scriptType === "single_line" && value > 300) {
            return false;
          }
          if (this.scriptText && this.scriptText.length > 0) {
            // Rough estimate: 150 words per minute, average 5 characters per word
            const estimatedWords = this.scriptText.length / 5;
            const estimatedDuration = (estimatedWords / 150) * 60;
            const tolerance = estimatedDuration * 0.5; // 50% tolerance
            return Math.abs(value - estimatedDuration) <= tolerance + 30; // +30 seconds buffer
          }
          return true;
        },
        message:
          "Expected duration should be reasonable for the script content length",
      },
    },

    // New field: Difficulty level
    difficultyLevel: {
      type: String,
      enum: ["beginner", "intermediate", "advanced", "expert"],
      default: "intermediate",
      validate: {
        validator: function (value) {
          // Expert level requires longer expected duration
          if (
            value === "expert" &&
            this.expectedDuration &&
            this.expectedDuration < 120
          ) {
            return false;
          }
          return true;
        },
        message:
          "Expert level scripts should have at least 2 minutes expected duration",
      },
    },

    // New field: Target audience
    targetAudience: {
      type: String,
      enum: [
        "general",
        "adult",
        "professional",
        "educational",
        "entertainment",
      ],
      default: "general",
    },

    // New field: Language and localization
    language: {
      type: String,
      enum: ["en", "es", "fr", "de", "it", "pt", "ru", "ja", "ko", "zh"],
      default: "en",
      required: [true, "Language is required"],
    },

    // New field: Voice characteristics requirements
    voiceRequirements: {
      gender: {
        type: String,
        enum: ["any", "male", "female", "non-binary"],
        default: "any",
      },
      ageRange: {
        type: String,
        enum: ["any", "18-25", "26-35", "36-45", "46-55", "55+"],
        default: "any",
      },
      accent: {
        type: String,
        enum: [
          "any",
          "american",
          "british",
          "australian",
          "canadian",
          "neutral",
        ],
        default: "any",
      },
      voiceType: {
        type: String,
        enum: ["any", "deep", "high", "raspy", "smooth", "energetic", "calm"],
        default: "any",
      },
    },

    // Enhanced script status and lifecycle with business logic
    status: {
      type: String,
      enum: [
        "draft",
        "active",
        "paused",
        "completed",
        "archived",
        "under_review",
        "rejected",
      ],
      default: "draft",
      validate: {
        validator: function (value) {
          // Templates cannot be in certain statuses
          if (this.isTemplate && ["under_review", "rejected"].includes(value)) {
            return false;
          }
          return true;
        },
        message: "Templates cannot have review-related statuses",
      },
    },
    isTemplate: {
      type: Boolean,
      default: false,
      validate: {
        validator: function (value) {
          // Templates should not have assignments
          if (value && this.totalAssignments > 0) {
            return false;
          }
          return true;
        },
        message: "Templates cannot have active assignments",
      },
    },

    // New field: Status history for audit trail
    statusHistory: [
      {
        status: {
          type: String,
          required: true,
        },
        timestamp: {
          type: Date,
          default: Date.now,
        },
        updatedBy: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Agency",
          required: true,
        },
        reason: {
          type: String,
          trim: true,
          maxlength: [500, "Status change reason cannot exceed 500 characters"],
        },
        metadata: {
          type: mongoose.Schema.Types.Mixed,
          default: {},
        },
      },
    ],

    // New field: Quality metrics
    qualityMetrics: {
      contentScore: {
        type: Number,
        min: [0, "Content score cannot be negative"],
        max: [100, "Content score cannot exceed 100"],
        default: null,
      },
      clarityScore: {
        type: Number,
        min: [0, "Clarity score cannot be negative"],
        max: [100, "Clarity score cannot exceed 100"],
        default: null,
      },
      engagementScore: {
        type: Number,
        min: [0, "Engagement score cannot be negative"],
        max: [100, "Engagement score cannot exceed 100"],
        default: null,
      },
      overallScore: {
        type: Number,
        min: [0, "Overall score cannot be negative"],
        max: [100, "Overall score cannot exceed 100"],
        default: null,
      },
      lastEvaluated: {
        type: Date,
        default: null,
      },
      evaluatedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Agency",
        default: null,
      },
    },

    // Enhanced assignment tracking with detailed metrics
    totalAssignments: {
      type: Number,
      default: 0,
      min: [0, "Total assignments cannot be negative"],
    },
    completedAssignments: {
      type: Number,
      default: 0,
      min: [0, "Completed assignments cannot be negative"],
      validate: {
        validator: function (value) {
          return value <= this.totalAssignments;
        },
        message: "Completed assignments cannot exceed total assignments",
      },
    },
    pendingAssignments: {
      type: Number,
      default: 0,
      min: [0, "Pending assignments cannot be negative"],
    },
    rejectedAssignments: {
      type: Number,
      default: 0,
      min: [0, "Rejected assignments cannot be negative"],
    },
    cancelledAssignments: {
      type: Number,
      default: 0,
      min: [0, "Cancelled assignments cannot be negative"],
    },

    // New field: Assignment performance metrics
    assignmentMetrics: {
      averageSubmissionTime: {
        type: Number, // in hours
        default: 0,
        min: [0, "Average submission time cannot be negative"],
      },
      firstSubmissionSuccessRate: {
        type: Number, // percentage
        min: [0, "Success rate cannot be negative"],
        max: [100, "Success rate cannot exceed 100"],
        default: 0,
      },
      modelSatisfactionScore: {
        type: Number,
        min: [1, "Satisfaction score must be at least 1"],
        max: [5, "Satisfaction score cannot exceed 5"],
        default: null,
      },
      popularityScore: {
        type: Number,
        min: [0, "Popularity score cannot be negative"],
        default: 0,
      },
    },

    // Enhanced metadata for comprehensive analytics
    averageCompletionTime: {
      type: Number, // in minutes
      default: 0,
      min: [0, "Average completion time cannot be negative"],
      validate: {
        validator: function (value) {
          // Completion time should be reasonable for expected duration
          if (value > 0 && this.expectedDuration) {
            const expectedMinutes = this.expectedDuration / 60;
            return value <= expectedMinutes * 10; // Allow up to 10x expected duration
          }
          return true;
        },
        message:
          "Average completion time seems unreasonably high for expected duration",
      },
    },
    averageRating: {
      type: Number,
      min: [1, "Rating must be at least 1"],
      max: [5, "Rating cannot exceed 5"],
      default: null,
      validate: {
        validator: function (value) {
          return value === null || (value >= 1 && value <= 5);
        },
        message: "Rating must be between 1 and 5",
      },
    },

    // New field: Detailed rating breakdown
    ratingBreakdown: {
      audioQuality: {
        average: { type: Number, min: 1, max: 5, default: null },
        count: { type: Number, min: 0, default: 0 },
      },
      voiceClarity: {
        average: { type: Number, min: 1, max: 5, default: null },
        count: { type: Number, min: 0, default: 0 },
      },
      emotionalDelivery: {
        average: { type: Number, min: 1, max: 5, default: null },
        count: { type: Number, min: 0, default: 0 },
      },
      technicalQuality: {
        average: { type: Number, min: 1, max: 5, default: null },
        count: { type: Number, min: 0, default: 0 },
      },
      overallSatisfaction: {
        average: { type: Number, min: 1, max: 5, default: null },
        count: { type: Number, min: 0, default: 0 },
      },
    },

    // Enhanced performance tracking
    lastAssignedAt: {
      type: Date,
      default: null,
      index: true,
    },
    lastCompletedAt: {
      type: Date,
      default: null,
    },
    lastModifiedAt: {
      type: Date,
      default: Date.now,
    },

    // New field: Usage analytics
    usageAnalytics: {
      totalViews: {
        type: Number,
        default: 0,
        min: [0, "Total views cannot be negative"],
      },
      uniqueViewers: {
        type: Number,
        default: 0,
        min: [0, "Unique viewers cannot be negative"],
      },
      totalDownloads: {
        type: Number,
        default: 0,
        min: [0, "Total downloads cannot be negative"],
      },
      shareCount: {
        type: Number,
        default: 0,
        min: [0, "Share count cannot be negative"],
      },
      bookmarkCount: {
        type: Number,
        default: 0,
        min: [0, "Bookmark count cannot be negative"],
      },
    },

    // Soft delete with enhanced tracking
    isDeleted: {
      type: Boolean,
      default: false,
      index: true,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
    deletedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      default: null,
    },

    // Enhanced version control for script updates
    version: {
      type: Number,
      default: 1,
      min: [1, "Version must be at least 1"],
    },
    versionHistory: [
      {
        version: {
          type: Number,
          required: true,
        },
        changes: {
          type: String,
          required: true,
          trim: true,
          maxlength: [
            1000,
            "Version changes description cannot exceed 1000 characters",
          ],
        },
        updatedBy: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Agency",
          required: true,
        },
        updatedAt: {
          type: Date,
          default: Date.now,
        },
        previousData: {
          type: mongoose.Schema.Types.Mixed,
          default: {},
        },
      },
    ],

    // Enhanced metadata for comprehensive analytics
    viewCount: {
      type: Number,
      default: 0,
      min: [0, "View count cannot be negative"],
    },
    lastViewedAt: {
      type: Date,
      default: null,
    },

    // New field: SEO and discoverability
    seoMetadata: {
      keywords: [
        {
          type: String,
          trim: true,
          maxlength: [50, "Keyword cannot exceed 50 characters"],
        },
      ],
      metaDescription: {
        type: String,
        trim: true,
        maxlength: [160, "Meta description cannot exceed 160 characters"],
      },
      searchableContent: {
        type: String,
        trim: true,
      },
    },

    // New field: Collaboration and feedback
    collaborationData: {
      allowComments: {
        type: Boolean,
        default: true,
      },
      allowRating: {
        type: Boolean,
        default: true,
      },
      moderationRequired: {
        type: Boolean,
        default: false,
      },
      lastFeedbackAt: {
        type: Date,
        default: null,
      },
    },

    // New field: Business intelligence
    businessMetrics: {
      revenueGenerated: {
        type: Number,
        default: 0,
        min: [0, "Revenue cannot be negative"],
      },
      costPerAssignment: {
        type: Number,
        default: 0,
        min: [0, "Cost cannot be negative"],
      },
      profitMargin: {
        type: Number,
        default: 0,
      },
      roi: {
        type: Number,
        default: 0,
      },
    },
  },
  {
    timestamps: true,
    // Enable optimistic concurrency control
    optimisticConcurrency: true,
    // Add version key for better conflict resolution
    versionKey: "__v",
  },
);

// Enhanced performance indexes for common queries
// Primary compound indexes for most frequent queries
voiceScriptSchema.index({ agencyId: 1, status: 1, isDeleted: 1 });
voiceScriptSchema.index({ agencyId: 1, createdAt: -1, isDeleted: 1 });
voiceScriptSchema.index({ agencyId: 1, category: 1, status: 1 });
voiceScriptSchema.index({ agencyId: 1, priority: 1, deadline: 1 });

// Template and assignment tracking indexes
voiceScriptSchema.index({ isTemplate: 1, agencyId: 1, status: 1 });
voiceScriptSchema.index({ status: 1, deadline: 1, isDeleted: 1 });
voiceScriptSchema.index({ lastAssignedAt: -1, status: 1 });

// Analytics and performance indexes
voiceScriptSchema.index({ category: 1, priority: 1, createdAt: -1 });
voiceScriptSchema.index({ tags: 1, status: 1 });
voiceScriptSchema.index({ averageRating: -1, completedAssignments: -1 });

// Sparse indexes for optional fields
voiceScriptSchema.index({ deadline: 1 }, { sparse: true });
voiceScriptSchema.index({ deletedAt: 1 }, { sparse: true });
voiceScriptSchema.index({ lastViewedAt: -1 }, { sparse: true });

// Enhanced text search index with weights
voiceScriptSchema.index(
  {
    title: "text",
    description: "text",
    scriptText: "text",
    singleLinePrompt: "text",
    tags: "text",
  },
  {
    weights: {
      title: 10,
      tags: 8,
      description: 5,
      scriptText: 3,
      singleLinePrompt: 3,
    },
    name: "script_text_search",
  },
);

// Unique compound index to prevent duplicate titles per agency
voiceScriptSchema.index(
  { agencyId: 1, title: 1 },
  {
    unique: true,
    partialFilterExpression: { isDeleted: false },
    name: "unique_title_per_agency",
  },
);

// Enhanced virtual properties for comprehensive calculated fields
voiceScriptSchema.virtual("completionPercentage").get(function () {
  if (this.totalAssignments === 0) return 0;
  return Math.round((this.completedAssignments / this.totalAssignments) * 100);
});

// Virtual for sentence count
voiceScriptSchema.virtual("sentenceCount").get(function () {
  return this.sentences ? this.sentences.length : 0;
});

// New virtual: Success rate calculation
voiceScriptSchema.virtual("successRate").get(function () {
  const totalProcessed = this.completedAssignments + this.rejectedAssignments;
  if (totalProcessed === 0) return 0;
  return Math.round((this.completedAssignments / totalProcessed) * 100);
});

// New virtual: Overall quality score
voiceScriptSchema.virtual("overallQualityScore").get(function () {
  const metrics = this.qualityMetrics;
  if (!metrics) return null;

  const scores = [
    metrics.contentScore,
    metrics.clarityScore,
    metrics.engagementScore,
  ].filter((s) => s !== null);
  if (scores.length === 0) return null;

  return Math.round(
    scores.reduce((sum, score) => sum + score, 0) / scores.length,
  );
});

// New virtual: Popularity index
voiceScriptSchema.virtual("popularityIndex").get(function () {
  const analytics = this.usageAnalytics;
  if (!analytics) return 0;

  // Weighted popularity calculation
  const viewWeight = 1;
  const downloadWeight = 3;
  const shareWeight = 5;
  const bookmarkWeight = 4;

  return Math.round(
    analytics.totalViews * viewWeight +
      analytics.totalDownloads * downloadWeight +
      analytics.shareCount * shareWeight +
      analytics.bookmarkCount * bookmarkWeight,
  );
});

// New virtual: Performance score
voiceScriptSchema.virtual("performanceScore").get(function () {
  const completion = this.completionPercentage;
  const success = this.successRate;
  const quality = this.overallQualityScore || 50; // Default to neutral if no quality data
  const popularity = Math.min(this.popularityIndex / 100, 100); // Cap at 100

  return Math.round(
    completion * 0.3 + success * 0.3 + quality * 0.25 + popularity * 0.15,
  );
});

// New virtual: Revenue per assignment
voiceScriptSchema.virtual("revenuePerAssignment").get(function () {
  if (this.totalAssignments === 0) return 0;
  return (
    Math.round(
      ((this.businessMetrics?.revenueGenerated || 0) / this.totalAssignments) *
        100,
    ) / 100
  );
});

// New virtual: Is trending (based on recent activity)
voiceScriptSchema.virtual("isTrending").get(function () {
  const now = new Date();
  const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

  // Check if there's been recent activity
  const recentAssignment =
    this.lastAssignedAt && this.lastAssignedAt > sevenDaysAgo;
  const recentView = this.lastViewedAt && this.lastViewedAt > sevenDaysAgo;
  const highPopularity = this.popularityIndex > 500;

  return recentAssignment && recentView && highPopularity;
});

// New virtual: Estimated completion time
voiceScriptSchema.virtual("estimatedCompletionTime").get(function () {
  if (this.averageCompletionTime > 0) {
    return this.averageCompletionTime;
  }

  // Fallback estimation based on script length and type
  if (this.scriptType === "detailed" && this.scriptText) {
    const wordCount = this.scriptText.split(/\s+/).length;
    return Math.round(wordCount * 0.5); // Rough estimate: 0.5 minutes per word
  }

  return this.expectedDuration ? Math.round(this.expectedDuration / 60) : 30; // Default 30 minutes
});

// New virtual: Content complexity score
voiceScriptSchema.virtual("complexityScore").get(function () {
  let score = 0;

  // Base complexity on script length
  if (this.scriptText) {
    const wordCount = this.scriptText.split(/\s+/).length;
    score += Math.min(wordCount / 100, 50); // Max 50 points for length
  }

  // Add complexity for sentence mode
  if (this.recordingMode === "sentence") {
    score += this.sentenceCount * 2;
  }

  // Add complexity for difficulty level
  const difficultyMultiplier = {
    beginner: 1,
    intermediate: 1.5,
    advanced: 2,
    expert: 2.5,
  };
  score *= difficultyMultiplier[this.difficultyLevel] || 1;

  return Math.round(Math.min(score, 100)); // Cap at 100
});

// Enhanced pre-save middleware with validation and auto-updates
voiceScriptSchema.pre("save", function (next) {
  // Auto-complete status when all assignments are done
  if (
    this.completedAssignments >= this.totalAssignments &&
    this.totalAssignments > 0
  ) {
    this.status = "completed";
  }

  // Increment version on content changes
  if (
    this.isModified("scriptText") ||
    this.isModified("singleLinePrompt") ||
    this.isModified("title")
  ) {
    this.version += 1;
  }

  // Set deletion timestamp
  if (this.isModified("isDeleted") && this.isDeleted && !this.deletedAt) {
    this.deletedAt = new Date();
  }

  // Clear deletion timestamp if undeleted
  if (this.isModified("isDeleted") && !this.isDeleted) {
    this.deletedAt = null;
    this.deletedBy = null;
  }

  // Validate sentence order consistency
  if (this.sentences && this.sentences.length > 0) {
    const orders = this.sentences.map((s) => s.order).sort((a, b) => a - b);
    const expectedOrders = Array.from(
      { length: orders.length },
      (_, i) => i + 1,
    );

    if (JSON.stringify(orders) !== JSON.stringify(expectedOrders)) {
      return next(
        new Error("Sentence orders must be consecutive starting from 1"),
      );
    }
  }

  next();
});

// Pre-remove middleware for soft delete
voiceScriptSchema.pre("remove", function (next) {
  this.isDeleted = true;
  this.deletedAt = new Date();
  next();
});

// Enhanced static methods with better performance and filtering
voiceScriptSchema.statics.findActiveByAgency = function (
  agencyId,
  options = {},
) {
  const query = {
    agencyId,
    status: { $in: ["active", "draft"] },
    isDeleted: false,
  };

  // Add optional filters
  if (options.category) query.category = options.category;
  if (options.priority) query.priority = options.priority;
  if (options.tags && options.tags.length > 0)
    query.tags = { $in: options.tags };

  return this.find(query)
    .sort({ priority: -1, createdAt: -1 })
    .limit(options.limit || 100)
    .lean(options.lean !== false); // Default to lean for better performance
};

// Static method to find scripts by performance metrics
voiceScriptSchema.statics.findTopPerforming = function (agencyId, limit = 10) {
  return this.find({
    agencyId,
    isDeleted: false,
    completedAssignments: { $gt: 0 },
  })
    .sort({ averageRating: -1, completedAssignments: -1 })
    .limit(limit)
    .lean();
};

// Static method for advanced search with aggregation
voiceScriptSchema.statics.searchScripts = function (
  agencyId,
  searchOptions = {},
) {
  const pipeline = [
    {
      $match: {
        agencyId: new mongoose.Types.ObjectId(agencyId),
        isDeleted: false,
        ...(searchOptions.status && { status: searchOptions.status }),
        ...(searchOptions.category && { category: searchOptions.category }),
        ...(searchOptions.priority && { priority: searchOptions.priority }),
      },
    },
  ];

  // Add text search if query provided
  if (searchOptions.query) {
    pipeline.unshift({
      $match: {
        $text: { $search: searchOptions.query },
      },
    });
    pipeline.push({
      $addFields: {
        score: { $meta: "textScore" },
      },
    });
  }

  // Add sorting
  const sortStage = searchOptions.query
    ? { score: { $meta: "textScore" }, createdAt: -1 }
    : { priority: -1, createdAt: -1 };

  pipeline.push({ $sort: sortStage });

  // Add pagination
  if (searchOptions.skip) pipeline.push({ $skip: searchOptions.skip });
  if (searchOptions.limit) pipeline.push({ $limit: searchOptions.limit });

  return this.aggregate(pipeline);
};

// Enhanced instance methods with better validation and functionality
voiceScriptSchema.methods.canBeAssigned = function () {
  const now = new Date();
  const isStatusValid = ["active", "draft"].includes(this.status);
  const isNotDeleted = !this.isDeleted;
  const isNotExpired = !this.deadline || this.deadline > now;

  return isStatusValid && isNotDeleted && isNotExpired;
};

// Instance method to check if script is overdue
voiceScriptSchema.methods.isOverdue = function () {
  if (!this.deadline) return false;
  return new Date() > this.deadline && this.status !== "completed";
};

// Instance method to get assignment statistics
voiceScriptSchema.methods.getAssignmentStats = async function () {
  const VoiceAssignment = mongoose.model("VoiceAssignment");

  const stats = await VoiceAssignment.aggregate([
    { $match: { scriptId: this._id, isDeleted: false } },
    {
      $group: {
        _id: "$status",
        count: { $sum: 1 },
      },
    },
  ]);

  const result = {
    total: 0,
    pending: 0,
    submitted: 0,
    approved: 0,
    rejected: 0,
  };

  stats.forEach((stat) => {
    result[stat._id] = stat.count;
    result.total += stat.count;
  });

  return result;
};

// Instance method to increment view count
voiceScriptSchema.methods.incrementViewCount = function () {
  this.viewCount += 1;
  this.lastViewedAt = new Date();
  return this.save({ validateBeforeSave: false });
};

// Instance method for soft delete with user tracking
voiceScriptSchema.methods.softDelete = function (deletedBy) {
  this.isDeleted = true;
  this.deletedAt = new Date();
  this.deletedBy = deletedBy;
  return this.save();
};

// Instance method to restore from soft delete
voiceScriptSchema.methods.restore = function () {
  this.isDeleted = false;
  this.deletedAt = null;
  this.deletedBy = null;
  return this.save();
};

// Instance method to add a sentence
voiceScriptSchema.methods.addSentence = function (text, agencyComments = "") {
  const newSentence = {
    id: new mongoose.Types.ObjectId().toString(),
    text: text.trim(),
    order: this.sentences.length + 1,
    agencyComments: agencyComments.trim(),
  };
  this.sentences.push(newSentence);
  return newSentence;
};

// Instance method to remove a sentence
voiceScriptSchema.methods.removeSentence = function (sentenceId) {
  const index = this.sentences.findIndex((s) => s.id === sentenceId);
  if (index === -1) {
    throw new Error("Sentence not found");
  }

  this.sentences.splice(index, 1);

  // Reorder remaining sentences
  this.sentences.forEach((sentence, idx) => {
    sentence.order = idx + 1;
  });

  return true;
};

// Instance method to reorder sentences
voiceScriptSchema.methods.reorderSentences = function (sentenceOrders) {
  // sentenceOrders should be an array of {id, order} objects
  sentenceOrders.forEach((orderInfo) => {
    const sentence = this.sentences.find((s) => s.id === orderInfo.id);
    if (sentence) {
      sentence.order = orderInfo.order;
    }
  });

  // Sort sentences by order
  this.sentences.sort((a, b) => a.order - b.order);

  return true;
};

// Enhanced instance method to update completion stats with rating calculation
voiceScriptSchema.methods.updateCompletionStats = async function () {
  const VoiceAssignment = mongoose.model("VoiceAssignment");

  const stats = await VoiceAssignment.aggregate([
    { $match: { scriptId: this._id, isDeleted: false } },
    {
      $group: {
        _id: null,
        total: { $sum: 1 },
        completed: {
          $sum: {
            $cond: [{ $eq: ["$status", "approved"] }, 1, 0],
          },
        },
        avgCompletionTime: {
          $avg: {
            $cond: [
              { $eq: ["$status", "approved"] },
              {
                $divide: [
                  { $subtract: ["$completedAt", "$assignedAt"] },
                  1000 * 60, // Convert to minutes
                ],
              },
              null,
            ],
          },
        },
        avgRating: {
          $avg: {
            $cond: [
              {
                $and: [
                  { $eq: ["$status", "approved"] },
                  { $ne: ["$rating", null] },
                ],
              },
              "$rating",
              null,
            ],
          },
        },
        lastAssigned: { $max: "$assignedAt" },
      },
    },
  ]);

  if (stats.length > 0) {
    const stat = stats[0];
    this.totalAssignments = stat.total;
    this.completedAssignments = stat.completed;
    this.averageCompletionTime = Math.round(stat.avgCompletionTime || 0);
    this.averageRating = stat.avgRating
      ? Math.round(stat.avgRating * 10) / 10
      : null;
    this.lastAssignedAt = stat.lastAssigned;

    // Use validateBeforeSave: false to avoid validation during stats update
    await this.save({ validateBeforeSave: false });
  }
};

// Instance method to clone script for template creation
voiceScriptSchema.methods.cloneAsTemplate = function (
  newTitle,
  newDescription,
) {
  const clonedData = this.toObject();
  delete clonedData._id;
  delete clonedData.__v;
  delete clonedData.createdAt;
  delete clonedData.updatedAt;

  clonedData.title = newTitle;
  clonedData.description = newDescription;
  clonedData.isTemplate = true;
  clonedData.status = "draft";
  clonedData.totalAssignments = 0;
  clonedData.completedAssignments = 0;
  clonedData.averageCompletionTime = 0;
  clonedData.averageRating = null;
  clonedData.version = 1;
  clonedData.viewCount = 0;
  clonedData.lastViewedAt = null;
  clonedData.lastAssignedAt = null;

  return new this.constructor(clonedData);
};

const VoiceScript = mongoose.model("VoiceScript", voiceScriptSchema);
export default VoiceScript;
