import React from "react";

const ClockWidget = () => {
  const [time, setTime] = React.useState(new Date());

  React.useEffect(() => {
    const timer = setInterval(() => {
      setTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  return (
    <div className="text-center text-white p-4 h-full flex flex-col justify-center">
      <div className="text-3xl font-mono mb-2 text-blue-400">
        {time.toLocaleTimeString()}
      </div>
      <div className="text-sm text-gray-300">Local Time</div>
      <div className="text-xs text-gray-400 mt-1">
        {time.toLocaleDateString()}
      </div>
    </div>
  );
};

export default ClockWidget;
