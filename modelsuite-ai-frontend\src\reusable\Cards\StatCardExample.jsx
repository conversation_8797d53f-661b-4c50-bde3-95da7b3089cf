import React from "react";
import { StatCard } from "@/reusable";
import {
  TrendingUp,
  Users,
  DollarSign,
  Activity,
  Clock,
  Target,
} from "lucide-react";

// Example usage of StatCard component
const StatCardExample = () => {
  return (
    <div className="p-6 space-y-6">
      <h2 className="text-2xl font-bold text-foreground mb-6">
        StatCard Examples
      </h2>

      {/* Grid of different stat cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {/* Default variant */}
        <StatCard
          title="Engagement Today"
          value="625.6K"
          icon={Activity}
          trend={12.5}
          variant="default"
        />

        {/* Earnings variant */}
        <StatCard
          title="Total Earnings"
          value="$24,560"
          subtitle="This month"
          icon={DollarSign}
          trend={-3.2}
          variant="earnings"
        />

        {/* Models variant */}
        <StatCard
          title="Models Online"
          value="47"
          subtitle="Active now"
          icon={Users}
          trend={8.1}
          variant="models"
        />

        {/* Engagement variant */}
        <StatCard
          title="Platform Reach"
          value="1.2M"
          subtitle="Total followers"
          icon={TrendingUp}
          trend={15.7}
          variant="engagement"
        />

        {/* Work tracker variant */}
        <StatCard
          title="Work Status"
          value="Online"
          subtitle="Daily tracker"
          icon={Clock}
          variant="work-tracker"
        />

        {/* No trend example */}
        <StatCard
          title="Pending Tasks"
          value="12"
          icon={Target}
          variant="default"
        />

        {/* Loading state example */}
        <StatCard
          title="Loading Data"
          value="Loading..."
          icon={Activity}
          loading={true}
          variant="default"
        />

        {/* Clickable example */}
        <StatCard
          title="Clickable Card"
          value="Click Me"
          icon={Target}
          variant="engagement"
          onClick={() => alert("StatCard clicked!")}
        />
      </div>

      {/* Code example */}
      <div className="mt-8 p-4 bg-muted rounded-lg">
        <h3 className="text-lg font-semibold mb-3">Usage Example:</h3>
        <pre className="text-sm text-muted-foreground overflow-x-auto">
          {`<StatCard
  title="Engagement Today"
  value="625.6K"
  subtitle="Platform Stats"
  icon={TrendingUp}
  trend={12.5}
  variant="engagement"
  onClick={handleClick}
/>`}
        </pre>
      </div>

      {/* Props documentation */}
      <div className="mt-6 p-4 bg-muted rounded-lg">
        <h3 className="text-lg font-semibold mb-3">Available Props:</h3>
        <ul className="text-sm text-muted-foreground space-y-1">
          <li>
            <code>title</code> - Main title text (required)
          </li>
          <li>
            <code>value</code> - Large display value (required)
          </li>
          <li>
            <code>subtitle</code> - Optional subtitle text
          </li>
          <li>
            <code>icon</code> - Lucide icon component
          </li>
          <li>
            <code>trend</code> - Percentage change (number)
          </li>
          <li>
            <code>variant</code> - "default" | "earnings" | "engagement" |
            "models" | "work-tracker"
          </li>
          <li>
            <code>loading</code> - Show loading state (boolean)
          </li>
          <li>
            <code>onClick</code> - Click handler function
          </li>
          <li>
            <code>className</code> - Additional CSS classes
          </li>
        </ul>
      </div>
    </div>
  );
};

export default StatCardExample;
