import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { fetchDashboardStats } from "@/redux/features/dashboard/dashboardSlice";

const MainHeader = ({ onToggleSidebar }) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const handleRefresh = () => {
    dispatch(fetchDashboardStats());
  };

  return (
    <header className="h-16 bg-[#0f0f0f] border-b border-[#1a1a1a] flex items-center justify-between px-8">
      <div className="flex items-center space-x-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={onToggleSidebar}
          className="-ml-3 h-8 w-8 rounded-md p-0 text-gray-400 hover:text-white hover:bg-[#1a1a1a]"
        >
          <PanelLeft className="h-5 w-5" />
          <span className="sr-only">Toggle Sidebar</span>
        </Button>
      </div>

      <div className="flex items-center space-x-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleRefresh}
          className="text-gray-400 hover:text-white hover:bg-[#1a1a1a]"
        >
          <RefreshCw className="w-5 h-5" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="text-gray-400 hover:text-white hover:bg-[#1a1a1a]"
        >
          <Bell className="w-5 h-5" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="text-gray-400 hover:text-white hover:bg-[#1a1a1a]"
        >
          <Settings className="w-5 h-5" />
        </Button>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button className="flex items-center p-1 rounded-full hover:bg-[#1a1a1a] transition-colors">
              <Avatar className="w-8 h-8">
                <AvatarFallback className="text-black text-xs">
                  RJ
                </AvatarFallback>
              </Avatar>
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            align="end"
            className="w-56 bg-[#1a1a1a] border-[#2a2a2a]"
          >
            <DropdownMenuItem className="text-gray-300 hover:bg-[#2a2a2a]">
              Profile
            </DropdownMenuItem>
            <DropdownMenuItem
              className="text-gray-300 hover:bg-[#2a2a2a]"
              onClick={() => navigate("profile/settings")}
            >
              Settings
            </DropdownMenuItem>
            <DropdownMenuItem className="text-gray-300 hover:bg-[#2a2a2a]">
              Sign out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
};

export default MainHeader;
