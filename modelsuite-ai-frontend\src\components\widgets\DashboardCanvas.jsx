import React from "react";
import { useSelector } from "react-redux";
import { selectPlacedWidgets } from "@/redux/features/dashboard/dashboardSlice";
import DropZone from "./DropZone";
import DroppedWidget from "./DroppedWidget";

const DashboardCanvas = ({ className = "", onWidgetDrop }) => {
  const placedWidgets = useSelector(selectPlacedWidgets);

  const handleWidgetDrop = (widget, position) => {
    console.log("Widget dropped:", widget.type, "at position:", position);
    if (onWidgetDrop) {
      onWidgetDrop(widget, position);
    }
  };

  return (
    <DropZone
      className={`
        h-[calc(100vh-200px)] w-full
        bg-transparent
        rounded-lg border-2 border-dashed border-gray-600/30
        relative overflow-hidden
        ${className}
      `}
      onDrop={handleWidgetDrop}
    >
      {/* Grid Pattern Background */}
      <div
        className="absolute inset-0 opacity-20"
        style={{
          backgroundImage: `
            radial-gradient(circle, #e5e7eb 1px, transparent 1px)
          `,
          backgroundSize: "20px 20px",
        }}
      />

      {/* Drop Zone Helper Text */}
      <div className="absolute top-2 right-2 bg-blue-100 text-blue-600 px-2 py-1 rounded text-xs font-medium">
        Drop Zone - Drag widgets here
      </div>

      {/* Empty State */}
      {placedWidgets.length === 0 && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center text-gray-500 z-10">
            <div className="text-xl font-medium mb-2">
              🎯 Your Dashboard Canvas
            </div>
            <p className="text-sm opacity-75 max-w-md">
              Drag widgets from the sidebar to create your personalized
              dashboard.
              <br />
              <strong>
                You can drop widgets anywhere in this entire area!
              </strong>
            </p>
            <div className="mt-4 text-xs text-gray-400">
              💡 Tip: Use the widget sidebar button to get started
            </div>
          </div>
        </div>
      )}

      {/* Render Dropped Widgets */}
      {placedWidgets.map((widget) => (
        <DroppedWidget key={widget.id} widget={widget} />
      ))}

      {/* Widget Counter */}
      {placedWidgets.length > 0 && (
        <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 text-xs text-gray-600 border border-gray-200">
          {placedWidgets.length} widget{placedWidgets.length !== 1 ? "s" : ""}{" "}
          on dashboard
        </div>
      )}
    </DropZone>
  );
};

export default DashboardCanvas;
