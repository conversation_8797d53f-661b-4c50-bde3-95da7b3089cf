import mongoose from "mongoose";

/**
 * Agency Configuration Schema - Stores agency-specific settings for content upload management,
 * reminder systems, and notification preferences
 */
const agencyConfigurationSchema = new mongoose.Schema(
  {
    agencyId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      required: [true, "Agency ID is required"],
      unique: true,
      index: true,
    },
    // General content upload settings
    contentUploadSettings: {
      defaultReminderFrequency: {
        type: Number,
        default: 7, // days
        min: [1, "Default reminder frequency must be at least 1 day"],
        max: [365, "Default reminder frequency cannot exceed 365 days"],
      },
      defaultReminderType: {
        type: String,
        enum: {
          values: ["hard", "soft", "one-time"],
          message: "Default reminder type must be one of: hard, soft, one-time",
        },
        default: "soft",
      },
      autoApprovalEnabled: {
        type: Boolean,
        default: false,
      },
      requiresWatermarkByDefault: {
        type: Boolean,
        default: false,
      },
      requiresConsentByDefault: {
        type: Boolean,
        default: true,
      },
      maxFileSize: {
        type: Number,
        default: 2147483648, // 2GB in bytes
        min: [1024, "Max file size must be at least 1KB"],
      },
      allowedFileFormats: {
        type: [String],
        default: ["jpg", "jpeg", "png", "mp4", "mov", "avi", "gif"],
        validate: {
          validator: function (formats) {
            return formats && formats.length > 0;
          },
          message: "At least one file format must be allowed",
        },
      },
    },
    // Reminder and notification settings
    reminderSettings: {
      enableReminders: {
        type: Boolean,
        default: true,
      },
      reminderEscalation: {
        enabled: {
          type: Boolean,
          default: false,
        },
        escalationDays: {
          type: Number,
          default: 3,
          min: [1, "Escalation days must be at least 1"],
        },
        maxEscalations: {
          type: Number,
          default: 3,
          min: [1, "Max escalations must be at least 1"],
        },
      },
      overdueThreshold: {
        type: Number,
        default: 2, // days after due date
        min: [0, "Overdue threshold cannot be negative"],
      },
      reminderTimes: {
        type: [String], // Array of times in HH:MM format
        default: ["09:00", "15:00"],
        validate: {
          validator: function (times) {
            return times.every((time) =>
              /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(time),
            );
          },
          message: "Reminder times must be in HH:MM format",
        },
      },
      timezone: {
        type: String,
        default: "UTC",
      },
    },
    // Notification preferences
    notificationSettings: {
      emailNotifications: {
        enabled: {
          type: Boolean,
          default: true,
        },
        recipients: {
          type: [String], // Array of email addresses
          default: [],
          validate: {
            validator: function (emails) {
              return emails.every((email) =>
                /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email),
              );
            },
            message: "All recipients must be valid email addresses",
          },
        },
        templates: {
          reminderTemplate: {
            type: String,
            default: "default",
          },
          overdueTemplate: {
            type: String,
            default: "default",
          },
          approvalTemplate: {
            type: String,
            default: "default",
          },
        },
      },
      smsNotifications: {
        enabled: {
          type: Boolean,
          default: false,
        },
        recipients: {
          type: [String], // Array of phone numbers
          default: [],
        },
      },
      inAppNotifications: {
        enabled: {
          type: Boolean,
          default: true,
        },
        showOnDashboard: {
          type: Boolean,
          default: true,
        },
      },
    },
    // Analytics and reporting settings
    analyticsSettings: {
      enableAnalytics: {
        type: Boolean,
        default: true,
      },
      reportingFrequency: {
        type: String,
        enum: {
          values: ["daily", "weekly", "monthly", "never"],
          message:
            "Reporting frequency must be one of: daily, weekly, monthly, never",
        },
        default: "weekly",
      },
      reportRecipients: {
        type: [String], // Array of email addresses
        default: [],
      },
      metricsToTrack: {
        type: [String],
        default: [
          "upload_count",
          "approval_rate",
          "overdue_content",
          "model_performance",
        ],
        enum: {
          values: [
            "upload_count",
            "approval_rate",
            "rejection_rate",
            "overdue_content",
            "model_performance",
            "category_performance",
            "response_time",
          ],
          message: "Invalid metric type",
        },
      },
    },
    // Workflow settings
    workflowSettings: {
      requireApprovalForAllUploads: {
        type: Boolean,
        default: false,
      },
      allowModelSelfApproval: {
        type: Boolean,
        default: false,
      },
      approvalTimeout: {
        type: Number,
        default: 72, // hours
        min: [1, "Approval timeout must be at least 1 hour"],
      },
      autoArchiveAfterDays: {
        type: Number,
        default: 90,
        min: [1, "Auto archive days must be at least 1"],
      },
      mainAgencyId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Agency",
        default: null,
        description:
          "When set, models will upload content under this agency ID instead of their own agency",
      },
    },
    // Custom fields and metadata
    customFields: {
      type: mongoose.Schema.Types.Mixed,
      default: {},
    },
    // Configuration metadata
    lastModifiedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
    },
    configurationVersion: {
      type: Number,
      default: 1,
    },
    isActive: {
      type: Boolean,
      default: true,
      index: true,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  },
);

// Indexes for efficient queries
agencyConfigurationSchema.index({ agencyId: 1, isActive: 1 });
agencyConfigurationSchema.index({ "reminderSettings.enableReminders": 1 });
agencyConfigurationSchema.index({
  "notificationSettings.emailNotifications.enabled": 1,
});

// Virtual for next reminder time
agencyConfigurationSchema.virtual("nextReminderTime").get(function () {
  if (
    !this.reminderSettings.enableReminders ||
    !this.reminderSettings.reminderTimes.length
  ) {
    return null;
  }

  const now = new Date();
  const currentTime = now.getHours() * 60 + now.getMinutes();

  for (const timeStr of this.reminderSettings.reminderTimes) {
    const [hours, minutes] = timeStr.split(":").map(Number);
    const reminderTime = hours * 60 + minutes;

    if (reminderTime > currentTime) {
      const nextReminder = new Date(now);
      nextReminder.setHours(hours, minutes, 0, 0);
      return nextReminder;
    }
  }

  // If no reminder time today, return first time tomorrow
  const [hours, minutes] = this.reminderSettings.reminderTimes[0]
    .split(":")
    .map(Number);
  const nextReminder = new Date(now);
  nextReminder.setDate(nextReminder.getDate() + 1);
  nextReminder.setHours(hours, minutes, 0, 0);
  return nextReminder;
});

// Pre-save middleware to increment version
agencyConfigurationSchema.pre("save", function (next) {
  if (this.isModified() && !this.isNew) {
    this.configurationVersion += 1;
  }
  next();
});

// Instance method to get effective reminder frequency for a category
agencyConfigurationSchema.methods.getEffectiveReminderFrequency = function (
  category,
) {
  return (
    category.reminderFrequency ||
    this.contentUploadSettings.defaultReminderFrequency
  );
};

// Instance method to get effective reminder type for a category
agencyConfigurationSchema.methods.getEffectiveReminderType = function (
  category,
) {
  return (
    category.reminderType || this.contentUploadSettings.defaultReminderType
  );
};

// Instance method to check if notifications are enabled
agencyConfigurationSchema.methods.areNotificationsEnabled = function (
  type = "email",
) {
  switch (type) {
    case "email":
      return this.notificationSettings.emailNotifications.enabled;
    case "sms":
      return this.notificationSettings.smsNotifications.enabled;
    case "inApp":
      return this.notificationSettings.inAppNotifications.enabled;
    default:
      return false;
  }
};

// Instance method to get notification recipients
agencyConfigurationSchema.methods.getNotificationRecipients = function (
  type = "email",
) {
  switch (type) {
    case "email":
      return this.notificationSettings.emailNotifications.recipients;
    case "sms":
      return this.notificationSettings.smsNotifications.recipients;
    default:
      return [];
  }
};

// Static method to find configuration by agency
agencyConfigurationSchema.statics.findByAgency = function (agencyId) {
  return this.findOne({ agencyId, isActive: true });
};

// Static method to create default configuration for new agency
agencyConfigurationSchema.statics.createDefault = function (
  agencyId,
  createdBy,
) {
  return this.create({
    agencyId,
    lastModifiedBy: createdBy,
  });
};

// Static method to get all agencies with reminders enabled
agencyConfigurationSchema.statics.findWithRemindersEnabled = function () {
  return this.find({
    isActive: true,
    "reminderSettings.enableReminders": true,
  }).populate("agencyId", "name email");
};

const AgencyConfiguration = mongoose.model(
  "AgencyConfiguration",
  agencyConfigurationSchema,
);

export default AgencyConfiguration;
