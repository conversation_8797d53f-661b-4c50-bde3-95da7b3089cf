import mongoose from "mongoose";

/**
 * Model Category Assignment Schema - Tracks which models are assigned to which categories
 * with assignment history and configuration settings
 */
const modelCategoryAssignmentSchema = new mongoose.Schema(
  {
    agencyId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      required: [true, "Agency ID is required"],
      index: true,
    },
    modelId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ModelUser",
      required: [true, "Model ID is required"],
      index: true,
    },
    categoryId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ContentCategory",
      required: [true, "Category ID is required"],
      index: true,
    },
    // Assignment configuration
    isActive: {
      type: Boolean,
      default: true,
      index: true,
    },
    priority: {
      type: String,
      enum: {
        values: ["low", "medium", "high", "urgent"],
        message: "Priority must be one of: low, medium, high, urgent",
      },
      default: "medium",
    },
    // Custom reminder settings for this assignment
    customReminderFrequency: {
      type: Number,
      min: [1, "Custom reminder frequency must be at least 1 day"],
      max: [365, "Custom reminder frequency cannot exceed 365 days"],
    },
    customReminderType: {
      type: String,
      enum: {
        values: ["hard", "soft", "one-time"],
        message: "Custom reminder type must be one of: hard, soft, one-time",
      },
    },
    // Due date tracking
    nextDueDate: {
      type: Date,
      index: true,
    },
    lastUploadDate: {
      type: Date,
    },
    // Assignment metadata
    assignedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      required: [true, "Assigned by is required"],
    },
    assignedAt: {
      type: Date,
      default: Date.now,
      index: true,
    },
    deactivatedAt: {
      type: Date,
    },
    deactivatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
    },
    deactivationReason: {
      type: String,
      trim: true,
      maxlength: [500, "Deactivation reason cannot exceed 500 characters"],
    },
    // Performance tracking
    totalUploads: {
      type: Number,
      default: 0,
    },
    approvedUploads: {
      type: Number,
      default: 0,
    },
    rejectedUploads: {
      type: Number,
      default: 0,
    },
    averageApprovalTime: {
      type: Number, // in hours
      default: 0,
    },
    // Reminder tracking
    remindersSent: {
      type: Number,
      default: 0,
    },
    lastReminderSent: {
      type: Date,
    },
    // Notes and comments
    notes: {
      type: String,
      trim: true,
      maxlength: [1000, "Notes cannot exceed 1000 characters"],
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  },
);

// Compound indexes for efficient queries
modelCategoryAssignmentSchema.index({ agencyId: 1, isActive: 1 });
modelCategoryAssignmentSchema.index({ modelId: 1, isActive: 1 });
modelCategoryAssignmentSchema.index({ categoryId: 1, isActive: 1 });
modelCategoryAssignmentSchema.index(
  { agencyId: 1, modelId: 1, categoryId: 1 },
  { unique: true },
);
modelCategoryAssignmentSchema.index({ nextDueDate: 1, isActive: 1 });
modelCategoryAssignmentSchema.index({ assignedAt: -1 });

// Virtual for upload count from ContentUpload
modelCategoryAssignmentSchema.virtual("recentUploads", {
  ref: "ContentUpload",
  localField: "_id",
  foreignField: "assignmentId",
  count: true,
  match: {
    createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }, // Last 30 days
  },
});

// Virtual for overdue status
modelCategoryAssignmentSchema.virtual("isOverdue").get(function () {
  return this.isActive && this.nextDueDate && this.nextDueDate < new Date();
});

// Virtual for days until due
modelCategoryAssignmentSchema.virtual("daysUntilDue").get(function () {
  if (!this.nextDueDate) return null;
  const diffTime = this.nextDueDate - new Date();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
});

// Pre-save middleware to calculate next due date
modelCategoryAssignmentSchema.pre("save", async function (next) {
  if (
    this.isNew ||
    this.isModified("customReminderFrequency") ||
    this.isModified("lastUploadDate")
  ) {
    await this.calculateNextDueDate();
  }
  next();
});

// Instance method to calculate next due date
modelCategoryAssignmentSchema.methods.calculateNextDueDate = async function () {
  try {
    // Get the category to determine frequency
    const category = await mongoose
      .model("ContentCategory")
      .findById(this.categoryId);
    if (!category) return;

    const frequency =
      this.customReminderFrequency || category.reminderFrequency;
    const baseDate = this.lastUploadDate || this.assignedAt || new Date();

    this.nextDueDate = new Date(
      baseDate.getTime() + frequency * 24 * 60 * 60 * 1000,
    );
  } catch (error) {
    console.error("Error calculating next due date:", error);
  }
};

// Instance method to update upload statistics
modelCategoryAssignmentSchema.methods.updateUploadStats = function (
  uploadStatus,
  approvalTime = null,
) {
  this.totalUploads += 1;

  if (uploadStatus === "approved") {
    this.approvedUploads += 1;
    if (approvalTime) {
      // Update average approval time
      const totalApprovalTime =
        this.averageApprovalTime * (this.approvedUploads - 1) + approvalTime;
      this.averageApprovalTime = totalApprovalTime / this.approvedUploads;
    }
  } else if (uploadStatus === "rejected") {
    this.rejectedUploads += 1;
  }

  this.lastUploadDate = new Date();
  return this.save();
};

// Instance method to deactivate assignment
modelCategoryAssignmentSchema.methods.deactivate = function (
  deactivatedBy,
  reason = null,
) {
  this.isActive = false;
  this.deactivatedAt = new Date();
  this.deactivatedBy = deactivatedBy;
  if (reason) {
    this.deactivationReason = reason;
  }
  return this.save();
};

// Static method to find active assignments for a model
modelCategoryAssignmentSchema.statics.findActiveByModel = function (modelId) {
  return this.find({ modelId, isActive: true })
    .populate("categoryId", "label platform reminderFrequency reminderType")
    .sort({ priority: -1, assignedAt: -1 });
};

// Static method to find active assignments for an agency
modelCategoryAssignmentSchema.statics.findActiveByAgency = function (agencyId) {
  return this.find({ agencyId, isActive: true })
    .populate("modelId", "firstName lastName email")
    .populate("categoryId", "label platform reminderFrequency")
    .sort({ nextDueDate: 1 });
};

// Static method to find overdue assignments
modelCategoryAssignmentSchema.statics.findOverdue = function (agencyId = null) {
  const query = {
    isActive: true,
    nextDueDate: { $lt: new Date() },
  };

  if (agencyId) {
    query.agencyId = agencyId;
  }

  return this.find(query)
    .populate("modelId", "firstName lastName email")
    .populate("categoryId", "label platform reminderType")
    .sort({ nextDueDate: 1 });
};

// Static method to find assignments due soon
modelCategoryAssignmentSchema.statics.findDueSoon = function (
  days = 3,
  agencyId = null,
) {
  const dueDate = new Date();
  dueDate.setDate(dueDate.getDate() + days);

  const query = {
    isActive: true,
    nextDueDate: { $lte: dueDate, $gte: new Date() },
  };

  if (agencyId) {
    query.agencyId = agencyId;
  }

  return this.find(query)
    .populate("modelId", "firstName lastName email")
    .populate("categoryId", "label platform reminderType")
    .sort({ nextDueDate: 1 });
};

const ModelCategoryAssignment = mongoose.model(
  "ModelCategoryAssignment",
  modelCategoryAssignmentSchema,
);

export default ModelCategoryAssignment;
