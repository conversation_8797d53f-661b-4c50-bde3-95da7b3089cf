# ModelSuite AI - Copilot Instructions

## Project Overview

ModelSuite is a comprehensive modeling agency management platform with two main components:

- **Frontend**: React + Vite app with Redux Toolkit, shadcn/ui components, and Tailwind CSS
- **Backend**: Node.js/Express API with MongoDB, Socket.IO, and Cloudinary/Cloudflare R2

## Architecture Patterns

### Frontend Structure

- **State Management**: Redux Toolkit with slices in `src/redux/features/`
- **API Layer**: Shared `axiosInstance` in `src/config/axiosInstance.js` with automatic auth headers
- **Routing**: React Router with role-based route protection (`/agency/*`, `/model/*`)
- **UI Components**: shadcn/ui + custom reusable components in `src/reusable/`
- **Layouts**: Context-driven with `SidebarProvider` and `DashboardLayoutProvider`

### Backend Architecture

- **Authentication**: JWT with dual tokens (access/refresh), role-based (`agency`, `model`, `employee`)
- **File Uploads**: Multer → Cloudinary (legacy) or Cloudflare R2 (new), with streaming support
- **Real-time**: Socket.IO for messaging, notifications, and status updates
- **Database**: MongoDB with Mongoose, extensive schema relationships

## Key Workflows

### Authentication Flow

```javascript
// Frontend: Always use axiosInstance for authenticated requests
import axiosInstance from "@/config/axiosInstance";
const response = await axiosInstance.get("/api/v1/endpoint");

// Backend: Protect routes with verifyToken middleware
router.get("/protected", verifyToken, verifyRole("agency"), controller);
```

### File Upload Pattern

```javascript
// Backend: Use appropriate multer config based on destination
import upload from "../middlewares/multer.js"; // For Cloudinary
// OR
const upload = multer({ storage: multer.memoryStorage() }); // For R2

// Frontend: Handle file uploads with progress tracking
const formData = new FormData();
formData.append("file", file);
```

### Database Seeding

The app auto-seeds critical data on startup:

- Default question templates via `seedQuestionTemplates()`
- Voice script templates via `seedDefaultTemplate()`

## Critical Integration Points

### Socket.IO Setup

- Server creates `io` instance in `server.js` and attaches to app: `app.set("io", io)`
- Modular socket handlers in `sockets/` directory
- User tracking with `connectedUsers` Map for online status

### Permission System

- Role-based: `agency` (full access), `model` (limited), `employee` (configurable)
- Middleware: `verifyRole()`, `checkPermission()`, `requireAdminOrAgency()`
- Employee permissions are granular and stored in user profile

### Content Upload System

- Multi-step workflow: Upload → Review → Approve/Reject
- Categories define upload rules and requirements
- Supports recurring assignments with deadline tracking

## Development Conventions

### API Response Format

```javascript
// Success responses
return res.status(200).json({
  success: true,
  data: result,
  message: "Operation successful",
});

// Error responses
return res.status(400).json({
  success: false,
  message: "Error description",
  errors: validationErrors,
});
```

### Redux Slice Pattern

```javascript
// Use createAsyncThunk for API calls
export const fetchData = createAsyncThunk(
  "slice/fetchData",
  async (params, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.get("/endpoint");
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.error || "Failed");
    }
  }
);
```

### Database Relationships

- Models reference agencies: `agencyId: { type: ObjectId, ref: "Agency" }`
- Always populate related fields in controllers for complete data
- Use compound indexes for performance: `{ modelId: 1, categoryId: 1 }`

## Testing & Development

### Environment Setup

- Frontend: Vite dev server on `:5173`, uses `VITE_API_BASE_URL`
- Backend: Express on `:${PORT}`, auto-seeding enabled
- Database: MongoDB with connection via `db.js`

### Key Testing Scripts

```bash
# Backend
npm run dev          # Start with nodemon
npm test            # Run test suite (when available)

# Frontend
npm run dev         # Vite dev server
npm run build       # Production build
```

### Debugging Socket.IO

```javascript
// Client-side: Check connection status
const socket = io();
socket.on("connect", () => console.log("Connected:", socket.id));

// Server-side: Access io instance from any controller
const io = req.app.get("io");
io.emit("eventName", data);
```

## Questionnaire System

- Templates created by agencies, assigned to models
- Question sections with reusable question templates
- Assignment workflow: Create → Assign → Complete → Review
- PDF generation available for responses

## Security Notes

- All routes protected with `verifyToken` middleware
- File uploads validated by mime type and size limits
- CORS configured for specific origins only
- Rate limiting implemented on auth endpoints
