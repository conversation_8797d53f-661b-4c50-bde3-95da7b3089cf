import React, { useEffect, useState } from "react";
import { useNavigate, Link } from "react-router-dom";
import { useForm } from "react-hook-form";
import { Form } from "@/components/ui/form";
import InputFormField from "../../reusable/Input/InputFormField";
import { useDispatch, useSelector } from "react-redux";
import z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { fetchMe, loginUser } from "@/redux/features/auth/loginSlice";
import { Button } from "@/components/ui/button";
import { Eye, EyeOff, Mail } from "lucide-react";
import { toast } from "sonner";

const formSchema = z.object({
  usernameOrEmail: z.string(),
  password: z.string(),
});

const Home = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { loading, user, error, successMsg, isAuthenticated } = useSelector((state) => state.authReducer);
  console.log(user,successMsg,error)

  // Tabs: agency/model
  const [userType, setUserType] = useState("agency");
  // UI states
  const [showPassword, setShowPassword] = useState(false);
  const [showMagicLink, setShowMagicLink] = useState(false);
  const [magicLinkSent, setMagicLinkSent] = useState(false);
  const [magicLinkEmail, setMagicLinkEmail] = useState("");
  const [magicLinkLoading, setMagicLinkLoading] = useState(false);
  const [otpSent, setOtpSent] = useState(false);
  const [otp, setOtp] = useState("");

  const onSubmit = async (values) => {
    console.log(values)
    dispatch(loginUser({ identifier: values.usernameOrEmail, password: values.password, userType: userType }))
  };

  const handleSuccess = () => {
    toast.success(successMsg);
    // Only call fetchMe after login, not after every success
  }
  const handleError = () => {
    toast.error(error)

  }

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      usernameOrEmail: "",
      password: "",
    },
  })
  useEffect(() => {
    // Only fetchMe when isAuthenticated becomes true and user is missing
    if (isAuthenticated && !user) {
      dispatch(fetchMe());
    }
    if (user) {
      navigate("/agency/dashboard");
    }
    // Only show login success toast after loginUser, not after fetchMe
    if (successMsg && !user) {
      handleSuccess();
    }
    if (error) {
      handleError();
    }
  }, [isAuthenticated, user, successMsg, error]);

  return (
    <div className="flex items-center justify-center min-h-screen bg-[#111827] text-white px-2 sm:px-0">
      <div className="shadow-lg border border-gray-500 rounded-2xl px-4 py-6 sm:px-6 sm:py-8 w-full max-w-md bg-[#181F2A]">
        <div className="flex flex-col items-center gap-y-3 justify-center mb-2">
          <img
            src={""}
            className="max-w-[60%] sm:max-w-[50%] mb-2"
            alt="Logo"
          />
          <h1 className="font-bold text-xl sm:text-2xl">WELCOME BACK</h1>
        </div>

        {/* Tabs below Welcome Back */}
        <div className="flex justify-center gap-2 mb-5 mt-5 sm:mb-6 sm:mt-6 flex-wrap">
          <Button
            onClick={() => setUserType("agency")}
            className={`px-4 py-2 rounded-lg font-semibold text-xs sm:text-sm transition-colors duration-200 ${
              userType === "agency"
                ? "bg-[#6917E0] text-white"
                : "bg-gray-700 text-gray-300 hover:bg-gray-600"
            }`}
          >
            Agency Login
          </Button>
          <Button
            onClick={() => setUserType("model")}
            className={`px-4 py-2 rounded-lg font-semibold text-xs sm:text-sm transition-colors duration-200 ${
              userType === "model"
                ? "bg-blue-600 text-white"
                : "bg-gray-700 text-gray-300 hover:bg-gray-600"
            }`}
          >
            Model Login
          </Button>
        </div>

        {/* Main Content */}
        {!otpSent && !showMagicLink && !magicLinkSent ? (
          // Regular Login Form
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 mt-6">
              <InputFormField name="usernameOrEmail" control={form.control} placeholder={"Enter your Email or Username"} label={"Email or Username"} type={"text"} />
              <div className="relative">
                <InputFormField name="password" control={form.control} placeholder={"Enter your Password"} label={"Password"} type={showPassword ? "text" : "password"} />
                <span
                  onClick={() => setShowPassword((prev) => !prev)}
                  className="absolute right-3 top-[38px] cursor-pointer text-gray-400 hover:text-gray-200 transition-colors"
                >
                  {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                </span>
              </div>
              <Link to={userType === "agency" ? "/agency/forgot-password" : "/model/forgot-password"}>
                <p className="text-primaryButtonColor text-sm cursor-pointer hover:underline">Forgot Password?</p>
              </Link>
              <Button type="submit" variant="primary" className="w-full mt-4 py-5 text-base">
                {loading ? "Logging in..." : "Login"}
              </Button>
              {/* Magic Link Section */}
              <div className="relative my-4">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-600"></div>
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-[#181F2A] text-gray-400">Or</span>
                </div>
              </div>
              <button
                type="button"
                onClick={() => setShowMagicLink(true)}
                className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white py-2 rounded-lg font-semibold transition-colors duration-200 flex items-center justify-center gap-2"
              >
                <Mail size={18} />
                Login via Magic Link
              </button>
            </form>
          </Form>
        ) : otpSent ? (
          // OTP Form
          <form onSubmit={handleOtpSubmit} className="space-y-4 mt-2">
            <div className="flex flex-col">
              <label
                htmlFor="otp"
                className="text-sm font-medium text-gray-300 mb-1"
              >
                Enter OTP
              </label>
              <input
                type="text"
                id="otp"
                value={otp}
                onChange={(e) => setOtp(e.target.value)}
                placeholder="Enter OTP"
                className="px-4 py-2 rounded-lg bg-gray-800 border border-gray-700 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
            <button
              type="submit"
              className={`w-full mt-2 ${
                activeTab === "agency"
                  ? "bg-[#6917E0] hover:bg-[#8f4ff0]"
                  : "bg-blue-600 hover:bg-blue-700"
              } transition-colors duration-200 text-white py-2 rounded-lg font-semibold`}
            >
              Verify OTP
            </button>
            <button
              type="button"
              onClick={resetToLogin}
              className="w-full bg-gray-600 hover:bg-gray-700 text-white py-2 rounded-lg font-semibold transition-colors duration-200"
            >
              Back to Login
            </button>
          </form>
        ) : showMagicLink && !magicLinkSent ? (
          // Magic Link Email Form
          <form onSubmit={handleMagicLinkSubmit} className="space-y-4 mt-2">
            <div className="flex flex-col">
              <label
                htmlFor="magicEmail"
                className="text-sm font-medium text-gray-300 mb-1"
              >
                Email Address
              </label>
              <input
                type="email"
                id="magicEmail"
                value={magicLinkEmail}
                onChange={(e) => setMagicLinkEmail(e.target.value)}
                placeholder="Enter your email"
                className="px-4 py-2 rounded-lg bg-gray-800 border border-gray-700 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500"
                required
              />
            </div>
            <button
              type="submit"
              className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white py-2 rounded-lg font-semibold transition-colors duration-200 flex items-center justify-center gap-2"
              disabled={magicLinkLoading}
            >
              {magicLinkLoading ? (
                <ButtonLoading />
              ) : (
                <>
                  <Mail size={18} />
                  Send Magic Link
                </>
              )}
            </button>
            <button
              type="button"
              onClick={resetToLogin}
              className="w-full bg-gray-600 hover:bg-gray-700 text-white py-2 rounded-lg font-semibold transition-colors duration-200"
            >
              Back to Login
            </button>
          </form>
        ) : (
          // Magic Link Sent Confirmation
          <div className="text-center space-y-4 mt-2">
            <div className="p-6 bg-gray-800 rounded-lg border border-gray-700">
              <Mail size={48} className="mx-auto text-purple-400 mb-4" />
              <h3 className="text-lg font-semibold mb-2">Magic Link Sent!</h3>
              <p className="text-gray-300 text-sm">
                Check your email at{" "}
                <span className="text-purple-400">{magicLinkEmail}</span> and
                click the magic link to log in.
              </p>
            </div>
            <button
              type="button"
              onClick={resetToLogin}
              className="w-full bg-gray-600 hover:bg-gray-700 text-white py-2 rounded-lg font-semibold transition-colors duration-200"
            >
              Back to Login
            </button>
          </div>
        )}

        <p className="text-sm text-center mt-4 text-gray-300">
          Don't have an account?{" "}
          <Link
            to={"/register"}
            className={`${
              userType === "agency" ? "text-[#6917E0]" : "text-blue-400"
            } hover:underline font-medium transition-colors`}
          >
            Register here
          </Link>
        </p>
      </div>
    </div>
  );
};

export default Home;