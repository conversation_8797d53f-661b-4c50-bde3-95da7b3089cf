export function canManageNotes(userState) {
  if (!userState) return false;
  const { role, permissions = [] } = userState;
  if (role === "agency") return true;
  // employees may have granular permissions
  if (role === "employee")
    return (
      permissions.includes("notes.manage") ||
      permissions.includes("notes.edit") ||
      permissions.includes("notes.delete")
    );
  return false;
}
