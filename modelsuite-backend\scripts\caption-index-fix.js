import mongoose from "mongoose";

async function captionIndexFix() {
  try {
    // Use the actual MongoDB URI from .env (MongoDB Atlas)
    const mongoUri =
      "mongodb+srv://devanand:Ram<PERSON><EMAIL>/modelsuite?retryWrites=true&w=majority&appName=Cluster0";
    await mongoose.connect(mongoUri);
    console.log("🔍 Connected to MongoDB Atlas database");

    const db = mongoose.connection.db;
    const collection = db.collection("captionquotas");

    console.log("\n🗂️ BEFORE - Current indexes:");
    const beforeIndexes = await collection.indexes();
    beforeIndexes.forEach((index, i) => {
      console.log(
        `${i + 1}. ${index.name}: ${JSON.stringify(index.key)} ${
          index.unique ? "(UNIQUE)" : ""
        }`,
      );
    });

    // Remove the problematic userId_1_date_1 index
    console.log("\n🗑️ Removing problematic userId_1_date_1 index...");
    try {
      await collection.dropIndex("userId_1_date_1");
      console.log("✅ Successfully dropped userId_1_date_1 index");
    } catch (error) {
      console.log("❌ Failed to drop userId_1_date_1 index:", error.message);
    }

    // Clean up the document with null userId
    console.log("\n🧹 Cleaning up documents with null userId...");
    try {
      const deleteResult = await collection.deleteMany({ userId: null });
      console.log(
        `✅ Deleted ${deleteResult.deletedCount} documents with null userId`,
      );
    } catch (error) {
      console.log(
        "❌ Failed to clean up null userId documents:",
        error.message,
      );
    }

    console.log("\n🗂️ AFTER - Remaining indexes:");
    const afterIndexes = await collection.indexes();
    afterIndexes.forEach((index, i) => {
      console.log(
        `${i + 1}. ${index.name}: ${JSON.stringify(index.key)} ${
          index.unique ? "(UNIQUE)" : ""
        }`,
      );
    });

    // Test quota update (this should work now)
    console.log("\n🧪 Testing quota update...");
    try {
      const testModelId = new mongoose.Types.ObjectId(
        "6877d1d1d4660031e907a8eb",
      ); // Using a test ObjectId
      const today = new Date().toISOString().split("T")[0]; // Today's date in YYYY-MM-DD format

      const testResult = await collection.findOneAndUpdate(
        { modelId: testModelId, date: today },
        {
          $inc: { usageCount: 1 },
          $setOnInsert: {
            modelId: testModelId,
            date: today,
            usageCount: 1,
            dailyLimit: 5,
            createdAt: new Date(),
          },
        },
        { upsert: true, new: true },
      );

      console.log(
        "✅ Test quota update successful:",
        testResult.value ? "Updated existing" : "Created new",
      );

      // Clean up test quota
      await collection.deleteOne({ modelId: testModelId, date: today });
      console.log("🧹 Test quota cleaned up");
    } catch (error) {
      console.log("❌ Test quota update failed:", error.message);
    }

    console.log("\n📊 Final collection stats:");
    const totalDocs = await collection.countDocuments({});
    const nullUserDocs = await collection.countDocuments({ userId: null });
    console.log(`Total documents: ${totalDocs}`);
    console.log(`Documents with null userId: ${nullUserDocs}`);

    await mongoose.disconnect();
    console.log("\n🎉 Caption index fix completed successfully!");
  } catch (error) {
    console.error("❌ Error:", error);
  }
}

captionIndexFix();
