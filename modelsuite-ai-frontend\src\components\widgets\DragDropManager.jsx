import React, { useEffect, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  selectIsDragging,
  selectDraggedWidget,
  endDrag,
} from "@/redux/features/widgetSidebar/widgetSidebarSlice";
import DragPreview from "./DragPreview";

const DragDropManager = ({ children }) => {
  const dispatch = useDispatch();
  const isDragging = useSelector(selectIsDragging);
  const draggedWidget = useSelector(selectDraggedWidget);

  // Handle global drag events
  const handleGlobalDragOver = useCallback(
    (e) => {
      if (isDragging) {
        e.preventDefault();
        e.dataTransfer.dropEffect = "copy";
      }
    },
    [isDragging]
  );

  const handleGlobalDrop = useCallback(
    (e) => {
      // Check if the drop target is within a valid drop zone
      const dropZone = e.target.closest("[data-drop-zone]");

      if (isDragging && !dropZone) {
        // Only end drag if we're NOT dropping on a valid drop zone
        e.preventDefault();
        dispatch(endDrag());
        console.log("Drop missed - ending drag operation");
      }
      // If there IS a drop zone, let it handle the drop event
    },
    [isDragging, dispatch]
  );

  const handleGlobalDragEnd = useCallback(() => {
    // Ensure drag state is cleaned up when drag ends
    if (isDragging) {
      dispatch(endDrag());
    }
  }, [isDragging, dispatch]);

  // Global event listeners for drag management
  useEffect(() => {
    if (isDragging) {
      // Prevent default drag behavior on the entire document
      document.addEventListener("dragover", handleGlobalDragOver);
      document.addEventListener("drop", handleGlobalDrop);
      document.addEventListener("dragend", handleGlobalDragEnd);

      // Add visual feedback class to body
      document.body.classList.add("drag-active");
      document.body.style.cursor = "grabbing";

      return () => {
        document.removeEventListener("dragover", handleGlobalDragOver);
        document.removeEventListener("drop", handleGlobalDrop);
        document.removeEventListener("dragend", handleGlobalDragEnd);

        // Clean up visual feedback
        document.body.classList.remove("drag-active");
        document.body.style.cursor = "";
      };
    }
  }, [isDragging, handleGlobalDragOver, handleGlobalDrop, handleGlobalDragEnd]);

  // Handle keyboard interruption (ESC key)
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === "Escape" && isDragging) {
        dispatch(endDrag());
      }
    };

    if (isDragging) {
      document.addEventListener("keydown", handleKeyDown);
      return () => document.removeEventListener("keydown", handleKeyDown);
    }
  }, [isDragging, dispatch]);

  // Calculate drag position for preview
  const [dragPosition, setDragPosition] = React.useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (e) => {
      if (isDragging) {
        setDragPosition({ x: e.clientX, y: e.clientY });
      }
    };

    if (isDragging) {
      document.addEventListener("mousemove", handleMouseMove);
      return () => document.removeEventListener("mousemove", handleMouseMove);
    }
  }, [isDragging]);

  return (
    <>
      {children}

      {/* Drag Preview */}
      {isDragging && draggedWidget && (
        <div
          className="fixed pointer-events-none z-[9999]"
          style={{
            left: dragPosition.x,
            top: dragPosition.y,
            transform: "translate(-50%, -50%)",
          }}
        >
          <DragPreview widget={draggedWidget} isDragging={isDragging} />
        </div>
      )}

      {/* Global drag styles */}
      {isDragging && (
        <style>{`
          .drag-active * {
            pointer-events: none !important;
          }

          .drag-active [data-drop-zone] {
            pointer-events: auto !important;
          }

          .drag-active [data-drop-zone] * {
            pointer-events: auto !important;
          }

          .drag-active [draggable="true"] {
            pointer-events: auto !important;
          }
        `}</style>
      )}
    </>
  );
};

export default DragDropManager;
