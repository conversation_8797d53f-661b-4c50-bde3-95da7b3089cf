import React, { useState, useEffect } from "react";
import { Check } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  availableFeatures,
  getSelectedFeatures,
  saveSelectedFeatures,
} from "./featureRegistry";

const LaunchpadModal = ({ isOpen, onClose, onSave }) => {
  const [selectedIds, setSelectedIds] = useState([]);
  const maxSelection = 5;

  useEffect(() => {
    if (isOpen) {
      // Load current selection when modal opens
      setSelectedIds(getSelectedFeatures());
    }
  }, [isOpen]);

  const handleFeatureToggle = (featureId) => {
    setSelectedIds((prev) => {
      if (prev.includes(featureId)) {
        // Remove if already selected
        return prev.filter((id) => id !== featureId);
      } else if (prev.length < maxSelection) {
        // Add if under limit
        return [...prev, featureId];
      }
      // Can't add more (at limit)
      return prev;
    });
  };

  const handleSave = () => {
    saveSelectedFeatures(selectedIds);
    onSave(selectedIds);
    onClose();
  };

  const handleCancel = () => {
    setSelectedIds(getSelectedFeatures()); // Reset to saved state
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Transparent backdrop - click to close */}
      <div
        className="absolute inset-0 bg-black/60 backdrop-blur-lg"
        onClick={handleCancel}
      />

      {/* Features Grid - No modal container, just floating icons */}
      <div className="relative z-10 grid grid-cols-5 gap-8 p-8">
        {availableFeatures.map((feature) => {
          const IconComponent = feature.icon;
          const isSelected = selectedIds.includes(feature.id);
          const canSelect = selectedIds.length < maxSelection || isSelected;

          return (
            <div
              key={feature.id}
              onClick={() => canSelect && handleFeatureToggle(feature.id)}
              className={`
                relative flex flex-col items-center gap-3 cursor-pointer
                transition-all duration-200 transform hover:scale-110
                ${canSelect ? "" : "opacity-50 cursor-not-allowed"}
              `}
            >
              {/* App Icon Container - macOS style */}
              <div
                className={`
                  w-20 h-20 rounded-2xl flex items-center justify-center
                  transition-all duration-200 shadow-2xl
                  ${
                    isSelected
                      ? "bg-gray-800/80 backdrop-blur-sm ring-1 ring-blue-400/30"
                      : "bg-gray-800/80 backdrop-blur-sm hover:bg-gray-700/80"
                  }
                `}
              >
                {/* Selection indicator - subtle glow */}
                {isSelected && (
                  <div className="absolute -top-0.5 -right-0.5 w-3 h-3 bg-white/20 backdrop-blur-sm rounded-full shadow-sm animate-pulse" />
                )}

                {/* Icon */}
                <IconComponent className="h-full w-full text-white" />
              </div>

              {/* App Name */}
              <span className="text-white text-sm font-medium text-center px-2">
                {feature.name}
              </span>
            </div>
          );
        })}
      </div>

      {/* Floating Save/Cancel buttons at bottom */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex gap-4">
        <Button
          variant="outline"
          onClick={handleCancel}
          className="bg-black/60 backdrop-blur-sm border-white/20 text-white hover:bg-black/80"
        >
          Cancel
        </Button>
        <Button
          onClick={handleSave}
          className="bg-blue-600/80 backdrop-blur-sm hover:bg-blue-700/80 text-white shadow-lg"
        >
          Save Selection ({selectedIds.length}/{maxSelection})
        </Button>
      </div>
    </div>
  );
};

export default LaunchpadModal;
