import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import {
  StatCard,
  MetricGrid,
  ConfirmModal,
  FormModal,
  InfoModal,
  FileDropzone,
  ImageUploader,
  DocumentUploader,
  FormWrapper,
  SelectField,
  TextAreaField,
  CheckboxField,
  RadioGroupField,
  InputFormField,
  Spinner,
  SkeletonLoader,
  ProgressBar,
  LoadingOverlay,
  LoadingButton,
  TabsWrapper,
  BreadcrumbNav,
  StatusBadge,
  SearchBar,
  DatePicker,
  AvatarGroup,
  EmptyState,
} from "@/reusable";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import z from "zod";
import {
  TrendingUp,
  Users,
  DollarSign,
  Activity,
  Clock,
  Target,
  Facebook,
  Instagram,
  X,
  User,
  Mail,
  Settings,
  Shield,
  BarChart3,
  FileText,
  Home,
  Building2,
} from "lucide-react";

const formSchema = z.object({
  password: z.string(),
  country: z.string().optional(),
  bio: z.string().optional(),
  skills: z.array(z.string()).optional(),
  notifications: z
    .object({
      email: z.boolean(),
      sms: z.boolean(),
      push: z.boolean(),
    })
    .optional(),
  plan: z.string().optional(),
});
const ResuableExample = () => {
  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      password: "",
      country: "",
      bio: "",
      skills: [],
      notifications: {
        email: true,
        sms: false,
        push: true,
      },
      plan: "basic",
    },
  });

  // Modal states
  const [showConfirm, setShowConfirm] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [showInfo, setShowInfo] = useState(false);

  // Loader states
  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgress] = useState(45);
  const [showSkeleton, setShowSkeleton] = useState(false);

  // Sample data for InfoModal
  const userData = {
    name: "John Doe",
    email: "<EMAIL>",
    role: "Model",
    status: "Active",
    joinDate: "2024-01-15",
  };

  const userFields = [
    { key: "name", label: "Full Name" },
    { key: "email", label: "Email Address" },
    {
      key: "role",
      label: "Role",
      type: "badge",
      badgeClasses: "bg-blue-100 text-blue-800",
    },
    {
      key: "status",
      label: "Status",
      type: "badge",
      badgeClasses: "bg-green-100 text-green-800",
    },
    { key: "joinDate", label: "Join Date" },
  ];
  return (
    <>
      <div className="min-h-screen bg-[#0a0a0a] text-white p-8 space-y-8">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold mb-2">Reusable Components Demo</h1>
          <p className="text-muted-foreground">Testing our components</p>
        </div>

        {/* StatCard Examples */}
        <div>
          <h2 className="text-2xl font-semibold mb-4">StatCard Component</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <StatCard
              title="Engagement Today"
              value="625.6K"
              icon={Activity}
              trend={12.5}
            />

            <StatCard
              title="Total Earnings"
              value="$24,560"
              subtitle="This month"
              icon={DollarSign}
              trend={-3.2}
            />

            <StatCard
              title="Models Online"
              value="47"
              subtitle="Active now"
              icon={Users}
            />

            <StatCard
              title="Platform Reach"
              value="1.2M"
              icon={TrendingUp}
              onClick={() => alert("Card clicked!")}
            />
          </div>
        </div>

        {/* MetricGrid Examples */}
        <div>
          <h2 className="text-2xl font-semibold mb-4">MetricGrid Component</h2>
          <MetricGrid
            columns={3}
            metrics={[
              {
                platform: "Facebook",
                value: "125.3K",
                growth: "+5.2%",
                icon: Facebook,
              },
              {
                platform: "Instagram",
                value: "89.7K",
                growth: "-2.1%",
                icon: Instagram,
              },
              {
                platform: "X (Twitter)",
                value: "67.4K",
                growth: "+8.7%",
                icon: X,
              },
            ]}
          />
        </div>

        {/* Modal Examples */}
        <div>
          <h2 className="text-2xl font-semibold mb-4">Modal Components</h2>
          <div className="flex gap-4 items-start flex-wrap">
            <Button onClick={() => setShowConfirm(true)} variant="destructive">
              Show Confirm Modal
            </Button>
            <Button onClick={() => setShowForm(true)}>Show Form Modal</Button>
            <Button onClick={() => setShowInfo(true)} variant="secondary">
              Show Info Modal
            </Button>
          </div>
        </div>

        {/* FileUpload Examples */}
        <div>
          <h2 className="text-2xl font-semibold mb-4">FileUpload Components</h2>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* FileDropzone */}
            <div className="space-y-2">
              <h3 className="text-lg font-medium">FileDropzone</h3>
              <FileDropzone
                accept="*"
                multiple={true}
                onUpload={(file) => {
                  // Simulate upload
                  return new Promise((resolve) => {
                    setTimeout(
                      () => resolve({ url: URL.createObjectURL(file) }),
                      1000
                    );
                  });
                }}
                onRemove={(fileId) => console.log("Removed file:", fileId)}
              />
            </div>

            {/* ImageUploader */}
            <div className="space-y-2">
              <h3 className="text-lg font-medium">ImageUploader</h3>
              <ImageUploader
                aspectRatio="16:9"
                onUpload={(file) => {
                  // Simulate upload
                  return new Promise((resolve) => {
                    setTimeout(
                      () => resolve({ url: URL.createObjectURL(file) }),
                      1000
                    );
                  });
                }}
                onRemove={() => console.log("Image removed")}
                showCrop={true}
              />
            </div>

            {/* DocumentUploader */}
            <div className="space-y-2">
              <h3 className="text-lg font-medium">DocumentUploader</h3>
              <DocumentUploader
                acceptedTypes={[".pdf", ".doc", ".docx", ".txt"]}
                multiple={true}
                onUpload={(file) => {
                  // Simulate upload
                  return new Promise((resolve) => {
                    setTimeout(
                      () => resolve({ url: URL.createObjectURL(file) }),
                      1000
                    );
                  });
                }}
                onRemove={(fileId) => console.log("Document removed:", fileId)}
                onPreview={(fileData) => console.log("Preview:", fileData.name)}
              />
            </div>
          </div>
        </div>

        {/* Form Components Examples */}
        <div>
          <h2 className="text-2xl font-semibold mb-4">Form Components</h2>
          <FormWrapper
            title="User Profile Form"
            description="Example form showcasing all form components"
            className="max-w-2xl"
          >
            <Form {...form}>
              <form className="space-y-6">
                {/* SelectField Examples */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <SelectField
                    control={form.control}
                    name="country"
                    label="Country"
                    placeholder="Select your country"
                    searchable={true}
                    clearable={true}
                    options={[
                      { value: "us", label: "United States", icon: "🇺🇸" },
                      { value: "uk", label: "United Kingdom", icon: "🇬🇧" },
                      { value: "ca", label: "Canada", icon: "🇨🇦" },
                      { value: "au", label: "Australia", icon: "🇦🇺" },
                      { value: "de", label: "Germany", icon: "🇩🇪" },
                    ]}
                  />

                  <SelectField
                    control={form.control}
                    name="skills"
                    label="Skills"
                    placeholder="Select your skills"
                    multiple={true}
                    searchable={true}
                    showSelectAll={true}
                    options={[
                      {
                        value: "react",
                        label: "React",
                        icon: <User className="h-4 w-4" />,
                      },
                      {
                        value: "nodejs",
                        label: "Node.js",
                        icon: <Settings className="h-4 w-4" />,
                      },
                      {
                        value: "python",
                        label: "Python",
                        icon: <Shield className="h-4 w-4" />,
                      },
                      {
                        value: "design",
                        label: "UI/UX Design",
                        icon: <Mail className="h-4 w-4" />,
                      },
                    ]}
                  />
                </div>

                {/* TextAreaField Example */}
                <TextAreaField
                  control={form.control}
                  name="bio"
                  label="Bio"
                  placeholder="Tell us about yourself..."
                  description="Write a brief description about your background and experience"
                  rows={4}
                  maxLength={500}
                  showCharCount={true}
                  showWordCount={true}
                  showCopy={true}
                  showClear={true}
                />

                {/* CheckboxField Examples */}
                <CheckboxField
                  control={form.control}
                  name="notifications"
                  label="Notification Preferences"
                  description="Choose how you want to receive notifications"
                  variant="group"
                  layout="vertical"
                  options={[
                    {
                      key: "email",
                      label: "Email Notifications",
                      description: "Receive updates via email",
                      icon: <Mail className="h-4 w-4" />,
                    },
                    {
                      key: "sms",
                      label: "SMS Notifications",
                      description: "Receive updates via text message",
                      icon: <User className="h-4 w-4" />,
                    },
                    {
                      key: "push",
                      label: "Push Notifications",
                      description: "Receive browser push notifications",
                      icon: <Settings className="h-4 w-4" />,
                    },
                  ]}
                />

                {/* RadioGroupField Examples */}
                <div className="space-y-6">
                  <RadioGroupField
                    control={form.control}
                    name="plan"
                    label="Subscription Plan"
                    description="Choose your subscription plan"
                    variant="card"
                    layout="grid"
                    gridCols={3}
                    options={[
                      {
                        value: "basic",
                        label: "Basic",
                        description: "Perfect for getting started",
                        badge: "$9/mo",
                        features: [
                          "5 Projects",
                          "Basic Support",
                          "1GB Storage",
                        ],
                        icon: <User className="h-5 w-5" />,
                      },
                      {
                        value: "pro",
                        label: "Pro",
                        description: "Best for growing businesses",
                        badge: "$29/mo",
                        features: [
                          "Unlimited Projects",
                          "Priority Support",
                          "10GB Storage",
                        ],
                        icon: <Shield className="h-5 w-5" />,
                      },
                      {
                        value: "enterprise",
                        label: "Enterprise",
                        description: "For large organizations",
                        badge: "$99/mo",
                        features: [
                          "Custom Solutions",
                          "24/7 Support",
                          "Unlimited Storage",
                        ],
                        icon: <Settings className="h-5 w-5" />,
                      },
                    ]}
                  />
                </div>

                <div className="flex gap-4 pt-4">
                  <Button
                    type="submit"
                    className="flex-1 bg-white text-black hover:bg-gray-200 transition-colors duration-200"
                  >
                    Save Profile
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    className="flex-1 bg-gray-800 border-gray-600 text-gray-200 hover:bg-gray-700 hover:border-gray-500 hover:text-white transition-colors duration-200"
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </Form>
          </FormWrapper>
        </div>

        {/* Loader Components */}
        <div>
          <h2 className="text-2xl font-semibold mb-4">Loader Components</h2>

          {/* Spinner Examples */}
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-3">Spinner</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-[#0a0a0a] border border-gray-800 rounded-lg">
              <div className="text-center">
                <Spinner size="sm" />
                <p className="text-xs text-gray-500 mt-2">Small</p>
              </div>
              <div className="text-center">
                <Spinner size="default" />
                <p className="text-xs text-gray-500 mt-2">Default</p>
              </div>
              <div className="text-center">
                <Spinner size="lg" />
                <p className="text-xs text-gray-500 mt-2">Large</p>
              </div>
              <div className="text-center">
                <Spinner variant="accent">Loading...</Spinner>
                <p className="text-xs text-gray-500 mt-2">With Text</p>
              </div>
            </div>
          </div>

          {/* SkeletonLoader Examples */}
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-3">Skeleton Loader</h3>
            <div className="space-y-4">
              <div className="flex gap-4">
                <button
                  onClick={() => setShowSkeleton(!showSkeleton)}
                  className="px-4 py-2 bg-[#1a1a1a] hover:bg-gray-800 text-gray-400 rounded text-sm transition-colors"
                >
                  Toggle Skeleton
                </button>
              </div>

              {showSkeleton ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <SkeletonLoader variant="text" lines={4} />
                  <SkeletonLoader variant="card" />
                  <SkeletonLoader variant="avatar" />
                  <SkeletonLoader variant="table" />
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 bg-[#0a0a0a] border border-gray-800 rounded-lg">
                    <h4 className="font-medium mb-2 text-gray-300">
                      Sample Content
                    </h4>
                    <p className="text-sm text-gray-500">
                      This is real content that would be shown when data is
                      loaded.
                    </p>
                  </div>
                  <div className="p-4 bg-[#0a0a0a] border border-gray-800 rounded-lg">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="h-10 w-10 bg-[#1a1a1a] rounded-full flex items-center justify-center">
                        <User className="h-5 w-5 text-gray-500" />
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-300">John Doe</h4>
                        <p className="text-sm text-gray-500">
                          <EMAIL>
                        </p>
                      </div>
                    </div>
                    <p className="text-sm text-gray-500">
                      Real user profile content
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* ProgressBar Examples */}
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-3">Progress Bar</h3>
            <div className="space-y-4 p-4 bg-[#0a0a0a] border border-gray-800 rounded-lg">
              <ProgressBar
                value={progress}
                showPercentage={true}
                showLabel={true}
                label="Upload Progress"
              />
              <ProgressBar
                value={75}
                variant="success"
                size="sm"
                showPercentage={true}
              />
              <ProgressBar
                value={30}
                variant="warning"
                size="lg"
                label="Processing"
              />
              <ProgressBar
                indeterminate={true}
                variant="default"
                label="Loading..."
              />
              <div className="flex gap-2 mt-4">
                <button
                  onClick={() => setProgress(Math.max(0, progress - 10))}
                  className="px-3 py-1 bg-[#1a1a1a] hover:bg-gray-800 text-gray-400 rounded text-xs transition-colors"
                >
                  -10%
                </button>
                <button
                  onClick={() => setProgress(Math.min(100, progress + 10))}
                  className="px-3 py-1 bg-[#1a1a1a] hover:bg-gray-800 text-gray-400 rounded text-xs transition-colors"
                >
                  +10%
                </button>
              </div>
            </div>
          </div>

          {/* LoadingOverlay Example */}
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-3">Loading Overlay</h3>
            <div className="space-y-4">
              <button
                onClick={() => setIsLoading(!isLoading)}
                className="px-4 py-2 bg-[#1a1a1a] hover:bg-gray-800 text-gray-400 rounded text-sm transition-colors"
              >
                Toggle Loading Overlay
              </button>

              <LoadingOverlay
                isLoading={isLoading}
                message="Processing your request..."
                size="lg"
              >
                <div className="p-6 bg-[#0a0a0a] border border-gray-800 rounded-lg">
                  <h4 className="font-medium mb-2 text-gray-300">
                    Sample Content
                  </h4>
                  <p className="text-gray-500 mb-4">
                    This content will be blurred and overlaid when loading is
                    active.
                  </p>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="p-3 bg-[#1a1a1a] rounded">
                      <h5 className="font-medium text-sm text-gray-300">
                        Feature 1
                      </h5>
                      <p className="text-xs text-gray-500">Description here</p>
                    </div>
                    <div className="p-3 bg-[#1a1a1a] rounded">
                      <h5 className="font-medium text-sm text-gray-300">
                        Feature 2
                      </h5>
                      <p className="text-xs text-gray-500">Description here</p>
                    </div>
                  </div>
                </div>
              </LoadingOverlay>
            </div>
          </div>

          {/* LoadingButton Examples */}
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-3">Loading Button</h3>
            <div className="flex flex-wrap gap-4 p-4 bg-[#0a0a0a] border border-gray-800 rounded-lg">
              <LoadingButton
                onClick={() => {
                  // Simulate API call
                  setTimeout(() => {}, 2000);
                }}
              >
                Default Button
              </LoadingButton>

              <LoadingButton
                variant="primary"
                isLoading={true}
                loadingText="Saving..."
              >
                Save Changes
              </LoadingButton>

              <LoadingButton variant="outline" size="sm">
                Small Button
              </LoadingButton>

              <LoadingButton variant="secondary" size="lg">
                Large Button
              </LoadingButton>
            </div>
          </div>
        </div>

        {/* Other Components */}
        <div>
          <h2 className="text-2xl font-semibold mb-4">Other Components</h2>
          <div className="flex gap-4 items-start">
            <Button variant={"primary"}>Click Me</Button>

            <Form {...form}>
              <form className="w-80">
                <InputFormField
                  name="password"
                  control={form.control}
                  placeholder={"Enter Password"}
                  label={"Password"}
                  type={"password"}
                />
              </form>
            </Form>
          </div>
        </div>

        {/* Modals */}
        <ConfirmModal
          isOpen={showConfirm}
          onClose={() => setShowConfirm(false)}
          onConfirm={() => {
            alert("Confirmed!");
            setShowConfirm(false);
          }}
          title="Delete User"
          message="Are you sure you want to delete this user? This action cannot be undone."
          confirmText="Delete"
          variant="destructive"
        />

        <FormModal
          isOpen={showForm}
          onClose={() => setShowForm(false)}
          title="Create New User"
          size="default"
        >
          <Form {...form}>
            <form className="space-y-4">
              <InputFormField
                name="password"
                control={form.control}
                placeholder={"Enter Password"}
                label={"Password"}
                type={"password"}
              />
              <div className="flex gap-2 pt-4">
                <Button type="submit" className="flex-1">
                  Create User
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowForm(false)}
                  className="flex-1"
                >
                  Cancel
                </Button>
              </div>
            </form>
          </Form>
        </FormModal>

        <InfoModal
          isOpen={showInfo}
          onClose={() => setShowInfo(false)}
          title="User Information"
          data={userData}
          fields={userFields}
          variant="user"
        />

        {/* Navigation Examples */}
        <div>
          <h2 className="text-2xl font-semibold mb-4">Navigation Components</h2>

          {/* TabsWrapper Examples */}
          <div className="mb-8">
            <h3 className="text-lg font-medium mb-4">TabsWrapper Component</h3>

            <div className="space-y-6">
              {/* Basic Tabs */}
              <div>
                <p className="text-sm text-gray-400 mb-3">Basic Tabs</p>
                <TabsWrapper
                  defaultValue="overview"
                  tabs={[
                    { value: "overview", label: "Overview" },
                    { value: "analytics", label: "Analytics" },
                    { value: "settings", label: "Settings" },
                  ]}
                >
                  <div className="p-4 bg-[#1a1a1a] rounded-lg">
                    <p className="text-gray-300">
                      Overview content goes here...
                    </p>
                  </div>
                </TabsWrapper>
              </div>

              {/* Tabs with Icons and Badges */}
              <div>
                <p className="text-sm text-gray-400 mb-3">
                  Tabs with Icons & Badges
                </p>
                <TabsWrapper
                  defaultValue="dashboard"
                  size="lg"
                  tabs={[
                    {
                      value: "dashboard",
                      label: "Dashboard",
                      icon: BarChart3,
                      badge: "5",
                    },
                    {
                      value: "models",
                      label: "Models",
                      icon: Users,
                      badge: "new",
                    },
                    {
                      value: "reports",
                      label: "Reports",
                      icon: FileText,
                    },
                  ]}
                >
                  <div className="p-4 bg-[#1a1a1a] rounded-lg">
                    <p className="text-gray-300">
                      Dashboard content with metrics...
                    </p>
                  </div>
                </TabsWrapper>
              </div>

              {/* Pills Variant */}
              <div>
                <p className="text-sm text-gray-400 mb-3">Pills Variant</p>
                <TabsWrapper
                  defaultValue="active"
                  variant="pills"
                  size="sm"
                  tabs={[
                    { value: "active", label: "Active", badge: "12" },
                    { value: "pending", label: "Pending", badge: "3" },
                    { value: "inactive", label: "Inactive" },
                  ]}
                >
                  <div className="p-4 bg-[#1a1a1a] rounded-lg">
                    <p className="text-gray-300">Active models list...</p>
                  </div>
                </TabsWrapper>
              </div>
            </div>
          </div>

          {/* BreadcrumbNav Examples */}
          <div>
            <h3 className="text-lg font-medium mb-4">
              BreadcrumbNav Component
            </h3>

            <div className="space-y-6">
              {/* Basic Breadcrumb */}
              <div>
                <p className="text-sm text-gray-400 mb-3">Basic Breadcrumb</p>
                <BreadcrumbNav
                  items={[
                    { label: "Dashboard", href: "/dashboard" },
                    { label: "Models", href: "/models" },
                    { label: "Profile Settings" },
                  ]}
                />
              </div>

              {/* With Icons and Badges */}
              <div>
                <p className="text-sm text-gray-400 mb-3">
                  With Icons & Badges
                </p>
                <BreadcrumbNav
                  homeIcon={Home}
                  items={[
                    {
                      label: "Agency",
                      href: "/agency",
                      icon: Building2,
                      badge: "Pro",
                    },
                    {
                      label: "Models",
                      href: "/agency/models",
                      icon: Users,
                    },
                    {
                      label: "Emma Wilson",
                      icon: User,
                      badge: "Online",
                    },
                  ]}
                />
              </div>

              {/* Different Variants */}
              <div>
                <p className="text-sm text-gray-400 mb-3">Pills Variant</p>
                <BreadcrumbNav
                  variant="pills"
                  size="lg"
                  showHome={false}
                  items={[
                    { label: "Settings", href: "/settings" },
                    { label: "Profile", href: "/settings/profile" },
                    { label: "Security" },
                  ]}
                />
              </div>

              {/* Long Breadcrumb (Auto-collapse) */}
              <div>
                <p className="text-sm text-gray-400 mb-3">
                  Auto-collapse (Max 3 items)
                </p>
                <BreadcrumbNav
                  maxItems={3}
                  items={[
                    { label: "Admin", href: "/admin" },
                    { label: "Users", href: "/admin/users" },
                    { label: "Management", href: "/admin/users/management" },
                    {
                      label: "Permissions",
                      href: "/admin/users/management/permissions",
                    },
                    { label: "Role Settings" },
                  ]}
                />
              </div>

              {/* Minimal Variant */}
              <div>
                <p className="text-sm text-gray-400 mb-3">Minimal Variant</p>
                <BreadcrumbNav
                  variant="minimal"
                  size="sm"
                  items={[
                    { label: "API", href: "/api" },
                    { label: "Documentation", href: "/api/docs" },
                    { label: "Webhooks" },
                  ]}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Status Examples */}
        <div>
          <h2 className="text-2xl font-semibold mb-4">Status Components</h2>

          {/* StatusBadge Examples */}
          <div className="mb-8">
            <h3 className="text-lg font-medium mb-4">StatusBadge Component</h3>

            <div className="space-y-6">
              {/* Basic Status Badges */}
              <div>
                <p className="text-sm text-gray-400 mb-3">Basic Status Types</p>
                <div className="flex flex-wrap gap-3">
                  <StatusBadge status="online" />
                  <StatusBadge status="offline" />
                  <StatusBadge status="active" />
                  <StatusBadge status="inactive" />
                  <StatusBadge status="pending" />
                  <StatusBadge status="processing" pulse />
                  <StatusBadge status="paused" />
                  <StatusBadge status="warning" />
                  <StatusBadge status="error" />
                  <StatusBadge status="success" />
                  <StatusBadge status="new" />
                  <StatusBadge status="verified" />
                  <StatusBadge status="unverified" />
                </div>
              </div>

              {/* Size Variations */}
              <div>
                <p className="text-sm text-gray-400 mb-3">Size Variations</p>
                <div className="flex items-center gap-4">
                  <StatusBadge status="online" size="sm" />
                  <StatusBadge status="online" size="default" />
                  <StatusBadge status="online" size="lg" />
                </div>
              </div>

              {/* Variant Styles */}
              <div>
                <p className="text-sm text-gray-400 mb-3">Variant Styles</p>
                <div className="flex flex-wrap gap-3">
                  <StatusBadge status="active" variant="default" />
                  <StatusBadge status="active" variant="outline" />
                  <StatusBadge status="active" variant="ghost" />
                  <StatusBadge
                    status="processing"
                    variant="dot"
                    showDot
                    pulse
                  />
                </div>
              </div>

              {/* Custom Labels */}
              <div>
                <p className="text-sm text-gray-400 mb-3">Custom Labels</p>
                <div className="flex flex-wrap gap-3">
                  <StatusBadge status="online" size="lg">
                    Emma Wilson
                  </StatusBadge>
                  <StatusBadge status="processing" showIcon={false}>
                    Uploading Files
                  </StatusBadge>
                  <StatusBadge status="verified" size="sm">
                    ID Verified
                  </StatusBadge>
                  <StatusBadge status="new" variant="outline">
                    New Feature
                  </StatusBadge>
                </div>
              </div>

              {/* Dot Only */}
              <div>
                <p className="text-sm text-gray-400 mb-3">Dot Indicators</p>
                <div className="flex flex-wrap gap-3">
                  <StatusBadge
                    status="online"
                    variant="dot"
                    showDot
                    showIcon={false}
                  >
                    Model Active
                  </StatusBadge>
                  <StatusBadge
                    status="offline"
                    variant="dot"
                    showDot
                    showIcon={false}
                  >
                    Offline
                  </StatusBadge>
                  <StatusBadge
                    status="processing"
                    variant="dot"
                    showDot
                    showIcon={false}
                    pulse
                  >
                    Processing
                  </StatusBadge>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Search Examples */}
        <div>
          <h2 className="text-2xl font-semibold mb-4">Search Components</h2>

          {/* SearchBar Examples */}
          <div className="mb-8">
            <h3 className="text-lg font-medium mb-4">SearchBar Component</h3>

            <div className="space-y-6">
              {/* Basic Search */}
              <div>
                <p className="text-sm text-gray-400 mb-3">Basic Search</p>
                <SearchBar
                  placeholder="Search models..."
                  onChange={(value) => console.log("Search:", value)}
                  className="max-w-md"
                />
              </div>

              {/* With Filters */}
              <div>
                <p className="text-sm text-gray-400 mb-3">With Filters</p>
                <SearchBar
                  placeholder="Search with filters..."
                  showFilter
                  filters={[
                    { value: "active", label: "Active Models", count: 24 },
                    { value: "verified", label: "Verified Only", count: 18 },
                    { value: "recent", label: "Recently Added", count: 7 },
                  ]}
                  activeFilters={["active"]}
                  onFilterChange={(filter) => console.log("Filter:", filter)}
                  className="max-w-md"
                />
              </div>

              {/* With Suggestions */}
              <div>
                <p className="text-sm text-gray-400 mb-3">With Suggestions</p>
                <SearchBar
                  placeholder="Search with suggestions..."
                  showSuggestions
                  suggestions={[
                    {
                      value: "Emma Wilson",
                      label: "Emma Wilson",
                      category: "Model",
                    },
                    {
                      value: "Fashion Photography",
                      label: "Fashion Photography",
                      category: "Category",
                    },
                    {
                      value: "Los Angeles",
                      label: "Los Angeles",
                      category: "Location",
                    },
                    {
                      value: "Portfolio Review",
                      label: "Portfolio Review",
                      category: "Task",
                    },
                  ]}
                  onChange={(value) => console.log("Search:", value)}
                  className="max-w-md"
                />
              </div>

              {/* Size Variations */}
              <div>
                <p className="text-sm text-gray-400 mb-3">Size Variations</p>
                <div className="space-y-3 max-w-md">
                  <SearchBar size="sm" placeholder="Small search..." />
                  <SearchBar size="default" placeholder="Default search..." />
                  <SearchBar size="lg" placeholder="Large search..." />
                </div>
              </div>

              {/* Variant Styles */}
              <div>
                <p className="text-sm text-gray-400 mb-3">Variant Styles</p>
                <div className="space-y-3 max-w-md">
                  <SearchBar
                    variant="default"
                    placeholder="Default variant..."
                  />
                  <SearchBar
                    variant="minimal"
                    placeholder="Minimal variant..."
                  />
                  <SearchBar
                    variant="bordered"
                    placeholder="Bordered variant..."
                  />
                </div>
              </div>

              {/* Loading State */}
              <div>
                <p className="text-sm text-gray-400 mb-3">Loading State</p>
                <SearchBar
                  placeholder="Searching..."
                  loading
                  value="Emma Wilson"
                  className="max-w-md"
                />
              </div>
            </div>
          </div>
        </div>

        {/* DatePicker Examples */}
        <div>
          <h2 className="text-2xl font-semibold mb-4">DatePicker Components</h2>

          {/* DatePicker Examples */}
          <div className="mb-8">
            <h3 className="text-lg font-medium mb-4">DatePicker Component</h3>

            <div className="space-y-6">
              {/* Basic DatePicker */}
              <div>
                <p className="text-sm text-gray-400 mb-3">
                  Basic Date Selection
                </p>
                <DatePicker
                  placeholder="Select date..."
                  onChange={(date) => console.log("Selected date:", date)}
                  className="max-w-xs"
                />
              </div>

              {/* DatePicker with Time */}
              <div>
                <p className="text-sm text-gray-400 mb-3">
                  Date & Time Selection
                </p>
                <DatePicker
                  placeholder="Select date and time..."
                  showTime
                  onChange={(date) => console.log("Selected datetime:", date)}
                  className="max-w-xs"
                />
              </div>

              {/* Size Variations */}
              <div>
                <p className="text-sm text-gray-400 mb-3">Size Variations</p>
                <div className="space-y-3 max-w-xs">
                  <DatePicker size="sm" placeholder="Small datepicker..." />
                  <DatePicker
                    size="default"
                    placeholder="Default datepicker..."
                  />
                  <DatePicker size="lg" placeholder="Large datepicker..." />
                </div>
              </div>

              {/* Variant Styles */}
              <div>
                <p className="text-sm text-gray-400 mb-3">Variant Styles</p>
                <div className="space-y-3 max-w-xs">
                  <DatePicker
                    variant="default"
                    placeholder="Default variant..."
                  />
                  <DatePicker
                    variant="outline"
                    placeholder="Outline variant..."
                  />
                  <DatePicker variant="ghost" placeholder="Ghost variant..." />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Avatar Examples */}
        <div>
          <h2 className="text-2xl font-semibold mb-4">Avatar Components</h2>

          {/* AvatarGroup Examples */}
          <div className="mb-8">
            <h3 className="text-lg font-medium mb-4">AvatarGroup Component</h3>

            <div className="space-y-6">
              {/* Basic Avatar Group */}
              <div>
                <p className="text-sm text-gray-400 mb-3">Basic Team Display</p>
                <AvatarGroup
                  avatars={[
                    { name: "Emma Wilson", src: null, status: "Online" },
                    { name: "Alex Johnson", src: null, status: "Away" },
                    { name: "Sarah Davis", src: null, status: "Busy" },
                    { name: "Mike Brown", src: null, status: "Offline" },
                  ]}
                  onAvatarClick={(avatar) =>
                    console.log("Clicked:", avatar.name)
                  }
                />
              </div>

              {/* Large Team with Counter */}
              <div>
                <p className="text-sm text-gray-400 mb-3">
                  Large Team (Max 4 shown)
                </p>
                <AvatarGroup
                  max={4}
                  avatars={[
                    { name: "Team Lead", src: null },
                    { name: "Developer 1", src: null },
                    { name: "Developer 2", src: null },
                    { name: "Designer 1", src: null },
                    { name: "Designer 2", src: null },
                    { name: "Product Manager", src: null },
                    { name: "QA Engineer", src: null },
                  ]}
                />
              </div>

              {/* Size Variations */}
              <div>
                <p className="text-sm text-gray-400 mb-3">Size Variations</p>
                <div className="space-y-4">
                  <div className="flex items-center gap-4">
                    <span className="text-xs text-gray-500 w-12">XS:</span>
                    <AvatarGroup
                      size="xs"
                      avatars={[
                        { name: "User 1" },
                        { name: "User 2" },
                        { name: "User 3" },
                      ]}
                    />
                  </div>
                  <div className="flex items-center gap-4">
                    <span className="text-xs text-gray-500 w-12">SM:</span>
                    <AvatarGroup
                      size="sm"
                      avatars={[
                        { name: "User 1" },
                        { name: "User 2" },
                        { name: "User 3" },
                      ]}
                    />
                  </div>
                  <div className="flex items-center gap-4">
                    <span className="text-xs text-gray-500 w-12">LG:</span>
                    <AvatarGroup
                      size="lg"
                      avatars={[
                        { name: "User 1" },
                        { name: "User 2" },
                        { name: "User 3" },
                      ]}
                    />
                  </div>
                </div>
              </div>

              {/* With Add Button */}
              <div>
                <p className="text-sm text-gray-400 mb-3">With Add Button</p>
                <AvatarGroup
                  showAddButton
                  avatars={[
                    { name: "Current Member 1" },
                    { name: "Current Member 2" },
                  ]}
                  onAddClick={() => console.log("Add new member")}
                />
              </div>

              {/* Vertical Layout */}
              <div>
                <p className="text-sm text-gray-400 mb-3">Vertical Layout</p>
                <AvatarGroup
                  direction="vertical"
                  avatars={[
                    { name: "Emma Wilson", status: "Project Lead" },
                    { name: "Alex Johnson", status: "Developer" },
                    { name: "Sarah Davis", status: "Designer" },
                  ]}
                />
              </div>
            </div>
          </div>
        </div>

        {/* EmptyState Examples */}
        <div>
          <h2 className="text-2xl font-semibold mb-4">EmptyState Components</h2>

          {/* EmptyState Examples */}
          <div className="mb-8">
            <h3 className="text-lg font-medium mb-4">EmptyState Component</h3>

            <div className="space-y-6">
              {/* Different Types */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Default */}
                <EmptyState
                  type="default"
                  onActionClick={() => console.log("Get started clicked")}
                />

                {/* Search */}
                <EmptyState
                  type="search"
                  onActionClick={() => console.log("Clear filters clicked")}
                />

                {/* Upload */}
                <EmptyState
                  type="upload"
                  onActionClick={() => console.log("Upload files clicked")}
                />

                {/* Users */}
                <EmptyState
                  type="users"
                  onActionClick={() => console.log("Invite users clicked")}
                />
              </div>

              {/* Custom Content */}
              <div>
                <p className="text-sm text-gray-400 mb-3">Custom Content</p>
                <EmptyState
                  title="No Models Found"
                  description="You haven't added any models to your agency yet. Start building your talent roster."
                  actionText="Add First Model"
                  secondaryActionText="Import Models"
                  onActionClick={() => console.log("Add model clicked")}
                  onSecondaryActionClick={() => console.log("Import clicked")}
                />
              </div>

              {/* Size Variations */}
              <div>
                <p className="text-sm text-gray-400 mb-3">Size Variations</p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <EmptyState
                    type="files"
                    size="sm"
                    onActionClick={() => console.log("Small action")}
                  />
                  <EmptyState
                    type="files"
                    size="default"
                    onActionClick={() => console.log("Default action")}
                  />
                  <EmptyState
                    type="files"
                    size="lg"
                    onActionClick={() => console.log("Large action")}
                  />
                </div>
              </div>

              {/* Variant Styles */}
              <div>
                <p className="text-sm text-gray-400 mb-3">Variant Styles</p>
                <div className="space-y-4">
                  <EmptyState
                    type="inbox"
                    variant="default"
                    showAction={false}
                  />
                  <EmptyState
                    type="documents"
                    variant="bordered"
                    showAction={false}
                  />
                  <EmptyState
                    type="create"
                    variant="minimal"
                    showAction={false}
                  />
                </div>
              </div>

              {/* Error States */}
              <div>
                <p className="text-sm text-gray-400 mb-3">
                  Error & Special States
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <EmptyState
                    type="error"
                    onActionClick={() => console.log("Retry clicked")}
                  />
                  <EmptyState
                    type="offline"
                    onActionClick={() => console.log("Retry connection")}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ResuableExample;
