import mongoose from "mongoose";

/**
 * ContentGoal Schema - Defines content goals for planning
 * Used to guide content creation with specific objectives
 */
const contentGoalSchema = new mongoose.Schema(
  {
    // Goal classification
    type: {
      type: String,
      required: true,
      enum: [
        "growth",
        "engagement",
        "brand_awareness",
        "conversion",
        "education",
        "entertainment",
        "community_building",
      ],
      index: true,
    },

    // Target audience definition
    targetAudience: {
      type: String,
      required: true,
      maxlength: 500,
      trim: true,
    },

    // SEO and content keywords
    keywords: {
      type: [String],
      required: true,
      validate: {
        validator: function (keywords) {
          return keywords && keywords.length >= 1 && keywords.length <= 20;
        },
        message: "Must have 1-20 keywords",
      },
    },

    // Associated persona for content alignment
    personaId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "PersonaProfile",
      required: true,
      index: true,
    },

    // Content tone specification
    contentTone: {
      type: String,
      required: true,
      enum: [
        "professional",
        "casual",
        "friendly",
        "authoritative",
        "humorous",
        "inspirational",
        "educational",
        "conversational",
      ],
    },

    // Content format preferences
    preferredFormats: {
      type: [String],
      enum: ["image", "video", "carousel", "reel", "story", "text"],
      default: ["image"],
    },

    // Platform-specific settings
    platforms: {
      type: [String],
      enum: ["instagram", "tiktok", "facebook", "twitter", "linkedin"],
      required: true,
    },

    // Goal metrics and targets
    metrics: {
      targetReach: {
        type: Number,
        min: 0,
      },
      targetEngagement: {
        type: Number,
        min: 0,
        max: 100, // percentage
      },
      targetFollowers: {
        type: Number,
        min: 0,
      },
    },

    // Goal duration and timeline
    duration: {
      startDate: {
        type: Date,
        required: true,
      },
      endDate: {
        type: Date,
        required: true,
      },
    },

    // Status tracking
    status: {
      type: String,
      enum: ["active", "completed", "paused", "cancelled"],
      default: "active",
      index: true,
    },

    // Performance tracking
    performance: {
      actualReach: {
        type: Number,
        default: 0,
      },
      actualEngagement: {
        type: Number,
        default: 0,
      },
      actualFollowers: {
        type: Number,
        default: 0,
      },
      lastUpdated: {
        type: Date,
        default: Date.now,
      },
    },

    // Ownership and creation tracking
    agencyId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      required: true,
      index: true,
    },

    modelId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ModelUser",
      required: true,
      index: true,
    },

    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
    },

    createdByType: {
      type: String,
      required: true,
      enum: ["Agency", "ModelUser", "Employee"],
    },

    // Versioning and archival
    version: {
      type: Number,
      default: 1,
    },

    isArchived: {
      type: Boolean,
      default: false,
      index: true,
    },

    // Notes and additional context
    description: {
      type: String,
      maxlength: 1000,
      trim: true,
    },

    // AI generation metadata
    aiGenerated: {
      type: Boolean,
      default: false,
    },

    generationPrompt: {
      type: String,
      maxlength: 2000,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  },
);

// Compound indexes for efficient queries
contentGoalSchema.index({ agencyId: 1, modelId: 1, status: 1 });
contentGoalSchema.index({ agencyId: 1, type: 1, createdAt: -1 });
contentGoalSchema.index({ modelId: 1, status: 1, "duration.startDate": 1 });

// Virtual for goal progress calculation
contentGoalSchema.virtual("progress").get(function () {
  if (!this.metrics.targetReach && !this.metrics.targetEngagement) {
    return 0;
  }

  let totalProgress = 0;
  let metricCount = 0;

  if (this.metrics.targetReach && this.metrics.targetReach > 0) {
    totalProgress += Math.min(
      (this.performance.actualReach / this.metrics.targetReach) * 100,
      100,
    );
    metricCount++;
  }

  if (this.metrics.targetEngagement && this.metrics.targetEngagement > 0) {
    totalProgress += Math.min(
      (this.performance.actualEngagement / this.metrics.targetEngagement) * 100,
      100,
    );
    metricCount++;
  }

  return metricCount > 0 ? Math.round(totalProgress / metricCount) : 0;
});

// Virtual for goal time remaining
contentGoalSchema.virtual("timeRemaining").get(function () {
  const now = new Date();
  const endDate = new Date(this.duration.endDate);

  if (endDate <= now) {
    return 0;
  }

  return Math.ceil((endDate - now) / (1000 * 60 * 60 * 24)); // days remaining
});

// Pre-save middleware
contentGoalSchema.pre("save", function (next) {
  // Validate date range
  if (this.duration.startDate >= this.duration.endDate) {
    return next(new Error("End date must be after start date"));
  }

  // Auto-archive completed goals
  if (this.status === "completed" && !this.isArchived) {
    this.performance.lastUpdated = new Date();
  }

  next();
});

// Static methods
contentGoalSchema.statics.findActiveGoals = function (agencyId, modelId) {
  return this.find({
    agencyId,
    modelId,
    status: "active",
    isArchived: false,
  }).populate("personaId", "personaText tags");
};

contentGoalSchema.statics.getGoalsByType = function (
  agencyId,
  type,
  limit = 10,
) {
  return this.find({
    agencyId,
    type,
    isArchived: false,
  })
    .sort({ createdAt: -1 })
    .limit(limit)
    .populate("modelId", "username fullName")
    .populate("personaId", "personaText tags");
};

// Instance methods
contentGoalSchema.methods.updatePerformance = function (metrics) {
  this.performance = {
    ...this.performance,
    ...metrics,
    lastUpdated: new Date(),
  };

  // Auto-complete if targets are met
  if (
    this.metrics.targetReach &&
    this.performance.actualReach >= this.metrics.targetReach &&
    this.metrics.targetEngagement &&
    this.performance.actualEngagement >= this.metrics.targetEngagement
  ) {
    this.status = "completed";
  }

  return this.save();
};

const ContentGoal = mongoose.model("ContentGoal", contentGoalSchema);
export default ContentGoal;
