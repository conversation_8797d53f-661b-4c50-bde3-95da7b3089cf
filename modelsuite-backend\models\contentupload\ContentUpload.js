import mongoose from "mongoose";

/**
 * Content Upload Schema - Manages file uploads with metadata,
 * approval workflow, and Cloudflare R2 integration
 */
const contentUploadSchema = new mongoose.Schema(
  {
    modelId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ModelUser",
      required: [true, "Model ID is required"],
      index: true,
    },
    agencyId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      required: [true, "Agency ID is required"],
      index: true,
    },
    categoryId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ContentCategory",
      required: [true, "Category ID is required"],
      index: true,
    },
    // File information
    filename: {
      type: String,
      required: [true, "Filename is required"],
      trim: true,
    },
    originalName: {
      type: String,
      required: [true, "Original filename is required"],
      trim: true,
    },
    mimeType: {
      type: String,
      required: [true, "MIME type is required"],
    },
    fileSize: {
      type: Number,
      required: [true, "File size is required"],
      min: [1, "File size must be greater than 0"],
    },
    fileExtension: {
      type: String,
      required: [true, "File extension is required"],
      lowercase: true,
    },
    // Cloudflare R2 storage details
    r2Key: {
      type: String,
      required: [true, "R2 key is required"],
      unique: true,
      index: true,
    },
    r2Url: {
      type: String,
      required: [true, "R2 URL is required"],
    },
    r2Bucket: {
      type: String,
      default: "modelsuite-uploads",
    },
    // Upload status and workflow
    status: {
      type: String,
      enum: {
        values: ["pending", "uploaded", "approved", "rejected", "archived"],
        message:
          "Status must be one of: pending, uploaded, approved, rejected, archived",
      },
      default: "uploaded",
      index: true,
    },
    // Compliance and consent
    hasConsent: {
      type: Boolean,
      required: [true, "Consent status is required"],
      default: false,
    },
    releaseFormUrl: {
      type: String,
      trim: true,
    },
    hasWatermark: {
      type: Boolean,
      default: false,
    },
    // Content metadata
    metadata: {
      title: {
        type: String,
        trim: true,
        maxlength: [200, "Title cannot exceed 200 characters"],
      },
      description: {
        type: String,
        trim: true,
        maxlength: [1000, "Description cannot exceed 1000 characters"],
      },
      tags: {
        type: [String],
        default: [],
        validate: {
          validator: function (tags) {
            return tags.length <= 20;
          },
          message: "Cannot have more than 20 tags",
        },
      },
      platformTags: {
        type: [String],
        default: [],
      },
      customFields: {
        type: mongoose.Schema.Types.Mixed,
        default: {},
      },
    },
    // Approval workflow
    approvedAt: {
      type: Date,
      index: true,
    },
    approvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
    },
    rejectedAt: {
      type: Date,
    },
    rejectedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
    },
    rejectionReason: {
      type: String,
      trim: true,
      maxlength: [500, "Rejection reason cannot exceed 500 characters"],
    },
    // Comments and feedback
    comments: [
      {
        commentBy: {
          type: mongoose.Schema.Types.ObjectId,
          required: true,
          refPath: "comments.commentByType",
        },
        commentByType: {
          type: String,
          required: true,
          enum: ["Agency", "ModelUser"],
        },
        comment: {
          type: String,
          required: true,
          trim: true,
          maxlength: [1000, "Comment cannot exceed 1000 characters"],
        },
        commentedAt: {
          type: Date,
          default: Date.now,
        },
      },
    ],
    // Due dates and reminders
    dueDate: {
      type: Date,
      index: true,
    },
    nextDueDate: {
      type: Date,
      index: true,
    },
    remindersSent: {
      type: Number,
      default: 0,
    },
    lastReminderSent: {
      type: Date,
    },
    // Analytics and tracking
    downloadCount: {
      type: Number,
      default: 0,
    },
    viewCount: {
      type: Number,
      default: 0,
    },
    lastAccessed: {
      type: Date,
    },
    // Soft delete
    isDeleted: {
      type: Boolean,
      default: false,
      index: true,
    },
    deletedAt: {
      type: Date,
    },
    deletedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  },
);

// Compound indexes for efficient queries
contentUploadSchema.index({ modelId: 1, categoryId: 1 });
contentUploadSchema.index({ agencyId: 1, status: 1 });
contentUploadSchema.index({ status: 1, createdAt: -1 });
contentUploadSchema.index({ dueDate: 1, status: 1 });
contentUploadSchema.index({ nextDueDate: 1, isDeleted: 1 });
contentUploadSchema.index({ agencyId: 1, createdAt: -1 });
contentUploadSchema.index({ modelId: 1, createdAt: -1 });

// Virtual for file size in human readable format
contentUploadSchema.virtual("fileSizeFormatted").get(function () {
  const bytes = this.fileSize;
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
});

// Virtual for checking if upload is overdue
contentUploadSchema.virtual("isOverdue").get(function () {
  if (!this.dueDate) return false;
  return new Date() > this.dueDate && this.status !== "approved";
});

// Virtual for days until due
contentUploadSchema.virtual("daysUntilDue").get(function () {
  if (!this.dueDate) return null;
  const now = new Date();
  const due = new Date(this.dueDate);
  const diffTime = due - now;
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
});

// Pre-save middleware to set next due date
contentUploadSchema.pre("save", async function (next) {
  if (this.isNew || this.isModified("categoryId")) {
    try {
      const category = await mongoose
        .model("ContentCategory")
        .findById(this.categoryId);
      if (category && category.reminderFrequency) {
        const nextDue = new Date();
        nextDue.setDate(nextDue.getDate() + category.reminderFrequency);
        this.nextDueDate = nextDue;
      }
    } catch (error) {
      console.error("Error setting next due date:", error);
    }
  }
  next();
});

// Static method to find uploads by status
contentUploadSchema.statics.findByStatus = function (status, options = {}) {
  const query = { status, isDeleted: false };
  if (options.agencyId) query.agencyId = options.agencyId;
  if (options.modelId) query.modelId = options.modelId;

  return this.find(query)
    .populate("modelId", "username profilePhoto")
    .populate("categoryId", "label platform")
    .sort({ createdAt: -1 });
};

// Static method to find overdue uploads
contentUploadSchema.statics.findOverdue = function (agencyId) {
  const now = new Date();
  return this.find({
    agencyId,
    dueDate: { $lt: now },
    status: { $nin: ["approved", "archived"] },
    isDeleted: false,
  })
    .populate("modelId", "username profilePhoto")
    .populate("categoryId", "label platform")
    .sort({ dueDate: 1 });
};

// Instance method to approve upload
contentUploadSchema.methods.approve = function (approvedBy, comment = null) {
  this.status = "approved";
  this.approvedAt = new Date();
  this.approvedBy = approvedBy;

  if (comment) {
    this.comments.push({
      commentBy: approvedBy,
      commentByType: "Agency",
      comment: comment,
    });
  }

  return this.save();
};

// Instance method to reject upload
contentUploadSchema.methods.reject = function (
  rejectedBy,
  reason,
  comment = null,
) {
  this.status = "rejected";
  this.rejectedAt = new Date();
  this.rejectedBy = rejectedBy;
  this.rejectionReason = reason;

  if (comment) {
    this.comments.push({
      commentBy: rejectedBy,
      commentByType: "Agency",
      comment: comment,
    });
  }

  return this.save();
};

// Instance method to add comment
contentUploadSchema.methods.addComment = function (
  commentBy,
  commentByType,
  comment,
) {
  this.comments.push({
    commentBy,
    commentByType,
    comment,
  });

  return this.save();
};

const ContentUpload = mongoose.model("ContentUpload", contentUploadSchema);

export default ContentUpload;
