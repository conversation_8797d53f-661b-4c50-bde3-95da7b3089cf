import * as React from "react"

// Import shadcn sidebar UI components
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from "@/components/ui/sidebar"

// You may add your own custom header or version switcher here if needed

// Example navigation data (replace with your own)
const navMain = [
  {
    title: "Dashboard",
    url: "/agency/dashboard",
    icon: "LayoutDashboard",
    items: [],
  },
  {
    title: "Models",
    url: "/agency/models",
    icon: "Users",
    items: [],
  },
  {
    title: "Contracts",
    url: "#",
    icon: "FileSignature",
    items: [
      { title: "Active Contracts", url: "/contracts/active" },
      { title: "Pending Approvals", url: "/contracts/pending" },
    ],
  },
  // ...add the rest of your navigation here
]

export function AppSidebar(props: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar {...props}>
      <SidebarHeader>
        {/* Add custom header content here if needed */}
        <span className="font-bold text-lg">ModelSuite.ai</span>
      </SidebarHeader>
      <SidebarContent>
        {navMain.map((item) => (
          <SidebarGroup key={item.title}>
            <SidebarGroupLabel>{item.title}</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {item.items && item.items.length > 0 ? (
                  item.items.map((child) => (
                    <SidebarMenuItem key={child.title}>
                      <SidebarMenuButton asChild>
                        <a href={child.url}>{child.title}</a>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  ))
                ) : (
                  <SidebarMenuItem>
                    <SidebarMenuButton asChild>
                      <a href={item.url}>{item.title}</a>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                )}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ))}
      </SidebarContent>
      <SidebarRail />
    </Sidebar>
  )
}
