import mongoose from "mongoose";

const shiftReportSchema = new mongoose.Schema(
  {
    employeeId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Employee",
      required: true,
    },
    agencyId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      required: true,
    },

    date: {
      type: String, // Format: YYYY-MM-DD
      required: true,
    },

    // Summary of the day
    workSummary: {
      type: String,
      required: true,
      maxlength: 1000,
    },

    // Tasks completed
    tasksCompleted: [
      {
        taskName: {
          type: String,
          required: true,
          maxlength: 200,
        },
        timeSpent: {
          type: Number, // in minutes
          required: true,
        },
        description: {
          type: String,
          maxlength: 300,
        },
        priority: {
          type: String,
          enum: ["low", "medium", "high", "urgent"],
          default: "medium",
        },
      },
    ],

    //maybe added/uncommented later
    // Challenges faced
    // challenges: {
    //     type: String,
    //     maxlength: 500,
    // },

    // // Tomorrow's priorities
    // tomorrowPriorities: {
    //     type: String,
    //     maxlength: 500,
    // },

    // Total work hours for the day
    totalHours: {
      type: Number,
      required: true,
    },

    // Productivity rating (1-5)
    productivityRating: {
      type: Number,
      min: 1,
      max: 5,
      required: true,
    },

    // Admin notes
    adminNotes: {
      type: String,
      maxlength: 500,
    },

    // // Status
    // status: {
    //     type: String,
    //     enum: ["draft", "submitted", "reviewed"],
    //     default: "submitted",
    // },

    // Submission metadata
    submittedAt: {
      type: Date,
      default: Date.now,
    },

    reviewedAt: {
      type: Date,
    },

    // reviewedBy: {
    //     type: mongoose.Schema.Types.ObjectId,
    //     ref: "Employee",
    // }
  },
  { timestamps: true },
);

// Compound index to ensure one report per employee per day
shiftReportSchema.index({ employeeId: 1, date: 1 }, { unique: true });
shiftReportSchema.index({ agencyId: 1, date: 1 });

export default mongoose.model("ShiftReport", shiftReportSchema);
