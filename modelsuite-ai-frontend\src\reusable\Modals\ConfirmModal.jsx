import * as React from "react";
import { cn } from "@/lib/utils";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { AlertTriangle, Trash2, CheckCircle, X } from "lucide-react";

const ConfirmModal = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = "Confirm",
  cancelText = "Cancel",
  variant = "default",
  loading = false,
  className,
  ...props
}) => {
  // Variant configurations
  const variants = {
    default: {
      icon: CheckCircle,
      iconColor: "text-blue-500",
      confirmVariant: "default",
    },
    destructive: {
      icon: Trash2,
      iconColor: "text-red-500",
      confirmVariant: "destructive",
    },
    warning: {
      icon: AlertTriangle,
      iconColor: "text-yellow-500",
      confirmVariant: "default",
    },
  };

  const config = variants[variant] || variants.default;
  const IconComponent = config.icon;

  const handleConfirm = () => {
    onConfirm();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose} {...props}>
      <DialogContent className={cn("sm:max-w-md", className)}>
        <DialogHeader className="space-y-4">
          {/* Icon */}
          <div className="flex justify-center">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-muted">
              <IconComponent className={cn("h-6 w-6", config.iconColor)} />
            </div>
          </div>

          {/* Title */}
          <DialogTitle className="text-center text-xl font-semibold">
            {title}
          </DialogTitle>

          {/* Message */}
          {message && (
            <DialogDescription className="text-center text-muted-foreground">
              {message}
            </DialogDescription>
          )}
        </DialogHeader>

        <DialogFooter className="gap-2 sm:gap-2">
          <Button
            variant={config.confirmVariant}
            onClick={handleConfirm}
            disabled={loading}
            className="flex-1"
          >
            {loading ? (
              <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
            ) : (
              <IconComponent className="mr-2 h-4 w-4" />
            )}
            {confirmText}
          </Button>
          <Button
            variant="outline"
            onClick={onClose}
            disabled={loading}
            className="flex-1"
          >
            {cancelText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ConfirmModal;
