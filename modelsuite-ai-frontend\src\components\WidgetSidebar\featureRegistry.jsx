import React from "react";
import {
  Users,
  StickyNote,
  Users2,
  <PERSON>,
  Bar<PERSON>hart3,
  <PERSON>ting<PERSON>,
  TrendingUp,
  FileText,
} from "lucide-react";
import LaunchpadRocketIcon from "./icons/LaunchpadRocketIcon";
import SettingsIcon from "./icons/SettingsIcon";
import TeamIcon from "./icons/TeamIcon";
import NotesIcon from "./icons/NotesIcon";
import AnalyticsIcon from "./icons/AnalyticsIcon";
import ContractsIcon from "./icons/ContractsIcon";
import ModelsIcon from "./icons/ModelsIcon";
import CompanyCardIcon from "./CompanyCardIcon";
import QuestionnaireIcon from "./QuestionnaireIcon";
import MessengerIcon from "./MessengerIcon";

// Top 10 features available for selection
export const availableFeatures = [
  {
    id: "models",
    name: "Models",
    icon: ModelsIcon,
    route: "/dashboard/models",
    description: "Manage your models",
  },
  {
    id: "questionnaire",
    name: "Questionnaire",
    icon: QuestionnaireIcon,
    route: "/dashboard/questionnaire",
    description: "Questionnaire system",
  },
  {
    id: "notes",
    name: "Notes System",
    icon: NotesIcon,
    route: "/dashboard/notes",
    description: "Notes and documentation",
  },
  {
    id: "team",
    name: "Team",
    icon: TeamIcon,
    route: "/dashboard/team",
    description: "Team management",
  },
  {
    id: "cards",
    name: "Comp Cards",
    icon: CompanyCardIcon,
    route: "/dashboard/cards",
    description: "Composite cards",
  },
  {
    id: "contracts",
    name: "Contracts",
    icon: ContractsIcon,
    route: "/dashboard/contracts",
    description: "Contract management",
  },
  {
    id: "messages",
    name: "Messages",
    icon: MessengerIcon,
    route: "/dashboard/messages",
    description: "Communication hub",
  },
  {
    id: "analytics",
    name: "Analytics",
    icon: AnalyticsIcon,
    route: "/dashboard/analytics",
    description: "Performance analytics",
  },
  {
    id: "settings",
    name: "Settings",
    icon: SettingsIcon,
    route: "/dashboard/settings",
    description: "System settings",
  },
  {
    id: "trends",
    name: "Trends",
    icon: TrendingUp,
    route: "/dashboard/trends",
    description: "Market trends",
  },
];

// Launchpad feature (always available)
export const launchpadFeature = {
  id: "launchpad",
  name: "Launchpad",
  icon: LaunchpadRocketIcon,
  isLaunchpad: true,
  description: "Open all features",
};

// LocalStorage keys
export const STORAGE_KEYS = {
  SELECTED_FEATURES: "widget_sidebar_selected_features",
};

// Get selected features from localStorage
export const getSelectedFeatures = () => {
  try {
    const stored = localStorage.getItem(STORAGE_KEYS.SELECTED_FEATURES);
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.error("Error reading selected features:", error);
    return [];
  }
};

// Save selected features to localStorage
export const saveSelectedFeatures = (selectedIds) => {
  try {
    localStorage.setItem(
      STORAGE_KEYS.SELECTED_FEATURES,
      JSON.stringify(selectedIds)
    );
  } catch (error) {
    console.error("Error saving selected features:", error);
  }
};

// Get features by IDs
export const getFeaturesByIds = (ids) => {
  return ids
    .map((id) => availableFeatures.find((feature) => feature.id === id))
    .filter(Boolean);
};
