import axiosInstance from "@/config/axiosInstance";

const base = "/notes";

export const fetchNotesByModel = (modelId, params = {}) => {
  return axiosInstance.get(base, { params: { modelId, ...params } });
};

export const getNote = (noteId) => axiosInstance.get(`${base}/${noteId}`);

export const createNote = (payload) => axiosInstance.post(base, payload);

export const updateNote = (noteId, data) =>
  axiosInstance.patch(`${base}/${noteId}`, data);

export const deleteNote = (noteId) => axiosInstance.delete(`${base}/${noteId}`);

export const togglePinNote = (noteId) =>
  axiosInstance.patch(`${base}/${noteId}/pin`);

export const bulkDeleteNotes = (noteIds) =>
  axiosInstance.delete(`${base}/bulk`, { data: { noteIds } });

export const bulkPinNotes = (noteIds, isPinned = true) =>
  axiosInstance.patch(`${base}/bulk/pin`, { noteIds, isPinned });

export const bulkArchiveNotes = (noteIds, isArchived = true) =>
  axiosInstance.patch(`${base}/bulk/archive`, { noteIds, isArchived });

export const getNoteStats = (modelId) =>
  axiosInstance.get(`${base}/model/${modelId}/stats`);

export const fetchNotesByAgency = (agencyId, params = {}) =>
  axiosInstance.get(`${base}/agency/${agencyId}`, { params });

// Attachment upload uses the upload route mounted under /api/v1/upload
export const uploadNoteAttachment = (noteId, file) => {
  const fd = new FormData();
  fd.append("file", file);
  return axiosInstance.post(`/upload/notes/${noteId}/attachments`, fd, {
    headers: { "Content-Type": "multipart/form-data" },
  });
};

export default {
  fetchNotesByModel,
  getNote,
  createNote,
  updateNote,
  deleteNote,
  togglePinNote,
  bulkDeleteNotes,
  bulkPinNotes,
  bulkArchiveNotes,
  getNoteStats,
  uploadNoteAttachment,
  fetchNotesByAgency,
};
