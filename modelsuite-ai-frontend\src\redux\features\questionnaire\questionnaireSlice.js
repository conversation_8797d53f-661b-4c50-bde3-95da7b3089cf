import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axiosInstance from "@/config/axiosInstance";

// Async thunks for Templates
export const fetchTemplates = createAsyncThunk(
  "questionnaire/fetchTemplates",
  async (_, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.get("/questionnaire/templates");
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.error || "Failed to fetch templates");
    }
  }
);

export const createTemplate = createAsyncThunk(
  "questionnaire/createTemplate",
  async (templateData, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.post("/questionnaire/templates", templateData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.error || "Failed to create template");
    }
  }
);

export const updateTemplate = createAsyncThunk(
  "questionnaire/updateTemplate",
  async ({ id, ...templateData }, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.put(`/questionnaire/templates/${id}`, templateData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.error || "Failed to update template");
    }
  }
);

export const deleteTemplate = createAsyncThunk(
  "questionnaire/deleteTemplate",
  async (templateId, { rejectWithValue }) => {
    try {
      await axiosInstance.delete(`/questionnaire/templates/${templateId}`);
      return templateId;
    } catch (error) {
      return rejectWithValue(error.response?.data?.error || "Failed to delete template");
    }
  }
);

export const fetchTemplateById = createAsyncThunk(
  "questionnaire/fetchTemplateById",
  async (templateId, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.get(`/questionnaire/templates/${templateId}`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.error || "Failed to fetch template");
    }
  }
);

// Async thunks for Assignments
export const fetchAssignments = createAsyncThunk(
  "questionnaire/fetchAssignments",
  async (_, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.get("/questionnaire/assignments");
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.error || "Failed to fetch assignments");
    }
  }
);

export const fetchMyAssignments = createAsyncThunk(
  "questionnaire/fetchMyAssignments",
  async (_, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.get("/questionnaire/assignments/my");
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.error || "Failed to fetch my assignments");
    }
  }
);

export const assignTemplate = createAsyncThunk(
  "questionnaire/assignTemplate",
  async (assignmentData, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.post("/questionnaire/assignments", assignmentData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.error || "Failed to assign template");
    }
  }
);

export const bulkAssignTemplate = createAsyncThunk(
  "questionnaire/bulkAssignTemplate",
  async (bulkData, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.post("/questionnaire/assignments/bulk", bulkData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.error || "Failed to bulk assign template");
    }
  }
);

export const updateAssignmentStatus = createAsyncThunk(
  "questionnaire/updateAssignmentStatus",
  async ({ assignmentId, status }, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.patch(`/questionnaire/assignments/${assignmentId}/status`, { status });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.error || "Failed to update assignment status");
    }
  }
);

// Async thunks for Answers
export const submitAnswers = createAsyncThunk(
  "questionnaire/submitAnswers",
  async (answerData, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.post("/questionnaire/answers", answerData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.error || "Failed to submit answers");
    }
  }
);

export const fetchMyAnswers = createAsyncThunk(
  "questionnaire/fetchMyAnswers",
  async (templateId, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.get(`/questionnaire/answers/template/${templateId}`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.error || "Failed to fetch my answers");
    }
  }
);

export const fetchModelAnswers = createAsyncThunk(
  "questionnaire/fetchModelAnswers",
  async ({ modelId, templateId, format = "json" }, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.get(`/questionnaire/answers/${modelId}/${templateId}`, {
        params: { format },
        responseType: format === "pdf" ? "blob" : "json",
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.error || "Failed to fetch model answers");
    }
  }
);

// Enhanced analytics thunk with detailed calculations
export const fetchTemplateAnalytics = createAsyncThunk(
  "questionnaire/fetchTemplateAnalytics",
  async (templateId, { rejectWithValue }) => {
    try {
      const [templateResponse, answersResponse, assignmentsResponse] = await Promise.all([
        axiosInstance.get(`/questionnaire/templates/${templateId}`),
        axiosInstance.get(`/questionnaire/answers/analytics/${templateId}`),
        axiosInstance.get(`/questionnaire/assignments`),
      ]);

      const template = templateResponse.data;
      const answers = answersResponse.data.answers || [];
      const allAssignments = assignmentsResponse.data || [];
      
      // Filter assignments for this template
      const templateAssignments = allAssignments.filter(
        assignment => assignment.templateId?._id === templateId
      );

      // Calculate analytics
      const totalAssignments = templateAssignments.length;
      const completedResponses = answers.length;
      const inProgressResponses = templateAssignments.filter(
        assignment => assignment.status === "In progress"
      ).length;
      const pendingResponses = templateAssignments.filter(
        assignment => assignment.status === "Not started"
      ).length;
      
      const completionRate = totalAssignments > 0 ? (completedResponses / totalAssignments) * 100 : 0;

      // Calculate average completion time
      const completedAnswers = answers.filter(answer => answer.submittedAt);
      const avgCompletionTime = completedAnswers.length > 0 
        ? completedAnswers.reduce((sum, answer) => {
            const assignment = templateAssignments.find(a => a.modelId === answer.modelId);
            if (assignment && answer.submittedAt) {
              const assignedDate = new Date(assignment.assignedAt);
              const completedDate = new Date(answer.submittedAt);
              return sum + (completedDate - assignedDate) / (1000 * 60 * 60 * 24); // days
            }
            return sum;
          }, 0) / completedAnswers.length
        : null;

      // Response trends (last 4 weeks)
      const now = new Date();
      const responseTrends = [];
      for (let i = 3; i >= 0; i--) {
        const weekStart = new Date(now);
        weekStart.setDate(now.getDate() - (i + 1) * 7);
        const weekEnd = new Date(now);
        weekEnd.setDate(now.getDate() - i * 7);
        
        const weekResponses = answers.filter(answer => {
          const submittedDate = new Date(answer.submittedAt);
          return submittedDate >= weekStart && submittedDate < weekEnd;
        });
        
        responseTrends.push({
          week: `Week ${4 - i}`,
          completed: weekResponses.length,
          date: weekStart.toISOString().split('T')[0]
        });
      }

      return {
        template,
        answers,
        assignments: templateAssignments,
        totalAssignments,
        completedResponses,
        inProgressResponses,
        pendingResponses,
        completionRate,
        averageCompletionTime: avgCompletionTime ? `${avgCompletionTime.toFixed(1)} days` : "N/A",
        responseTrends,
        // Individual responses for detailed view
        individualResponses: answers.map(answer => {
          const assignment = templateAssignments.find(a => a.modelId?._id === answer.modelId);
          return {
            ...answer,
            modelName: assignment?.modelId?.fullName || "Unknown Model",
            modelEmail: assignment?.modelId?.email || "Unknown Email",
            assignedAt: assignment?.assignedAt,
            timeTaken: assignment && answer.submittedAt 
              ? Math.round((new Date(answer.submittedAt) - new Date(assignment.assignedAt)) / (1000 * 60 * 60 * 24))
              : null
          };
        })
      };
    } catch (error) {
      return rejectWithValue(error.response?.data?.error || "Failed to fetch template analytics");
    }
  }
);

// Export PDF for individual questionnaire response
export const exportQuestionnairePDF = createAsyncThunk(
  "questionnaire/exportPDF",
  async ({ modelId, templateId }, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.get(`/questionnaire/answers/${modelId}/${templateId}?format=pdf`, {
        responseType: "blob",
      });
      
      // Create blob URL and download
      const blob = new Blob([response.data], { type: "application/pdf" });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `questionnaire-response-${modelId}-${templateId}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      return { success: true };
    } catch (error) {
      return rejectWithValue(error.response?.data?.error || "Failed to export PDF");
    }
  }
);

const initialState = {
  // Templates
  templates: [],
  selectedTemplate: null,
  templateLoading: false,
  templateError: null,

  // Assignments
  assignments: [],
  myAssignments: [],
  assignmentLoading: false,
  assignmentError: null,

  // Answers
  currentAnswers: null,
  modelAnswers: null,
  answerLoading: false,
  answerError: null,

  // Analytics
  analytics: null,
  analyticsLoading: false,
  analyticsError: null,

  // UI State
  currentQuestionnaire: null,
  formProgress: {},
  loading: false,
  error: null,
};

const questionnaireSlice = createSlice({
  name: "questionnaire",
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
      state.templateError = null;
      state.assignmentError = null;
      state.answerError = null;
      state.analyticsError = null;
    },
    setSelectedTemplate: (state, action) => {
      state.selectedTemplate = action.payload;
    },
    setCurrentQuestionnaire: (state, action) => {
      state.currentQuestionnaire = action.payload;
    },
    updateFormProgress: (state, action) => {
      const { questionnaireId, progress } = action.payload;
      state.formProgress[questionnaireId] = progress;
    },
    clearFormProgress: (state, action) => {
      const questionnaireId = action.payload;
      delete state.formProgress[questionnaireId];
    },
    resetQuestionnaireState: (state) => {
      state.currentQuestionnaire = null;
      state.currentAnswers = null;
      state.selectedTemplate = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Templates
      .addCase(fetchTemplates.pending, (state) => {
        state.templateLoading = true;
        state.templateError = null;
      })
      .addCase(fetchTemplates.fulfilled, (state, action) => {
        state.templateLoading = false;
        state.templates = action.payload;
      })
      .addCase(fetchTemplates.rejected, (state, action) => {
        state.templateLoading = false;
        state.templateError = action.payload;
      })

      .addCase(createTemplate.pending, (state) => {
        state.templateLoading = true;
        state.templateError = null;
      })
      .addCase(createTemplate.fulfilled, (state, action) => {
        state.templateLoading = false;
        state.templates.push(action.payload);
      })
      .addCase(createTemplate.rejected, (state, action) => {
        state.templateLoading = false;
        state.templateError = action.payload;
      })

      .addCase(updateTemplate.fulfilled, (state, action) => {
        const index = state.templates.findIndex(t => t._id === action.payload._id);
        if (index !== -1) {
          state.templates[index] = action.payload;
        }
      })

      .addCase(deleteTemplate.fulfilled, (state, action) => {
        state.templates = state.templates.filter(t => t._id !== action.payload);
      })

      .addCase(fetchTemplateById.fulfilled, (state, action) => {
        state.selectedTemplate = action.payload;
      })

      // Assignments
      .addCase(fetchAssignments.pending, (state) => {
        state.assignmentLoading = true;
        state.assignmentError = null;
      })
      .addCase(fetchAssignments.fulfilled, (state, action) => {
        state.assignmentLoading = false;
        state.assignments = action.payload;
      })
      .addCase(fetchAssignments.rejected, (state, action) => {
        state.assignmentLoading = false;
        state.assignmentError = action.payload;
      })

      .addCase(fetchMyAssignments.pending, (state) => {
        state.assignmentLoading = true;
        state.assignmentError = null;
      })
      .addCase(fetchMyAssignments.fulfilled, (state, action) => {
        state.assignmentLoading = false;
        state.myAssignments = action.payload;
      })
      .addCase(fetchMyAssignments.rejected, (state, action) => {
        state.assignmentLoading = false;
        state.assignmentError = action.payload;
      })

      .addCase(assignTemplate.fulfilled, (state, action) => {
        state.assignments.push(action.payload);
      })

      .addCase(bulkAssignTemplate.fulfilled, (state, action) => {
        state.assignments.push(...action.payload);
      })

      .addCase(updateAssignmentStatus.fulfilled, (state, action) => {
        const index = state.assignments.findIndex(a => a._id === action.payload._id);
        if (index !== -1) {
          state.assignments[index] = action.payload;
        }
        const myIndex = state.myAssignments.findIndex(a => a._id === action.payload._id);
        if (myIndex !== -1) {
          state.myAssignments[myIndex] = action.payload;
        }
      })

      // Answers
      .addCase(submitAnswers.pending, (state) => {
        state.answerLoading = true;
        state.answerError = null;
      })
      .addCase(submitAnswers.fulfilled, (state, action) => {
        state.answerLoading = false;
        state.currentAnswers = action.payload;
      })
      .addCase(submitAnswers.rejected, (state, action) => {
        state.answerLoading = false;
        state.answerError = action.payload;
      })

      .addCase(fetchMyAnswers.fulfilled, (state, action) => {
        state.currentAnswers = action.payload;
      })

      .addCase(fetchModelAnswers.fulfilled, (state, action) => {
        state.modelAnswers = action.payload;
      })

      // Analytics
      .addCase(fetchTemplateAnalytics.pending, (state) => {
        state.analyticsLoading = true;
        state.analyticsError = null;
      })
      .addCase(fetchTemplateAnalytics.fulfilled, (state, action) => {
        state.analyticsLoading = false;
        state.analytics = action.payload;
      })
      .addCase(fetchTemplateAnalytics.rejected, (state, action) => {
        state.analyticsLoading = false;
        state.analyticsError = action.payload;
      })

      .addCase(exportQuestionnairePDF.fulfilled, (state) => {
        // PDF export successful - maybe show a success message
      });
  },
});

export const {
  clearError,
  setSelectedTemplate,
  setCurrentQuestionnaire,
  updateFormProgress,
  clearFormProgress,
  resetQuestionnaireState,
} = questionnaireSlice.actions;

export default questionnaireSlice.reducer;
