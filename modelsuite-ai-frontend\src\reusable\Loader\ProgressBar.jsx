import * as React from "react";
import { cn } from "@/lib/utils";

const ProgressBar = ({
  value = 0, // 0-100
  max = 100,
  size = "default", // "sm", "default", "lg"
  variant = "default", // "default", "success", "warning", "error"
  showPercentage = false,
  showLabel = false,
  label,
  indeterminate = false,
  className,
  ...props
}) => {
  const normalizedValue = Math.min(Math.max(value, 0), max);
  const percentage = (normalizedValue / max) * 100;

  const getSizeClass = () => {
    switch (size) {
      case "sm":
        return "h-2";
      case "lg":
        return "h-4";
      default:
        return "h-3";
    }
  };

  const getVariantClass = () => {
    switch (variant) {
      case "success":
        return "bg-gray-600";
      case "warning":
        return "bg-gray-500";
      case "error":
        return "bg-gray-700";
      default:
        return "bg-gray-600";
    }
  };

  const getTextColor = () => {
    switch (variant) {
      case "success":
        return "text-gray-400";
      case "warning":
        return "text-gray-400";
      case "error":
        return "text-gray-400";
      default:
        return "text-gray-400";
    }
  };

  return (
    <div className={cn("w-full", className)} {...props}>
      {/* Label */}
      {(showLabel || label) && (
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-gray-300">
            {label || "Progress"}
          </span>
          {showPercentage && !indeterminate && (
            <span className={cn("text-sm font-medium", getTextColor())}>
              {Math.round(percentage)}%
            </span>
          )}
        </div>
      )}

      {/* Progress Bar */}
      <div
        className={cn(
          "w-full bg-[#1a1a1a] rounded-full overflow-hidden",
          getSizeClass()
        )}
      >
        <div
          className={cn(
            "h-full rounded-full transition-all duration-300 ease-out",
            getVariantClass(),
            indeterminate && "animate-pulse"
          )}
          style={{
            width: indeterminate ? "100%" : `${percentage}%`,
            animation: indeterminate
              ? "progress-indeterminate 2s ease-in-out infinite"
              : undefined,
          }}
        />
      </div>

      {/* Inline percentage for small bars */}
      {showPercentage && !showLabel && !indeterminate && (
        <div className="mt-1 text-right">
          <span className={cn("text-xs", getTextColor())}>
            {Math.round(percentage)}%
          </span>
        </div>
      )}

      <style>{`
        @keyframes progress-indeterminate {
          0% {
            transform: translateX(-100%);
          }
          50% {
            transform: translateX(0%);
          }
          100% {
            transform: translateX(100%);
          }
        }
      `}</style>
    </div>
  );
};

export default ProgressBar;
