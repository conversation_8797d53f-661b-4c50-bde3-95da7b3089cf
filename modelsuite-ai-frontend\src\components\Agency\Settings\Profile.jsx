import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

const Profile = () => {
  const [form, setForm] = useState({
    firstName: "",
    lastName: "",
    email: "",
    username: "",
    agencyName: ""
  });

  const handleChange = (e) => {
    setForm((prev) => ({ ...prev, [e.target.name]: e.target.value }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // TODO: Add submit logic
    console.log(form);
  };

  return (
    <form onSubmit={handleSubmit} className="w-full">
      <div className="flex flex-col md:flex-row items-start gap-6 mb-8 pl-2">
        <img
          className="w-20 h-20 rounded-full object-cover border border-[#2a2a2a]"
          src="https://github.com/shadcn.png"
          alt="Profile"
        />
        <div className="mt-2">
          <div className="text-lg font-semibold text-white mb-1">
            {/* Replace with dynamic name if available */}
            Rahul
          </div>
          <div className="text-sm text-gray-400">Agency Manager</div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pl-2">
        <div>
          <label htmlFor="firstName" className="block text-sm font-medium text-gray-300 mb-1">
            First Name
          </label>
          <Input
            id="firstName"
            name="firstName"
            placeholder="Enter first name"
            value={form.firstName}
            onChange={handleChange}
            className="bg-[#1a1a1a] border border-[#2a2a2a] text-white focus:border-[#2563EB] focus:ring-1 focus:ring-[#2563EB]"
            autoComplete="off"
          />
        </div>
        <div>
          <label htmlFor="lastName" className="block text-sm font-medium text-gray-300 mb-1">
            Last Name
          </label>
          <Input
            id="lastName"
            name="lastName"
            placeholder="Enter last name"
            value={form.lastName}
            onChange={handleChange}
            className="bg-[#1a1a1a] border border-[#2a2a2a] text-white focus:border-[#2563EB] focus:ring-1 focus:ring-[#2563EB]"
            autoComplete="off"
          />
        </div>
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-1">
            Email Address
          </label>
          <Input
            id="email"
            name="email"
            type="email"
            placeholder="Enter email address"
            value={form.email}
            onChange={handleChange}
            className="bg-[#1a1a1a] border border-[#2a2a2a] text-white focus:border-[#2563EB] focus:ring-1 focus:ring-[#2563EB]"
            autoComplete="off"
          />
        </div>
        <div>
          <label htmlFor="username" className="block text-sm font-medium text-gray-300 mb-1">
            Username
          </label>
          <Input
            id="username"
            name="username"
            placeholder="Enter username"
            value={form.username}
            onChange={handleChange}
            className="bg-[#1a1a1a] border border-[#2a2a2a] text-white focus:border-[#2563EB] focus:ring-1 focus:ring-[#2563EB]"
            autoComplete="off"
          />
        </div>
        <div className="md:col-span-2">
          <label htmlFor="agencyName" className="block text-sm font-medium text-gray-300 mb-1">
            Agency Name
          </label>
          <Input
            id="agencyName"
            name="agencyName"
            placeholder="Enter agency name"
            value={form.agencyName}
            onChange={handleChange}
            className="bg-[#1a1a1a] border border-[#2a2a2a] text-white focus:border-[#2563EB] focus:ring-1 focus:ring-[#2563EB]"
            autoComplete="off"
          />
        </div>
      </div>

      <div className="flex justify-end mt-8 pr-2">
        <Button
          type="submit"
          className="px-6 py-2 rounded-md text-sm font-semibold bg-[#2563EB] text-white hover:bg-[#2563EB]/90 focus:ring-2 focus:ring-[#2563EB] focus:ring-offset-2"
        >
          Save Changes
        </Button>
      </div>
    </form>
  );
};

export default Profile;
