import express from "express";
import { Trend } from "../../models/viraltrends/trendschema.js";
import axios from "axios";
import {
  extractTags,
  calculateViralScore,
  getCreatorRelevanceScore,
  categorizeTrend,
} from "../../models/viraltrends/trendschema.js";
import { configDotenv } from "dotenv";
import { parseString } from "xml2js";
configDotenv();
const API_KEYS = {
  NEWS_API: process.env.NEWS_API_KEY || "4563f96fea7241b09e149051749128d6",
  YOUTUBE_API:
    process.env.YOUTUBE_API_KEY || "***************************************",
  RAPIDAPI_KEY: process.env.RAPIDAPI_KEY || "YOUR_RAPIDAPI_KEY_HERE",
  TWITTER_BEARER:
    process.env.TWITTER_BEARER_TOKEN || "YOUR_TWITTER_BEARER_TOKEN_HERE",
};
async function fetchGoogleTrends() {
  try {
    const response = await axios.get(
      "https://trends.google.com/trends/trendingsearches/daily/rss?geo=US",
    );

    // For demo, returning mock data - implement XML parsing for real data
    return [
      {
        title: "AI ChatGPT Updates",
        summary: "New AI features released for content creators",
        source: "Google Trends",
        url: "https://trends.google.com",
        volume: 95,
      },
      {
        title: "TikTok Algorithm Changes",
        summary: "Latest algorithm updates affecting creator reach",
        source: "Google Trends",
        url: "https://trends.google.com",
        volume: 88,
      },
    ];
  } catch (error) {
    return [];
  }
}
async function fetchNewsData() {
  try {
    const keywords =
      'TikTok OR Instagram OR YouTube OR "social media" OR viral OR AI OR creator';
    const response = await axios.get("https://newsapi.org/v2/everything", {
      params: {
        q: keywords,
        language: "en",
        sortBy: "popularity",
        pageSize: 15,
        from: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        apiKey: API_KEYS.NEWS_API,
      },
    });

    return response.data.articles.map((article) => ({
      title: article.title,
      summary: article.description || article.title.substring(0, 150),
      source: "News API",
      url: article.url,
      volume: 70,
    }));
  } catch (error) {
    return [];
  }
}

// 3. YouTube Trending (Free with quota limits)
async function fetchYouTubeTrending() {
  try {
    const response = await axios.get(
      "https://www.googleapis.com/youtube/v3/videos",
      {
        params: {
          part: "snippet,statistics",
          chart: "mostPopular",
          regionCode: "US",
          maxResults: 10,
          videoCategoryId: "24", // Entertainment
          key: API_KEYS.YOUTUBE_API,
        },
      },
    );

    return response.data.items.map((video) => ({
      title: video.snippet.title,
      summary:
        video.snippet.description?.substring(0, 150) || video.snippet.title,
      source: "YouTube",
      url: `https://youtube.com/watch?v=${video.id}`,
      volume: Math.min(parseInt(video.statistics.viewCount) / 1000000, 100),
    }));
  } catch (error) {
    // console.error("YouTube API fetch error:", error);
    return [];
  }
}

// 4. Twitter/X Trends (using RapidAPI)
async function fetchTwitterTrends() {
  try {
    const response = await axios.get(
      "https://twitter-trends5.p.rapidapi.com/twitter/request.php",
      {
        params: {
          woeid: "1", // Worldwide
        },
        headers: {
          "X-RapidAPI-Key": API_KEYS.RAPIDAPI_KEY,
          "X-RapidAPI-Host": "twitter-trends5.p.rapidapi.com",
        },
      },
    );

    return (
      response.data.trends?.map((trend) => ({
        title: trend.name,
        summary: `Trending on Twitter: ${trend.name}`,
        source: "Twitter",
        url: trend.url || "#",
        volume: trend.tweet_volume
          ? Math.min(trend.tweet_volume / 10000, 100)
          : 60,
      })) || []
    );
  } catch (error) {
    // console.error("Twitter API fetch error:", error.message);
    return [];
  }
}
async function fetchRedditTrends() {
  try {
    const subreddits = ["popular", "all", "TikTok", "YouTube", "socialmedia"];
    const allTrends = [];

    for (const subreddit of subreddits) {
      const response = await axios.get(
        `https://www.reddit.com/r/${subreddit}/hot.json`,
        {
          params: { limit: 5 },
        },
      );

      const posts = response.data.data.children.map((post) => ({
        title: post.data.title,
        summary: post.data.selftext?.substring(0, 150) || post.data.title,
        source: "Reddit",
        url: `https://reddit.com${post.data.permalink}`,
        volume: Math.min(post.data.score / 100, 100),
      }));

      allTrends.push(...posts);
    }
    // console.log("====================================");
    // console.log("Fetch Reddit Trends Successfully!!!!");
    // console.log("====================================");
    return allTrends.slice(0, 10);
  } catch (error) {
    // console.error("Reddit API fetch error:", error.message);
    return [];
  }
}
async function fetchHackerNewsTrends() {
  try {
    const response = await axios.get(
      "https://hacker-news.firebaseio.com/v0/topstories.json",
    );
    const topStories = response.data.slice(0, 10);

    const trends = [];
    for (const id of topStories) {
      const storyResponse = await axios.get(
        `https://hacker-news.firebaseio.com/v0/item/${id}.json`,
      );
      const story = storyResponse.data;

      if (
        story.title &&
        story.title.match(/tech|ai|startup|app|social|digital/i)
      ) {
        trends.push({
          title: story.title,
          summary: story.text?.substring(0, 150) || story.title,
          source: "Hacker News",
          url: story.url || `https://news.ycombinator.com/item?id=${id}`,
          volume: Math.min(story.score / 10, 100),
        });
      }
    }
    // console.log("Perfectly Fetched HackerNews trend!!!");

    return trends;
  } catch (error) {
    // console.error("Hacker News API fetch error:", error.message);
    return [];
  }
}

// Main fetch function
export async function fetchAllTrends() {
  // console.log("🚀 Starting trend fetch...");

  const trendSources = [
    fetchGoogleTrends,
    fetchNewsData,
    fetchYouTubeTrending,
    // fetchTwitterTrends,
    fetchRedditTrends,
    fetchHackerNewsTrends,
  ];

  const allTrends = [];

  for (const fetchFunction of trendSources) {
    try {
      const trends = await fetchFunction();
      allTrends.push(...trends);
      // console.log(
      //   `✅ Fetched ${trends.length} trends from ${fetchFunction.name}`,
      // );
    } catch (error) {
      // console.error(`❌ Error in ${fetchFunction.name}:`, error.message);
    }
  }

  // Process trends
  const processedTrends = allTrends.map((trend) => {
    const category = categorizeTrend(trend.title, trend.summary);
    const relevanceScore = getCreatorRelevanceScore(trend.title, trend.summary);
    const viralScore = calculateViralScore(
      trend.volume,
      80,
      relevanceScore,
      70,
    );
    const tags = extractTags(trend.title, trend.summary, category);

    return {
      ...trend,
      category,
      viral_score: viralScore,
      tags,
      region: "global",
    };
  });

  // Sort and save
  const topTrends = processedTrends
    .sort((a, b) => b.viral_score - a.viral_score)
    .slice(0, 50);

  try {
    await Trend.deleteMany({ expires_at: { $lt: new Date() } });

    for (const trend of topTrends) {
      await Trend.findOneAndUpdate(
        { title: trend.title, source: trend.source },
        trend,
        { upsert: true, new: true },
      );
    }

    console.log(`💾 Saved ${topTrends.length} viral trends to database`);
  } catch (error) {
    // console.error("Database save error:", error);
  }
}
