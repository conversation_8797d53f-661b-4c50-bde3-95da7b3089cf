import cron from "node-cron";
import fs from "fs";
import path from "path";
import { promisify } from "util";

const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);
const unlink = promisify(fs.unlink);

// Directory containing temporary voice files
const TEMP_VOICE_DIR = path.join(process.cwd(), "public", "temp", "voice");
// Files older than this threshold (in milliseconds) will be deleted (e.g., 1 hour)
const MAX_FILE_AGE = 1000 * 60 * 60;

/**
 * Delete files older than MAX_FILE_AGE in TEMP_VOICE_DIR
 */
async function cleanOldVoiceFiles() {
  try {
    const files = await readdir(TEMP_VOICE_DIR);
    const now = Date.now();

    for (const file of files) {
      const filePath = path.join(TEMP_VOICE_DIR, file);
      const fileStats = await stat(filePath);
      if (now - fileStats.mtimeMs > MAX_FILE_AGE) {
        await unlink(filePath);
        console.log(`Deleted old temp voice file: ${file}`);
      }
    }
  } catch (error) {
    if (error.code !== "ENOENT") {
      console.error("Error cleaning temp voice files:", error);
    }
  }
}

// Schedule cleanup every hour
cron.schedule("0 * * * *", cleanOldVoiceFiles);
// Run cleanup immediately on startup
cleanOldVoiceFiles();
