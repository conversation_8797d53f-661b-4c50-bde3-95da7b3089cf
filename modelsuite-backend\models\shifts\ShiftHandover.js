import mongoose from "mongoose";

const shiftHandoverSchema = new mongoose.Schema(
  {
    agencyId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      required: true,
      index: true,
    },
    originalShift: {
      weeklyScheduleId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "WeeklySchedule",
        required: true,
      },
      shiftId: {
        type: mongoose.Schema.Types.ObjectId,
        required: true,
      },
      day: {
        type: String,
        enum: [
          "Monday",
          "Tuesday",
          "Wednesday",
          "Thursday",
          "Friday",
          "Saturday",
          "Sunday",
        ],
        required: true,
      },
      date: {
        type: Date,
        required: true,
      },
      startTime: {
        type: String,
        required: true,
      },
      endTime: {
        type: String,
        required: true,
      },
      position: {
        type: String,
        required: true,
      },
    },
    requestedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Employee",
      required: true,
      index: true,
    },
    reason: {
      type: String,
      trim: true,
      maxlength: 500,
      default: "",
    },
    status: {
      type: String,
      enum: ["pending", "accepted", "rejected", "cancelled"],
      default: "pending",
      index: true,
    },
    acceptedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Employee",
      default: null,
    },
    acceptedAt: {
      type: Date,
      default: null,
    },
    rejectedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Employee",
      default: null,
    },
    rejectedAt: {
      type: Date,
      default: null,
    },

    expiresAt: {
      type: Date,
      required: true,
      index: { expireAfterSeconds: 0 },
    },
  },
  {
    timestamps: true,
  },
);

// Compound indexes for efficient queries
shiftHandoverSchema.index({ agencyId: 1, status: 1 });
shiftHandoverSchema.index({ requestedBy: 1, status: 1 });
shiftHandoverSchema.index({ acceptedBy: 1, status: 1 });
shiftHandoverSchema.index({ expiresAt: 1 });
shiftHandoverSchema.index({ "originalShift.date": 1, status: 1 });

// Virtual for checking if handover is expired
shiftHandoverSchema.virtual("isExpired").get(function () {
  return new Date() > this.expiresAt;
});

// Method to check if employee can accept this handover
shiftHandoverSchema.methods.canEmployeeAccept = function (employeeId) {
  return (
    this.status === "pending" &&
    !this.isExpired &&
    !this.requestedBy.equals(employeeId)
  );
};

// Static method to find active handovers for an agency
shiftHandoverSchema.statics.findActiveHandovers = function (agencyId) {
  return this.find({
    agencyId,
    status: "pending",
    expiresAt: { $gt: new Date() },
  })
    .populate("requestedBy", "firstName lastName email")
    .populate("originalShift.weeklyScheduleId")
    .sort({ createdAt: -1 });
};

// Static method to find handovers by employee
shiftHandoverSchema.statics.findByEmployee = function (
  employeeId,
  status = null,
) {
  const query = {
    $or: [{ requestedBy: employeeId }, { acceptedBy: employeeId }],
  };

  if (status) {
    query.status = status;
  }

  return this.find(query)
    .populate("requestedBy", "firstName lastName email")
    .populate("acceptedBy", "firstName lastName email")
    .populate("originalShift.weeklyScheduleId")
    .sort({ createdAt: -1 });
};

const ShiftHandover = mongoose.model("ShiftHandover", shiftHandoverSchema);

export default ShiftHandover;
