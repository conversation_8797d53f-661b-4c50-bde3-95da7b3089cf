import express from "express";
import * as taskController from "../../controllers/task/taskController.js";
import { verifyToken } from "../../middlewares/authMiddleware.js";
import checkPermission from "../../middlewares/permissionCheck.js";
import multer from "multer";

const router = express.Router();

// Configure multer for memory storage
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    // Allow images, videos, and PDFs
    if (
      file.mimetype.startsWith("image/") ||
      file.mimetype.startsWith("video/") ||
      file.mimetype === "application/pdf"
    ) {
      cb(null, true);
    } else {
      cb(
        new Error(
          "Invalid file type. Only images, videos, and PDFs are allowed.",
        ),
      );
    }
  },
});

// Apply auth middleware to all routes
router.use(verifyToken);

// Task routes
router.get("/list", checkPermission("tasks.view"), taskController.getTasks);
router.post(
  "/create",
  checkPermission("tasks.create"),
  taskController.createTask,
);
router.put(
  "/:taskId",
  checkPermission("tasks.edit"),
  taskController.updateTask,
);
router.delete(
  "/:taskId",
  checkPermission("tasks.delete"),
  taskController.deleteTask,
);
router.put(
  "/:taskId/hold",
  checkPermission("tasks.edit"),
  taskController.putTaskOnHold,
);

// Comment routes
router.get(
  "/:taskId/comments",
  checkPermission("tasks.view"),
  taskController.getComments,
);
router.post(
  "/:taskId/comments",
  checkPermission("tasks.create"),
  taskController.addComment,
);
router.delete(
  "/:taskId/comments/:commentId",
  checkPermission("tasks.delete"),
  taskController.deleteComment,
);

// Attachment routes
router.get(
  "/:taskId/attachments",
  checkPermission("tasks.view"),
  taskController.getAttachments,
);
router.post(
  "/:taskId/attachments",
  upload.single("file"),
  checkPermission("tasks.create"),
  taskController.uploadAttachment,
);
router.delete(
  "/:taskId/attachments/:attachmentId",
  checkPermission("tasks.delete"),
  taskController.deleteAttachment,
);

export default router;
