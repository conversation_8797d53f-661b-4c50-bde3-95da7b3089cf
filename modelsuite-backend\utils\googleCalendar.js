import { google } from "googleapis";

export const insertEventToGoogleCalendar = async (modelUser, eventData) => {
  const oauth2Client = new google.auth.OAuth2(
    process.env.GOOGLE_CLIENT_ID,
    process.env.GOOGLE_CLIENT_SECRET,
    process.env.GOOGLE_REDIRECT_URI,
  );

  oauth2Client.setCredentials({
    access_token: modelUser.googleAccessToken,
    refresh_token: modelUser.googleRefreshToken,
  });

  const calendar = google.calendar({ version: "v3", auth: oauth2Client });

  // 🟡 Base event structure
  const event = {
    summary: eventData.title,
    description: eventData.description,
  };

  // Handle all-day events vs timed events
  if (eventData.allDay) {
    event.start = {
      date: new Date(eventData.start).toISOString().split("T")[0],
      timeZone: eventData.timezone,
    };
    event.end = {
      date: new Date(eventData.end).toISOString().split("T")[0],
      timeZone: eventData.timezone,
    };
  } else {
    // Ensure proper ISO 8601 format with seconds
    const startDate = new Date(eventData.start);
    const endDate = new Date(eventData.end);

    event.start = {
      dateTime: startDate.toISOString(),
      timeZone: eventData.timezone,
    };
    event.end = {
      dateTime: endDate.toISOString(),
      timeZone: eventData.timezone,
    };
  }

  // Handle recurrence
  if (eventData.recurrence && eventData.recurrence !== "none") {
    const recurrenceRules = [];

    switch (eventData.recurrence) {
      case "daily":
        recurrenceRules.push("RRULE:FREQ=DAILY");
        break;
      case "weekly":
        recurrenceRules.push("RRULE:FREQ=WEEKLY");
        break;
      case "monthly":
        recurrenceRules.push("RRULE:FREQ=MONTHLY");
        break;
    }

    // Add end date if specified
    if (eventData.recurrenceEndDate) {
      const endDate = new Date(eventData.recurrenceEndDate)
        .toISOString()
        .split("T")[0]
        .replace(/-/g, "");
      recurrenceRules[0] += `;UNTIL=${endDate}`;
    }

    event.recurrence = recurrenceRules;
  }

  // ✅ If guests are provided, attach them and enable Meet
  if (eventData.guests && eventData.guests.length > 0) {
    event.attendees = eventData.guests.map((email) => ({ email }));
    event.conferenceData = {
      createRequest: {
        requestId: `meet-${Date.now()}`,
        conferenceSolutionKey: { type: "hangoutsMeet" },
      },
    };
  }

  const response = await calendar.events.insert({
    calendarId: "primary",
    resource: event,
    conferenceDataVersion: event.conferenceData ? 1 : 0,
    sendUpdates: "all",
  });

  return response.data; // includes Google Event ID, Meet link if created
};
