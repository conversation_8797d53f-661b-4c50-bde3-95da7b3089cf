// Widget registry following ModelSuite patterns
import React from "react";
import WeatherWidgetSidebar from "./widgets/WeatherWidgetSidebar";
import ClockWidget from "./widgets/ClockWidget";
import MetricsWidget from "./widgets/MetricsWidget";
import NotificationWidget from "./widgets/NotificationWidget";

// Simple placeholder widgets for testing

export const widgetRegistry = {
  weather: {
    id: "weather",
    title: "Weather",
    component: WeatherWidgetSidebar,
    refreshInterval: 300000, // 5 minutes
    requiredRole: ["agency", "model"], // Role-based access
    defaultEnabled: true,
    description: "Current weather information",
    size: "medium", // small, medium, large
    defaultSize: { width: 300, height: 200 },
  },
  clock: {
    id: "clock",
    title: "Clock",
    component: ClockWidget,
    refreshInterval: 1000, // 1 second
    requiredRole: ["agency", "model"],
    defaultEnabled: true,
    description: "Current time display",
    size: "small",
    defaultSize: { width: 250, height: 150 },
  },
  metrics: {
    id: "metrics",
    title: "Quick Metrics",
    component: MetricsWidget,
    refreshInterval: 60000, // 1 minute
    requiredRole: ["agency"],
    defaultEnabled: true,
    description: "Key agency metrics",
    size: "medium",
    defaultSize: { width: 300, height: 200 },
  },
  notifications: {
    id: "notifications",
    title: "Notifications",
    component: NotificationWidget,
    refreshInterval: 30000, // 30 seconds
    requiredRole: ["agency", "model"],
    defaultEnabled: false,
    description: "Recent notifications",
    size: "medium",
    defaultSize: { width: 320, height: 240 },
  },
};

export const getWidgetById = (id) => {
  return widgetRegistry[id] || null;
};

export const getAvailableWidgets = (userRole = null) => {
  if (!userRole) return Object.values(widgetRegistry);

  return Object.values(widgetRegistry).filter(
    (widget) => !widget.requiredRole || widget.requiredRole.includes(userRole)
  );
};

export const getDefaultWidgets = (userRole = null) => {
  return getAvailableWidgets(userRole).filter(
    (widget) => widget.defaultEnabled
  );
};
