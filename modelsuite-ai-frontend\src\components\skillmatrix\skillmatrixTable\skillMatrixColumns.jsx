export const skillMatrixColumns = [
    {
        id: "srno",
        header: "Sr No.",
        cell: ({ row }) => <p>{row.index + 1}</p>,
    },
    {
        accessorKey: "name",
        header: "STAFF NAME",
    },
    {
        accessorKey: "communicating",
        header: "COMMUNICATING",
        cell: ({ row }) => <p>{row.original.communicating ? "YES" : "NO"}</p>,
    },
    {
        accessorKey: "socialMedia",
        header: "SOCIAL MEDIA",
        cell: ({ row }) => <p>{row.original.socialMedia ? "YES" : "NO"}</p>,
    },
    {
        accessorKey: "photoshop",
        header: "PHOTOSHOP",
        cell: ({ row }) => <p>{row.original.photoshop ? "YES" : "NO"}</p>,
    },
    {
        accessorKey: "creativeDesign",
        header: "CREATIVE DESIGN",
        cell: ({ row }) => <p>{row.original.creativeDesign ? "YES" : "NO"}</p>,
    },
    {
        accessorKey: "videoEditing",
        header: "VIDEO EDITING",
        cell: ({ row }) => <p>{row.original.videoEditing ? "YES" : "NO"}</p>,
    },
    {
        accessorKey: "chatting",
        header: "CHATTING",
        cell: ({ row }) => <p>{row.original.chatting ? "YES" : "NO"}</p>,
    },
    {
        accessorKey: "manager",
        header: "MANAGER",
        cell: ({ row }) => <p>{row.original.manager ? "YES" : "NO"}</p>,
    },
];
