import CaptionLog from "../models/caption/CaptionLog.js";
import gptService from "./captionServices/gptService.js";
import geminiService from "./captionServices/geminiService.js";

/**
 * Service for analyzing caption history to extract tone, style, and content patterns
 * Used for persona generation to understand model's content preferences
 */
class CaptionAnalysisService {
  /**
   * Analyze caption history for a specific model to extract tone and content patterns
   * @param {string} modelId - The model's ID
   * @param {number} limit - Number of recent captions to analyze (default: 20)
   * @returns {object} Analysis results including tone, style, themes, and patterns
   */
  static async analyzeCaptionHistory(modelId, limit = 20) {
    try {
      // Fetch recent caption history for the model
      const captionHistory = await CaptionLog.find({ modelId })
        .sort({ createdAt: -1 })
        .limit(limit)
        .select("captions prompt mediaType aiService createdAt nsfwScore");

      if (!captionHistory || captionHistory.length === 0) {
        return {
          hasData: false,
          message: "No caption history available for analysis",
          tone: "neutral",
          style: "professional",
          themes: [],
          contentTypes: [],
          averageLength: 0,
          totalCaptions: 0,
        };
      }

      // Extract all captions for analysis
      const allCaptions = [];
      const contentTypes = { image: 0, video: 0 };
      const aiServices = { "gpt-4": 0, "gemini-vision": 0 };

      captionHistory.forEach((log) => {
        if (log.captions && log.captions.length > 0) {
          allCaptions.push(...log.captions);
        }
        contentTypes[log.mediaType]++;
        aiServices[log.aiService]++;
      });

      if (allCaptions.length === 0) {
        return {
          hasData: false,
          message: "No captions found in history",
          tone: "neutral",
          style: "professional",
          themes: [],
          contentTypes: [],
          averageLength: 0,
          totalCaptions: 0,
        };
      }

      // Perform AI-powered analysis of caption patterns
      const analysisResult = await this.performAIAnalysis(allCaptions);

      // Calculate basic statistics
      const averageLength = Math.round(
        allCaptions.reduce((sum, caption) => sum + caption.length, 0) /
          allCaptions.length,
      );

      return {
        hasData: true,
        totalCaptions: allCaptions.length,
        totalSessions: captionHistory.length,
        averageLength,
        contentTypes,
        aiServices,
        dateRange: {
          oldest: captionHistory[captionHistory.length - 1]?.createdAt,
          newest: captionHistory[0]?.createdAt,
        },
        ...analysisResult,
      };
    } catch (error) {
      console.error("Caption analysis failed:", error);
      return {
        hasData: false,
        error: error.message,
        tone: "neutral",
        style: "professional",
        themes: [],
        contentTypes: [],
        averageLength: 0,
        totalCaptions: 0,
      };
    }
  }

  /**
   * Use AI to analyze caption patterns and extract insights
   * @param {Array} captions - Array of caption strings
   * @returns {object} AI analysis results
   */
  static async performAIAnalysis(captions) {
    const prompt = `Analyze the following social media captions to extract content patterns and tone:

CAPTIONS:
${captions
  .slice(0, 15)
  .map((caption, index) => `${index + 1}. ${caption}`)
  .join("\n")}

Please analyze these captions and return a JSON response with the following structure:
{
  "tone": "dominant tone (e.g., professional, casual, playful, inspirational, edgy)",
  "style": "writing style (e.g., conversational, formal, trendy, minimalist)",
  "themes": ["theme1", "theme2", "theme3"],
  "keywords": ["keyword1", "keyword2", "keyword3"],
  "emotionalTone": "emotional undertone (e.g., positive, motivational, confident, authentic)",
  "targetAudience": "inferred target audience based on language and content",
  "contentFocus": "main content focus (e.g., lifestyle, fitness, fashion, business)",
  "engagementStyle": "how they engage audience (e.g., questions, calls-to-action, storytelling)"
}

Focus on identifying patterns that would help understand the creator's brand and audience.`;

    try {
      // Try GPT first, fallback to Gemini
      let aiResult;
      try {
        const gptResponse = await gptService.generatePersona(prompt);
        aiResult = gptResponse.content || gptResponse;
      } catch (gptError) {
        console.log("GPT analysis failed, trying Gemini:", gptError.message);
        const geminiResponse = await geminiService.generatePersona(prompt);
        aiResult = geminiResponse.content || geminiResponse;
      }

      // Parse the AI response
      const parsed = this.parseAnalysisResponse(aiResult);
      return parsed;
    } catch (error) {
      console.error("AI analysis failed:", error);
      return {
        tone: "neutral",
        style: "professional",
        themes: ["general content"],
        keywords: [],
        emotionalTone: "neutral",
        targetAudience: "general audience",
        contentFocus: "lifestyle",
        engagementStyle: "standard",
      };
    }
  }

  /**
   * Parse AI analysis response and validate structure
   * @param {string|object} aiResponse - AI response to parse
   * @returns {object} Parsed analysis data
   */
  static parseAnalysisResponse(aiResponse) {
    try {
      let parsed;

      if (typeof aiResponse === "string") {
        // Extract JSON from string response
        const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          parsed = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error("No JSON found in response");
        }
      } else {
        parsed = aiResponse;
      }

      // Validate and provide defaults
      return {
        tone: parsed.tone || "neutral",
        style: parsed.style || "professional",
        themes: Array.isArray(parsed.themes)
          ? parsed.themes
          : ["general content"],
        keywords: Array.isArray(parsed.keywords) ? parsed.keywords : [],
        emotionalTone: parsed.emotionalTone || "neutral",
        targetAudience: parsed.targetAudience || "general audience",
        contentFocus: parsed.contentFocus || "lifestyle",
        engagementStyle: parsed.engagementStyle || "standard",
      };
    } catch (error) {
      console.error("Failed to parse analysis response:", error);
      return {
        tone: "neutral",
        style: "professional",
        themes: ["general content"],
        keywords: [],
        emotionalTone: "neutral",
        targetAudience: "general audience",
        contentFocus: "lifestyle",
        engagementStyle: "standard",
      };
    }
  }

  /**
   * Format caption analysis for persona generation prompt
   * @param {object} analysis - Caption analysis results
   * @returns {string} Formatted text for AI prompt
   */
  static formatAnalysisForPrompt(analysis) {
    if (!analysis.hasData) {
      return "No caption history available for analysis.";
    }

    return `CAPTION HISTORY ANALYSIS (${analysis.totalCaptions} captions analyzed):
- Content Tone: ${analysis.tone}
- Writing Style: ${analysis.style}
- Emotional Tone: ${analysis.emotionalTone}
- Main Themes: ${analysis.themes.join(", ")}
- Content Focus: ${analysis.contentFocus}
- Target Audience: ${analysis.targetAudience}
- Engagement Style: ${analysis.engagementStyle}
- Average Caption Length: ${analysis.averageLength} characters
- Content Types: ${analysis.contentTypes.image || 0} images, ${analysis.contentTypes.video || 0} videos
- Keywords: ${analysis.keywords.join(", ")}`;
  }
}

export default CaptionAnalysisService;
