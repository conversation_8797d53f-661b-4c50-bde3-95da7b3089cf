import Reward from "../models/Rewards/BaseRewards.js";
import { REWARD_CONFIGS, calculateLevel } from "../config/rewardConfig.js";

export class RewardService {
  /**
   * just to understand ig
   * Record activity and update rewards
   * @param {string} userId - User ID
   * @param {string} userType - 'model', 'agency', 'employee'
   * @param {string} rewardType - Type of reward to update
   * @param {Object} options - Activity specific options
   */

  static async recordActivity(userId, userType, rewardType, options = {}) {
    try {
      const config = REWARD_CONFIGS[rewardType];
      if (!config) {
        throw new Error(`Invalid reward type: ${rewardType}`);
      }

      if (!config.userTypes.includes(userType)) {
        throw new Error(
          `Reward type ${rewardType} not available for user type ${userType}`,
        );
      }

      const today = new Date().toISOString().split("T")[0];

      let rewardData = await Reward.findOne({ userId, userType, rewardType });

      if (!rewardData) {
        rewardData = new Reward({
          userId,
          userType,
          rewardType,
          currentValue: 0,
          bestValue: 0,
          totalValue: 0,
          metadata: {},
          badges: [],
          totalPoints: 0,
          level: 1,
          activityHistory: [],
        });
      }

      // Handle different reward type logic
      const result = await this.handleRewardTypeLogic(
        rewardData,
        rewardType,
        today,
        options,
      );

      // Check for new badges
      const newBadges = await this.checkAndAwardBadges(rewardData, rewardType);

      await rewardData.save();

      return {
        ...rewardData.toObject(),
        newBadges,
        ...result,
      };
    } catch (error) {
      console.error("Error recording activity:", error);
      throw new Error(
        `Failed to record ${rewardType} activity: ${error.message}`,
      );
    }
  }

  static async handleRewardTypeLogic(rewardData, rewardType, today, options) {
    const config = REWARD_CONFIGS[rewardType];
    let result = {};

    switch (rewardType) {
      case "login_streak":
        result = await this.handleLoginStreak(rewardData, today, options);
        break;

      case "post_count":
        result = await this.handlePostCount(rewardData, today, options);
        break;

      case "model_signup":
        result = await this.handleModelSignup(rewardData, today, options);
        break;

      default:
        throw new Error(
          `Handler not implemented for reward type: ${rewardType}`,
        );
    }

    // Recalculate level
    rewardData.level = calculateLevel(rewardData.totalPoints);
    rewardData.lastUpdated = new Date();

    return result;
  }

  static async handleLoginStreak(rewardData, today, options) {
    const lastActivity =
      rewardData.activityHistory[rewardData.activityHistory.length - 1];

    if (lastActivity && lastActivity.date === today) {
      return { alreadyRecordedToday: true, streakBroken: false };
    }
    let streakBroken = false;
    let pointsEarned = 20; // Base points
    let isFirstLogin = false;

    if (!lastActivity) {
      //first ever login

      rewardData.currentValue = 1;
      rewardData.bestValue = 1;
      rewardData.totalValue = 1;
      isFirstLogin = true;
    } else {
      const lastDate = new Date(lastActivity.date);
      const todayDate = new Date(today);
      const diffDays = Math.ceil(
        (todayDate - lastDate) / (1000 * 60 * 60 * 24),
      );

      if (diffDays === 1) {
        // Consecutive login
        rewardData.currentValue += 1;
        rewardData.bestValue = Math.max(
          rewardData.bestValue,
          rewardData.currentValue,
        );

        // Streak bonus points
        pointsEarned += Math.floor(rewardData.currentValue / 7) * 10;
      } else if (diffDays > 1) {
        // Streak broken
        streakBroken = true;
        rewardData.currentValue = 1;
        pointsEarned = 20; // Base points only
      }

      rewardData.totalValue += 1;
    }

    rewardData.activityHistory.push({
      date: today,
      value: rewardData.currentValue,
      action: "login",
      metadata: { streakBroken }, //true if diffDays > 1, false otherwise
      pointsEarned,
    });
    rewardData.totalPoints += pointsEarned;
    //keep only last 100 activities
    if (rewardData.activityHistory.length > 100) {
      rewardData.activityHistory = rewardData.activityHistory.slice(-100);
    }
    return { streakBroken, pointsEarned, isFirstLogin };
  }

  static async handlePostCount(rewardData, today, options) {
    const { postCount = 1 } = options;

    // Add to current and total counts
    rewardData.currentValue += postCount;
    rewardData.totalValue += postCount;
    rewardData.bestValue = Math.max(
      rewardData.bestValue,
      rewardData.currentValue,
    );

    const pointsEarned = postCount * 10; // 10 points per post

    // Add to activity history
    rewardData.activityHistory.push({
      date: today,
      value: postCount,
      action: "post_created",
      metadata: { postCount },
      pointsEarned,
    });

    rewardData.totalPoints += pointsEarned;
    return { pointsEarned, newPostCount: postCount };
  }

  static async handleModelSignup(rewardData, today, options) {
    const { modelId, modelName } = options;

    rewardData.currentValue += 1;

    rewardData.totalValue += 1;
    rewardData.bestValue = Math.max(
      rewardData.bestValue,
      rewardData.currentValue,
    );

    const pointsEarned = 100;

    rewardData.activityHistory.push({
      date: today,
      value: 1,

      action: "model_signed",
      metadata: { modelId, modelName },
      pointsEarned,
    });

    rewardData.totalPoints += pointsEarned;
    return { pointsEarned, modelSigned: { modelId, modelName } };
  }

  static async checkAndAwardBadges(rewardData, rewardType) {
    const config = REWARD_CONFIGS[rewardType];
    const badges = config.badges;
    const newBadges = [];
    const existingBadgeIds = rewardData.badges.map((badge) => badge.badgeId);

    // Use currentValue for streaks, totalValue for milestones
    const checkValue = rewardType.includes("streak")
      ? rewardData.currentValue
      : rewardData.totalValue;

    for (const badgeKey in badges) {
      const badge = badges[badgeKey];
      if (
        checkValue >= badge.milestone &&
        !existingBadgeIds.includes(badge.id)
      ) {
        const newBadge = {
          badgeId: badge.id,
          badgeName: badge.name,
          description: badge.description,
          category: badge.category,
          earnedAt: new Date(),
          milestone: badge.milestone,
          rarity: badge.rarity,
        };

        rewardData.badges.push(newBadge);
        rewardData.totalPoints += badge.points;
        newBadges.push(newBadge);
      }
    }
    return newBadges;
  }

  static async getRewardData(userId, userType, rewardType) {
    try {
      let rewardData = await Reward.findOne({ userId, userType, rewardType });

      if (!rewardData) {
        return this.getDefaultRewardData(userId, userType, rewardType);
      }

      return {
        ...rewardData.toObject(),
        nextBadge: this.getNextBadge(rewardData, rewardType),
      };
    } catch (error) {
      console.error("Error getting reward data:", error);
      throw new Error("Failed to get reward data");
    }
  }

  static async getAllUserRewards(userId, userType) {
    try {
      const rewards = await Reward.find({ userId, userType });
      const result = {};

      for (const reward of rewards) {
        result[reward.rewardType] = {
          ...reward.toObject(),
          nextBadge: this.getNextBadge(reward, reward.rewardType),
        };
      }

      const userRewardTypes = REWARD_CONFIGS;
      for (const [rewardType, config] of Object.entries(userRewardTypes)) {
        if (config.userTypes.includes(userType) && !result[rewardType]) {
          result[rewardType] = this.getDefaultRewardData(
            userId,
            userType,
            rewardType,
          );
        }
      }

      return result;
    } catch (error) {
      console.error("Error getting all user rewards:", error);
      throw new Error("Failed to get user rewards");
    }
  }

  static getDefaultRewardData(userId, userType, rewardType) {
    return {
      userId,
      userType,
      rewardType,
      currentValue: 0,
      bestValue: 0,
      totalValue: 0,
      lastUpdated: null,
      metadata: {},
      badges: [],
      totalPoints: 0,
      level: 1,
      activityHistory: [],
      nextBadge: this.getNextBadge(
        { currentValue: 0, totalValue: 0, badges: [] },
        rewardType,
      ),
    };
  }

  static getNextBadge(rewardData, rewardType) {
    const config = REWARD_CONFIGS[rewardType];

    const badges = config.badges;

    const earnedBadgeIds = rewardData.badges.map((badge) => badge.badgeId);

    //if streak type, take current, otherwise take total
    const checkValue = rewardType.includes("streak")
      ? rewardData.currentValue
      : rewardData.totalValue;

    for (const badgeKey in badges) {
      const badge = badges[badgeKey];

      if (!earnedBadgeIds.includes(badge.id) && badge.milestone > checkValue) {
        return {
          ...badge,
          progressNeeded: badge.milestone - checkValue,
          progressPercentage: Math.round((checkValue / badge.milestone) * 100),
        };
      }
    }

    return null; // All badges earned
  }

  static async getLeaderboard(rewardType, userType, limit = 10) {
    try {
      const getPopulateFields = (type) => {
        switch (type) {
          case "model":
            return "fullName profilePicture ";
          case "agency":
            return "agencyName profilePicture  agencyEmail";
          case "employee":
            return "name email department position";
          default:
            return "name email"; // fallback
        }
      };

      const populateFields = getPopulateFields(userType);

      return await Reward.find({ rewardType, userType })
        .populate("userId", populateFields)
        .sort({ currentValue: -1, bestValue: -1, totalPoints: -1 })
        .limit(limit)
        .select("userId currentValue bestValue totalPoints level badges");
    } catch (error) {
      console.error("Error getting leaderboard:", error);
      throw new Error("Failed to get leaderboard");
    }
  }
}
