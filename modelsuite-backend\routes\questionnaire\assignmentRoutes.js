import express from "express";
import {
  assignTemplateToModel,
  getAssignedTemplatesForModel,
  getAssignmentsForAgency,
  updateAssignmentStatus,
} from "../../controllers/questionnaire/assignmentController.js";
import { verifyToken, verifyRole } from "../../middlewares/authMiddleware.js";
import checkPermission from "../../middlewares/permissionCheck.js";

const router = express.Router();

// Assign template to a model (agency)
router.post(
  "/",
  verifyToken,
  checkPermission("assignments.create"),
  assignTemplateToModel,
);
// Bulk assign templates to multiple models (agency)
router.post(
  "/bulk",
  verifyToken,
  verifyRole(["agency", "employee"]),
  checkPermission("assignments.create"),
  assignTemplateToModel,
);

// Get all assignments created by this agency
router.get(
  "/",
  verifyToken,
  verifyRole(["agency", "employee"]),
  checkPermission("assignments.view"),
  getAssignmentsForAgency,
);

// Get all templates assigned to logged-in model
router.get(
  "/my",
  verifyToken,
  checkPermission("assignments.view"),
  getAssignedTemplatesForModel,
);

// Update assignment status
router.patch(
  "/:assignmentId/status",
  verifyToken,
  checkPermission("assignments.edit"),
  updateAssignmentStatus,
);

export default router;
