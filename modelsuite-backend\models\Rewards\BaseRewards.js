import mongoose from "mongoose";

const baseRewardSchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,

      //ref will be diff for diff users like model and agency
    },
    userType: {
      type: String,
      enum: ["model", "agency", "employee"],
      required: true,
    },
    rewardType: {
      type: String,
      enum: [
        "login_streak",
        "post_count",
        "model_signup",
        "revenue_milestone",
        "engagement_rate",
      ],
      required: true,
      index: true,
    },
    currentValue: {
      type: Number,
      default: 0,
      min: 0,
    },
    bestValue: {
      type: Number, // Best streak, highest posts in a month, etc.
      default: 0,
      min: 0,
    },
    totalValue: {
      type: Number, // Total logins, total posts, total models signed, etc.
      default: 0,
      min: 0,
    },
    lastUpdated: {
      type: Date,
      default: Date.now,
    },
    // Flexible metadata for different reward types
    metadata: {
      type: mongoose.Schema.Types.Mixed,
      default: {},
    },
    badges: [
      {
        badgeId: {
          type: String,
          required: true,
        },
        badgeName: {
          type: String,
          required: true,
        },
        description: {
          type: String,
          required: true,
        },
        category: {
          type: String, // 'streak', 'milestone', 'achievement'
          required: true,
        },
        earnedAt: {
          type: Date,
          default: Date.now,
        },
        milestone: {
          type: Number,
          required: true,
        },
        rarity: {
          type: String,
          enum: ["common", "rare", "epic", "legendary"],
          default: "common",
        },
      },
    ],

    // Points system
    totalPoints: {
      type: Number,
      default: 0,
      min: 0,
    },
    level: {
      type: Number,
      default: 1,
      min: 1,
      max: 50,
    },
    // Activity history (flexible for different reward types)
    activityHistory: [
      {
        date: {
          type: String, // YYYY-MM-DD
          required: true,
        },
        value: {
          type: Number,
          required: true,
        },
        action: {
          type: String, // 'login', 'post_created', 'model_signed', etc.
          required: true,
        },
        metadata: {
          type: mongoose.Schema.Types.Mixed,
          default: {},
        },
        pointsEarned: {
          type: Number,
          default: 0,
        },
      },
    ],
  },
  {
    timestamps: true,
  },
);

baseRewardSchema.index(
  { userId: 1, userType: 1, rewardType: 1 },
  { unique: true },
);
baseRewardSchema.index({ rewardType: 1, currentValue: -1 });
baseRewardSchema.index({ userType: 1, totalPoints: -1 });

const Reward = mongoose.model("Reward", baseRewardSchema);
export default Reward;
