import mongoose from "mongoose";
import Agency from "./models/agency.js";
import ModelUser from "./models/model.js";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

async function checkAgenciesAndFixUser() {
  try {
    console.log("=== Checking Agencies and Model User Association ===");

    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI);
    console.log("✅ Connected to MongoDB");

    // Check existing agencies
    const agencies = await Agency.find(
      {},
      { _id: 1, companyName: 1, email: 1 },
    );
    console.log("\n📋 Available Agencies:");
    agencies.forEach((agency, index) => {
      console.log(
        `${index + 1}. ID: ${agency._id}, Company: ${agency.companyName}, Email: ${agency.email}`,
      );
    });

    if (agencies.length === 0) {
      console.log("\n❌ No agencies found. Creating a test agency...");

      const testAgency = new Agency({
        companyName: "Test Agency",
        email: "<EMAIL>",
        password: "$2b$10$hashedpassword", // dummy hashed password
        role: "agency",
      });

      await testAgency.save();
      console.log("✅ Test agency created:", testAgency._id);

      // Update the model user to associate with this agency
      const updateResult = await ModelUser.updateOne(
        { username: "sasa" },
        { agencyId: testAgency._id },
      );

      console.log("\n📝 Updated model user association:", updateResult);

      // Verify the update
      const updatedUser = await ModelUser.findOne(
        { username: "sasa" },
        { username: 1, agencyId: 1, role: 1 },
      );
      console.log("✅ Updated user:", updatedUser);
    } else {
      // Use the first available agency
      const firstAgency = agencies[0];
      console.log(
        `\n🔗 Associating user 'sasa' with agency: ${firstAgency.companyName}`,
      );

      const updateResult = await ModelUser.updateOne(
        { username: "sasa" },
        { agencyId: firstAgency._id },
      );

      console.log("📝 Update result:", updateResult);

      // Verify the update
      const updatedUser = await ModelUser.findOne(
        { username: "sasa" },
        { username: 1, agencyId: 1, role: 1 },
      );
      console.log("✅ Updated user:", updatedUser);
    }
  } catch (error) {
    console.error("❌ Error:", error.message);
  } finally {
    await mongoose.disconnect();
    console.log("\n🔌 Disconnected from MongoDB");
  }
}

checkAgenciesAndFixUser();
