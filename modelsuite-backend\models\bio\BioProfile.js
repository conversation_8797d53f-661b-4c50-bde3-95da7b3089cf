import mongoose from "mongoose";

/**
 * BioProfile Schema - Stores generated bios for models
 */
const bioProfileSchema = new mongoose.Schema(
  {
    modelId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ModelUser",
      required: true,
      index: true,
    },
    agencyId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "AgencyUser",
      required: true,
      index: true,
    },
    shortBio: {
      type: String,
      required: true,
      maxlength: 200,
      trim: true,
    },
    mediumBio: {
      type: String,
      required: true,
      maxlength: 500,
      trim: true,
    },
    longBio: {
      type: String,
      required: true,
      maxlength: 1000,
      trim: true,
    },
    personaId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "PersonaProfile",
      required: true,
      index: true,
    },
    version: {
      type: Number,
      default: 1,
      min: 1,
    },
    isArchived: {
      type: Boolean,
      default: false,
      index: true,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
    },
    createdByType: {
      type: String,
      enum: ["Agency", "ModelUser", "Employee"],
      required: true,
      default: "Agency",
    },
    // Metadata for analytics
    metadata: {
      wordCount: {
        short: { type: Number, default: 0 },
        medium: { type: Number, default: 0 },
        long: { type: Number, default: 0 },
      },
      tone: {
        type: String,
        enum: ["professional", "casual", "friendly", "formal", "creative"],
        default: "professional",
      },
      language: {
        type: String,
        default: "en",
      },
      aiModel: {
        type: String,
        enum: ["gpt-4", "gpt-3.5-turbo", "gemini-pro"],
        required: true,
      },
    },
    // Performance tracking
    performance: {
      rating: {
        type: Number,
        min: 1,
        max: 5,
        default: null,
      },
      usageCount: {
        type: Number,
        default: 0,
        min: 0,
      },
      lastUsed: {
        type: Date,
        default: null,
      },
    },
  },
  {
    timestamps: true,
    collection: "bioprofiles",
  },
);

// Indexes for performance
bioProfileSchema.index({ modelId: 1, agencyId: 1 });
bioProfileSchema.index({ modelId: 1, personaId: 1 });
bioProfileSchema.index({ agencyId: 1, isArchived: 1 });
bioProfileSchema.index({ createdAt: -1 });
bioProfileSchema.index({ "performance.rating": -1 });

// Virtual for getting the latest non-archived bio
bioProfileSchema.virtual("isActive").get(function () {
  return !this.isArchived;
});

// Instance methods
bioProfileSchema.methods.archive = function () {
  this.isArchived = true;
  return this.save();
};

bioProfileSchema.methods.updateUsage = function () {
  this.performance.usageCount += 1;
  this.performance.lastUsed = new Date();
  return this.save();
};

bioProfileSchema.methods.rate = function (rating) {
  if (rating >= 1 && rating <= 5) {
    this.performance.rating = rating;
    return this.save();
  }
  throw new Error("Rating must be between 1 and 5");
};

// Static methods
bioProfileSchema.statics.getLatestForModel = function (
  modelId,
  includeArchived = false,
) {
  const query = { modelId };
  if (!includeArchived) {
    query.isArchived = false;
  }
  return this.findOne(query).sort({ createdAt: -1 }).populate("personaId");
};

bioProfileSchema.statics.getTopRated = function (agencyId, limit = 10) {
  return this.find({
    agencyId,
    isArchived: false,
    "performance.rating": { $gte: 4 },
  })
    .sort({ "performance.rating": -1, "performance.usageCount": -1 })
    .limit(limit)
    .populate("modelId", "username fullName")
    .populate("personaId", "name");
};

bioProfileSchema.statics.getMostUsed = function (agencyId, limit = 10) {
  return this.find({
    agencyId,
    isArchived: false,
  })
    .sort({ "performance.usageCount": -1 })
    .limit(limit)
    .populate("modelId", "username fullName")
    .populate("personaId", "name");
};

// Pre-save middleware to calculate word counts
bioProfileSchema.pre("save", function (next) {
  if (
    this.isModified("shortBio") ||
    this.isModified("mediumBio") ||
    this.isModified("longBio")
  ) {
    this.metadata.wordCount.short = this.shortBio.split(/\s+/).length;
    this.metadata.wordCount.medium = this.mediumBio.split(/\s+/).length;
    this.metadata.wordCount.long = this.longBio.split(/\s+/).length;
  }
  next();
});

// Pre-save middleware to auto-increment version for same model+persona combination
bioProfileSchema.pre("save", async function (next) {
  if (this.isNew) {
    const latestBio = await this.constructor
      .findOne({
        modelId: this.modelId,
        personaId: this.personaId,
      })
      .sort({ version: -1 });

    if (latestBio) {
      this.version = latestBio.version + 1;
    }
  }
  next();
});

// Ensure JSON output includes virtuals
bioProfileSchema.set("toJSON", { virtuals: true });
bioProfileSchema.set("toObject", { virtuals: true });

const BioProfile = mongoose.model("BioProfile", bioProfileSchema);

export default BioProfile;
