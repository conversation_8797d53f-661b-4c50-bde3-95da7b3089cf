import { CloudinaryStorage } from "multer-storage-cloudinary";
import multer from "multer";
import cloudinary from "../config/cloudinary.js";

const storage = new CloudinaryStorage({
  cloudinary,
  params: {
    folder: "modelsuite/profile_photos",
    allowed_formats: ["jpg", "jpeg", "png", "webp", "audio/webm", "audio/mp3"],
    transformation: [{ width: 500, height: 500, crop: "limit" }],
  },
});

const upload = multer({ storage });

const uploadPortfolio = multer({
  storage: new CloudinaryStorage({
    cloudinary,
    params: {
      folder: "modelsuite/portfolio",
      allowed_formats: ["jpg", "jpeg", "png", "webp"],
      transformation: [
        {
          width: 1200,
          height: 1200,
          crop: "limit",
          quality: "auto",
          fetch_format: "auto",
        },
      ],
    },
  }),
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB
});

export { uploadPortfolio };

export default upload;
