import { Router } from "express";
import { ApiError } from "../utils/ApiError.js";
import {
  createNote,
  getNotesByModel,
  getNotesByAgency,
  getNoteById,
  updateNote,
  deleteNote,
  togglePinNote,
  getNoteStats,
  bulkDeleteNotes,
  bulkPinNotes,
  bulkArchiveNotes,
} from "../controllers/noteController.js";
import { verifyToken } from "../middlewares/authMiddleware.js";
import checkPermission from "../middlewares/permissionCheck.js";
import { stripNoteUpdateFields } from "../middlewares/stripNoteFields.js";

const router = Router();

// Apply authentication middleware to all routes
router.use(verifyToken);

// Add this temporary route to noteRoutes.js for debugging
router.get("/debug/user", (req, res) => {
  res.json({
    user: req.user,
    role: req.user.role,
    id: req.user._id,
  });
});

// Note CRUD operations
// GET /api/v1/notes?modelId=xxx  - List notes for a model via query param
router
  .route("/")
  .get(
    (req, res, next) => {
      const { modelId } = req.query;
      if (!modelId) {
        return next(new ApiError(400, "modelId query param is required"));
      }
      // map to params for controller
      req.params.modelId = modelId;
      next();
    },
    checkPermission("notes.view"),
    getNotesByModel
  )
  .post(checkPermission("notes.create"), createNote); // POST /api/v1/notes - Create a new note
router
  .route("/model/:modelId")
  .get(checkPermission("notes.view"), getNotesByModel); // GET /api/v1/notes/model/:modelId - Get notes for a specific model
// GET /api/v1/notes/agency/:agencyId - Get notes across models for an agency
router
  .route("/agency/:agencyId")
  .get(checkPermission("notes.view"), getNotesByAgency);
router
  .route("/model/:modelId/stats")
  .get(checkPermission("notes.view"), getNoteStats); // GET /api/v1/notes/model/:modelId/stats - Get note statistics for a model

// Bulk operations (declared before dynamic :noteId routes)
router.route("/bulk").delete(checkPermission("notes.delete"), bulkDeleteNotes); // DELETE /api/v1/notes/bulk
router.route("/bulk/pin").patch(checkPermission("notes.edit"), bulkPinNotes); // PATCH /api/v1/notes/bulk/pin
router
  .route("/bulk/archive")
  .patch(checkPermission("notes.edit"), bulkArchiveNotes); // PATCH /api/v1/notes/bulk/archive

// Note CRUD operations
router.route("/:noteId").get(checkPermission("notes.view"), getNoteById); // GET /api/v1/notes/:noteId - Get a specific note
router
  .route("/:noteId")
  .patch(checkPermission("notes.edit"), stripNoteUpdateFields, updateNote); // PATCH /api/v1/notes/:noteId - Update a note (only allowed fields)
router.route("/:noteId").delete(checkPermission("notes.delete"), deleteNote); // DELETE /api/v1/notes/:noteId - Soft delete a note

router
  .route("/:noteId/pin")
  .patch(checkPermission("notes.edit"), togglePinNote); // PATCH /api/v1/notes/:noteId/pin - Toggle pin status

export default router;
