import * as React from "react";
import { cn } from "@/lib/utils";

const LoadingButton = ({
  isLoading = false,
  disabled = false,
  children,
  loadingText = "Loading...",
  variant = "default", // "default", "primary", "secondary", "outline", "ghost"
  size = "default", // "sm", "default", "lg"
  className,
  onClick,
  ...props
}) => {
  const getVariantClass = () => {
    switch (variant) {
      case "primary":
        return cn(
          "bg-gray-300 text-black hover:bg-gray-200",
          isLoading && "bg-gray-400"
        );
      case "secondary":
        return cn(
          "bg-[#1a1a1a] text-gray-300 hover:bg-gray-800",
          isLoading && "bg-[#0f0f0f]"
        );
      case "outline":
        return cn(
          "border border-gray-600 text-gray-400 hover:bg-[#1a1a1a]",
          isLoading && "border-gray-700 text-gray-500"
        );
      case "ghost":
        return cn(
          "text-gray-400 hover:bg-[#1a1a1a]",
          isLoading && "text-gray-500"
        );
      default:
        return cn(
          "bg-[#1a1a1a] text-gray-300 hover:bg-gray-800",
          isLoading && "bg-[#0f0f0f]"
        );
    }
  };

  const getSizeClass = () => {
    switch (size) {
      case "sm":
        return "h-8 px-3 text-sm";
      case "lg":
        return "h-12 px-6 text-lg";
      default:
        return "h-10 px-4";
    }
  };

  const getSpinnerSize = () => {
    switch (size) {
      case "sm":
        return "h-3 w-3";
      case "lg":
        return "h-5 w-5";
      default:
        return "h-4 w-4";
    }
  };

  const handleClick = (e) => {
    if (!isLoading && !disabled && onClick) {
      onClick(e);
    }
  };

  return (
    <button
      className={cn(
        "inline-flex items-center justify-center gap-2 rounded-md font-medium",
        "transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-600 focus:ring-offset-2 focus:ring-offset-[#0f0f0f]",
        "disabled:pointer-events-none disabled:opacity-50",
        getSizeClass(),
        getVariantClass(),
        className
      )}
      disabled={isLoading || disabled}
      onClick={handleClick}
      {...props}
    >
      {isLoading && (
        <svg
          className={cn("animate-spin", getSpinnerSize())}
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      )}
      {isLoading ? loadingText : children}
    </button>
  );
};

export default LoadingButton;
