import * as React from "react";
import { cn } from "@/lib/utils";
import { ChevronRight, Home } from "lucide-react";
import { Button } from "@/components/ui/button";
import * as Icons from "lucide-react";

const BreadcrumbNav = ({
  items = [],
  separator = <ChevronRight className="h-4 w-4" />,
  showHome = true,
  homeHref = "/",
  homeLabel = "Home",
  homeIcon = <Home className="h-4 w-4" />,
  maxItems = 3, // Show max items, rest will be collapsed
  size = "default", // "sm", "default", "lg"
  variant = "default", // "default", "minimal", "pills"
  className,
  itemClassName,
  separatorClassName,
  onClick,
  ...props
}) => {
  const getSizeClasses = () => {
    switch (size) {
      case "sm":
        return "text-xs px-2 py-1";
      case "lg":
        return "text-base px-4 py-2";
      default:
        return "text-sm px-3 py-1.5";
    }
  };

  const getVariantClasses = (isActive = false, isClickable = false) => {
    const baseClasses = cn(
      "transition-colors duration-200 rounded-sm",
      getSizeClasses(),
      "focus:outline-none focus:ring-2 focus:ring-gray-600 focus:ring-offset-2 focus:ring-offset-[#0f0f0f]"
    );

    switch (variant) {
      case "minimal":
        return cn(
          baseClasses,
          isActive
            ? "text-gray-300 font-medium"
            : isClickable
            ? "text-gray-500 hover:text-gray-300"
            : "text-gray-600"
        );
      case "pills":
        return cn(
          baseClasses,
          isActive
            ? "text-gray-200 bg-[#1a1a1a] font-medium"
            : isClickable
            ? "text-gray-500 hover:text-gray-300 hover:bg-[#1a1a1a]"
            : "text-gray-600"
        );
      default:
        return cn(
          baseClasses,
          isActive
            ? "text-gray-300 font-medium"
            : isClickable
            ? "text-gray-500 hover:text-gray-300 hover:bg-[#1a1a1a]"
            : "text-gray-600"
        );
    }
  };

  const getSeparatorClasses = () => {
    return cn("text-gray-600 flex-shrink-0", separatorClassName);
  };

  // Prepare breadcrumb items
  const allItems = React.useMemo(() => {
    const breadcrumbItems = [];

    // Add home if enabled
    if (showHome) {
      breadcrumbItems.push({
        href: homeHref,
        label: homeLabel,
        icon: homeIcon,
        isHome: true,
      });
    }

    // Add provided items
    breadcrumbItems.push(...items);

    return breadcrumbItems;
  }, [items, showHome, homeHref, homeLabel, homeIcon]);

  // Handle item collapsing if too many items
  const displayItems = React.useMemo(() => {
    if (allItems.length <= maxItems) {
      return allItems;
    }

    const first = allItems[0];
    const last = allItems.slice(-2); // Last 2 items
    const collapsed = allItems.slice(1, -2);

    return [
      first,
      {
        label: "...",
        isCollapsed: true,
        collapsedItems: collapsed,
      },
      ...last,
    ];
  }, [allItems, maxItems]);

  const handleItemClick = (item, index) => {
    if (onClick) {
      onClick(item, index);
    }
  };

  const renderBreadcrumbItem = (item, index, isLast) => {
    const isClickable = !isLast && (item.href || item.onClick || onClick);
    const isActive = isLast;

    if (item.isCollapsed) {
      return (
        <div
          key="collapsed"
          className={cn(
            getVariantClasses(false, false),
            "cursor-default",
            itemClassName
          )}
        >
          {item.label}
        </div>
      );
    }

    const content = (
      <div className="flex items-center gap-1">
        {/* Item Icon */}
        {item.icon && (
          <span className="flex-shrink-0">
            {React.isValidElement(item.icon) ? (
              item.icon
            ) : typeof item.icon === "string" ? (
              React.createElement(Icons[item.icon] || Icons.Circle, {
                className: "h-4 w-4",
              })
            ) : (
              <item.icon className="h-4 w-4" />
            )}
          </span>
        )}

        {/* Item Label */}
        <span className="truncate max-w-32">{item.label}</span>

        {/* Item Badge */}
        {item.badge && (
          <span className="ml-1 text-xs bg-gray-800 text-gray-400 px-1.5 py-0.5 rounded">
            {item.badge}
          </span>
        )}
      </div>
    );

    if (isClickable) {
      if (item.href) {
        return (
          <a
            key={index}
            href={item.href}
            className={cn(
              getVariantClasses(isActive, true),
              "inline-flex items-center",
              itemClassName
            )}
            onClick={(e) => {
              if (item.onClick) {
                e.preventDefault();
                item.onClick(e);
              }
              handleItemClick(item, index);
            }}
          >
            {content}
          </a>
        );
      } else {
        return (
          <Button
            key={index}
            variant="ghost"
            size="sm"
            className={cn(
              getVariantClasses(isActive, true),
              "h-auto p-0 font-normal",
              itemClassName
            )}
            onClick={(e) => {
              if (item.onClick) {
                item.onClick(e);
              }
              handleItemClick(item, index);
            }}
          >
            {content}
          </Button>
        );
      }
    }

    return (
      <div
        key={index}
        className={cn(
          getVariantClasses(isActive, false),
          "inline-flex items-center",
          itemClassName
        )}
      >
        {content}
      </div>
    );
  };

  if (!displayItems.length) {
    return null;
  }

  return (
    <nav
      className={cn(
        "flex items-center space-x-1 text-sm text-gray-500",
        className
      )}
      aria-label="Breadcrumb"
      {...props}
    >
      <ol className="flex items-center space-x-1">
        {displayItems.map((item, index) => {
          const isLast = index === displayItems.length - 1;

          return (
            <li key={index} className="flex items-center">
              {renderBreadcrumbItem(item, index, isLast)}

              {/* Separator */}
              {!isLast && (
                <span className={cn("mx-2", getSeparatorClasses())}>
                  {separator}
                </span>
              )}
            </li>
          );
        })}
      </ol>
    </nav>
  );
};

export default BreadcrumbNav;
