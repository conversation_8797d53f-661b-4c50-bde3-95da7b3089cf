import contentPlannerService from "../../services/planner/contentPlannerService.js";
import schedulerService from "../../services/planner/schedulerService.js";
import { asyncHandler } from "../../utils/asyncHandler.js";
import { ApiError } from "../../utils/ApiError.js";

/**
 * Content Planner Controller
 * Handles all content planning and scheduling operations
 */

// Create a new content plan
export const createPlan = asyncHandler(async (req, res) => {
  const { modelId, weekStart, goals, name, themes, description } = req.body;

  // Validate required fields
  if (!modelId || !weekStart) {
    throw new ApiError(400, "Model ID and week start date are required");
  }

  // Get agency ID based on user role
  let agencyId;
  let createdBy = req.user.id;
  let createdByType =
    req.user.role === "agency"
      ? "Agency"
      : req.user.role === "model"
        ? "ModelUser"
        : "Employee";

  if (req.user.role === "agency") {
    agencyId = req.user.id;
  } else if (req.user.role === "model") {
    agencyId = req.user.agencyId;
  } else {
    throw new ApiError(403, "Invalid user role for plan creation");
  }

  const planData = {
    modelId,
    agencyId,
    weekStart: new Date(weekStart),
    name:
      name ||
      `Content Plan - Week of ${new Date(weekStart).toLocaleDateString()}`,
    themes: themes || [],
    description,
    goals: goals || [],
  };

  const plan = await contentPlannerService.createPlan(
    planData,
    createdBy,
    createdByType,
  );

  res.status(201).json({
    success: true,
    statusCode: 201,
    data: plan,
    message: "Content plan created successfully",
  });
});

// Add a task to an existing plan
export const addTask = asyncHandler(async (req, res) => {
  const {
    planId,
    title,
    caption,
    date,
    mediaType,
    platforms,
    hashtags,
    keywords,
    goalIds,
    priority,
    notes,
  } = req.body;

  // Validate required fields
  if (!planId || !title || !caption || !date || !mediaType || !platforms) {
    throw new ApiError(
      400,
      "Plan ID, title, caption, date, media type, and platforms are required",
    );
  }

  const createdBy = req.user.id;
  const createdByType =
    req.user.role === "agency"
      ? "Agency"
      : req.user.role === "model"
        ? "ModelUser"
        : "Employee";

  const taskData = {
    planId,
    title,
    caption,
    date: new Date(date),
    mediaType,
    platforms: platforms.map((platform) => ({
      name: platform,
      scheduled: false,
      published: false,
    })),
    hashtags: hashtags || [],
    keywords: keywords || [],
    goalIds: goalIds || [],
    priority: priority || "medium",
    notes,
  };

  const task = await contentPlannerService.addTask(
    taskData,
    createdBy,
    createdByType,
  );

  res.status(201).json({
    success: true,
    statusCode: 201,
    data: task,
    message: "Task added to plan successfully",
  });
});

// Get weekly plan for a model
export const getWeeklyPlan = asyncHandler(async (req, res) => {
  const { modelId } = req.params;
  const { weekStart } = req.query;

  if (!modelId || !weekStart) {
    throw new ApiError(400, "Model ID and week start date are required");
  }

  // Get agency ID based on user role
  let agencyId;
  if (req.user.role === "agency") {
    agencyId = req.user.id;
  } else if (req.user.role === "model") {
    agencyId = req.user.agencyId;
    // Verify model is requesting their own plan
    if (modelId !== req.user.id) {
      throw new ApiError(403, "Access denied to this model's plan");
    }
  } else {
    throw new ApiError(403, "Invalid user role");
  }

  const weekPlan = await contentPlannerService.getWeeklyPlan(
    modelId,
    weekStart,
    agencyId,
  );

  if (!weekPlan) {
    return res.status(404).json({
      success: false,
      statusCode: 404,
      message: "No plan found for this week",
    });
  }

  res.status(200).json({
    success: true,
    statusCode: 200,
    data: weekPlan,
    message: "Weekly plan retrieved successfully",
  });
});

// Edit an existing plan
export const editPlan = asyncHandler(async (req, res) => {
  const { planId } = req.params;
  const updateData = req.body;

  const userId = req.user.id;
  const userType =
    req.user.role === "agency"
      ? "Agency"
      : req.user.role === "model"
        ? "ModelUser"
        : "Employee";

  const updatedPlan = await contentPlannerService.editPlan(
    planId,
    updateData,
    userId,
    userType,
  );

  res.status(200).json({
    success: true,
    statusCode: 200,
    data: updatedPlan,
    message: "Plan updated successfully",
  });
});

// Add goal to plan
export const addGoalToPlan = asyncHandler(async (req, res) => {
  const { planId } = req.params;
  const goalData = req.body;

  const createdBy = req.user.id;
  const createdByType =
    req.user.role === "agency"
      ? "Agency"
      : req.user.role === "model"
        ? "ModelUser"
        : "Employee";

  const goal = await contentPlannerService.addGoalToPlan(
    planId,
    goalData,
    createdBy,
    createdByType,
  );

  res.status(201).json({
    success: true,
    statusCode: 201,
    data: goal,
    message: "Goal added to plan successfully",
  });
});

// Get plans by date range
export const getPlansByDateRange = asyncHandler(async (req, res) => {
  const { startDate, endDate, modelId } = req.query;

  if (!startDate || !endDate) {
    throw new ApiError(400, "Start date and end date are required");
  }

  // Get agency ID based on user role
  let agencyId;
  if (req.user.role === "agency") {
    agencyId = req.user.id;
  } else if (req.user.role === "model") {
    agencyId = req.user.agencyId;
  } else {
    throw new ApiError(403, "Invalid user role");
  }

  const plans = await contentPlannerService.getPlansByDateRange(
    agencyId,
    startDate,
    endDate,
    modelId,
  );

  res.status(200).json({
    success: true,
    statusCode: 200,
    data: plans,
    message: "Plans retrieved successfully",
  });
});

// Update task status
export const updateTaskStatus = asyncHandler(async (req, res) => {
  const { taskId } = req.params;
  const { status, additionalData } = req.body;

  if (!status) {
    throw new ApiError(400, "Status is required");
  }

  const userId = req.user.id;
  const userType =
    req.user.role === "agency"
      ? "Agency"
      : req.user.role === "model"
        ? "ModelUser"
        : "Employee";

  const updatedTask = await contentPlannerService.updateTaskStatus(
    taskId,
    status,
    userId,
    userType,
    additionalData,
  );

  res.status(200).json({
    success: true,
    statusCode: 200,
    data: updatedTask,
    message: "Task status updated successfully",
  });
});

// Auto-schedule: Suggest optimal posting slots
export const suggestPostingSlots = asyncHandler(async (req, res) => {
  const { modelId, dateRange, preferences } = req.body;

  if (!modelId || !dateRange || !dateRange.startDate || !dateRange.endDate) {
    throw new ApiError(400, "Model ID and date range are required");
  }

  // Verify access to model
  if (req.user.role === "model" && modelId !== req.user.id) {
    throw new ApiError(403, "Access denied to this model");
  }

  if (req.user.role === "agency") {
    // Verify model belongs to agency (this check would be more comprehensive in production)
    // For now, assuming agency has access to all models under their account
  }

  const suggestions = await schedulerService.suggestPostingSlots(
    modelId,
    dateRange,
    preferences,
  );

  res.status(200).json({
    success: true,
    statusCode: 200,
    data: suggestions,
    message: "Posting slots suggested successfully",
  });
});

// Check calendar availability
export const checkCalendarAvailability = asyncHandler(async (req, res) => {
  const { modelId } = req.params;
  const { startDate, endDate } = req.query;

  if (!startDate || !endDate) {
    throw new ApiError(400, "Start date and end date are required");
  }

  // Verify access to model
  if (req.user.role === "model" && modelId !== req.user.id) {
    throw new ApiError(403, "Access denied to this model");
  }

  const dateRange = { startDate, endDate };
  const availability = await schedulerService.checkCalendarAvailability(
    modelId,
    dateRange,
  );

  res.status(200).json({
    success: true,
    statusCode: 200,
    data: availability,
    message: "Calendar availability checked successfully",
  });
});

// Sync selected slots to calendar
export const syncToCalendar = asyncHandler(async (req, res) => {
  const { modelId, selectedSlots, logId } = req.body;

  if (!modelId || !selectedSlots || !Array.isArray(selectedSlots) || !logId) {
    throw new ApiError(
      400,
      "Model ID, selected slots array, and log ID are required",
    );
  }

  // Verify access to model
  if (req.user.role === "model" && modelId !== req.user.id) {
    throw new ApiError(403, "Access denied to this model");
  }

  const syncResult = await schedulerService.autoScheduleToCalendar(
    modelId,
    selectedSlots,
    logId,
  );

  res.status(200).json({
    success: true,
    statusCode: 200,
    data: syncResult,
    message: "Calendar sync completed",
  });
});

// Get scheduling logs
export const getSchedulingLogs = asyncHandler(async (req, res) => {
  const { modelId } = req.params;
  const { status, dateFrom, dateTo, page = 1, limit = 20 } = req.query;

  // Get agency ID based on user role
  let agencyId;
  if (req.user.role === "agency") {
    agencyId = req.user.id;
  } else if (req.user.role === "model") {
    agencyId = req.user.agencyId;
    // Verify model is requesting their own logs
    if (modelId !== req.user.id) {
      throw new ApiError(403, "Access denied to this model's logs");
    }
  } else {
    throw new ApiError(403, "Invalid user role");
  }

  const filters = {
    status,
    dateFrom,
    dateTo,
    page: parseInt(page),
    limit: parseInt(limit),
  };

  const logs = await schedulerService.getSchedulingLogs(
    modelId,
    agencyId,
    filters,
  );

  res.status(200).json({
    success: true,
    statusCode: 200,
    data: logs,
    message: "Scheduling logs retrieved successfully",
  });
});

// Get agency analytics
export const getAnalytics = asyncHandler(async (req, res) => {
  const { timeframe = 30 } = req.query;

  // Only agencies can access analytics
  if (req.user.role !== "agency") {
    throw new ApiError(403, "Access denied: Agency role required");
  }

  const agencyId = req.user.id;
  const analytics = await contentPlannerService.getAgencyAnalytics(
    agencyId,
    parseInt(timeframe),
  );

  res.status(200).json({
    success: true,
    statusCode: 200,
    data: analytics,
    message: "Analytics retrieved successfully",
  });
});

// Get scheduling analytics
export const getSchedulingAnalytics = asyncHandler(async (req, res) => {
  const { timeframe = 30 } = req.query;

  // Only agencies can access analytics
  if (req.user.role !== "agency") {
    throw new ApiError(403, "Access denied: Agency role required");
  }

  const agencyId = req.user.id;
  const analytics = await schedulerService.getSchedulingAnalytics(
    agencyId,
    parseInt(timeframe),
  );

  res.status(200).json({
    success: true,
    statusCode: 200,
    data: analytics,
    message: "Scheduling analytics retrieved successfully",
  });
});

// Create plan from template
export const createFromTemplate = asyncHandler(async (req, res) => {
  const { templateId, planData } = req.body;

  if (!templateId || !planData) {
    throw new ApiError(400, "Template ID and plan data are required");
  }

  const createdBy = req.user.id;
  const createdByType =
    req.user.role === "agency"
      ? "Agency"
      : req.user.role === "model"
        ? "ModelUser"
        : "Employee";

  const plan = await contentPlannerService.createFromTemplate(
    templateId,
    planData,
    createdBy,
    createdByType,
  );

  res.status(201).json({
    success: true,
    statusCode: 201,
    data: plan,
    message: "Plan created from template successfully",
  });
});

// Archive a plan
export const archivePlan = asyncHandler(async (req, res) => {
  const { planId } = req.params;

  const userId = req.user.id;
  const userType =
    req.user.role === "agency"
      ? "Agency"
      : req.user.role === "model"
        ? "ModelUser"
        : "Employee";

  const result = await contentPlannerService.archivePlan(
    planId,
    userId,
    userType,
  );

  res.status(200).json({
    success: true,
    statusCode: 200,
    data: result,
    message: "Plan archived successfully",
  });
});

// Get model-specific analytics
export const getModelAnalytics = asyncHandler(async (req, res) => {
  const { modelId } = req.params;
  const { timeframe = 30 } = req.query;

  // Verify access to model
  if (req.user.role === "model" && modelId !== req.user.id) {
    throw new ApiError(403, "Access denied to this model's analytics");
  }

  // Get agency ID
  let agencyId;
  if (req.user.role === "agency") {
    agencyId = req.user.id;
  } else if (req.user.role === "model") {
    agencyId = req.user.agencyId;
  } else {
    throw new ApiError(403, "Invalid user role");
  }

  // Get model-specific analytics (similar to agency analytics but filtered by model)
  const analytics = await contentPlannerService.getAgencyAnalytics(
    agencyId,
    parseInt(timeframe),
  );

  // TODO: Filter analytics by modelId
  // This would require additional methods in the service to filter by model

  res.status(200).json({
    success: true,
    statusCode: 200,
    data: analytics,
    message: "Model analytics retrieved successfully",
  });
});

// Get plan summary
export const getPlanSummary = asyncHandler(async (req, res) => {
  const { planId } = req.params;

  // Get the plan and generate summary
  const plan = await contentPlannerService.getWeeklyPlan(
    req.query.modelId,
    req.query.weekStart,
    req.user.role === "agency" ? req.user.id : req.user.agencyId,
  );

  if (!plan) {
    throw new ApiError(404, "Plan not found");
  }

  // Generate weekly report
  const summary = await plan.plan.generateWeeklyReport();

  res.status(200).json({
    success: true,
    statusCode: 200,
    data: summary,
    message: "Plan summary retrieved successfully",
  });
});

// Bulk operations for tasks
export const bulkUpdateTasks = asyncHandler(async (req, res) => {
  const { taskIds, updateData } = req.body;

  if (!taskIds || !Array.isArray(taskIds) || taskIds.length === 0) {
    throw new ApiError(400, "Task IDs array is required");
  }

  const userId = req.user.id;
  const userType =
    req.user.role === "agency"
      ? "Agency"
      : req.user.role === "model"
        ? "ModelUser"
        : "Employee";

  const results = [];

  // Update each task
  for (const taskId of taskIds) {
    try {
      const updatedTask = await contentPlannerService.updateTaskStatus(
        taskId,
        updateData.status,
        userId,
        userType,
        updateData.additionalData,
      );
      results.push({ taskId, success: true, task: updatedTask });
    } catch (error) {
      results.push({ taskId, success: false, error: error.message });
    }
  }

  const successCount = results.filter((r) => r.success).length;
  const failureCount = results.filter((r) => !r.success).length;

  res.status(200).json({
    success: true,
    statusCode: 200,
    data: {
      results,
      summary: {
        total: taskIds.length,
        successful: successCount,
        failed: failureCount,
      },
    },
    message: `Bulk update completed: ${successCount}/${taskIds.length} tasks updated successfully`,
  });
});

// ===== AI-POWERED ENDPOINTS =====

// Generate AI content strategy
export const generateAIStrategy = asyncHandler(async (req, res) => {
  const { modelId } = req.params;
  const { preferences = {} } = req.body;

  if (!modelId) {
    throw new ApiError(400, "Model ID is required");
  }

  // Get agency ID based on user role
  let agencyId;
  if (req.user.role === "agency") {
    agencyId = req.user.id;
  } else if (req.user.role === "model") {
    agencyId = req.user.agencyId;
  } else {
    throw new ApiError(403, "Invalid user role for strategy generation");
  }

  const strategy = await contentPlannerService.generateAIStrategy(
    modelId,
    agencyId,
    preferences,
  );

  res.status(200).json({
    success: true,
    statusCode: 200,
    data: strategy,
    message: "AI strategy generated successfully",
  });
});

// Get AI content suggestions
export const getAIContentSuggestions = asyncHandler(async (req, res) => {
  const { planId } = req.params;
  const { platform, count = 5 } = req.query;

  if (!planId) {
    throw new ApiError(400, "Plan ID is required");
  }

  if (!platform) {
    throw new ApiError(400, "Platform is required");
  }

  const suggestions = await contentPlannerService.getAIContentSuggestions(
    planId,
    platform,
    parseInt(count),
  );

  res.status(200).json({
    success: true,
    statusCode: 200,
    data: suggestions,
    message: "AI content suggestions generated successfully",
  });
});

// Analyze optimal posting times
export const analyzeOptimalTiming = asyncHandler(async (req, res) => {
  const { modelId } = req.params;
  const { platform, weekData = {} } = req.body;

  if (!modelId) {
    throw new ApiError(400, "Model ID is required");
  }

  if (!platform) {
    throw new ApiError(400, "Platform is required");
  }

  const analysis = await contentPlannerService.analyzeOptimalTiming(
    modelId,
    platform,
    weekData,
  );

  res.status(200).json({
    success: true,
    statusCode: 200,
    data: analysis,
    message: "Timing analysis completed successfully",
  });
});

// Generate AI theme suggestions
export const generateAIThemes = asyncHandler(async (req, res) => {
  const { modelId } = req.params;
  const { preferences = {} } = req.body;

  if (!modelId) {
    throw new ApiError(400, "Model ID is required");
  }

  // Get agency ID based on user role
  let agencyId;
  if (req.user.role === "agency") {
    agencyId = req.user.id;
  } else if (req.user.role === "model") {
    agencyId = req.user.agencyId;
  } else {
    throw new ApiError(403, "Invalid user role for theme generation");
  }

  const themes = await contentPlannerService.generateAIThemes(
    modelId,
    agencyId,
    preferences,
  );

  res.status(200).json({
    success: true,
    statusCode: 200,
    data: themes,
    message: "AI themes generated successfully",
  });
});

// Create AI-powered content plan
export const createAIPoweredPlan = asyncHandler(async (req, res) => {
  const {
    modelId,
    weekStart,
    goals,
    name,
    themes,
    description,
    aiPreferences = {},
  } = req.body;

  // Validate required fields
  if (!modelId || !weekStart) {
    throw new ApiError(400, "Model ID and week start date are required");
  }

  // Get agency ID based on user role
  let agencyId;
  let createdBy = req.user.id;
  let createdByType =
    req.user.role === "agency"
      ? "Agency"
      : req.user.role === "model"
        ? "ModelUser"
        : "Employee";

  if (req.user.role === "agency") {
    agencyId = req.user.id;
  } else if (req.user.role === "model") {
    agencyId = req.user.agencyId;
  } else {
    throw new ApiError(403, "Invalid user role for plan creation");
  }

  const planData = {
    modelId,
    agencyId,
    weekStart: new Date(weekStart),
    name:
      name ||
      `AI Content Plan - Week of ${new Date(weekStart).toLocaleDateString()}`,
    themes: themes || [],
    description,
    goals: goals || [],
  };

  const plan = await contentPlannerService.createAIPoweredPlan(
    planData,
    createdBy,
    createdByType,
    aiPreferences,
  );

  res.status(201).json({
    success: true,
    statusCode: 201,
    data: plan,
    message: "AI-powered content plan created successfully",
  });
});

// Optimize existing plan with AI
export const optimizePlanWithAI = asyncHandler(async (req, res) => {
  const { planId } = req.params;
  const { optimizationOptions = {} } = req.body;

  if (!planId) {
    throw new ApiError(400, "Plan ID is required");
  }

  const optimization = await contentPlannerService.optimizePlanWithAI(
    planId,
    optimizationOptions,
  );

  res.status(200).json({
    success: true,
    statusCode: 200,
    data: optimization,
    message: "Plan optimized with AI successfully",
  });
});
