import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import {
  Plus,
  FileText,
  Users,
  BarChart3,
  Eye,
  Edit,
  Trash2,
  TrendingUp,
  ChevronDown,
  ChevronRight,
} from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { DataTable } from "@/reusable/table/DataTable";
import StatusBadge from "@/reusable/Status/StatusBadge";

import {
  fetchTemplates,
  deleteTemplate,
} from "@/redux/features/questionnaire/questionnaireSlice";

export default function QuestionnaireTemplatesPage() {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { templates, templateLoading, templateError } = useSelector(
    (state) => state.questionnaireReducer
  );

  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });

  const [expandedTemplate, setExpandedTemplate] = useState(null);
  const [expandedSections, setExpandedSections] = useState({});

  useEffect(() => {
    dispatch(fetchTemplates());
  }, [dispatch]);

  const handleCreateTemplate = () => {
    navigate("/agency/questionnaires/create");
  };

  const handleEditTemplate = (templateId) => {
    navigate(`/agency/questionnaires/edit/${templateId}`);
  };

  const handleViewAnalytics = (templateId) => {
    navigate(`/agency/questionnaires/analytics/${templateId}`);
  };

  const handleDeleteTemplate = async (templateId) => {
    if (confirm("Are you sure you want to delete this template?")) {
      try {
        await dispatch(deleteTemplate(templateId)).unwrap();
        toast.success("Template deleted successfully!");
      } catch (error) {
        console.error("Failed to delete template:", error);
        toast.error(
          error?.message || "Failed to delete template. Please try again."
        );
      }
    }
  };

  const columns = [
    {
      accessorKey: "title",
      header: "Template Name",
      cell: ({ row }) => (
        <div className="flex items-center gap-2 min-w-0">
          <FileText className="h-4 w-4 text-gray-400 flex-shrink-0" />
          <div className="min-w-0 flex-1">
            <div className="font-medium text-white truncate">
              {row.original.title}
            </div>
            {!row.original.agencyId && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setExpandedTemplate(
                    expandedTemplate === row.original._id
                      ? null
                      : row.original._id
                  );
                }}
                className="flex items-center gap-1 text-xs text-gray-300 hover:text-gray-400 mt-1"
              >
                {expandedTemplate === row.original._id ? (
                  <ChevronDown className="h-3 w-3" />
                ) : (
                  <ChevronRight className="h-3 w-3" />
                )}
                View{" "}
                {row.original.sections?.reduce(
                  (total, section) => total + (section.questions?.length || 0),
                  0
                ) || 0}{" "}
                Questions
              </button>
            )}
          </div>
        </div>
      ),
    },
    {
      accessorKey: "description",
      header: "Description",
      cell: ({ row }) => (
        <div className="text-sm text-gray-300 max-w-xs">
          <p className="line-clamp-2">
            {row.original.description || "No description"}
          </p>
        </div>
      ),
    },
    {
      accessorKey: "sections",
      header: "Sections",
      cell: ({ row }) => (
        <div className="text-center">
          <span className="text-sm text-white font-medium">
            {row.original.sections?.length || 0}
          </span>
        </div>
      ),
    },
    {
      accessorKey: "isActive",
      header: "Status",
      cell: ({ row }) => (
        <StatusBadge
          status={row.original.isActive ? "active" : "inactive"}
          variant={row.original.isActive ? "success" : "secondary"}
        />
      ),
    },
    {
      accessorKey: "createdAt",
      header: "Created",
      cell: ({ row }) => (
        <span className="text-sm text-gray-400">
          {new Date(row.original.createdAt).toLocaleDateString()}
        </span>
      ),
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 text-gray-400 hover:text-white hover:bg-[#27272a]"
            onClick={() => handleViewAnalytics(row.original._id)}
          >
            <BarChart3 className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 text-gray-400 hover:text-white hover:bg-[#27272a]"
            onClick={() => handleEditTemplate(row.original._id)}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 text-gray-400 hover:text-red-400 hover:bg-[#27272a]"
            onClick={() => handleDeleteTemplate(row.original._id)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="p-4">
      <div className="max-w-7xl mx-auto space-y-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold tracking-tight text-white">
              Questionnaire Templates
            </h1>
            <p className="text-gray-400">
              Create and manage questionnaire templates for your models
            </p>
          </div>
          <Button
            onClick={handleCreateTemplate}
            className="bg-[#1a1a1a] hover:bg-[#0f0f0f] text-white"
          >
            <Plus className="mr-2 h-4 w-4" />
            Create Template
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-3">
          <Card className="bg-[#18181b] border-[#27272a]">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-300">
                Total Templates
              </CardTitle>
              <FileText className="h-4 w-4 text-gray-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                {templates?.length || 0}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-[#18181b] border-[#27272a]">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-300">
                Active Templates
              </CardTitle>
              <Eye className="h-4 w-4 text-gray-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                {templates?.filter((t) => t.isActive)?.length || 0}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-[#18181b] border-[#27272a]">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-300">
                Quick Actions
              </CardTitle>
              <Users className="h-4 w-4 text-gray-400" />
            </CardHeader>
            <CardContent>
              <Button
                variant="outline"
                size="sm"
                className="bg-[#27272a] border-[#3f3f46] text-white hover:bg-[#3f3f46] hover:text-white"
                onClick={() =>
                  navigate("/agency/questionnaires/assignments")
                }
              >
                View Assignments
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Seeded Templates Section */}
        {templates?.filter((t) => !t.agencyId)?.length > 0 && (
          <div className="space-y-4">
            {/* Section Header - Detached */}
            <div className="bg-[#18181b] border border-[#27272a] rounded-lg p-4">
              <div className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-white" />
                <h3 className="text-lg font-medium text-white">
                  Default Templates
                </h3>
              </div>
              <p className="text-sm text-gray-400 mt-1">
                Pre-built templates ready to use for your questionnaires
              </p>
            </div>

            {/* Table - Separate Card */}
            <div className="bg-[#18181b] border border-[#27272a] rounded-lg">
              <DataTable
                columns={columns}
                data={templates?.filter((t) => !t.agencyId) || []}
                pagination={pagination}
                setPagination={setPagination}
                loading={templateLoading}
                disableHover={true}
              />
            </div>

            {/* Expanded Template Questions */}
            {expandedTemplate && (
              <div className="p-4 bg-[#0f0f0f] border border-[#3f3f46] rounded-lg">
                {(() => {
                  const template = templates?.find(
                    (t) => t._id === expandedTemplate
                  );
                  if (!template) return null;

                  return (
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <h4 className="text-lg font-medium text-white">
                          {template.title} - Question Details
                        </h4>
                        <button
                          onClick={() => {
                            setExpandedTemplate(null);
                            setExpandedSections({});
                          }}
                          className="text-gray-400 hover:text-white text-sm"
                        >
                          Close
                        </button>
                      </div>

                      <div className="grid gap-4">
                        {template.sections?.map((section, sectionIndex) => (
                          <div key={sectionIndex} className="space-y-3">
                            <button
                              onClick={() => {
                                const key = `${expandedTemplate}-${sectionIndex}`;
                                setExpandedSections((prev) => ({
                                  ...prev,
                                  [key]: !prev[key],
                                }));
                              }}
                              className="w-full text-left"
                            >
                              <h5 className="font-medium text-gray-300 flex items-center gap-2 hover:text-gray-400 transition-colors">
                                {expandedSections[
                                  `${expandedTemplate}-${sectionIndex}`
                                ] ? (
                                  <ChevronDown className="h-4 w-4" />
                                ) : (
                                  <ChevronRight className="h-4 w-4" />
                                )}
                                <span className="bg-[#1a1a1a] text-gray-300 text-xs px-2 py-1 rounded">
                                  Section {sectionIndex + 1}
                                </span>
                                {section.title}
                                <span className="text-xs text-gray-500">
                                  ({section.questions?.length || 0} questions)
                                </span>
                              </h5>
                            </button>

                            {expandedSections[
                              `${expandedTemplate}-${sectionIndex}`
                            ] && (
                              <div className="grid gap-2 pl-8 animate-in slide-in-from-top-2 duration-200">
                                {section.questions?.map((question, qIndex) => (
                                  <div
                                    key={qIndex}
                                    className="flex items-center gap-2 text-sm p-2 bg-[#1a1a1d] rounded border border-[#27272a]"
                                  >
                                    <span className="text-gray-500 text-xs min-w-[24px]">
                                      {qIndex + 1}.
                                    </span>
                                    <span className="text-gray-300 flex-1">
                                      {question.text || question.label}
                                    </span>
                                    <span className="text-xs text-gray-500 bg-gray-800 px-2 py-1 rounded">
                                      {question.type}
                                    </span>
                                    {question.required && (
                                      <span className="text-xs text-red-400">
                                        *
                                      </span>
                                    )}
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>

                      <div className="mt-4 pt-4 border-t border-[#3f3f46]">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-400">
                            Total Questions:{" "}
                            {template.sections?.reduce(
                              (total, section) =>
                                total + (section.questions?.length || 0),
                              0
                            ) || 0}
                          </span>
                          <button
                            onClick={() =>
                              navigate(
                                "/agency/questionnaires/create"
                              )
                            }
                            className="bg-[#1a1a1a] hover:bg-[#0f0f0f] text-gray-300 px-3 py-1 rounded text-sm"
                          >
                            Create from Template
                          </button>
                        </div>
                      </div>
                    </div>
                  );
                })()}
              </div>
            )}
          </div>
        )}

        {/* Custom Templates Section */}
        <div className="space-y-4">
          {/* Section Header - Detached */}
          <div className="bg-[#18181b] border border-[#27272a] rounded-lg p-4">
            <div className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-white" />
              <h3 className="text-lg font-medium text-white">Your Templates</h3>
            </div>
            <p className="text-sm text-gray-400 mt-1">
              Custom templates created by your agency
            </p>
          </div>

          {/* Table - Separate Card */}
          <Card className="bg-[#18181b] border-[#27272a]">
            <CardContent className="p-0">
              <DataTable
                columns={columns}
                data={templates?.filter((t) => t.agencyId) || []}
                pagination={pagination}
                setPagination={setPagination}
                loading={templateLoading}
                disableHover={true}
              />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
