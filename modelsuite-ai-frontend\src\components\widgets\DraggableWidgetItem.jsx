import React from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  startDrag,
  endDrag,
  selectDraggedWidget,
} from "@/redux/features/widgetSidebar/widgetSidebarSlice";
import WidgetIcon from "./WidgetIcon";

const DraggableWidgetItem = ({ widget }) => {
  const dispatch = useDispatch();
  const draggedWidget = useSelector(selectDraggedWidget);
  const isDragging = draggedWidget?.type === widget.type;

  const handleDragStart = (e) => {
    dispatch(startDrag(widget));

    // Set drag data for the HTML5 Drag API
    e.dataTransfer.setData("application/json", JSON.stringify(widget));
    e.dataTransfer.effectAllowed = "copy";

    // Create a custom drag image (optional)
    const dragImage = e.target.cloneNode(true);
    dragImage.style.opacity = "0.8";
    e.dataTransfer.setDragImage(dragImage, 12, 12);
  };

  const handleDragEnd = () => {
    dispatch(endDrag());
  };

  return (
    <div
      draggable
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      className={`
        group cursor-grab active:cursor-grabbing
        rounded-lg border-2 border-transparent
        transition-all duration-200
        flex items-center justify-center w-16 h-16
        ${isDragging ? "opacity-50 scale-95" : ""}
      `}
      title={`Drag ${widget.name} to dashboard`}
    >
      <WidgetIcon
        type={widget.type}
        size={36}
        className="text-gray-600 group-hover:text-blue-600 transition-colors"
        isDragging={isDragging}
      />

      {/* Visual drag indicator */}
      {isDragging && (
        <div className="absolute inset-0 border-2 border-dashed border-blue-400 rounded-lg" />
      )}
    </div>
  );
};

export default DraggableWidgetItem;
