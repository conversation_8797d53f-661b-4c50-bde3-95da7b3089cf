import express from "express";
import { verifyToken } from "../../middlewares/authMiddleware.js";
import checkPermission, {
  checkLegacyPermission,
} from "../../middlewares/permissionCheck.js";
import attachmentRoutes from "../../controllers/messages/messangerUpload.js";

const router = express.Router();

import {
  getTemplates,
  createTemplate,
  updateTemplate,
  deleteTemplate,
  translateMsg,
  translateBulkMessages,
} from "../../controllers/messages/chatController.js";
import {
  setConvoLanguage,
  toggleAutoTranslate,
} from "../../controllers/messages/dmController.js";

// All routes below require authentication
router.use(verifyToken);

router.use(
  "/attachments",
  checkLegacyPermission("chat", "manage"),
  attachmentRoutes,
);

// GET templates
router.get("/templates", getTemplates);

// CREATE new template
router.post("/templates/create", createTemplate);

// UPDATE existing template
router.put("/templates/update", updateTemplate);

// DELETE a template
router.delete("/templates/delete", deleteTemplate);

// translate a message
router.post("/translate", translateMsg);

router.patch("/autoTranslate", toggleAutoTranslate);
router.patch("/setLanguage", setConvoLanguage);
router.post("/translateBulkMassages", translateBulkMessages);

export default router;
