// Utility functions for formatting dashboard data

export const formatNumber = (num) => {
  if (num === null || num === undefined || num === "---") return "---";
  
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + "M";
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + "K";
  }
  return num.toString();
};

export const formatCurrency = (amount) => {
  if (amount === null || amount === undefined || amount === "---") return "---";
  
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

export const formatPlatformNumber = (num) => {
  if (num === null || num === undefined) return "0";
  
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + "k";
  }
  return num.toString();
};
