import React from "react";

const CompanyCardIcon = ({ className }) => {
  return (
    <svg
      width="100%"
      height="100%"
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <defs>
        <radialGradient id="companyBgRadial" cx="70%" cy="25%" r="85%">
          <stop offset="0%" stopColor="#4D1BFF" />
          <stop offset="45%" stopColor="#141227" />
          <stop offset="100%" stopColor="#090A16" />
        </radialGradient>
        <radialGradient id="companyPinkHalo" cx="70%" cy="25%" r="55%">
          <stop offset="0%" stopColor="#FF3CF6" stopOpacity="0.45" />
          <stop offset="100%" stopColor="#090A16" stopOpacity="0" />
        </radialGradient>
        <linearGradient
          id="companyStrokeGrad"
          x1="15%"
          y1="15%"
          x2="85%"
          y2="85%"
        >
          <stop offset="0%" stopColor="#43D3FF" />
          <stop offset="50%" stopColor="#8A43FF" />
          <stop offset="100%" stopColor="#FF3CF6" />
        </linearGradient>
        <linearGradient
          id="companyAvatarGrad"
          x1="0%"
          y1="0%"
          x2="100%"
          y2="100%"
        >
          <stop offset="0%" stopColor="#8A43FF" />
          <stop offset="100%" stopColor="#FF3CF6" />
        </linearGradient>
        <filter id="companyGlow" x="-60%" y="-60%" width="220%" height="220%">
          <feGaussianBlur stdDeviation="14" result="blur" />
          <feMerge>
            <feMergeNode in="blur" />
            <feMergeNode in="SourceGraphic" />
          </feMerge>
        </filter>
      </defs>

      <rect
        x="64"
        y="64"
        width="896"
        height="896"
        rx="210"
        fill="url(#companyBgRadial)"
      />
      <rect
        x="64"
        y="64"
        width="896"
        height="896"
        rx="210"
        fill="url(#companyPinkHalo)"
      />

      <g
        fill="none"
        strokeLinecap="round"
        strokeLinejoin="round"
        transform="rotate(-10 680 380)"
      >
        <rect
          x="560"
          y="240"
          width="300"
          height="460"
          rx="48"
          stroke="url(#companyStrokeGrad)"
          strokeWidth="28"
          filter="url(#companyGlow)"
        />
        <rect
          x="560"
          y="240"
          width="300"
          height="460"
          rx="48"
          stroke="url(#companyStrokeGrad)"
          strokeWidth="14"
        />
      </g>

      <g fill="none" strokeLinecap="round" strokeLinejoin="round">
        <rect
          x="260"
          y="220"
          width="500"
          height="600"
          rx="60"
          stroke="url(#companyStrokeGrad)"
          strokeWidth="30"
          filter="url(#companyGlow)"
        />
        <rect
          x="260"
          y="220"
          width="500"
          height="600"
          rx="60"
          stroke="url(#companyStrokeGrad)"
          strokeWidth="16"
        />
      </g>

      <rect
        x="420"
        y="300"
        width="180"
        height="180"
        rx="28"
        fill="url(#companyAvatarGrad)"
      />
      <g stroke="url(#companyStrokeGrad)" strokeLinecap="round">
        <line
          x1="380"
          y1="560"
          x2="640"
          y2="560"
          strokeWidth="24"
          filter="url(#companyGlow)"
        />
        <line
          x1="380"
          y1="620"
          x2="640"
          y2="620"
          strokeWidth="24"
          filter="url(#companyGlow)"
        />
        <line x1="380" y1="560" x2="640" y2="560" strokeWidth="14" />
        <line x1="380" y1="620" x2="640" y2="620" strokeWidth="14" />
      </g>
    </svg>
  );
};

export default CompanyCardIcon;
