import React from "react";

const LaunchpadRocketIcon = ({ className }) => {
  return (
    <svg
      width="100%"
      height="100%"
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <defs>
        {/* Background gradients */}
        <radialGradient id="bgRadial" cx="65%" cy="25%" r="80%">
          <stop offset="0%" stopColor="#5A0CF9" />
          <stop offset="40%" stopColor="#1E0B3A" />
          <stop offset="100%" stopColor="#0A0A19" />
        </radialGradient>
        <radialGradient id="blueGlow" cx="35%" cy="75%" r="60%">
          <stop offset="0%" stopColor="#0A9CFF" stopOpacity="0.5" />
          <stop offset="100%" stopColor="#0A0A19" stopOpacity="0" />
        </radialGradient>

        {/* Rocket stroke gradient + glow */}
        <linearGradient id="strokeGrad" x1="15%" y1="20%" x2="85%" y2="80%">
          <stop offset="0%" stopColor="#FF3CF6" />
          <stop offset="55%" stopColor="#A43CFF" />
          <stop offset="100%" stopColor="#0AA3FF" />
        </linearGradient>
        <filter id="outerGlow" x="-50%" y="-50%" width="200%" height="200%">
          <feGaussianBlur stdDeviation="14" result="blur" />
          <feMerge>
            <feMergeNode in="blur" />
            <feMergeNode in="SourceGraphic" />
          </feMerge>
        </filter>
      </defs>

      {/* Rounded-square background */}
      <rect
        x="64"
        y="64"
        width="896"
        height="896"
        rx="220"
        fill="url(#bgRadial)"
      />
      <rect
        x="64"
        y="64"
        width="896"
        height="896"
        rx="220"
        fill="url(#blueGlow)"
      />

      {/* Rocket */}
      <g id="rocket" fill="none" strokeLinecap="round" strokeLinejoin="round">
        {/* glow pass */}
        <g filter="url(#outerGlow)" stroke="url(#strokeGrad)" strokeWidth="30">
          <path
            d="M512 170
                   C610 230 670 340 670 430
                   L670 600
                   L354 600
                   L354 430
                   C354 340 414 230 512 170 Z"
          />
          <circle cx="570" cy="365" r="55" />
          <path d="M354 545 L300 630 L400 630 L382 545 Z" />
          <path d="M670 545 L650 630 L750 630 L690 545 Z" />
          <path d="M480 640 C420 700 430 770 480 820" />
          <path d="M560 640 C620 700 610 770 560 820" />
        </g>
        {/* crisp pass */}
        <g stroke="url(#strokeGrad)" strokeWidth="16">
          <path
            d="M512 170
                   C610 230 670 340 670 430
                   L670 600
                   L354 600
                   L354 430
                   C354 340 414 230 512 170 Z"
          />
          <circle cx="570" cy="365" r="55" />
          <path d="M354 545 L300 630 L400 630 L382 545 Z" />
          <path d="M670 545 L650 630 L750 630 L690 545 Z" />
          <path d="M480 640 C420 700 430 770 480 820" />
          <path d="M560 640 C620 700 610 770 560 820" />
        </g>
      </g>
    </svg>
  );
};

export default LaunchpadRocketIcon;
