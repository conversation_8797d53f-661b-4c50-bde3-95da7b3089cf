import DmConversation from "../../models/messanger/dmConversations.js";
import DmMessage from "../../models/messanger/dmMessages.js";
import ModelUser from "../../models/model.js";
import Agency from "../../models/agency.js";

import { bulkTranslateWithGemini } from "./chatController.js";

export const populateTranslatedText = async (messages, targetLang) => {
  if (!Array.isArray(messages) || messages.length === 0) {
    return messages;
  }

  try {
    // Step 1: Prepare array for bulk translator
    const messagesToTranslate = messages.map((msg) => ({
      messageId: msg._id.toString(),
      text: msg.text || "",
    }));

    // Step 2: Call bulk translator
    const translations = await bulkTranslateWithGemini(
      messagesToTranslate,
      targetLang,
    );

    // Step 3: Create a quick lookup map
    const translationMap = {};
    translations.forEach((t) => {
      translationMap[t.messageId] = t.text;
    });

    // Step 4: Merge translatedText into each message
    return messages.map((msg) => ({
      ...msg,
      translatedText: translationMap[msg._id.toString()] || null,
    }));
  } catch (err) {
    console.error("Error in populateTranslatedText:", err);
    // Fallback: return messages without translations
    return messages.map((msg) => ({ ...msg, translatedText: null }));
  }
};

// Helper function to populate replyTo messages with sender details
const populateReplyToMessages = async (messages) => {
  if (!messages || messages.length === 0) return messages;

  // Get all unique replyTo message IDs
  const replyToIds = messages
    .filter((msg) => msg.replyTo)
    .map((msg) => msg.replyTo)
    .filter((id) => id); // Remove null/undefined values

  if (replyToIds.length === 0) return messages;

  // Fetch all replyTo messages
  const replyToMessages = await DmMessage.find({
    _id: { $in: replyToIds },
  }).lean();

  // Get all sender IDs from replyTo messages
  const senderIds = replyToMessages
    .map((msg) => msg.senderId)
    .filter((id) => id);

  // Fetch sender details from both ModelUser and Agency collections
  const [modelUsers, agencies] = await Promise.all([
    ModelUser.find({ _id: { $in: senderIds } })
      .select("_id fullName profilePhoto")
      .lean(),
    Agency.find({ _id: { $in: senderIds } })
      .select("_id agencyName profilePhoto")
      .lean(),
  ]);

  // Create sender details map
  const senderDetailsMap = {};

  modelUsers.forEach((user) => {
    senderDetailsMap[user._id.toString()] = {
      name: user.fullName,
      avatar: user.profilePhoto || null,
    };
  });

  agencies.forEach((agency) => {
    senderDetailsMap[agency._id.toString()] = {
      name: agency.agencyName,
      avatar: agency.profilePhoto || null,
    };
  });

  // Create replyTo messages map with sender details
  const replyToMessagesMap = {};
  replyToMessages.forEach((msg) => {
    const senderDetails = senderDetailsMap[msg.senderId.toString()] || {
      name: "Unknown User",
      avatar: null,
    };

    replyToMessagesMap[msg._id.toString()] = {
      ...msg,
      senderName: senderDetails.name,
      senderAvatar: senderDetails.avatar,
    };
  });

  // Populate replyTo property in original messages
  return messages.map((msg) => ({
    ...msg,
    replyTo: msg.replyTo
      ? replyToMessagesMap[msg.replyTo.toString()] || null
      : null,
  }));
};

const populateSenderDetails = async (messages) => {
  if (!messages || messages.length === 0) return messages;

  // Get all unique sender IDs
  const senderIds = messages.map((msg) => msg.senderId).filter((id) => id); // Remove null/undefined values

  if (senderIds.length === 0) return messages;

  // Fetch sender details from both ModelUser and Agency collections
  const [modelUsers, agencies] = await Promise.all([
    ModelUser.find({ _id: { $in: senderIds } })
      .select("_id fullName profilePhoto")
      .lean(),
    Agency.find({ _id: { $in: senderIds } })
      .select("_id agencyName profilePhoto")
      .lean(),
  ]);

  // Create sender details map
  const senderDetailsMap = {};

  modelUsers.forEach((user) => {
    senderDetailsMap[user._id.toString()] = {
      name: user.fullName,
      avatar: user.profilePhoto || null,
    };
  });

  agencies.forEach((agency) => {
    senderDetailsMap[agency._id.toString()] = {
      name: agency.agencyName,
      avatar: agency.profilePhoto || null,
    };
  });

  // Populate sender details in messages
  return messages.map((msg) => {
    const senderDetails = senderDetailsMap[msg.senderId?.toString()] || {
      name: "Unknown User",
      avatar: null,
    };

    return {
      ...msg,
      senderName: senderDetails.name,
      senderAvatar: senderDetails.avatar,
    };
  });
};

export const getAllDMConversations = async (req, res) => {
  try {
    const userId = req.user.id;

    // Step 1: Fetch all conversations the user is part of
    const conversations = await DmConversation.find({
      "members.userId": userId,
    }).lean();

    const convoIds = conversations.map((convo) => convo._id);

    // Step 2: Fetch last 31 messages per conversation (extra 1 to check hasMore)
    const messagesByConvo = {};
    const pinnedMessagesByConvo = {};

    await Promise.all(
      convoIds.map(async (convoId) => {
        const convo = conversations.find(
          (c) => c._id.toString() === convoId.toString(),
        );
        const currentMember = convo.members.find(
          (m) => m.userId.toString() === userId,
        );

        const autoTranslate = currentMember?.autoTranslate || false;
        const chatLanguage = currentMember?.language || null;
        // Fetch regular messages
        let messages = await DmMessage.find({ convoId })
          .sort({ createdAt: -1 }) // newest first
          .limit(31) // fetch 31, show 30
          .lean();

        const hasMoreMessages = messages.length > 30;
        messages = messages.slice(0, 30).reverse(); // reverse to oldest -> newest

        // Populate replyTo messages
        messages = await populateReplyToMessages(messages);
        // populate senderDetails
        messages = await populateSenderDetails(messages);

        if (autoTranslate && chatLanguage) {
          messages = await populateTranslatedText(messages, chatLanguage);
        }

        messagesByConvo[convoId.toString()] = {
          messages,
          hasMoreOlder: hasMoreMessages,
        };

        // Fetch all pinned messages for this conversation
        let pinnedMessages = await DmMessage.find({
          convoId,
          pinned: true,
        })
          .sort({ createdAt: -1 }) // newest first
          .lean();

        // Populate replyTo messages for pinned messages
        pinnedMessages = await populateReplyToMessages(pinnedMessages);

        pinnedMessagesByConvo[convoId.toString()] = pinnedMessages;
      }),
    );

    // Step 3: Collect all member IDs
    const memberIdSet = new Set();
    conversations.forEach((convo) => {
      convo.members.forEach((member) => {
        memberIdSet.add(member.userId.toString());
      });
    });
    const memberIds = Array.from(memberIdSet);

    // Step 4: Fetch user and agency details
    const [modelUsers, agencies] = await Promise.all([
      ModelUser.find({ _id: { $in: memberIds } })
        .select("_id fullName profilePhoto lastOnline")
        .lean(),
      Agency.find({ _id: { $in: memberIds } })
        .select("_id agencyName profilePhoto lastOnline")
        .lean(),
    ]);

    const userDetailsMap = {};

    modelUsers.forEach((user) => {
      userDetailsMap[user._id.toString()] = {
        name: user.fullName,
        avatar: user.profilePhoto || null,
        status: {
          isOnline: null,
          isTyping: null,
          lastOnline: user.lastOnline || null,
        },
      };
    });

    agencies.forEach((agency) => {
      userDetailsMap[agency._id.toString()] = {
        name: agency.agencyName,
        avatar: agency.profilePhoto || null,
        status: {
          isOnline: null,
          isTyping: null,
          lastOnline: agency.lastOnline || null,
        },
      };
    });

    // Step 5: Format final conversations
    const formattedConversations = conversations.map((convo) => {
      const convoIdStr = convo._id.toString();
      const msgData = messagesByConvo[convoIdStr] || {
        messages: [],
        hasMoreOlder: false,
        hasMoreNewer: false,
      };
      const pinnedData = pinnedMessagesByConvo[convoIdStr] || [];

      // Format pinned messages with sender details
      const formattedPinnedMessages = pinnedData.map((msg) => ({
        ...msg,
        senderName:
          userDetailsMap[msg.senderId.toString()]?.name || "Unknown User",
        senderAvatar: userDetailsMap[msg.senderId.toString()]?.avatar || null,
      }));

      return {
        convoId: convoIdStr,
        type: convo.type,
        createdBy: convo.createdBy,
        createdAt: convo.createdAt,
        isLoading: false,
        hasMoreOlder: msgData.hasMoreOlder,
        hasMoreNewer: false,
        messages: msgData.messages,
        pinnedMessages: formattedPinnedMessages,
        members: convo.members.map((member) => {
          const details = userDetailsMap[member.userId.toString()] || {
            name: null,
            avatar: null,
            status: { isOnline: null, isTyping: null, lastOnline: null },
          };
          return {
            userId: member.userId.toString(),
            joinedAt: member.joinedAt,
            pinned: member.pinned,
            name: details.name,
            avatar: details.avatar,
            status: details.status,
            language: member.language || null,
            autoTranslate: member.autoTranslate || null,
          };
        }),
      };
    });

    return res.status(200).json(formattedConversations);
  } catch (error) {
    console.error("Error fetching DM conversations:", error);
    return res.status(500).json({ error: "Server error" });
  }
};

// fetch older messages
export const fetchOlderDMMessages = async (req, res) => {
  try {
    const userId = req.user.id;
    const { convoId, oldestTimestamp } = req.query;

    if (!convoId || !oldestTimestamp) {
      return res
        .status(400)
        .json({ error: "Missing convoId or oldestTimestamp" });
    }

    const timestamp = new Date(oldestTimestamp);
    const LIMIT = 30;

    // ✅ Get current user's conversation settings
    const convo = await DmConversation.findById(convoId).lean();
    if (!convo) {
      return res.status(404).json({ error: "Conversation not found" });
    }

    const currentMember = convo.members.find(
      (m) => m.userId.toString() === userId,
    );
    const autoTranslate = currentMember?.autoTranslate || false;
    const chatLanguage = currentMember?.language || null;

    // Fetch 31 messages older than the given timestamp, newest first
    let messages = await DmMessage.find({
      convoId,
      createdAt: { $lt: timestamp },
    })
      .sort({ createdAt: -1 }) // newest of the older ones first
      .limit(LIMIT + 1)
      .lean();

    const hasMoreOlder = messages.length > LIMIT;

    // Only keep the first 30 (most recent of the older messages)
    if (hasMoreOlder) messages = messages.slice(0, LIMIT);

    // Now reverse so frontend gets oldest → newest
    messages.reverse();

    // Populate replyTo messages
    messages = await populateReplyToMessages(messages);

    // Populate senderDetails
    messages = await populateSenderDetails(messages);

    // ✅ Auto-translate if enabled and language is set
    if (autoTranslate && chatLanguage) {
      messages = await populateTranslatedText(messages, chatLanguage);
    }

    return res.status(200).json({
      messages,
      hasMoreOlder,
    });
  } catch (error) {
    console.error("Error fetching more DM messages:", error);
    return res.status(500).json({ error: "Server error" });
  }
};

// Controller to fetch newer messages after a given timestamp
export const fetchNewerDMMessages = async (req, res) => {
  try {
    const userId = req.user.id;
    const { convoId, newestTimestamp } = req.query;

    if (!convoId || !newestTimestamp) {
      return res
        .status(400)
        .json({ error: "Missing convoId or newestTimestamp" });
    }

    const timestamp = new Date(newestTimestamp);
    const LIMIT = 30;

    // ✅ Get current user's conversation settings
    const convo = await DmConversation.findById(convoId).lean();
    if (!convo) {
      return res.status(404).json({ error: "Conversation not found" });
    }

    const currentMember = convo.members.find(
      (m) => m.userId.toString() === userId,
    );
    const autoTranslate = currentMember?.autoTranslate || false;
    const chatLanguage = currentMember?.language || null;

    // Fetch 31 messages newer than the given timestamp, oldest first
    let messages = await DmMessage.find({
      convoId,
      createdAt: { $gt: timestamp },
    })
      .sort({ createdAt: 1 }) // oldest of the newer ones first
      .limit(LIMIT + 1)
      .lean();

    const hasMoreNewer = messages.length > LIMIT;
    if (hasMoreNewer) messages = messages.slice(0, LIMIT);

    // Populate replyTo messages
    messages = await populateReplyToMessages(messages);

    // Populate senderDetails
    messages = await populateSenderDetails(messages);

    // ✅ Auto-translate if enabled
    if (autoTranslate && chatLanguage) {
      messages = await populateTranslatedText(messages, chatLanguage);
    }

    return res.status(200).json({
      messages,
      hasMoreNewer,
    });
  } catch (error) {
    console.error("Error fetching newer DM messages:", error);
    return res.status(500).json({ error: "Server error" });
  }
};

// Controller to fetch messages around a specific message (20 before + target + 20 after)
export const fetchSpecificDMMessages = async (req, res) => {
  try {
    const userId = req.user.id;
    const { convoId, messageId } = req.query;

    if (!convoId || !messageId) {
      return res.status(400).json({ error: "Missing convoId or messageId" });
    }

    // ✅ Get current user's conversation settings
    const convo = await DmConversation.findById(convoId).lean();
    if (!convo) {
      return res.status(404).json({ error: "Conversation not found" });
    }

    const currentMember = convo.members.find(
      (m) => m.userId.toString() === userId,
    );
    const autoTranslate = currentMember?.autoTranslate || false;
    const chatLanguage = currentMember?.language || null;

    // First, get the target message to find its timestamp
    const targetMessage = await DmMessage.findOne({
      _id: messageId,
      convoId,
    }).lean();

    if (!targetMessage) {
      return res.status(404).json({ error: "Message not found" });
    }

    const targetTimestamp = targetMessage.createdAt;
    const RANGE_LIMIT = 20;

    // Fetch 20 messages before the target message (newest first, then reverse)
    const messagesBefore = await DmMessage.find({
      convoId,
      createdAt: { $lt: targetTimestamp },
    })
      .sort({ createdAt: -1 })
      .limit(RANGE_LIMIT)
      .lean();

    // Fetch 20 messages after the target message (oldest first)
    const messagesAfter = await DmMessage.find({
      convoId,
      createdAt: { $gt: targetTimestamp },
    })
      .sort({ createdAt: 1 })
      .limit(RANGE_LIMIT)
      .lean();

    // Reverse the before messages to get chronological order
    messagesBefore.reverse();

    // Combine: older messages + target message + newer messages
    let messages = [...messagesBefore, targetMessage, ...messagesAfter];

    // Populate replyTo messages
    messages = await populateReplyToMessages(messages);

    // Populate senderDetails
    messages = await populateSenderDetails(messages);

    // ✅ Auto-translate if enabled
    if (autoTranslate && chatLanguage) {
      messages = await populateTranslatedText(messages, chatLanguage);
    }

    return res.status(200).json({
      messages,
      targetMessageIndex: messagesBefore.length,
    });
  } catch (error) {
    console.error("Error fetching specific DM messages:", error);
    return res.status(500).json({ error: "Server error" });
  }
};

//utility function

export const deleteAllDmsOfUser = async (req, res) => {
  try {
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({ error: "userId is required in body" });
    }

    // Step 1: Find all DM conversations where this user is a member
    const conversations = await DmConversation.find({
      "members.userId": userId,
    }).select("_id");

    const convoIds = conversations.map((convo) => convo._id);

    // Step 2: Delete all DM messages from those conversations
    const msgDeleteResult = await DmMessage.deleteMany({
      convoId: { $in: convoIds },
    });

    // Step 3: Delete all those conversations
    const convoDeleteResult = await DmConversation.deleteMany({
      _id: { $in: convoIds },
    });

    return res.status(200).json({
      message: `Deleted ${convoDeleteResult.deletedCount} conversations and ${msgDeleteResult.deletedCount} messages for user ${userId}`,
    });
  } catch (error) {
    console.error("Error deleting DMs for user:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const createNewDm = async (req, res) => {
  try {
    const { targetUserId } = req.body;
    const createdBy = req.user.id;

    // Check for existing DM
    const existingConvo = await DmConversation.findOne({
      "members.userId": { $all: [createdBy, targetUserId] },
      $expr: { $eq: [{ $size: "$members" }, 2] },
    }).lean();

    const convo =
      existingConvo ||
      (await DmConversation.create({
        type: "dm",
        members: [{ userId: createdBy }, { userId: targetUserId }],
        createdBy,
      }));

    // Normalize convo object (whether found or created)
    const convoDoc = existingConvo ? convo : convo.toObject();

    const memberIds = convoDoc.members.map((m) => m.userId.toString());

    // Fetch user details
    const [modelUsers, agencies] = await Promise.all([
      ModelUser.find({ _id: { $in: memberIds } })
        .select("_id fullName profilePhoto lastOnline")
        .lean(),
      Agency.find({ _id: { $in: memberIds } })
        .select("_id agencyName profilePhoto lastOnline")
        .lean(),
    ]);

    const userDetailsMap = {};
    modelUsers.forEach((user) => {
      userDetailsMap[user._id.toString()] = {
        name: user.fullName,
        avatar: user.profilePhoto || null,
        status: {
          isOnline: null,
          isTyping: null,
          lastOnline: user.lastOnline || null,
        },
      };
    });

    agencies.forEach((agency) => {
      userDetailsMap[agency._id.toString()] = {
        name: agency.agencyName,
        avatar: agency.profilePhoto || null,
        status: {
          isOnline: null,
          isTyping: null,
          lastOnline: agency.lastOnline || null,
        },
      };
    });

    const formattedConvo = {
      convoId: convoDoc._id.toString(),
      type: convo.type,
      createdBy: convoDoc.createdBy,
      createdAt: convoDoc.createdAt,
      isLoading: false,
      hasMore: false,
      messages: [], // no messages on creation
      members: convoDoc.members.map((member) => {
        const details = userDetailsMap[member.userId.toString()] || {
          name: null,
          avatar: null,
          status: { isOnline: null, isTyping: null, lastOnline: null },
        };
        return {
          userId: member.userId.toString(),
          joinedAt: member.joinedAt || null,
          pinned: member.pinned || false,
          name: details.name,
          avatar: details.avatar,
          status: details.status,
        };
      }),
    };

    return res.status(existingConvo ? 200 : 201).json({
      success: true,
      conversation: formattedConvo,
    });
  } catch (error) {
    console.error("Error creating new DM:", error);
    return res.status(500).json({ success: false, message: "Server error" });
  }
};

// Delete latest message from a given convoId
export const deleteLatestMsg = async (req, res) => {
  try {
    const { convoId } = req.params;

    // Find the latest message in this convo
    const latestMessage = await DmMessage.findOne({ convoId })
      .sort({ createdAt: -1 })
      .exec();

    if (!latestMessage) {
      return res
        .status(404)
        .json({ message: "No messages found in this chat." });
    }

    // Delete the latest message
    await DmMessage.deleteOne({ _id: latestMessage._id });

    res.status(200).json({
      message: "Latest message deleted.",
      deletedMessage: latestMessage,
    });
    return null;
  } catch (error) {
    console.error("Error deleting latest message:", error);
    res.status(500).json({ message: "Server error while deleting message." });
    return null;
  }
};

export const getPinnedMessagesWithUserDetails = async (req, res) => {
  try {
    const userId = req.user.id;
    const { convoId } = req.query;

    if (!convoId) {
      return res.status(400).json({ error: "Conversation ID is required" });
    }

    // Step 1: Verify user is part of this conversation
    const conversation = await DmConversation.findOne({
      _id: convoId,
      "members.userId": userId,
    }).lean();

    if (!conversation) {
      return res
        .status(404)
        .json({ error: "Conversation not found or access denied" });
    }

    // Step 2: Fetch all pinned messages for this conversation
    const pinnedMessages = await DmMessage.find({
      convoId,
      pinned: true,
    })
      .sort({ createdAt: -1 }) // newest first
      .lean();

    if (pinnedMessages.length === 0) {
      return res.status(200).json([]);
    }

    // Step 3: Collect all sender IDs from pinned messages
    const senderIds = [
      ...new Set(pinnedMessages.map((msg) => msg.senderId.toString())),
    ];

    // Step 4: Fetch user and agency details for senders
    const [modelUsers, agencies] = await Promise.all([
      ModelUser.find({ _id: { $in: senderIds } })
        .select("_id fullName profilePhoto")
        .lean(),
      Agency.find({ _id: { $in: senderIds } })
        .select("_id agencyName profilePhoto")
        .lean(),
    ]);

    // Step 5: Create user details map
    const userDetailsMap = {};

    modelUsers.forEach((user) => {
      userDetailsMap[user._id.toString()] = {
        name: user.fullName,
        avatar: user.profilePhoto || null,
      };
    });

    agencies.forEach((agency) => {
      userDetailsMap[agency._id.toString()] = {
        name: agency.agencyName,
        avatar: agency.profilePhoto || null,
      };
    });

    // Step 6: Format pinned messages with sender details
    const formattedPinnedMessages = pinnedMessages.map((msg) => ({
      ...msg,
      senderName:
        userDetailsMap[msg.senderId.toString()]?.name || "Unknown User",
      senderAvatar: userDetailsMap[msg.senderId.toString()]?.avatar || null,
    }));

    return res.status(200).json(formattedPinnedMessages);
  } catch (error) {
    console.error("Error fetching pinned messages with user details:", error);
    return res.status(500).json({ error: "Server error" });
  }
};

export const searchDMMessages = async (req, res) => {
  try {
    const { convoId, query, offset = 0, limit = 20 } = req.query;

    if (!convoId || !query) {
      return res.status(400).json({ error: "Missing convoId or query" });
    }

    const searchQuery = query.trim();
    if (searchQuery.length < 2) {
      return res
        .status(400)
        .json({ error: "Query must be at least 2 characters" });
    }

    const offsetNum = parseInt(offset) || 0;
    const limitNum = Math.min(parseInt(limit) || 20, 50); // Max 50 results per request

    // Build search criteria
    const searchCriteria = {
      convoId,
      $or: [
        { text: { $regex: searchQuery, $options: "i" } },
        { "attachments.name": { $regex: searchQuery, $options: "i" } },
      ],
      // Exclude deleted messages
      $and: [
        { deletedFor: { $ne: "everyone" } },
        { deletedFor: { $not: { $in: [req.user._id.toString()] } } },
      ],
    };

    // Get total count for pagination info
    const totalCount = await DmMessage.countDocuments(searchCriteria);

    // Fetch messages with pagination
    const messages = await DmMessage.find(searchCriteria)
      .sort({ createdAt: -1 }) // Most recent first
      .skip(offsetNum)
      .limit(limitNum + 1) // Fetch one extra to check if there are more
      .lean();

    const hasMore = messages.length > limitNum;
    if (hasMore) {
      messages.pop(); // Remove the extra message
    }

    // Get unique sender IDs
    const senderIds = [
      ...new Set(messages.map((msg) => msg.senderId.toString())),
    ];

    // Fetch sender details from both Model and Agency collections
    const [modelSenders, agencySenders] = await Promise.all([
      ModelUser.find({ _id: { $in: senderIds } })
        .select("_id fullName profilePhoto")
        .lean(),
      Agency.find({ _id: { $in: senderIds } })
        .select("_id agencyName profilePhoto")
        .lean(),
    ]);

    // Create sender lookup map
    const senderMap = {};

    modelSenders.forEach((model) => {
      senderMap[model._id.toString()] = {
        name: model.fullName,
        avatar: model.profilePhoto,
        type: "model",
      };
    });

    agencySenders.forEach((agency) => {
      senderMap[agency._id.toString()] = {
        name: agency.agencyName,
        avatar: agency.profilePhoto,
        type: "agency",
      };
    });

    // Enhance messages with sender info
    const enhancedMessages = messages.map((message) => ({
      _id: message._id,
      text: message.text,
      attachments: message.attachments,
      createdAt: message.createdAt,
      senderId: message.senderId,
      senderName:
        senderMap[message.senderId.toString()]?.name || "Unknown User",
      senderAvatar: senderMap[message.senderId.toString()]?.avatar || null,
      senderType: senderMap[message.senderId.toString()]?.type || "unknown",
      type: message.type,
      edited: message.edited,
      pinned: message.pinned,
      replyTo: message.replyTo,
    }));

    return res.status(200).json({
      messages: enhancedMessages,
      total: totalCount,
      hasMore,
      offset: offsetNum,
      limit: limitNum,
    });
  } catch (error) {
    console.error("Error searching DM messages:", error);
    return res.status(500).json({ error: "Server error" });
  }
};

// controllers/dmMediaController.js
// controllers/dmMediaController.js

export const getDmMedia = async (req, res) => {
  try {
    const { convoId } = req.params;
    const { type = "photos", page = 1, limit = 20 } = req.query;

    // Validate convoId
    if (!convoId) {
      return res.status(400).json({
        success: false,
        error: "Missing conversation ID",
      });
    }

    const pageNum = parseInt(page) || 1;
    const limitNum = Math.min(parseInt(limit) || 20, 50);
    const skip = (pageNum - 1) * limitNum;

    // Enhanced media type detection
    const getMediaType = (mimeType, fileName) => {
      if (!mimeType && !fileName) return "docs";

      const type = mimeType ? mimeType.toLowerCase() : "";
      const extension = fileName ? fileName.split(".").pop().toLowerCase() : "";

      // Image types (including GIFs)
      if (
        type.startsWith("image") ||
        ["jpg", "jpeg", "png", "gif", "bmp", "svg", "webp"].includes(extension)
      ) {
        return "photos";
      }

      // Video types
      if (
        type.startsWith("video") ||
        ["mp4", "avi", "mov", "wmv", "flv", "mkv"].includes(extension)
      ) {
        return "videos";
      }

      // Everything else is docs (including audio files)
      return "docs";
    };

    // Get file extension for docs
    const getFileExtension = (fileName) => {
      if (!fileName) return "FILE";
      const ext = fileName.split(".").pop().toLowerCase();
      const extensionMap = {
        pdf: "PDF",
        doc: "DOC",
        docx: "DOC",
        txt: "TXT",
        rtf: "RTF",
        odt: "ODT",
        pages: "PAGE",
        xls: "XLS",
        xlsx: "XLS",
        csv: "CSV",
        ods: "ODS",
        numbers: "NUM",
        ppt: "PPT",
        pptx: "PPT",
        odp: "ODP",
        key: "KEY",
        zip: "ZIP",
        rar: "RAR",
        "7z": "7Z",
        tar: "TAR",
        gz: "GZ",
        json: "JSON",
        xml: "XML",
        html: "HTML",
        css: "CSS",
        js: "JS",
        ts: "TS",
        jsx: "JSX",
        tsx: "TSX",
        py: "PY",
        java: "JAVA",
        cpp: "CPP",
        c: "C",
        h: "H",
        php: "PHP",
        rb: "RB",
        go: "GO",
        rs: "RS",
        md: "MD",
        tex: "TEX",
        log: "LOG",
      };
      return extensionMap[ext] || ext.toUpperCase();
    };

    // Get color for file type
    const getFileTypeColor = (extension) => {
      const colorMap = {
        PDF: "#ef4444",
        DOC: "#3b82f6",
        TXT: "#10b981",
        RTF: "#10b981",
        ODT: "#3b82f6",
        PAGE: "#3b82f6",
        XLS: "#059669",
        CSV: "#059669",
        ODS: "#059669",
        NUM: "#059669",
        PPT: "#f59e0b",
        ODP: "#f59e0b",
        KEY: "#f59e0b",
        ZIP: "#8b5cf6",
        RAR: "#8b5cf6",
        "7Z": "#8b5cf6",
        TAR: "#8b5cf6",
        GZ: "#8b5cf6",
        JSON: "#eab308",
        XML: "#eab308",
        HTML: "#f97316",
        CSS: "#3b82f6",
        JS: "#eab308",
        TS: "#3b82f6",
        JSX: "#06b6d4",
        TSX: "#06b6d4",
        PY: "#3b82f6",
        JAVA: "#f97316",
        CPP: "#3b82f6",
        C: "#6b7280",
        H: "#6b7280",
        PHP: "#8b5cf6",
        RB: "#ef4444",
        GO: "#06b6d4",
        RS: "#f97316",
        MD: "#6b7280",
        TEX: "#10b981",
        LOG: "#6b7280",
      };
      return colorMap[extension] || "#6b7280";
    };

    // Build search criteria
    const searchCriteria = {
      convoId,
      attachments: { $exists: true, $ne: [] },
      $and: [
        { deletedFor: { $ne: "everyone" } },
        { deletedFor: { $not: { $in: [req.user._id.toString()] } } },
      ],
    };

    // Get all messages with attachments
    const messages = await DmMessage.find(searchCriteria)
      .sort({ createdAt: -1 })
      .lean();

    // Process and categorize media items
    const allMediaItems = [];

    messages.forEach((message) => {
      if (message.attachments && message.attachments.length > 0) {
        message.attachments.forEach((attachment) => {
          if (attachment.cloudinaryUrl) {
            const mediaType = getMediaType(
              attachment.mimeType,
              attachment.name,
            );

            if (attachment.mimeType?.includes("audio")) {
              return;
            }

            // Only include files that match our supported categories
            if (mediaType) {
              const fileExtension = getFileExtension(attachment.name);

              allMediaItems.push({
                id: attachment.fileId || attachment._id,
                messageId: message._id,
                convoId: message.convoId,
                type: mediaType,
                name: attachment.name || "Unnamed file",
                mimeType: attachment.mimeType,
                url: attachment.cloudinaryUrl,
                size: attachment.size || 0,
                width: attachment.width,
                height: attachment.height,
                duration: attachment.duration,
                createdAt: message.createdAt,
                senderId: message.senderId,
                fileExtension,
                fileTypeColor: getFileTypeColor(fileExtension),
              });
            }
          }
        });
      }
    });

    // Filter by type
    const filteredItems = allMediaItems.filter((item) => item.type === type);

    // Apply pagination
    const totalCount = filteredItems.length;
    const paginatedItems = filteredItems.slice(skip, skip + limitNum);
    const hasNext = skip + limitNum < totalCount;

    // Calculate counts by category
    const counts = {
      photos: allMediaItems.filter((item) => item.type === "photos").length,
      videos: allMediaItems.filter((item) => item.type === "videos").length,
      docs: allMediaItems.filter((item) => item.type === "docs").length,
    };

    // Prepare pagination info
    const pagination = {
      currentPage: pageNum,
      totalPages: Math.ceil(totalCount / limitNum),
      totalItems: totalCount,
      hasNext,
      nextPage: hasNext ? pageNum + 1 : null,
    };

    res.status(200).json({
      success: true,
      data: {
        mediaItems: paginatedItems,
        counts,
        pagination,
        currentType: type,
      },
      message: `Found ${totalCount} ${type} items`,
    });
  } catch (error) {
    console.error("Error fetching DM media:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch media items",
      details:
        process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

// controllers/dmChatController.js
export const toggleAutoTranslate = async (req, res) => {
  try {
    const { convoId, userId, value } = req.body;
    console.log(convoId, userId, value);
    if (!convoId || !userId || typeof value !== "boolean") {
      return res.status(400).json({ error: "Invalid request data" });
    }

    const convo = await DmConversation.findById(convoId);
    if (!convo) {
      return res.status(404).json({ error: "Conversation not found" });
    }

    let updated = false;
    convo.members = convo.members.map((member) => {
      if (member.userId.toString() === userId) {
        updated = true;
        return { ...member.toObject(), autoTranslate: value };
      }
      return member;
    });

    if (!updated) {
      return res.status(404).json({ error: "User not in conversation" });
    }

    await convo.save();
    return res.json({ success: true });
  } catch (err) {
    console.error("toggleAutoTranslate error:", err);
    res.status(500).json({ error: "Failed to update autoTranslate" });
  }
};

export const setConvoLanguage = async (req, res) => {
  try {
    const { convoId, userId, lang } = req.body;

    if (!convoId || !userId || !lang) {
      return res.status(400).json({ error: "Invalid request data" });
    }

    const convo = await DmConversation.findById(convoId);
    if (!convo) {
      return res.status(404).json({ error: "Conversation not found" });
    }

    let updated = false;
    convo.members = convo.members.map((member) => {
      if (member.userId.toString() === userId) {
        updated = true;
        return { ...member.toObject(), language: lang };
      }
      return member;
    });

    if (!updated) {
      return res.status(404).json({ error: "User not in conversation" });
    }

    await convo.save();
    return res.json({ success: true });
  } catch (err) {
    console.error("setConvoLanguage error:", err);
    res.status(500).json({ error: "Failed to set language" });
  }
};
