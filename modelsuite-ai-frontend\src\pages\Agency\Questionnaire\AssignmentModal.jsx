import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { CalendarIcon, X, Search, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { Checkbox } from "@/components/ui/checkbox";
import {
  assignTemplate,
  fetchTemplates,
} from "@/redux/features/questionnaire/questionnaireSlice";
import { fetchAgencyModels } from "@/redux/features/models/modelsSlice";
import ButtonLoader from "@/reusable/Loader/ButtonLoader";

const AssignmentModal = ({ isOpen, onClose }) => {
  const dispatch = useDispatch();
  const { templates, assignmentLoading } = useSelector(
    (state) => state.questionnaireReducer
  );
  const { agencyModels, agencyModelsLoading } = useSelector(
    (state) => state.modelsReducer
  );

  const [formData, setFormData] = useState({
    templateId: "",
    modelIds: [],
    dueDate: null,
    priority: "medium",
    instructions: "",
  });

  const [selectedModels, setSelectedModels] = useState([]);
  const [modelSearchTerm, setModelSearchTerm] = useState("");

  useEffect(() => {
    if (isOpen) {
      dispatch(fetchTemplates());
      dispatch(fetchAgencyModels());
    }
  }, [dispatch, isOpen]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleModelToggle = (model) => {
    setSelectedModels((prev) => {
      const exists = prev.find((m) => m._id === model._id);
      if (exists) {
        return prev.filter((m) => m._id !== model._id);
      } else {
        return [...prev, model];
      }
    });

    setFormData((prev) => ({
      ...prev,
      modelIds: selectedModels.map((m) => m._id),
    }));
  };

  const handleSubmit = async () => {
    if (!formData.templateId || selectedModels.length === 0) {
      toast.error("Please select a template and at least one model");
      return;
    }

    const assignmentData = {
      templateId: formData.templateId,
      modelIds: selectedModels.map((m) => m._id),
      dueDate: formData.dueDate,
      priority: formData.priority,
      instructions: formData.instructions,
    };

    try {
      await dispatch(assignTemplate(assignmentData)).unwrap();
      toast.success(
        `Assignment created successfully for ${selectedModels.length} model(s)!`
      );
      handleClose();
    } catch (error) {
      console.error("Failed to assign template:", error);
      toast.error(
        error?.message || "Failed to create assignment. Please try again."
      );
    }
  };

  const handleClose = () => {
    setFormData({
      templateId: "",
      modelIds: [],
      dueDate: null,
      priority: "medium",
      instructions: "",
    });
    setSelectedModels([]);
    setModelSearchTerm("");
    onClose();
  };

  const removeModel = (modelId) => {
    setSelectedModels((prev) => prev.filter((m) => m._id !== modelId));
    setFormData((prev) => ({
      ...prev,
      modelIds: prev.modelIds.filter((id) => id !== modelId),
    }));
  };

  // Filter models based on search term
  const filteredModels =
    agencyModels?.filter((model) => {
      const searchLower = modelSearchTerm.toLowerCase();
      return (
        model.fullName?.toLowerCase().includes(searchLower) ||
        model.username?.toLowerCase().includes(searchLower) ||
        model.email?.toLowerCase().includes(searchLower)
      );
    }) || [];

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="bg-[#18181b] border-[#27272a] text-white max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-white">
            Create New Assignment
          </DialogTitle>
          <DialogDescription className="text-gray-400">
            Assign a questionnaire template to one or more models
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Template Selection */}
          <div className="space-y-2">
            <Label className="text-gray-300">Select Template</Label>
            <Select
              value={formData.templateId}
              onValueChange={(value) =>
                setFormData((prev) => ({ ...prev, templateId: value }))
              }
            >
              <SelectTrigger className="bg-[#27272a] border-[#3f3f46] text-white">
                <SelectValue placeholder="Choose a template" />
              </SelectTrigger>
              <SelectContent className="bg-[#18181b] border-[#27272a]">
                {/* Default Templates */}
                {templates
                  ?.filter((t) => !t.agencyId && t.isActive)
                  .map((template) => (
                    <SelectItem
                      key={template._id}
                      value={template._id}
                      className="text-white hover:bg-[#27272a]"
                    >
                      <div className="flex items-center gap-2">
                        <span className="bg-green-500/20 text-green-400 text-xs px-1.5 py-0.5 rounded">
                          Default
                        </span>
                        {template.title}
                      </div>
                    </SelectItem>
                  ))}

                {/* Custom Templates */}
                {templates
                  ?.filter((t) => t.agencyId && t.isActive)
                  .map((template) => (
                    <SelectItem
                      key={template._id}
                      value={template._id}
                      className="text-white hover:bg-[#27272a]"
                    >
                      <div className="flex items-center gap-2">
                        <span className="bg-[#1a1a1a] text-gray-300 text-xs px-1.5 py-0.5 rounded">
                          Custom
                        </span>
                        {template.title}
                      </div>
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>

            {/* Selected Template Info */}
            {formData.templateId && (
              <div className="mt-2 p-3 bg-[#0f0f0f] border border-[#3f3f46] rounded-lg">
                {(() => {
                  const selectedTemplate = templates?.find(
                    (t) => t._id === formData.templateId
                  );
                  if (!selectedTemplate) return null;

                  const totalQuestions =
                    selectedTemplate.sections?.reduce(
                      (total, section) =>
                        total + (section.questions?.length || 0),
                      0
                    ) || 0;

                  return (
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <h4 className="text-sm font-medium text-white">
                          {selectedTemplate.title}
                        </h4>
                        <div className="flex items-center gap-2 text-xs text-gray-400">
                          <span>
                            {selectedTemplate.sections?.length || 0} sections
                          </span>
                          <span>•</span>
                          <span>{totalQuestions} questions</span>
                        </div>
                      </div>
                      {selectedTemplate.description && (
                        <p className="text-xs text-gray-400">
                          {selectedTemplate.description}
                        </p>
                      )}
                    </div>
                  );
                })()}
              </div>
            )}
          </div>

          {/* Model Selection */}
          <div className="space-y-2">
            <Label className="text-gray-300">Select Models</Label>

            {/* Model Search */}
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search models..."
                value={modelSearchTerm}
                onChange={(e) => setModelSearchTerm(e.target.value)}
                className="pl-8 bg-[#27272a] border-[#3f3f46] text-white"
              />
            </div>

            <div className="border border-[#3f3f46] rounded-md p-2 bg-[#27272a] min-h-[100px]">
              {/* Selected Models */}
              <div className="flex flex-wrap gap-2 mb-2">
                {selectedModels.map((model) => (
                  <div
                    key={model._id}
                    className="flex items-center gap-1 bg-[#3f3f46] text-white px-2 py-1 rounded text-sm"
                  >
                    {model.fullName || model.username}
                    <button
                      onClick={() => removeModel(model._id)}
                      className="ml-1 hover:text-red-300"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </div>
                ))}
              </div>

              {/* Models List */}
              <div className="max-h-32 overflow-y-auto space-y-1">
                {agencyModelsLoading ? (
                  <div className="flex items-center justify-center py-4">
                    <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                    <span className="ml-2 text-gray-400">
                      Loading models...
                    </span>
                  </div>
                ) : filteredModels.length === 0 ? (
                  <div className="text-center py-4 text-gray-400">
                    {modelSearchTerm
                      ? "No models found matching your search"
                      : "No models available"}
                  </div>
                ) : (
                  filteredModels.map((model) => {
                    const isSelected = selectedModels.find(
                      (m) => m._id === model._id
                    );
                    return (
                      <div
                        key={model._id}
                        className={cn(
                          "flex items-center gap-2 p-2 rounded cursor-pointer",
                          isSelected
                            ? "bg-[#3f3f46] text-white"
                            : "hover:bg-[#3f3f46]/50 text-gray-300"
                        )}
                        onClick={() => handleModelToggle(model)}
                      >
                        <Checkbox
                          checked={!!isSelected}
                          onCheckedChange={() => handleModelToggle(model)}
                        />
                        <div className="flex flex-col">
                          <span className="text-sm font-medium">
                            {model.fullName || model.username}
                          </span>
                          <span className="text-xs text-gray-500">
                            {model.email}
                          </span>
                        </div>
                      </div>
                    );
                  })
                )}
              </div>
            </div>
          </div>

          {/* Due Date */}
          <div className="space-y-2">
            <Label className="text-gray-300">Due Date (Optional)</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal bg-[#27272a] border-[#3f3f46] text-white hover:bg-[#1a1a1a] hover:text-white",
                    !formData.dueDate && "text-gray-500"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {formData.dueDate
                    ? formData.dueDate.toLocaleDateString()
                    : "Pick a date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0 bg-[#18181b] border-[#27272a]">
                <Calendar
                  mode="single"
                  selected={formData.dueDate}
                  onSelect={(date) =>
                    setFormData((prev) => ({ ...prev, dueDate: date }))
                  }
                  initialFocus
                  className="text-white"
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* Priority */}
          <div className="space-y-2">
            <Label className="text-gray-300">Priority</Label>
            <Select
              value={formData.priority}
              onValueChange={(value) =>
                setFormData((prev) => ({ ...prev, priority: value }))
              }
            >
              <SelectTrigger className="bg-[#27272a] border-[#3f3f46] text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-[#18181b] border-[#27272a]">
                <SelectItem
                  value="low"
                  className="text-white hover:bg-[#27272a]"
                >
                  Low
                </SelectItem>
                <SelectItem
                  value="medium"
                  className="text-white hover:bg-[#27272a]"
                >
                  Medium
                </SelectItem>
                <SelectItem
                  value="high"
                  className="text-white hover:bg-[#27272a]"
                >
                  High
                </SelectItem>
                <SelectItem
                  value="urgent"
                  className="text-white hover:bg-[#27272a]"
                >
                  Urgent
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Instructions */}
          <div className="space-y-2">
            <Label className="text-gray-300">Instructions (Optional)</Label>
            <Textarea
              name="instructions"
              value={formData.instructions}
              onChange={handleInputChange}
              placeholder="Add any special instructions for this assignment..."
              className="bg-[#27272a] border-[#3f3f46] text-white placeholder:text-gray-500"
              rows={3}
            />
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleClose}
            className="bg-[#27272a] border-[#27272a] text-white hover:border-[#1a1a1a]"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={
              assignmentLoading ||
              !formData.templateId ||
              selectedModels.length === 0
            }
            className="bg-[#1a1a1a] hover:bg-[#0f0f0f] text-white disabled:opacity-50"
          >
            {assignmentLoading ? <ButtonLoader /> : "Create Assignment"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AssignmentModal;
