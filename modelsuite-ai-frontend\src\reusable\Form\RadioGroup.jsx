import * as React from "react";
import { cn } from "@/lib/utils";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Badge } from "@/components/ui/badge";

const RadioGroupField = ({
  control,
  name,
  label,
  description,
  options = [],
  disabled = false,
  required = false,
  layout = "vertical", // "vertical", "horizontal", "grid", "cards"
  gridCols = 2,
  variant = "default", // "default", "button", "card"
  size = "default", // "sm", "default", "lg"
  className,
  ...props
}) => {
  const getLayoutClass = () => {
    switch (layout) {
      case "horizontal":
        return "flex flex-wrap gap-4";
      case "grid":
        return `grid grid-cols-${gridCols} gap-4`;
      case "cards":
        return "grid grid-cols-1 sm:grid-cols-2 gap-3";
      default:
        return "space-y-3";
    }
  };

  const getSizeClass = () => {
    switch (size) {
      case "sm":
        return "text-sm";
      case "lg":
        return "text-lg";
      default:
        return "text-base";
    }
  };

  // Default radio buttons
  const renderDefaultRadio = (field) => (
    <RadioGroup
      value={field.value}
      onValueChange={field.onChange}
      disabled={disabled}
      className={getLayoutClass()}
    >
      {options.map((option) => {
        const isSelected = field.value === option.value;
        const optionId = `${name}-${option.value}`;

        return (
          <div key={option.value} className="flex items-center space-x-2">
            <RadioGroupItem
              value={option.value}
              id={optionId}
              disabled={disabled || option.disabled}
              className="border-gray-600 text-blue-600"
            />
            <FormLabel
              htmlFor={optionId}
              className={cn(
                "font-medium cursor-pointer",
                getSizeClass(),
                option.disabled ? "text-gray-500" : "text-white"
              )}
            >
              <div className="flex items-center gap-2">
                {option.icon && (
                  <span className="flex-shrink-0 text-gray-300">
                    {option.icon}
                  </span>
                )}
                <span>{option.label}</span>
                {option.badge && (
                  <Badge
                    variant="outline"
                    className="text-xs bg-gray-700 text-gray-200 border-gray-600"
                  >
                    {option.badge}
                  </Badge>
                )}
              </div>
              {option.description && (
                <div className="text-xs text-gray-400 font-normal mt-1">
                  {option.description}
                </div>
              )}
            </FormLabel>
          </div>
        );
      })}
    </RadioGroup>
  );

  // Button style radio group
  const renderButtonRadio = (field) => (
    <div className={getLayoutClass()}>
      {options.map((option) => {
        const isSelected = field.value === option.value;
        const isDisabled = disabled || option.disabled;

        return (
          <button
            key={option.value}
            type="button"
            onClick={() => !isDisabled && field.onChange(option.value)}
            disabled={isDisabled}
            className={cn(
              "px-4 py-2 rounded-md border transition-all duration-200",
              "flex items-center justify-center gap-2 text-sm font-medium",
              isSelected
                ? "bg-gray-700 border-gray-600 text-white"
                : "bg-gray-800/50 border-gray-700 text-gray-300 hover:bg-gray-700 hover:border-gray-600",
              isDisabled && "opacity-50 cursor-not-allowed",
              getSizeClass()
            )}
          >
            {option.icon && (
              <span className="flex-shrink-0 text-gray-300">{option.icon}</span>
            )}
            <span>{option.label}</span>
            {option.badge && (
              <Badge
                variant="secondary"
                className="text-xs bg-gray-600 text-gray-200 border-gray-500"
              >
                {option.badge}
              </Badge>
            )}
          </button>
        );
      })}
    </div>
  );

  // Card style radio group
  const renderCardRadio = (field) => (
    <div className={getLayoutClass()}>
      {options.map((option) => {
        const isSelected = field.value === option.value;
        const isDisabled = disabled || option.disabled;

        return (
          <div
            key={option.value}
            onClick={() => !isDisabled && field.onChange(option.value)}
            className={cn(
              "p-4 rounded-lg border cursor-pointer transition-all duration-200",
              "hover:border-gray-600 hover:bg-gray-800/30",
              isSelected
                ? "border-gray-600 bg-gray-700/30"
                : "border-gray-700 bg-gray-800/20",
              isDisabled && "opacity-50 cursor-not-allowed"
            )}
          >
            <div className="flex items-start gap-3">
              <div className="pt-1">
                <div
                  className={cn(
                    "w-4 h-4 rounded-full border-2 flex items-center justify-center",
                    isSelected
                      ? "border-gray-500 bg-gray-600"
                      : "border-gray-600"
                  )}
                >
                  {isSelected && (
                    <div className="w-2 h-2 rounded-full bg-white" />
                  )}
                </div>
              </div>

              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  {option.icon && (
                    <span className="flex-shrink-0 text-gray-300">
                      {option.icon}
                    </span>
                  )}
                  <span
                    className={cn(
                      "font-medium",
                      getSizeClass(),
                      isDisabled ? "text-gray-500" : "text-white"
                    )}
                  >
                    {option.label}
                  </span>
                  {option.badge && (
                    <Badge
                      variant="outline"
                      className="text-xs bg-gray-700 text-gray-200 border-gray-600"
                    >
                      {option.badge}
                    </Badge>
                  )}
                </div>

                {option.description && (
                  <p className="text-xs text-gray-400">{option.description}</p>
                )}

                {option.features && (
                  <ul className="text-xs text-gray-400 mt-2 space-y-1">
                    {option.features.map((feature, index) => (
                      <li key={index} className="flex items-center gap-1">
                        <span className="w-1 h-1 bg-gray-400 rounded-full" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                )}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );

  const renderRadioGroup = (field) => {
    switch (variant) {
      case "button":
        return renderButtonRadio(field);
      case "card":
        return renderCardRadio(field);
      default:
        return renderDefaultRadio(field);
    }
  };

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className={cn("space-y-3", className)}>
          {label && (
            <FormLabel className="text-sm font-medium text-white">
              {label}
              {required && <span className="text-red-400 ml-1">*</span>}
            </FormLabel>
          )}

          <FormControl>{renderRadioGroup(field)}</FormControl>

          {description && (
            <FormDescription className="text-xs text-gray-400">
              {description}
            </FormDescription>
          )}

          <FormMessage className="text-xs text-red-400" />
        </FormItem>
      )}
      {...props}
    />
  );
};

export default RadioGroupField;
