import nodemailer from "nodemailer";
import ModelCategoryAssignment from "../../models/contentupload/ModelCategoryAssignment.js";
import { ApiError } from "../../utils/ApiError.js";

/**
 * Notification Service - Handles basic notification system,
 * email/SMS reminders, notification templates, and notification history
 */
class NotificationService {
  /**
   * Initialize email transporter based on agency configuration
   * @param {string} agencyId - Agency ID
   * @returns {Promise<Object>} Email transporter
   */
  static async getEmailTransporter(agencyId) {
    try {
      // Use environment variables or default SMTP configuration
      // Agency-specific configuration has been removed
      return nodemailer.createTransporter({
        host: process.env.SMTP_HOST || "localhost",
        port: process.env.SMTP_PORT || 587,
        secure: process.env.SMTP_SECURE === "true",
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS,
        },
      });
    } catch (error) {
      throw new ApiError(
        500,
        `Failed to initialize email transporter: ${error.message}`,
      );
    }
  }

  /**
   * Send reminder notification to model
   * @param {string} assignmentId - Assignment ID
   * @param {string} notificationType - Type of notification ('email', 'sms', 'both')
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Notification result
   */
  static async sendReminderNotification(
    assignmentId,
    notificationType = "email",
    options = {},
  ) {
    try {
      const assignment = await ModelCategoryAssignment.findById(assignmentId)
        .populate("modelId", "firstName lastName email phone")
        .populate("categoryId", "label platform instructionTooltip")
        .populate("agencyId", "name email");

      if (!assignment) {
        throw new ApiError(404, "Assignment not found");
      }

      const model = assignment.modelId;
      const category = assignment.categoryId;
      const agency = assignment.agencyId;

      // Calculate overdue days
      const now = new Date();
      const daysPastDue =
        assignment.nextDueDate < now
          ? Math.floor((now - assignment.nextDueDate) / (24 * 60 * 60 * 1000))
          : 0;

      const isOverdue = daysPastDue > 0;
      const daysUntilDue = isOverdue
        ? 0
        : Math.ceil((assignment.nextDueDate - now) / (24 * 60 * 60 * 1000));

      const templateData = {
        modelName: `${model.firstName} ${model.lastName}`,
        categoryLabel: category.label,
        platform: category.platform,
        agencyName: agency.name,
        dueDate: assignment.nextDueDate,
        isOverdue,
        daysPastDue,
        daysUntilDue,
        instructionTooltip: category.instructionTooltip,
        priority: assignment.priority,
        uploadUrl:
          options.uploadUrl ||
          `${process.env.FRONTEND_URL}/upload/${category._id}`,
      };

      const results = {};

      // Send email notification
      if (notificationType === "email" || notificationType === "both") {
        if (model.email) {
          results.email = await this.sendEmailReminder(
            assignment.agencyId,
            model.email,
            templateData,
            options,
          );
        } else {
          results.email = {
            success: false,
            error: "No email address available",
          };
        }
      }

      // Send SMS notification (placeholder for future implementation)
      if (notificationType === "sms" || notificationType === "both") {
        if (model.phone) {
          results.sms = await this.sendSMSReminder(
            assignment.agencyId,
            model.phone,
            templateData,
            options,
          );
        } else {
          results.sms = { success: false, error: "No phone number available" };
        }
      }

      // Log notification history
      await this.logNotificationHistory(
        assignmentId,
        notificationType,
        results,
        templateData,
      );

      return {
        assignmentId,
        notificationType,
        results,
        timestamp: new Date(),
      };
    } catch (error) {
      if (error instanceof ApiError) throw error;
      throw new ApiError(
        500,
        `Failed to send reminder notification: ${error.message}`,
      );
    }
  }

  /**
   * Send email reminder
   * @param {string} agencyId - Agency ID
   * @param {string} email - Recipient email
   * @param {Object} templateData - Template data
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Email result
   */
  static async sendEmailReminder(agencyId, email, templateData, options = {}) {
    try {
      const transporter = await this.getEmailTransporter(agencyId);

      // Get email template with default configuration
      const template = this.getEmailTemplate(
        templateData,
        null,
        options.templateType,
      );

      const mailOptions = {
        from: process.env.FROM_EMAIL || "<EMAIL>",
        to: email,
        subject: template.subject,
        html: template.html,
        text: template.text,
      };

      const result = await transporter.sendMail(mailOptions);

      return {
        success: true,
        messageId: result.messageId,
        recipient: email,
        timestamp: new Date(),
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        recipient: email,
        timestamp: new Date(),
      };
    }
  }

  /**
   * Send SMS reminder (placeholder for future implementation)
   * @param {string} agencyId - Agency ID
   * @param {string} phone - Recipient phone
   * @param {Object} templateData - Template data
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} SMS result
   */
  static async sendSMSReminder(agencyId, phone, templateData, options = {}) {
    try {
      // Placeholder for SMS implementation (Twilio, AWS SNS, etc.)
      // For now, return a mock success response

      const smsText = this.getSMSTemplate(templateData, options.templateType);

      // TODO: Implement actual SMS sending logic
      console.log(`SMS Reminder to ${phone}: ${smsText}`);

      return {
        success: true,
        messageId: `sms_${Date.now()}`,
        recipient: phone,
        message: smsText,
        timestamp: new Date(),
        note: "SMS sending not implemented yet",
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        recipient: phone,
        timestamp: new Date(),
      };
    }
  }

  /**
   * Get email template based on reminder type
   * @param {Object} data - Template data
   * @param {Object} agencyConfig - Agency configuration
   * @param {string} templateType - Template type
   * @returns {Object} Email template
   */
  static getEmailTemplate(data, agencyConfig, templateType = "standard") {
    const {
      modelName,
      categoryLabel,
      platform,
      agencyName,
      isOverdue,
      daysPastDue,
      daysUntilDue,
      uploadUrl,
    } = data;

    // Agency configuration has been removed, use default templates only
    // Default templates
    if (isOverdue) {
      return {
        subject: `OVERDUE: Content Upload Required - ${categoryLabel} (${platform})`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #dc3545;">⚠️ Overdue Content Upload</h2>
            <p>Hi ${modelName},</p>
            <p><strong>Your content upload for ${categoryLabel} (${platform}) is ${daysPastDue} day(s) overdue.</strong></p>
            <p>Please upload your content as soon as possible to stay compliant with your agreement.</p>
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <h3>Upload Details:</h3>
              <ul>
                <li><strong>Category:</strong> ${categoryLabel}</li>
                <li><strong>Platform:</strong> ${platform}</li>
                <li><strong>Days Overdue:</strong> ${daysPastDue}</li>
                <li><strong>Priority:</strong> ${data.priority}</li>
              </ul>
            </div>
            <a href="${uploadUrl}" style="background-color: #dc3545; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 20px 0;">Upload Now</a>
            <p>If you have any questions, please contact ${agencyName}.</p>
            <hr style="margin: 30px 0;">
            <p style="font-size: 12px; color: #6c757d;">This is an automated reminder from ${agencyName}.</p>
          </div>
        `,
        text: `OVERDUE: Content Upload Required\n\nHi ${modelName},\n\nYour content upload for ${categoryLabel} (${platform}) is ${daysPastDue} day(s) overdue.\n\nPlease upload your content as soon as possible: ${uploadUrl}\n\nContact ${agencyName} if you have questions.`,
      };
    } else {
      return {
        subject: `Reminder: Content Upload Due Soon - ${categoryLabel} (${platform})`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #007bff;">📅 Content Upload Reminder</h2>
            <p>Hi ${modelName},</p>
            <p>This is a friendly reminder that your content upload for ${categoryLabel} (${platform}) is due in ${daysUntilDue} day(s).</p>
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <h3>Upload Details:</h3>
              <ul>
                <li><strong>Category:</strong> ${categoryLabel}</li>
                <li><strong>Platform:</strong> ${platform}</li>
                <li><strong>Due Date:</strong> ${data.dueDate.toLocaleDateString()}</li>
                <li><strong>Days Until Due:</strong> ${daysUntilDue}</li>
              </ul>
            </div>
            ${data.instructionTooltip ? `<div style="background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;"><strong>Instructions:</strong> ${data.instructionTooltip}</div>` : ""}
            <a href="${uploadUrl}" style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 20px 0;">Upload Content</a>
            <p>Thank you for staying on top of your content schedule!</p>
            <p>Best regards,<br>${agencyName}</p>
            <hr style="margin: 30px 0;">
            <p style="font-size: 12px; color: #6c757d;">This is an automated reminder from ${agencyName}.</p>
          </div>
        `,
        text: `Content Upload Reminder\n\nHi ${modelName},\n\nYour content upload for ${categoryLabel} (${platform}) is due in ${daysUntilDue} day(s).\n\nDue Date: ${data.dueDate.toLocaleDateString()}\n\nUpload here: ${uploadUrl}\n\nBest regards,\n${agencyName}`,
      };
    }
  }

  /**
   * Get SMS template
   * @param {Object} data - Template data
   * @param {string} templateType - Template type
   * @returns {string} SMS text
   */
  static getSMSTemplate(data, templateType = "standard") {
    const {
      modelName,
      categoryLabel,
      platform,
      isOverdue,
      daysPastDue,
      daysUntilDue,
      agencyName,
    } = data;

    if (isOverdue) {
      return `OVERDUE: Hi ${modelName}, your ${categoryLabel} (${platform}) content is ${daysPastDue} day(s) overdue. Please upload ASAP. - ${agencyName}`;
    } else {
      return `Reminder: Hi ${modelName}, your ${categoryLabel} (${platform}) content is due in ${daysUntilDue} day(s). - ${agencyName}`;
    }
  }

  /**
   * Replace template variables with actual data
   * @param {string} template - Template string
   * @param {Object} data - Data to replace
   * @returns {string} Processed template
   */
  static replaceTemplateVariables(template, data) {
    let processed = template;

    Object.keys(data).forEach((key) => {
      const regex = new RegExp(`{{${key}}}`, "g");
      processed = processed.replace(regex, data[key]);
    });

    return processed;
  }

  /**
   * Send bulk reminders for multiple assignments
   * @param {Array} assignmentIds - Array of assignment IDs
   * @param {string} notificationType - Type of notification
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Bulk notification results
   */
  static async sendBulkReminders(
    assignmentIds,
    notificationType = "email",
    options = {},
  ) {
    const results = {
      successful: [],
      failed: [],
      total: assignmentIds.length,
    };

    for (const assignmentId of assignmentIds) {
      try {
        const result = await this.sendReminderNotification(
          assignmentId,
          notificationType,
          options,
        );
        results.successful.push(result);
      } catch (error) {
        results.failed.push({
          assignmentId,
          error: error.message,
        });
      }
    }

    return results;
  }

  /**
   * Log notification history
   * @param {string} assignmentId - Assignment ID
   * @param {string} notificationType - Type of notification
   * @param {Object} results - Notification results
   * @param {Object} templateData - Template data used
   * @returns {Promise<void>}
   */
  static async logNotificationHistory(
    assignmentId,
    notificationType,
    results,
    templateData,
  ) {
    try {
      // For now, we'll store notification history in the assignment document
      // In a production system, you might want a separate NotificationHistory model

      const assignment = await ModelCategoryAssignment.findById(assignmentId);
      if (!assignment) return;

      if (!assignment.notificationHistory) {
        assignment.notificationHistory = [];
      }

      assignment.notificationHistory.push({
        timestamp: new Date(),
        notificationType,
        results,
        templateData: {
          isOverdue: templateData.isOverdue,
          daysPastDue: templateData.daysPastDue,
          daysUntilDue: templateData.daysUntilDue,
        },
      });

      // Keep only last 50 notification records
      if (assignment.notificationHistory.length > 50) {
        assignment.notificationHistory =
          assignment.notificationHistory.slice(-50);
      }

      await assignment.save();
    } catch (error) {
      console.error("Failed to log notification history:", error);
    }
  }

  /**
   * Get notification history for an assignment
   * @param {string} assignmentId - Assignment ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Notification history
   */
  static async getNotificationHistory(assignmentId, options = {}) {
    try {
      const { limit = 20 } = options;

      const assignment = await ModelCategoryAssignment.findById(
        assignmentId,
      ).select("notificationHistory");

      if (!assignment || !assignment.notificationHistory) {
        return [];
      }

      return assignment.notificationHistory
        .sort((a, b) => b.timestamp - a.timestamp)
        .slice(0, limit);
    } catch (error) {
      throw new ApiError(
        500,
        `Failed to get notification history: ${error.message}`,
      );
    }
  }

  /**
   * Test notification configuration
   * @param {string} agencyId - Agency ID
   * @param {string} testEmail - Test email address
   * @returns {Promise<Object>} Test result
   */
  static async testNotificationConfiguration(agencyId, testEmail) {
    try {
      const testData = {
        modelName: "Test Model",
        categoryLabel: "Test Category",
        platform: "Test Platform",
        agencyName: "Test Agency",
        isOverdue: false,
        daysUntilDue: 3,
        uploadUrl: "https://example.com/upload",
      };

      const result = await this.sendEmailReminder(
        agencyId,
        testEmail,
        testData,
        {
          templateType: "test",
        },
      );

      return {
        success: result.success,
        message: result.success
          ? "Test email sent successfully"
          : "Failed to send test email",
        details: result,
      };
    } catch (error) {
      return {
        success: false,
        message: "Failed to test notification configuration",
        error: error.message,
      };
    }
  }
}

export default NotificationService;
