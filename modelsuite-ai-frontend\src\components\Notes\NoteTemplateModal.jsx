import React, { useState, useMemo } from "react";
import {
  Book<PERSON>pen,
  Search,
  Star,
  Clock,
  User,
  FileText,
  MessageSquare,
  Calendar,
  DollarSign,
  AlertCircle,
  CheckCircle,
  Plus,
} from "lucide-react";

// UI Components
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

// Reusable Components
import { SearchBar, EmptyState } from "@/reusable";

// Predefined note templates
const NOTE_TEMPLATES = [
  {
    id: "performance-review",
    title: "Performance Review",
    category: "performance",
    icon: Star,
    description: "Template for documenting model performance reviews",
    tags: ["performance", "review", "evaluation"],
    content: `# Performance Review - [Model Name]

## Review Period
**From:** [Start Date]  
**To:** [End Date]

## Overall Performance
**Rating:** [1-5 stars]  
**Summary:** [Brief overall assessment]

## Key Achievements
- [Achievement 1]
- [Achievement 2]
- [Achievement 3]

## Areas of Excellence
- **[Skill/Area]:** [Details]
- **[Skill/Area]:** [Details]

## Areas for Improvement
- **[Area]:** [Specific feedback and suggestions]
- **[Area]:** [Specific feedback and suggestions]

## Goals for Next Period
1. [Goal 1]
2. [Goal 2]
3. [Goal 3]

## Additional Notes
[Any additional comments or observations]

---
*Review completed by: [Reviewer Name]*  
*Date: [Review Date]*`,
    priority: "medium",
    visibility: "internal",
  },
  {
    id: "meeting-notes",
    title: "Meeting Notes",
    category: "meeting",
    icon: Calendar,
    description: "Template for recording meeting notes and action items",
    tags: ["meeting", "notes", "action-items"],
    content: `# Meeting Notes - [Meeting Title]

## Meeting Details
**Date:** [Date]  
**Time:** [Start Time] - [End Time]  
**Location/Platform:** [Location or video platform]  
**Attendees:** [List of attendees]

## Agenda
1. [Agenda item 1]
2. [Agenda item 2]
3. [Agenda item 3]

## Discussion Points

### [Topic 1]
- [Key point 1]
- [Key point 2]
- [Decision/Outcome]

### [Topic 2]
- [Key point 1]
- [Key point 2]
- [Decision/Outcome]

## Action Items
- [ ] [Action item 1] - **Assigned to:** [Name] - **Due:** [Date]
- [ ] [Action item 2] - **Assigned to:** [Name] - **Due:** [Date]
- [ ] [Action item 3] - **Assigned to:** [Name] - **Due:** [Date]

## Next Steps
[What happens next, follow-up meetings, etc.]

## Notes
[Any additional notes or observations]`,
    priority: "medium",
    visibility: "internal",
  },
  {
    id: "feedback-session",
    title: "Feedback Session",
    category: "feedback",
    icon: MessageSquare,
    description: "Template for constructive feedback sessions",
    tags: ["feedback", "coaching", "development"],
    content: `# Feedback Session - [Model Name]

## Session Details
**Date:** [Date]  
**Duration:** [Duration]  
**Focus Area:** [Main focus of the session]

## Positive Feedback
### What's Working Well
- [Specific behavior/skill 1]: [Details and examples]
- [Specific behavior/skill 2]: [Details and examples]
- [Specific behavior/skill 3]: [Details and examples]

### Strengths to Leverage
- [Strength 1]: [How to leverage this strength]
- [Strength 2]: [How to leverage this strength]

## Areas for Development
### [Area 1]
**Current State:** [Where they are now]  
**Desired State:** [Where they should be]  
**Action Steps:**
1. [Specific action 1]
2. [Specific action 2]
3. [Specific action 3]

### [Area 2]
**Current State:** [Where they are now]  
**Desired State:** [Where they should be]  
**Action Steps:**
1. [Specific action 1]
2. [Specific action 2]

## Development Plan
**Short-term goals (1-3 months):**
- [Goal 1]
- [Goal 2]

**Long-term goals (3-6 months):**
- [Goal 1]
- [Goal 2]

## Resources & Support
- [Resource/training 1]
- [Resource/training 2]
- [Support person/mentor]

## Follow-up
**Next check-in:** [Date]  
**Review date:** [Date]`,
    priority: "medium",
    visibility: "shared_with_model",
  },
  {
    id: "contract-notes",
    title: "Contract Discussion",
    category: "contract",
    icon: FileText,
    description: "Template for contract-related discussions and notes",
    tags: ["contract", "legal", "business"],
    content: `# Contract Discussion - [Model Name]

## Contract Details
**Contract Type:** [Type of contract]  
**Start Date:** [Date]  
**End Date:** [Date]  
**Value:** [Contract value]

## Key Terms Discussed
- **Commission Rate:** [Percentage]
- **Payment Schedule:** [Details]
- **Exclusivity:** [Yes/No and details]
- **Usage Rights:** [Details]
- **Territory:** [Geographic limitations]

## Model Concerns/Questions
1. [Concern 1] - **Resolution:** [How addressed]
2. [Concern 2] - **Resolution:** [How addressed]
3. [Concern 3] - **Resolution:** [How addressed]

## Agency Notes
- [Internal note 1]
- [Internal note 2]
- [Internal note 3]

## Action Items
- [ ] [Action 1] - **Due:** [Date]
- [ ] [Action 2] - **Due:** [Date]
- [ ] [Action 3] - **Due:** [Date]

## Next Steps
[What happens next in the contract process]

## Attachments
- [ ] Contract draft
- [ ] Rate card
- [ ] Portfolio requirements
- [ ] Other: [Specify]`,
    priority: "high",
    visibility: "internal",
  },
  {
    id: "payment-tracking",
    title: "Payment Tracking",
    category: "payment",
    icon: DollarSign,
    description: "Template for tracking payments and financial matters",
    tags: ["payment", "finance", "tracking"],
    content: `# Payment Tracking - [Model Name]

## Payment Summary
**Period:** [Date range]  
**Total Earnings:** $[Amount]  
**Commission Rate:** [Percentage]  
**Net Payment:** $[Amount after commission]

## Work Completed
| Date | Project/Client | Type | Rate | Hours/Sessions | Amount |
|------|---------------|------|------|----------------|--------|
| [Date] | [Client/Project] | [Type] | $[Rate] | [Hours] | $[Amount] |
| [Date] | [Client/Project] | [Type] | $[Rate] | [Hours] | $[Amount] |
| **TOTAL** | | | | | **$[Total]** |

## Payment Details
**Payment Method:** [Bank transfer, PayPal, etc.]  
**Payment Date:** [Date paid]  
**Transaction ID:** [Reference number]  
**Status:** [Pending/Paid/Overdue]

## Deductions
- **Agency Commission:** $[Amount] ([Percentage]%)
- **Tax Withholding:** $[Amount] ([Percentage]%)
- **Other Fees:** $[Amount] ([Description])

## Notes
- [Note about payment timing]
- [Note about any issues]
- [Note about future payments]

## Next Payment
**Expected Date:** [Date]  
**Estimated Amount:** $[Amount]`,
    priority: "medium",
    visibility: "shared_with_model",
  },
  {
    id: "incident-report",
    title: "Incident Report",
    category: "behavior",
    icon: AlertCircle,
    description: "Template for documenting incidents or behavioral issues",
    tags: ["incident", "behavior", "documentation"],
    content: `# Incident Report - [Model Name]

## Incident Details
**Date:** [Date]  
**Time:** [Time]  
**Location/Platform:** [Where it occurred]  
**Reported By:** [Who reported it]

## Incident Description
**What Happened:**
[Detailed description of the incident]

**People Involved:**
- [Person 1] - [Role]
- [Person 2] - [Role]
- [Person 3] - [Role]

## Severity Level
- [ ] Minor - No significant impact
- [ ] Moderate - Some impact on work/relationships
- [ ] Major - Significant impact requiring immediate action
- [ ] Critical - Severe impact requiring urgent intervention

## Impact Assessment
**Client Impact:** [How it affected client/project]  
**Team Impact:** [How it affected team dynamics]  
**Business Impact:** [Financial or reputational impact]

## Immediate Actions Taken
1. [Action 1]
2. [Action 2]
3. [Action 3]

## Investigation Findings
[Results of any investigation]

## Resolution Plan
**Short-term actions:**
- [Action 1] - **By:** [Date]
- [Action 2] - **By:** [Date]

**Long-term actions:**
- [Action 1] - **By:** [Date]
- [Action 2] - **By:** [Date]

## Prevention Measures
[Steps to prevent similar incidents]

## Follow-up Required
**Next Review:** [Date]  
**Follow-up Actions:** [What needs to be monitored]

---
**Report Filed By:** [Name]  
**Date Filed:** [Date]  
**Report Status:** [Open/Resolved/Under Review]`,
    priority: "high",
    visibility: "internal",
  },
  {
    id: "general-note",
    title: "General Note",
    category: "general",
    icon: FileText,
    description: "Simple template for general notes and observations",
    tags: ["general", "notes"],
    content: `# [Note Title]

## Summary
[Brief summary of the note]

## Details
[Main content of your note]

## Key Points
- [Point 1]
- [Point 2]
- [Point 3]

## Action Items
- [ ] [Action 1]
- [ ] [Action 2]
- [ ] [Action 3]

## Tags
#[tag1] #[tag2] #[tag3]

---
*Created on [Date]*`,
    priority: "medium",
    visibility: "internal",
  },
];

const NoteTemplateModal = ({ open, onClose, onSelect }) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");

  // Filter templates based on search and category
  const filteredTemplates = useMemo(() => {
    return NOTE_TEMPLATES.filter((template) => {
      const matchesSearch =
        searchQuery === "" ||
        template.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.description
          .toLowerCase()
          .includes(searchQuery.toLowerCase()) ||
        template.tags.some((tag) =>
          tag.toLowerCase().includes(searchQuery.toLowerCase())
        );

      const matchesCategory =
        selectedCategory === "all" || template.category === selectedCategory;

      return matchesSearch && matchesCategory;
    });
  }, [searchQuery, selectedCategory]);

  // Get unique categories
  const categories = useMemo(() => {
    const cats = [...new Set(NOTE_TEMPLATES.map((t) => t.category))];
    return [{ value: "all", label: "All Categories" }].concat(
      cats.map((cat) => ({
        value: cat,
        label: cat.charAt(0).toUpperCase() + cat.slice(1),
      }))
    );
  }, []);

  const handleSelectTemplate = (template) => {
    onSelect({
      title: template.title,
      content: template.content,
      category: template.category,
      priority: template.priority,
      visibility: template.visibility,
      tags: [...template.tags],
    });
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="!w-[50vw] !max-w-[50vw] max-h-[90vh] bg-[#18181b] border-[#27272a] text-white">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Note Templates
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Search and Filter */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <SearchBar
                placeholder="Search templates..."
                value={searchQuery}
                onChange={setSearchQuery}
                className="bg-[#1a1a1a] border-[#27272a] text-white placeholder:text-gray-400"
              />
            </div>

            <Tabs
              value={selectedCategory}
              onValueChange={setSelectedCategory}
              className="w-auto"
            >
              <TabsList className="bg-[#1a1a1a] border border-[#27272a]">
                {categories.map((category) => (
                  <TabsTrigger
                    key={category.value}
                    value={category.value}
                    className="text-xs text-gray-300 data-[state=active]:text-white"
                  >
                    {category.label}
                  </TabsTrigger>
                ))}
              </TabsList>
            </Tabs>
          </div>

          {/* Templates Grid */}
          <div className="max-h-[60vh] overflow-y-auto">
            {filteredTemplates.length === 0 ? (
              <EmptyState
                icon={BookOpen}
                title="No templates found"
                description="Try adjusting your search or filter criteria"
              />
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {filteredTemplates.map((template) => (
                  <TemplateCard
                    key={template.id}
                    template={template}
                    onSelect={() => handleSelectTemplate(template)}
                  />
                ))}
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-2 pt-4 border-t border-[#27272a]">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Template Card Component
const TemplateCard = ({ template, onSelect }) => {
  const IconComponent = template.icon;

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "critical":
        return "bg-red-500";
      case "high":
        return "bg-orange-500";
      case "medium":
        return "bg-yellow-500";
      case "low":
        return "bg-green-500";
      default:
        return "bg-gray-500";
    }
  };

  const getVisibilityIcon = (visibility) => {
    switch (visibility) {
      case "shared_with_model":
        return User;
      case "neutral":
        return CheckCircle;
      default:
        return FileText;
    }
  };

  const VisibilityIcon = getVisibilityIcon(template.visibility);

  return (
    <Card className="bg-[#1a1a1a] border-[#27272a] hover:border-[#3f3f46] transition-all cursor-pointer group">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <IconComponent className="h-5 w-5 text-blue-400" />
            <span className="text-lg text-white font-semibold">
              {template.title}
            </span>
          </div>
          <div className="flex items-center gap-1 ml-auto">
            <div
              className={`w-2 h-2 rounded-full ${getPriorityColor(
                template.priority
              )}`}
            />
            <VisibilityIcon className="h-3 w-3 text-gray-300" />
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent className="pt-0">
        <p className="text-gray-300 text-sm mb-3 line-clamp-2">
          {template.description}
        </p>

        <div className="flex flex-wrap gap-2 mb-4">
          {template.tags.map((tag) => (
            <Badge
              key={tag}
              variant="secondary"
              className="text-xs bg-[#0f1720] text-gray-100 border border-gray-700"
            >
              {tag}
            </Badge>
          ))}
        </div>

        <div className="flex items-center justify-between">
          <Badge
            variant="outline"
            className="text-xs text-gray-300 border border-gray-600 bg-transparent"
          >
            {template.category}
          </Badge>

          <Button
            size="sm"
            onClick={onSelect}
            className="transition-opacity bg-white/5 text-white hover:bg-white/10 border border-gray-700"
          >
            <Plus className="h-3 w-3 mr-1" />
            Use Template
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default NoteTemplateModal;
