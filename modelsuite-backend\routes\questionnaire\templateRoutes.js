import express from "express";
import {
  createTemplate,
  getTemplates,
  getTemplateById,
  updateTemplate,
  deleteTemplate,
} from "../../controllers/questionnaire/templateController.js";
import { verifyToken } from "../../middlewares/authMiddleware.js";
import checkPermission from "../../middlewares/permissionCheck.js";

const router = express.Router();

// Only logged in users can do these
router.post(
  "/",
  verifyToken,
  checkPermission("templates.create"),
  createTemplate,
);
router.get("/", verifyToken, checkPermission("templates.view"), getTemplates);
router.get(
  "/:id",
  verifyToken,
  checkPermission("templates.view"),
  getTemplateById,
);
router.put(
  "/:id",
  verifyToken,
  checkPermission("templates.edit"),
  updateTemplate,
);
router.delete(
  "/:id",
  verifyToken,
  checkPermission("templates.delete"),
  deleteTemplate,
);

export default router;
