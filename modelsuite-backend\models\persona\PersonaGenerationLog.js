import mongoose from "mongoose";

/**
 * PersonaGenerationLog Schema - Tracks persona generation metrics and prompts
 * Used for AI optimization, debugging, and performance analysis
 */
const personaGenerationLogSchema = new mongoose.Schema(
  {
    // Reference to the generated persona
    personaId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "PersonaProfile",
      required: true,
    },

    // Reference to the model
    modelId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ModelUser",
      required: true,
    },

    // Agency that triggered generation
    agencyId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      required: true,
    },

    // User who triggered generation
    triggeredBy: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      refPath: "triggeredByModel",
    },

    triggeredByModel: {
      type: String,
      required: true,
      enum: ["Agency", "Employee"],
    },

    // Generation details
    generationType: {
      type: String,
      enum: ["new", "regeneration", "update"],
      required: true,
    },

    aiEngine: {
      type: String,
      enum: ["gpt-4", "gemini-vision"],
      required: true,
    },

    // Prompt and response data
    promptData: {
      finalPrompt: {
        type: String,
        required: true,
      },
      promptVersion: {
        type: String,
        default: "1.0",
      },
      inputDataSources: {
        profileData: {
          type: mongoose.Schema.Types.Mixed,
          default: {},
        },
        questionnaireData: {
          type: mongoose.Schema.Types.Mixed,
          default: {},
        },
        captionAnalysis: {
          type: mongoose.Schema.Types.Mixed,
          default: {},
        },
        userTags: {
          type: [String],
          default: [],
        },
      },
    },

    // Performance metrics
    performance: {
      startTime: {
        type: Date,
        required: true,
      },
      endTime: {
        type: Date,
        required: true,
      },
      totalProcessingTime: {
        type: Number, // in milliseconds
        required: true,
      },
      aiResponseTime: {
        type: Number, // AI-specific response time in milliseconds
        required: true,
      },
      dataGatheringTime: {
        type: Number, // Time spent gathering input data
        default: 0,
      },
    },

    // Response quality metrics
    responseMetrics: {
      tokenCount: {
        type: Number,
        default: 0,
      },
      wordCount: {
        type: Number,
        default: 0,
      },
      completenessScore: {
        type: Number, // 0-100 percentage
        default: 0,
      },
      structureValidation: {
        type: Boolean,
        default: false,
      },
      fieldsPopulated: {
        type: Number, // Count of populated schema fields
        default: 0,
      },
    },

    // Input data quality assessment
    inputQuality: {
      profileDataAvailable: {
        type: Boolean,
        default: false,
      },
      questionnaireDataAvailable: {
        type: Boolean,
        default: false,
      },
      captionHistoryLength: {
        type: Number,
        default: 0,
      },
      userTagsCount: {
        type: Number,
        default: 0,
      },
      dataRichness: {
        type: String,
        enum: ["low", "medium", "high"],
        default: "medium",
      },
    },

    // Error tracking
    errors: [
      {
        errorType: {
          type: String,
          enum: ["ai_error", "data_error", "validation_error", "timeout_error"],
        },
        errorMessage: {
          type: String,
        },
        errorTime: {
          type: Date,
          default: Date.now,
        },
        resolved: {
          type: Boolean,
          default: false,
        },
      },
    ],

    // Success indicators
    success: {
      type: Boolean,
      required: true,
    },

    retryCount: {
      type: Number,
      default: 0,
    },

    // Context information
    context: {
      userAgent: String,
      ipAddress: String,
      sessionId: String,
    },

    // Persona field influence tracking (which fields affected generation)
    fieldInfluence: {
      toneInfluence: {
        type: Number, // 0-100 percentage
        default: 0,
      },
      interestsInfluence: {
        type: Number,
        default: 0,
      },
      goalsInfluence: {
        type: Number,
        default: 0,
      },
      demographicsInfluence: {
        type: Number,
        default: 0,
      },
      behavioralInfluence: {
        type: Number,
        default: 0,
      },
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  },
);

// Indexes for performance and analytics
personaGenerationLogSchema.index({ personaId: 1 });
personaGenerationLogSchema.index({ modelId: 1, createdAt: -1 });
personaGenerationLogSchema.index({ agencyId: 1, createdAt: -1 });
personaGenerationLogSchema.index({ aiEngine: 1, success: 1 });
personaGenerationLogSchema.index({ "performance.totalProcessingTime": 1 });
personaGenerationLogSchema.index({ "responseMetrics.completenessScore": -1 });
personaGenerationLogSchema.index({ success: 1, createdAt: -1 });

// Virtual for performance rating
personaGenerationLogSchema.virtual("performanceRating").get(function () {
  const time = this.performance.totalProcessingTime;
  if (time < 5000) return "excellent"; // < 5 seconds
  if (time < 10000) return "good"; // < 10 seconds
  if (time < 20000) return "average"; // < 20 seconds
  return "poor"; // > 20 seconds
});

// Static method for performance analytics
personaGenerationLogSchema.statics.getPerformanceAnalytics = function (
  dateRange = 30,
) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - dateRange);

  return this.aggregate([
    { $match: { createdAt: { $gte: startDate } } },
    {
      $group: {
        _id: null,
        totalGenerations: { $sum: 1 },
        successfulGenerations: {
          $sum: { $cond: ["$success", 1, 0] },
        },
        averageProcessingTime: { $avg: "$performance.totalProcessingTime" },
        averageAiResponseTime: { $avg: "$performance.aiResponseTime" },
        averageCompleteness: { $avg: "$responseMetrics.completenessScore" },
        gptGenerations: {
          $sum: { $cond: [{ $eq: ["$aiEngine", "gpt-4"] }, 1, 0] },
        },
        geminiGenerations: {
          $sum: { $cond: [{ $eq: ["$aiEngine", "gemini-vision"] }, 1, 0] },
        },
      },
    },
  ]);
};

// Static method for error analysis
personaGenerationLogSchema.statics.getErrorAnalytics = function (
  dateRange = 30,
) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - dateRange);

  return this.aggregate([
    { $match: { createdAt: { $gte: startDate }, success: false } },
    { $unwind: "$errors" },
    {
      $group: {
        _id: "$errors.errorType",
        count: { $sum: 1 },
        examples: { $push: "$errors.errorMessage" },
      },
    },
    { $sort: { count: -1 } },
  ]);
};

// Static method for AI engine comparison
personaGenerationLogSchema.statics.compareAiEngines = function (
  dateRange = 30,
) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - dateRange);

  return this.aggregate([
    { $match: { createdAt: { $gte: startDate } } },
    {
      $group: {
        _id: "$aiEngine",
        totalRequests: { $sum: 1 },
        successRate: {
          $avg: { $cond: ["$success", 1, 0] },
        },
        averageResponseTime: { $avg: "$performance.aiResponseTime" },
        averageCompleteness: { $avg: "$responseMetrics.completenessScore" },
        averageRetries: { $avg: "$retryCount" },
      },
    },
  ]);
};

// Static method to get generation trends
personaGenerationLogSchema.statics.getGenerationTrends = function (days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  return this.aggregate([
    { $match: { createdAt: { $gte: startDate } } },
    {
      $group: {
        _id: {
          year: { $year: "$createdAt" },
          month: { $month: "$createdAt" },
          day: { $dayOfMonth: "$createdAt" },
        },
        dailyGenerations: { $sum: 1 },
        successfulGenerations: {
          $sum: { $cond: ["$success", 1, 0] },
        },
        averageProcessingTime: { $avg: "$performance.totalProcessingTime" },
      },
    },
    { $sort: { "_id.year": 1, "_id.month": 1, "_id.day": 1 } },
  ]);
};

// Pre-save middleware to calculate derived metrics
personaGenerationLogSchema.pre("save", function (next) {
  // Calculate total processing time if not set
  if (
    this.performance.startTime &&
    this.performance.endTime &&
    !this.performance.totalProcessingTime
  ) {
    this.performance.totalProcessingTime =
      this.performance.endTime - this.performance.startTime;
  }

  // Calculate word count if not set
  if (this.promptData.finalPrompt && !this.responseMetrics.wordCount) {
    this.responseMetrics.wordCount =
      this.promptData.finalPrompt.split(/\s+/).length;
  }

  next();
});

const PersonaGenerationLog = mongoose.model(
  "PersonaGenerationLog",
  personaGenerationLogSchema,
);

export default PersonaGenerationLog;
