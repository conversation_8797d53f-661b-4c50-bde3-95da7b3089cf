import mongoose from "mongoose";

/**
 * Content Category Schema - Defines agency-specific content categories
 * with custom frequencies, reminder types, and validation rules
 */
const contentCategorySchema = new mongoose.Schema(
  {
    agencyId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      required: true,
      index: true,
    },
    label: {
      type: String,
      required: [true, "Category label is required"],
      trim: true,
      maxlength: [100, "Category label cannot exceed 100 characters"],
    },
    platform: {
      type: String,
      required: [true, "Platform is required"],
      enum: {
        values: [
          "instagram",
          "tiktok",
          "onlyfans",
          "twitter",
          "youtube",
          "other",
        ],
        message:
          "Platform must be one of: instagram, tiktok, onlyfans, twitter, youtube, other",
      },
    },
    reminderFrequency: {
      type: Number,
      required: [true, "Reminder frequency is required"],
      min: [1, "Reminder frequency must be at least 1 day"],
      max: [365, "Reminder frequency cannot exceed 365 days"],
    },
    reminderType: {
      type: String,
      enum: {
        values: ["hard", "soft", "one-time"],
        message: "Reminder type must be one of: hard, soft, one-time",
      },
      default: "soft",
    },
    instructionTooltip: {
      type: String,
      maxlength: [500, "Instruction tooltip cannot exceed 500 characters"],
      trim: true,
    },
    hardRules: {
      maxFileSize: {
        type: Number,
        default: 2147483648, // 2GB in bytes
        min: [1024, "Max file size must be at least 1KB"],
      },
      allowedFormats: {
        type: [String],
        default: ["jpg", "jpeg", "png", "mp4", "mov", "avi", "gif"],
        validate: {
          validator: function (formats) {
            return formats && formats.length > 0;
          },
          message: "At least one file format must be allowed",
        },
      },
      requiresApproval: {
        type: Boolean,
        default: false,
      },
      requiresWatermark: {
        type: Boolean,
        default: false,
      },
      requiresConsent: {
        type: Boolean,
        default: true,
      },
    },
    isActive: {
      type: Boolean,
      default: true,
      index: true,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      required: true,
    },
    lastModifiedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  },
);

// Compound indexes for efficient queries
contentCategorySchema.index({ agencyId: 1, isActive: 1 });
contentCategorySchema.index({ agencyId: 1, platform: 1 });
contentCategorySchema.index({ createdAt: -1 });

// Virtual for upload count (to be populated when needed)
contentCategorySchema.virtual("uploadCount", {
  ref: "ContentUpload",
  localField: "_id",
  foreignField: "categoryId",
  count: true,
});

// Pre-save middleware to update lastModifiedBy
contentCategorySchema.pre("save", function (next) {
  if (this.isModified() && !this.isNew) {
    this.lastModifiedBy = this.createdBy;
  }
  next();
});

// Static method to find active categories for an agency
contentCategorySchema.statics.findActiveByAgency = function (agencyId) {
  return this.find({ agencyId, isActive: true }).sort({ createdAt: -1 });
};

// Instance method to check if file format is allowed
contentCategorySchema.methods.isFileFormatAllowed = function (fileExtension) {
  const extension = fileExtension.toLowerCase().replace(".", "");
  return this.hardRules.allowedFormats.includes(extension);
};

// Instance method to check if file size is within limits
contentCategorySchema.methods.isFileSizeAllowed = function (fileSizeBytes) {
  return fileSizeBytes <= this.hardRules.maxFileSize;
};

const ContentCategory = mongoose.model(
  "ContentCategory",
  contentCategorySchema,
);

export default ContentCategory;
