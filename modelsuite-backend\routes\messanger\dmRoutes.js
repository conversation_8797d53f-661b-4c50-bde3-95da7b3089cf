import express from "express";
import { verifyToken } from "../../middlewares/authMiddleware.js";
import checkPermission, {
  checkLegacyPermission,
} from "../../middlewares/permissionCheck.js";
import {
  createNewDm,
  deleteAllDmsOfUser,
  deleteLatestMsg,
  fetchNewerDMMessages,
  fetchOlderDMMessages,
  fetchSpecificDMMessages,
  getAllDMConversations,
  getDmMedia,
  getPinnedMessagesWithUserDetails,
  searchDMMessages,
} from "../../controllers/messages/dmController.js";
const router = express.Router();

router.get("/getAllDMConversations", verifyToken, getAllDMConversations);
router.post("/createNewDm", verifyToken, createNewDm);
router.get("/fetchOlderDmMessages", verifyToken, fetchOlderDMMessages);
router.get("/fetchNewerDmMessages", verifyToken, fetchNewerDMMessages);
router.get("/fetchSpecificDMMessages", verifyToken, fetchSpecificDMMessages);
router.delete("/deleteLatestMsg/:convoId", deleteLatestMsg);
router.get(
  "/pinnedMessagesWithUserDetails",
  verifyToken,
  getPinnedMessagesWithUserDetails,
);
router.get("/searchMessages", verifyToken, searchDMMessages);
router.get("/fetchDmMedia/:convoId", verifyToken, getDmMedia);

router.delete(
  "/deleteAllDmsOfUser",
  verifyToken,
  checkLegacyPermission("messages", "delete"),
  deleteAllDmsOfUser,
);

export default router;
