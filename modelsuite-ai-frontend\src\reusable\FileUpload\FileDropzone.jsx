import * as React from "react";
import { cn } from "@/lib/utils";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Upload, File, X, CheckCircle, AlertCircle } from "lucide-react";

const FileDropzone = ({
  accept = "*",
  maxSize = 10 * 1024 * 1024, // 10MB default
  multiple = false,
  onUpload,
  onRemove,
  preview = true,
  disabled = false,
  className,
  ...props
}) => {
  const [dragActive, setDragActive] = React.useState(false);
  const [files, setFiles] = React.useState([]);
  const [uploading, setUploading] = React.useState(false);
  const fileInputRef = React.useRef(null);

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // Validate file
  const validateFile = (file) => {
    if (file.size > maxSize) {
      return `File size exceeds ${formatFileSize(maxSize)}`;
    }
    return null;
  };

  // Handle file selection
  const handleFiles = (fileList) => {
    const newFiles = Array.from(fileList).map((file) => ({
      file,
      id: Math.random().toString(36).substr(2, 9),
      preview: file.type.startsWith("image/")
        ? URL.createObjectURL(file)
        : null,
      error: validateFile(file),
      status: "pending", // pending, uploading, success, error
    }));

    if (multiple) {
      setFiles((prev) => [...prev, ...newFiles]);
    } else {
      setFiles(newFiles);
    }

    // Auto upload valid files
    newFiles.forEach((fileData) => {
      if (!fileData.error && onUpload) {
        uploadFile(fileData);
      }
    });
  };

  // Upload file
  const uploadFile = async (fileData) => {
    setFiles((prev) =>
      prev.map((f) =>
        f.id === fileData.id ? { ...f, status: "uploading" } : f
      )
    );

    try {
      setUploading(true);
      await onUpload(fileData.file);
      setFiles((prev) =>
        prev.map((f) =>
          f.id === fileData.id ? { ...f, status: "success" } : f
        )
      );
    } catch (error) {
      setFiles((prev) =>
        prev.map((f) =>
          f.id === fileData.id
            ? { ...f, status: "error", error: error.message }
            : f
        )
      );
    } finally {
      setUploading(false);
    }
  };

  // Remove file
  const removeFile = (fileId) => {
    const fileToRemove = files.find((f) => f.id === fileId);
    if (fileToRemove?.preview) {
      URL.revokeObjectURL(fileToRemove.preview);
    }
    setFiles((prev) => prev.filter((f) => f.id !== fileId));
    if (onRemove) {
      onRemove(fileId);
    }
  };

  // Drag handlers
  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    if (disabled) return;

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFiles(e.dataTransfer.files);
    }
  };

  const handleInputChange = (e) => {
    if (e.target.files && e.target.files.length > 0) {
      handleFiles(e.target.files);
    }
  };

  const openFileDialog = () => {
    if (fileInputRef.current && !disabled) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className={cn("space-y-4", className)} {...props}>
      {/* Drop Zone */}
      <Card
        className={cn(
          "relative overflow-hidden transition-all duration-200 cursor-pointer border-2 border-dashed bg-[#0f0f0f] border-gray-800",
          dragActive && "border-gray-600 bg-[#1a1a1a]",
          disabled && "cursor-not-allowed opacity-50",
          !disabled && "hover:border-gray-700"
        )}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={openFileDialog}
      >
        <CardContent className="p-4 text-center">
          <div className="space-y-3">
            <div className="flex justify-center">
              <Upload className="h-8 w-8 text-gray-400" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-300">
                {dragActive
                  ? "Drop files here"
                  : "Drop files or click to upload"}
              </p>
              <p className="text-sm text-gray-500 mt-1">
                {accept !== "*" && `Supported formats: ${accept}`}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                Max size: {formatFileSize(maxSize)}
                {multiple && " • Multiple files allowed"}
              </p>
            </div>
            <Button
              variant="outline"
              size="sm"
              disabled={disabled}
              className="border-gray-600 bg-[#1a1a1a] text-gray-400 hover:bg-gray-800 hover:text-gray-300 hover:border-gray-500"
            >
              Browse Files
            </Button>
          </div>
        </CardContent>

        <input
          ref={fileInputRef}
          type="file"
          accept={accept}
          multiple={multiple}
          onChange={handleInputChange}
          className="hidden"
          disabled={disabled}
        />
      </Card>

      {/* File List */}
      {preview && files.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-300">Uploaded Files</h4>
          <div className="space-y-2">
            {files.map((fileData) => (
              <Card
                key={fileData.id}
                className="p-3 bg-[#0f0f0f] border-gray-800"
              >
                <div className="flex items-center gap-3">
                  {/* File Preview/Icon */}
                  <div className="flex-shrink-0">
                    {fileData.preview ? (
                      <img
                        src={fileData.preview}
                        alt="Preview"
                        className="h-10 w-10 rounded object-cover"
                      />
                    ) : (
                      <div className="flex h-10 w-10 items-center justify-center rounded bg-gray-700">
                        <File className="h-5 w-5 text-gray-400" />
                      </div>
                    )}
                  </div>

                  {/* File Info */}
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate text-gray-300">
                      {fileData.file.name}
                    </p>
                    <p className="text-xs text-gray-500">
                      {formatFileSize(fileData.file.size)}
                    </p>
                    {fileData.error && (
                      <p className="text-xs text-red-400">{fileData.error}</p>
                    )}
                  </div>

                  {/* Status */}
                  <div className="flex items-center gap-2">
                    {fileData.status === "uploading" && (
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-blue-400 border-t-transparent" />
                    )}
                    {fileData.status === "success" && (
                      <CheckCircle className="h-4 w-4 text-green-400" />
                    )}
                    {fileData.status === "error" && (
                      <AlertCircle className="h-4 w-4 text-red-400" />
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        removeFile(fileData.id);
                      }}
                      className="h-8 w-8 p-0 text-gray-500 hover:text-gray-300 hover:bg-[#1a1a1a]"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default FileDropzone;
