import express from "express";
import {
  createEvent,
  getEvents,
  updateEvent,
  deleteEvent,
} from "../../controllers/calendar/eventController.js";
import { verifyToken } from "../../middlewares/authMiddleware.js";
import checkPermission from "../../middlewares/permissionCheck.js";

const router = express.Router();

// All routes require auth
router.use(verifyToken);

// Create new event
router.post("/create", createEvent);

// Get all events for a model (with optional date filters)
router.get("/:modelId", getEvents);

// Update event by ID
router.put("/update/:id", updateEvent);

// Delete event by ID
router.delete("/delete/:id", checkPermission("calendar.delete"), deleteEvent);

export default router;
