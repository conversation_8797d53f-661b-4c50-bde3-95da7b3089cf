import CaptionQuota from "../models/caption/CaptionQuota.js";
import { ApiError } from "../utils/ApiError.js";
import { asyncHandler } from "../utils/asyncHandler.js";
import { getToday } from "../utils/captionUtils.js";
import { CAPTION_CONFIG } from "../config/captionConfig.js";

const captionRateLimit = asyncHandler(async (req, res, next) => {
  const modelId = req.user._id;
  const today = getToday();

  // Atomic operation to check and reserve quota
  const updatedQuota = await CaptionQuota.findOneAndUpdate(
    {
      modelId,
      date: today,
      usageCount: { $lt: CAPTION_CONFIG.DAILY_QUOTA },
    },
    {
      $inc: { usageCount: 1 },
      $set: { lastUsed: new Date() },
    },
    {
      upsert: true,
      new: true,
      setDefaultsOnInsert: true,
    },
  );

  // If no document was updated, user has exceeded quota
  if (!updatedQuota || updatedQuota.usageCount > CAPTION_CONFIG.DAILY_QUOTA) {
    // Rollback if over limit due to race condition
    if (updatedQuota && updatedQuota.usageCount > CAPTION_CONFIG.DAILY_QUOTA) {
      await CaptionQuota.findOneAndUpdate(
        { modelId, date: today },
        { $inc: { usageCount: -1 } },
      );
    }

    throw new ApiError(
      429,
      `Daily caption generation limit reached (${CAPTION_CONFIG.DAILY_QUOTA}/${CAPTION_CONFIG.DAILY_QUOTA}). Try again tomorrow.`,
    );
  }

  req.captionQuota = updatedQuota;
  next();
});

export default captionRateLimit;
