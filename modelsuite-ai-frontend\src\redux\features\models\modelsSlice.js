import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axiosInstance from "@/config/axiosInstance";

// Async thunks for Models
export const fetchAgencyModels = createAsyncThunk(
  "models/fetchAgencyModels",
  async (filters = {}, { rejectWithValue }) => {
    try {
      const params = new URLSearchParams();
      
      // Add filters to params if they exist
      Object.keys(filters).forEach(key => {
        if (filters[key] !== undefined && filters[key] !== null && filters[key] !== '') {
          params.append(key, filters[key]);
        }
      });

      const response = await axiosInstance.get(`/agency/agency-models?${params.toString()}`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.error || "Failed to fetch agency models");
    }
  }
);

export const searchAllModels = createAsyncThunk(
  "models/searchAllModels",
  async (searchQuery, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.get(`/agency/search-all-models?search=${encodeURIComponent(searchQuery)}`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.error || "Failed to search models");
    }
  }
);

export const addModelToAgency = createAsyncThunk(
  "models/addModelToAgency",
  async (modelId, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.post("/agency/add-model", { modelId });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.error || "Failed to add model to agency");
    }
  }
);

export const removeModelFromAgency = createAsyncThunk(
  "models/removeModelFromAgency",
  async (modelId, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.delete(`/agency/models/remove/${modelId}`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.error || "Failed to remove model from agency");
    }
  }
);

const initialState = {
  // Agency Models
  agencyModels: [],
  agencyModelsLoading: false,
  agencyModelsError: null,

  // Search Results
  searchResults: [],
  searchLoading: false,
  searchError: null,

  // UI State
  selectedModels: [],
  filters: {},
  loading: false,
  error: null,
};

const modelsSlice = createSlice({
  name: "models",
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
      state.agencyModelsError = null;
      state.searchError = null;
    },
    setSelectedModels: (state, action) => {
      state.selectedModels = action.payload;
    },
    addSelectedModel: (state, action) => {
      const model = action.payload;
      const exists = state.selectedModels.find(m => m._id === model._id);
      if (!exists) {
        state.selectedModels.push(model);
      }
    },
    removeSelectedModel: (state, action) => {
      const modelId = action.payload;
      state.selectedModels = state.selectedModels.filter(m => m._id !== modelId);
    },
    clearSelectedModels: (state) => {
      state.selectedModels = [];
    },
    setFilters: (state, action) => {
      state.filters = action.payload;
    },
    clearSearchResults: (state) => {
      state.searchResults = [];
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Agency Models
      .addCase(fetchAgencyModels.pending, (state) => {
        state.agencyModelsLoading = true;
        state.agencyModelsError = null;
      })
      .addCase(fetchAgencyModels.fulfilled, (state, action) => {
        state.agencyModelsLoading = false;
        state.agencyModels = action.payload.data?.models || [];
      })
      .addCase(fetchAgencyModels.rejected, (state, action) => {
        state.agencyModelsLoading = false;
        state.agencyModelsError = action.payload;
      })

      // Search All Models
      .addCase(searchAllModels.pending, (state) => {
        state.searchLoading = true;
        state.searchError = null;
      })
      .addCase(searchAllModels.fulfilled, (state, action) => {
        state.searchLoading = false;
        state.searchResults = action.payload.data || [];
      })
      .addCase(searchAllModels.rejected, (state, action) => {
        state.searchLoading = false;
        state.searchError = action.payload;
      })

      // Add Model to Agency
      .addCase(addModelToAgency.fulfilled, (state, action) => {
        // Refresh agency models after adding
        state.agencyModels.push(action.payload.model);
      })

      // Remove Model from Agency
      .addCase(removeModelFromAgency.fulfilled, (state, action) => {
        // Remove model from agency models list
        const removedModelId = action.payload.removedModelId;
        state.agencyModels = state.agencyModels.filter(m => m._id !== removedModelId);
      });
  },
});

export const {
  clearError,
  setSelectedModels,
  addSelectedModel,
  removeSelectedModel,
  clearSelectedModels,
  setFilters,
  clearSearchResults,
} = modelsSlice.actions;

export default modelsSlice.reducer;
