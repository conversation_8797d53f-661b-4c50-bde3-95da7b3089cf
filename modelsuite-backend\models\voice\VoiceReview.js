import mongoose from "mongoose";

/**
 * Voice Review Model - Represents the review and approval process for voice recordings
 * Tracks feedback, ratings, and approval workflow history
 */
const voiceReviewSchema = new mongoose.Schema(
  {
    // Core relationships
    assignmentId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "VoiceAssignment",
      required: [true, "Assignment ID is required"],
    },
    recordingId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "VoiceRecording",
      required: [true, "Recording ID is required"],
    },
    modelId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ModelUser",
      required: [true, "Model ID is required"],
    },
    agencyId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      required: [true, "Agency ID is required"],
    },

    // Review metadata
    reviewedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      required: [true, "Reviewer ID is required"],
    },
    reviewedAt: {
      type: Date,
      default: Date.now,
    },

    // Review decision
    decision: {
      type: String,
      enum: ["approved", "rejected", "needs_revision", "pending"],
      required: [true, "Review decision is required"],
    },

    // Rating and scoring
    overallRating: {
      type: Number,
      min: 1,
      max: 5,
      required: function () {
        return this.decision === "approved";
      },
    },

    // Detailed ratings breakdown
    ratings: {
      audioQuality: {
        type: Number,
        min: 1,
        max: 5,
        default: null,
      },
      voiceClarity: {
        type: Number,
        min: 1,
        max: 5,
        default: null,
      },
      emotionalTone: {
        type: Number,
        min: 1,
        max: 5,
        default: null,
      },
      pacing: {
        type: Number,
        min: 1,
        max: 5,
        default: null,
      },
      backgroundNoise: {
        type: Number,
        min: 1,
        max: 5,
        default: null,
      },
    },

    // Feedback and comments
    comments: {
      type: String,
      trim: true,
      maxlength: [2000, "Comments cannot exceed 2000 characters"],
    },
    privateNotes: {
      type: String,
      trim: true,
      maxlength: [1000, "Private notes cannot exceed 1000 characters"],
    },

    // Specific feedback categories
    feedback: {
      positiveAspects: [
        {
          category: {
            type: String,
            enum: [
              "voice_quality",
              "question_delivery",
              "emotional_expression",
              "technical_quality",
              "creativity",
              "professionalism",
            ],
          },
          comment: String,
        },
      ],
      improvementAreas: [
        {
          category: {
            type: String,
            enum: [
              "voice_quality",
              "question_delivery",
              "emotional_expression",
              "technical_quality",
              "timing",
              "background_noise",
            ],
          },
          comment: String,
          priority: {
            type: String,
            enum: ["low", "medium", "high"],
            default: "medium",
          },
        },
      ],
      suggestions: [
        {
          type: String,
          trim: true,
        },
      ],
    },

    // Quality assessment flags
    qualityFlags: {
      technicalIssues: {
        hasBackgroundNoise: { type: Boolean, default: false },
        hasAudioClipping: { type: Boolean, default: false },
        volumeTooLow: { type: Boolean, default: false },
        volumeTooHigh: { type: Boolean, default: false },
        poorAudioQuality: { type: Boolean, default: false },
      },
      contentIssues: {
        questionDeviation: { type: Boolean, default: false },
        inappropriateTone: { type: Boolean, default: false },
        missingContent: { type: Boolean, default: false },
        extraContent: { type: Boolean, default: false },
        timingIssues: { type: Boolean, default: false },
      },
      performanceIssues: {
        lackOfEmotion: { type: Boolean, default: false },
        inconsistentPacing: { type: Boolean, default: false },
        pronunciationIssues: { type: Boolean, default: false },
        breathingNoises: { type: Boolean, default: false },
      },
    },

    // Revision requirements (if rejected)
    revisionRequirements: {
      mustFix: [
        {
          issue: String,
          description: String,
          priority: {
            type: String,
            enum: ["critical", "important", "minor"],
            default: "important",
          },
        },
      ],
      suggestions: [
        {
          improvement: String,
          description: String,
        },
      ],
      deadline: {
        type: Date,
        validate: {
          validator: function (value) {
            return !value || value > new Date();
          },
          message: "Revision deadline must be in the future",
        },
      },
    },

    // Review process tracking
    reviewDuration: {
      type: Number, // in minutes
      default: null,
    },
    reviewStartedAt: {
      type: Date,
      default: null,
    },

    // Approval workflow
    approvalLevel: {
      type: String,
      enum: ["first_review", "second_review", "final_approval"],
      default: "first_review",
    },
    requiresSecondReview: {
      type: Boolean,
      default: false,
    },
    secondReviewBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      default: null,
    },

    // Automated analysis integration
    aiAnalysis: {
      transcriptionAccuracy: Number, // 0-100
      sentimentMatch: Number, // 0-100
      keywordCompliance: Number, // 0-100
      qualityScore: Number, // 0-100
      flaggedIssues: [String],
      confidence: Number, // 0-1
    },

    // Review history and versioning
    reviewVersion: {
      type: Number,
      default: 1,
    },
    previousReviews: [
      {
        reviewedBy: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Agency",
        },
        reviewedAt: Date,
        decision: String,
        rating: Number,
        comments: String,
      },
    ],

    // Escalation and dispute handling
    isEscalated: {
      type: Boolean,
      default: false,
    },
    escalatedAt: {
      type: Date,
      default: null,
    },
    escalatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ModelUser",
      default: null,
    },
    escalationReason: {
      type: String,
      trim: true,
    },

    // Model response to feedback
    modelResponse: {
      acknowledged: {
        type: Boolean,
        default: false,
      },
      acknowledgedAt: {
        type: Date,
        default: null,
      },
      response: {
        type: String,
        trim: true,
        maxlength: [1000, "Model response cannot exceed 1000 characters"],
      },
      disputeRaised: {
        type: Boolean,
        default: false,
      },
      disputeReason: {
        type: String,
        trim: true,
      },
    },

    // Soft delete
    isDeleted: {
      type: Boolean,
      default: false,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  {
    timestamps: true,
    // Add indexes for better query performance
    indexes: [
      { assignmentId: 1 },
      { recordingId: 1 },
      { modelId: 1, decision: 1 },
      { agencyId: 1, reviewedAt: -1 },
      { reviewedBy: 1 },
      { decision: 1, reviewedAt: -1 },
      { isEscalated: 1 },
    ],
  },
);

// Virtual for average rating calculation
voiceReviewSchema.virtual("averageDetailedRating").get(function () {
  const ratings = this.ratings;
  const validRatings = Object.values(ratings).filter(
    (rating) => rating !== null && rating !== undefined,
  );

  if (validRatings.length === 0) return null;

  const sum = validRatings.reduce((acc, rating) => acc + rating, 0);
  return Math.round((sum / validRatings.length) * 10) / 10; // Round to 1 decimal
});

// Virtual for review completion time
voiceReviewSchema.virtual("reviewTime").get(function () {
  if (!this.reviewStartedAt || !this.reviewedAt) return null;
  return Math.floor((this.reviewedAt - this.reviewStartedAt) / (1000 * 60)); // minutes
});

// Virtual for total issues count
voiceReviewSchema.virtual("totalIssuesCount").get(function () {
  let count = 0;
  const flags = this.qualityFlags;

  Object.values(flags.technicalIssues || {}).forEach((flag) => flag && count++);
  Object.values(flags.contentIssues || {}).forEach((flag) => flag && count++);
  Object.values(flags.performanceIssues || {}).forEach(
    (flag) => flag && count++,
  );

  return count;
});

// Pre-save middleware to calculate review duration
voiceReviewSchema.pre("save", function (next) {
  // Calculate review duration if completed
  if (this.reviewStartedAt && this.reviewedAt && !this.reviewDuration) {
    this.reviewDuration = Math.floor(
      (this.reviewedAt - this.reviewStartedAt) / (1000 * 60),
    );
  }

  // Auto-calculate overall rating from detailed ratings if not provided
  if (!this.overallRating && this.decision === "approved") {
    const avgRating = this.averageDetailedRating;
    if (avgRating) {
      this.overallRating = Math.round(avgRating);
    }
  }

  next();
});

// Static method to find reviews by agency
voiceReviewSchema.statics.findByAgency = function (agencyId, decision = null) {
  const query = { agencyId, isDeleted: false };
  if (decision) query.decision = decision;

  return this.find(query)
    .populate("recordingId", "generatedFilename duration fileSize")
    .populate("modelId", "fullName username")
    .sort({ reviewedAt: -1 });
};

// Static method to find reviews by model
voiceReviewSchema.statics.findByModel = function (modelId, decision = null) {
  const query = { modelId, isDeleted: false };
  if (decision) query.decision = decision;

  return this.find(query)
    .populate("recordingId", "generatedFilename duration")
    .populate("reviewedBy", "agencyName")
    .sort({ reviewedAt: -1 });
};

// Static method to get review statistics
voiceReviewSchema.statics.getReviewStats = function (
  agencyId,
  dateRange = null,
) {
  const matchStage = { agencyId, isDeleted: false };

  if (dateRange) {
    matchStage.reviewedAt = {
      $gte: dateRange.start,
      $lte: dateRange.end,
    };
  }

  return this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: "$decision",
        count: { $sum: 1 },
        avgRating: { $avg: "$overallRating" },
        avgReviewTime: { $avg: "$reviewDuration" },
        totalReviewTime: { $sum: "$reviewDuration" },
      },
    },
  ]);
};

// Static method to find pending reviews
voiceReviewSchema.statics.findPendingReviews = function (agencyId) {
  return this.find({
    agencyId,
    decision: "pending",
    isDeleted: false,
  })
    .populate("recordingId")
    .populate("modelId", "fullName username")
    .sort({ createdAt: 1 }); // Oldest first
};

// Instance method to approve review
voiceReviewSchema.methods.approve = function (rating, comments = null) {
  this.decision = "approved";
  this.overallRating = rating;
  this.comments = comments;
  this.reviewedAt = new Date();
};

// Instance method to reject review
voiceReviewSchema.methods.reject = function (
  comments,
  revisionRequirements = null,
) {
  this.decision = "rejected";
  this.comments = comments;
  this.revisionRequirements = revisionRequirements;
  this.reviewedAt = new Date();
};

// Instance method to request revision
voiceReviewSchema.methods.requestRevision = function (comments, requirements) {
  this.decision = "needs_revision";
  this.comments = comments;
  this.revisionRequirements = requirements;
  this.reviewedAt = new Date();
};

// Instance method to escalate review
voiceReviewSchema.methods.escalate = function (escalatedBy, reason) {
  this.isEscalated = true;
  this.escalatedAt = new Date();
  this.escalatedBy = escalatedBy;
  this.escalationReason = reason;
};

// Instance method to acknowledge feedback (by model)
voiceReviewSchema.methods.acknowledgeFeedback = function (response = null) {
  this.modelResponse.acknowledged = true;
  this.modelResponse.acknowledgedAt = new Date();
  if (response) {
    this.modelResponse.response = response;
  }
};

// Instance method to calculate quality score based on ratings
voiceReviewSchema.methods.calculateQualityScore = function () {
  const ratings = this.ratings;
  const weights = {
    audioQuality: 0.3,
    voiceClarity: 0.25,
    emotionalTone: 0.2,
    pacing: 0.15,
    backgroundNoise: 0.1,
  };

  let weightedSum = 0;
  let totalWeight = 0;

  Object.entries(weights).forEach(([key, weight]) => {
    if (ratings[key] !== null && ratings[key] !== undefined) {
      weightedSum += ratings[key] * weight;
      totalWeight += weight;
    }
  });

  return totalWeight > 0 ? Math.round((weightedSum / totalWeight) * 20) : null; // Convert to 0-100 scale
};

const VoiceReview = mongoose.model("VoiceReview", voiceReviewSchema);
export default VoiceReview;
