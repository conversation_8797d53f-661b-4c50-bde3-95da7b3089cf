import mongoose from "mongoose";

// A single question inside a section
const questionSchema = new mongoose.Schema(
  {
    type: {
      type: String,
      enum: ["text", "textarea", "number", "select", "radio", "checkbox", "email", "phone", "date", "multi-select", "boolean"],
      required: true,
    },
    label: { type: String, required: true },
    text: { type: String }, // For backward compatibility
    required: { type: Boolean, default: false },
    helpText: { type: String },
    options: [String],
  },
  { _id: true },
); // keep _id for questionId reference

// A section groups multiple questions (e.g., "Personal Info")
const sectionSchema = new mongoose.Schema(
  {
    title: { type: String, required: true },
    questions: [questionSchema],
  },
  { _id: true },
); // keep _id for section reference

const templateSchema = new mongoose.Schema({
  agencyId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Agency",
    default: null,
  }, // null means "global" template; if set, it's private to that agency
  title: { type: String, required: true },
  description: { type: String },
  sections: [sectionSchema],
  isActive: { type: Boolean, default: true }, // Add isActive field
  createdBy: { type: mongoose.Schema.Types.ObjectId, ref: "User" }, // who created it
  createdAt: { type: Date, default: Date.now },
});

export default mongoose.model("Template", templateSchema);
