import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import { fetchNotes } from "@/redux/features/notes/notesSlice";
import RichTextEditor from "@/components/Notes/RichTextEditor";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Pin, Edit, Trash2 } from "lucide-react";
import { EmptyState } from "@/reusable";

const NotesPage = () => {
  const { id: modelId } = useParams();
  const dispatch = useDispatch();
  const notesState = useSelector((s) => s.notesReducer);
  const [openEditor, setOpenEditor] = useState(false);
  const [editingNote, setEditingNote] = useState(null);

  useEffect(() => {
    if (modelId)
      dispatch(fetchNotes({ modelId, params: { page: 1, limit: 20 } }));
  }, [dispatch, modelId]);

  // Realtime disabled: notes refresh on mount and manual actions

  const handleCreate = () => {
    setEditingNote(null);
    setOpenEditor(true);
  };

  const handleEdit = (note) => {
    setEditingNote(note);
    setOpenEditor(true);
  };

  const { allIds = [], byId = {}, loading } = notesState || {};
  const notes = allIds.map((id) => byId[id]).filter(Boolean);

  return (
    <div className="p-4">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold text-white">Notes</h2>
        <Button onClick={handleCreate} className="bg-[#1a1a1a]">
          New Note
        </Button>
      </div>

      {loading ? (
        <div className="text-gray-400">Loading...</div>
      ) : notes.length === 0 ? (
        <EmptyState
          title="No notes found"
          description="Create your first note for this model"
          action={<Button onClick={handleCreate}>Create Note</Button>}
        />
      ) : (
        <div className="space-y-3">
          {notes.map((note) => (
            <Card key={note._id} className="bg-[#1a1a1a] border-gray-800">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    {note.isPinned && (
                      <Pin className="h-4 w-4 text-yellow-500" />
                    )}
                    <CardTitle className="text-base">{note.title}</CardTitle>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEdit(note)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-gray-400 text-sm mb-2 line-clamp-2">
                  {note.content}
                </p>
                <div className="flex items-center justify-between">
                  <div className="flex gap-1">
                    {note.tags?.slice(0, 2).map((tag) => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                  <span className="text-xs text-gray-500">
                    {new Date(note.createdAt).toLocaleDateString()}
                  </span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      <RichTextEditor
        open={openEditor}
        onClose={() => setOpenEditor(false)}
        modelId={modelId}
        note={editingNote}
        onSave={() => {
          setOpenEditor(false);
          setEditingNote(null);
          // Refresh notes
          dispatch(fetchNotes({ modelId, params: { page: 1, limit: 20 } }));
        }}
      />
    </div>
  );
};

export default NotesPage;
