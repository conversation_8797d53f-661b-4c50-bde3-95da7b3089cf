import {
  createSlice,
  createAsyncThunk,
  createSelector,
} from "@reduxjs/toolkit";
import axiosInstance from "@/config/axiosInstance";

// Fetch widget data
export const fetchWidgetData = createAsyncThunk(
  "widgetSidebar/fetchWidgetData",
  async ({ type }, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.get(`/api/v1/widgets/${type}`);
      return {
        type,
        data: response.data,
      };
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || `Failed to fetch ${type} widget data`
      );
    }
  }
);

// Initial state
const initialState = {
  isOpen: false,
  loading: false,
  error: null,
  // Available widgets for dragging (not placed on dashboard)
  availableWidgets: [
    {
      type: "weather",
      name: "Weather",
      description: "Current weather information",
    },
    { type: "clock", name: "Clock", description: "Current time display" },
    {
      type: "metrics",
      name: "Quick Metrics",
      description: "Key agency metrics",
    },
    {
      type: "notifications",
      name: "Notifications",
      description: "Recent notifications",
    },
  ],
  // Drag state
  isDragging: false,
  draggedWidget: null, // { type: 'weather', id: 'temp-id' }
  // Widget data cache
  widgetData: {
    weather: null,
  },
};

const widgetSidebarSlice = createSlice({
  name: "widgetSidebar",
  initialState,
  reducers: {
    toggleSidebar: (state) => {
      state.isOpen = !state.isOpen;
    },
    openSidebar: (state) => {
      state.isOpen = true;
    },
    closeSidebar: (state) => {
      state.isOpen = false;
    },
    clearError: (state) => {
      state.error = null;
    },
    resetSidebar: () => {
      return initialState;
    },
    // Drag & Drop actions
    startDrag: (state, action) => {
      state.isDragging = true;
      state.draggedWidget = action.payload; // { type: 'weather', tempId: 'widget-123' }
    },
    endDrag: (state) => {
      state.isDragging = false;
      state.draggedWidget = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchWidgetData.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchWidgetData.fulfilled, (state, action) => {
        state.loading = false;
        const { type, data } = action.payload;
        state.widgetData[type] = data;
      })
      .addCase(fetchWidgetData.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const {
  toggleSidebar,
  openSidebar,
  closeSidebar,
  clearError,
  resetSidebar,
  startDrag,
  endDrag,
} = widgetSidebarSlice.actions;

// Selectors
export const selectWidgetSidebar = createSelector(
  (state) => state.widgetSidebarReducer,
  (widgetSidebar) => widgetSidebar
);

export const selectIsWidgetSidebarOpen = createSelector(
  (state) => state.widgetSidebarReducer,
  (widgetSidebar) => widgetSidebar.isOpen
);

export const selectWidgetData = createSelector(
  (state) => state.widgetSidebarReducer,
  (widgetSidebar) => widgetSidebar.widgetData
);

export const selectWidgetLoading = createSelector(
  (state) => state.widgetSidebarReducer,
  (widgetSidebar) => widgetSidebar.loading
);

export const selectWidgetError = createSelector(
  (state) => state.widgetSidebarReducer,
  (widgetSidebar) => widgetSidebar.error
);

// New selectors for drag & drop
export const selectAvailableWidgets = createSelector(
  (state) => state.widgetSidebarReducer,
  (widgetSidebar) => widgetSidebar.availableWidgets
);

export const selectIsDragging = createSelector(
  (state) => state.widgetSidebarReducer,
  (widgetSidebar) => widgetSidebar.isDragging
);

export const selectDraggedWidget = createSelector(
  (state) => state.widgetSidebarReducer,
  (widgetSidebar) => widgetSidebar.draggedWidget
);

export default widgetSidebarSlice.reducer;
