import {
  S3Client,
  PutO<PERSON>Command,
  GetObjectCommand,
  DeleteObjectCommand,
  HeadObjectCommand,
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import crypto from "crypto";

/**
 * Cloudflare R2 Storage Service
 * Handles file operations with Cloudflare R2 using AWS S3 compatible API
 */
class CloudflareR2 {
  constructor() {
    this.client = new S3Client({
      region: "auto",
      endpoint: process.env.CLOUDFLARE_R2_ENDPOINT,
      credentials: {
        accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID,
        secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY,
      },
    });
    this.bucketName = process.env.CLOUDFLARE_R2_BUCKET_NAME;
    this.publicDomain = process.env.CLOUDFLARE_R2_PUBLIC_DOMAIN;
  }

  /**
   * Generate a unique file key for R2 storage
   * @param {string} originalName - Original filename
   * @param {string} agencyId - Agency ID
   * @param {string} modelId - Model ID
   * @param {string} categoryId - Category ID
   * @returns {string} - Unique file key
   */
  generateFileKey(originalName, agencyId, modelId, categoryId) {
    const timestamp = Date.now();
    const randomId = crypto.randomBytes(8).toString("hex");
    const extension = originalName.split(".").pop();
    return `content/${agencyId}/${modelId}/${categoryId}/${timestamp}-${randomId}.${extension}`;
  }

  /**
   * Upload a file to Cloudflare R2
   * @param {Buffer} fileBuffer - File buffer
   * @param {string} key - File key/path in R2
   * @param {string} contentType - MIME type
   * @param {Object} metadata - Additional metadata
   * @returns {Object} - Upload result with key, url, and bucket
   */
  async uploadFile(fileBuffer, key, contentType, metadata = {}) {
    try {
      // Ensure all metadata values are strings
      const stringifiedMetadata = {};
      Object.keys(metadata).forEach((metaKey) => {
        stringifiedMetadata[metaKey] = String(metadata[metaKey]);
      });

      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: key,
        Body: fileBuffer,
        ContentType: contentType,
        Metadata: {
          uploadedAt: new Date().toISOString(),
          ...stringifiedMetadata,
        },
      });

      await this.client.send(command);

      return {
        key,
        url: this.getPublicUrl(key),
        bucket: this.bucketName,
      };
    } catch (error) {
      console.error("R2 upload error:", error);
      throw new Error(`Failed to upload file to R2: ${error.message}`);
    }
  }

  /**
   * Get public URL for a file
   * @param {string} key - File key
   * @returns {string} - Public URL
   */
  getPublicUrl(key) {
    if (this.publicDomain) {
      return `https://${this.publicDomain}/${key}`;
    }
    return `${process.env.CLOUDFLARE_R2_ENDPOINT}/${this.bucketName}/${key}`;
  }

  /**
   * Get a signed URL for private access
   * @param {string} key - File key
   * @param {number} expiresIn - Expiration time in seconds (default: 3600)
   * @returns {string} - Signed URL
   */
  async getSignedUrl(key, expiresIn = 3600) {
    try {
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      return await getSignedUrl(this.client, command, { expiresIn });
    } catch (error) {
      console.error("Error generating signed URL:", error);
      throw new Error(`Failed to generate signed URL: ${error.message}`);
    }
  }

  /**
   * Delete a file from R2
   * @param {string} key - File key
   * @returns {boolean} - Success status
   */
  async deleteFile(key) {
    try {
      const command = new DeleteObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      await this.client.send(command);
      return true;
    } catch (error) {
      console.error("Error deleting file from R2:", error);
      throw new Error(`Failed to delete file from R2: ${error.message}`);
    }
  }

  /**
   * Check if a file exists in R2
   * @param {string} key - File key
   * @returns {boolean} - File existence status
   */
  async fileExists(key) {
    try {
      const command = new HeadObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      await this.client.send(command);
      return true;
    } catch (error) {
      if (error.name === "NotFound") {
        return false;
      }
      console.error("Error checking file existence:", error);
      throw new Error(`Failed to check file existence: ${error.message}`);
    }
  }

  /**
   * Upload content file with metadata
   * @param {Object} file - Multer file object
   * @param {string} agencyId - Agency ID
   * @param {string} modelId - Model ID
   * @param {string} categoryId - Category ID
   * @param {Object} metadata - Additional metadata
   * @returns {Object} - Upload result with success status
   */
  async uploadContentFile(file, agencyId, modelId, categoryId, metadata = {}) {
    try {
      console.log("Starting R2 upload for file:", file.originalname);
      console.log("File size:", file.size, "bytes");
      console.log("MIME type:", file.mimetype);

      // Validate configuration first
      if (!this.validateConfiguration()) {
        throw new Error("R2 configuration is invalid");
      }

      const key = this.generateFileKey(
        file.originalname,
        agencyId,
        modelId,
        categoryId,
      );
      console.log("Generated R2 key:", key);

      const fileMetadata = {
        originalName: file.originalname,
        size: file.size.toString(),
        agencyId,
        modelId,
        categoryId,
        ...metadata,
      };

      console.log("File metadata:", fileMetadata);

      const result = await this.uploadFile(
        file.buffer,
        key,
        file.mimetype,
        fileMetadata,
      );
      console.log("R2 upload successful:", result);

      return {
        success: true,
        ...result,
      };
    } catch (error) {
      console.error("Upload content file error:", error);
      console.error("Error stack:", error.stack);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Validate R2 configuration
   * @returns {boolean} - Configuration validity
   */
  validateConfiguration() {
    const requiredEnvVars = [
      "CLOUDFLARE_R2_ENDPOINT",
      "CLOUDFLARE_R2_ACCESS_KEY_ID",
      "CLOUDFLARE_R2_SECRET_ACCESS_KEY",
      "CLOUDFLARE_R2_BUCKET_NAME",
    ];

    const missingVars = requiredEnvVars.filter(
      (varName) => !process.env[varName],
    );

    if (missingVars.length > 0) {
      console.error("Missing R2 configuration variables:", missingVars);
      return false;
    }

    return true;
  }

  /**
   * Test R2 connection
   * @returns {boolean} - Connection status
   */
  async testConnection() {
    try {
      // Try to list objects in the bucket (limit to 1 for efficiency)
      const command = new HeadObjectCommand({
        Bucket: this.bucketName,
        Key: "test-connection-" + Date.now(),
      });

      // This will fail with NotFound, but if credentials are wrong, it will fail differently
      await this.client.send(command);
      return true;
    } catch (error) {
      if (error.name === "NotFound") {
        // This is expected - the test file doesn't exist, but we can connect
        return true;
      }
      console.error("R2 connection test failed:", error);
      return false;
    }
  }
}

// Create and export a singleton instance
const r2Client = new CloudflareR2();
export default r2Client;
