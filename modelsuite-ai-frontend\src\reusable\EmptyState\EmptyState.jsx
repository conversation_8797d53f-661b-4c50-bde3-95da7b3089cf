import * as React from "react";
import { cn } from "@/lib/utils";
import {
  Search,
  Plus,
  Upload,
  FileText,
  Users,
  Inbox,
  Database,
  FolderOpen,
  AlertCircle,
  Wifi,
  WifiOff,
} from "lucide-react";
import { Button } from "@/components/ui/button";

const EmptyState = ({
  type = "default", // "default", "search", "upload", "error", "offline", "loading"
  title,
  description,
  icon: CustomIcon,
  size = "default", // "sm", "default", "lg"
  variant = "default", // "default", "minimal", "bordered"
  showAction = true,
  actionText = "Take Action",
  secondaryActionText,
  onActionClick,
  onSecondaryActionClick,
  children,
  className,
  ...props
}) => {
  // Type configurations
  const typeConfig = {
    default: {
      icon: Database,
      title: "No data available",
      description:
        "There's nothing to show here yet. Get started by adding some content.",
      actionText: "Get Started",
    },
    search: {
      icon: Search,
      title: "No results found",
      description:
        "We couldn't find anything matching your search. Try adjusting your filters or search terms.",
      actionText: "Clear Filters",
    },
    upload: {
      icon: Upload,
      title: "No files uploaded",
      description:
        "Upload your first file to get started. Drag and drop or click to browse.",
      actionText: "Upload Files",
    },
    create: {
      icon: Plus,
      title: "Nothing here yet",
      description:
        "Create your first item to get started and see it appear here.",
      actionText: "Create New",
    },
    users: {
      icon: Users,
      title: "No users found",
      description:
        "No users match your current search or filter criteria. Try broadening your search.",
      actionText: "Invite Users",
    },
    inbox: {
      icon: Inbox,
      title: "Inbox is empty",
      description:
        "All caught up! You have no new messages or notifications at the moment.",
      actionText: "Compose Message",
    },
    files: {
      icon: FolderOpen,
      title: "No files found",
      description:
        "This folder is empty. Start by uploading some files or creating new content.",
      actionText: "Add Files",
    },
    documents: {
      icon: FileText,
      title: "No documents",
      description:
        "You haven't created any documents yet. Start building your content library.",
      actionText: "Create Document",
    },
    error: {
      icon: AlertCircle,
      title: "Something went wrong",
      description:
        "We encountered an error while loading this content. Please try again.",
      actionText: "Retry",
    },
    offline: {
      icon: WifiOff,
      title: "No internet connection",
      description:
        "Check your connection and try again. Some features may not be available offline.",
      actionText: "Retry",
    },
    loading: {
      icon: Database,
      title: "Loading...",
      description: "Please wait while we fetch your content.",
      actionText: null,
    },
  };

  const config = typeConfig[type] || typeConfig.default;
  const IconComponent = CustomIcon || config.icon;

  // Size configurations
  const sizeConfig = {
    sm: {
      container: "py-8 px-4",
      icon: "h-12 w-12",
      title: "text-lg",
      description: "text-sm",
      spacing: "space-y-3",
    },
    default: {
      container: "py-12 px-6",
      icon: "h-16 w-16",
      title: "text-xl",
      description: "text-base",
      spacing: "space-y-4",
    },
    lg: {
      container: "py-16 px-8",
      icon: "h-20 w-20",
      title: "text-2xl",
      description: "text-lg",
      spacing: "space-y-6",
    },
  };

  const sizes = sizeConfig[size] || sizeConfig.default;

  // Variant styles
  const getVariantClasses = () => {
    switch (variant) {
      case "minimal":
        return "bg-transparent";
      case "bordered":
        return "bg-[#0f0f0f] border-2 border-dashed border-gray-700 rounded-lg";
      default:
        return "bg-[#0f0f0f] border border-gray-800 rounded-lg";
    }
  };

  // Get icon color based on type
  const getIconColor = () => {
    switch (type) {
      case "error":
        return "text-gray-400";
      case "offline":
        return "text-gray-500";
      case "loading":
        return "text-gray-400 animate-pulse";
      default:
        return "text-gray-500";
    }
  };

  const displayTitle = title || config.title;
  const displayDescription = description || config.description;
  const displayActionText =
    actionText === "Take Action" ? config.actionText : actionText;

  return (
    <div
      className={cn(
        "flex flex-col items-center justify-center text-center",
        getVariantClasses(),
        sizes.container,
        className
      )}
      {...props}
    >
      <div className={cn("flex flex-col items-center", sizes.spacing)}>
        {/* Icon */}
        {IconComponent && (
          <div className="flex items-center justify-center">
            <IconComponent className={cn(sizes.icon, getIconColor())} />
          </div>
        )}

        {/* Content */}
        <div className={cn("flex flex-col items-center", "space-y-2")}>
          {/* Title */}
          {displayTitle && (
            <h3
              className={cn(
                "font-semibold text-gray-300 tracking-tight",
                sizes.title
              )}
            >
              {displayTitle}
            </h3>
          )}

          {/* Description */}
          {displayDescription && (
            <p className={cn("text-gray-500 max-w-md", sizes.description)}>
              {displayDescription}
            </p>
          )}
        </div>

        {/* Actions */}
        {showAction && (displayActionText || secondaryActionText) && (
          <div className="flex flex-col sm:flex-row gap-3 items-center">
            {/* Primary Action */}
            {displayActionText && onActionClick && (
              <Button
                onClick={onActionClick}
                size={size === "sm" ? "sm" : "default"}
                className="bg-[#1a1a1a] text-gray-300 hover:bg-gray-800 border-gray-600"
              >
                {type === "create" || type === "upload" ? (
                  <Plus className="mr-2 h-4 w-4" />
                ) : type === "search" ? (
                  <Search className="mr-2 h-4 w-4" />
                ) : null}
                {displayActionText}
              </Button>
            )}

            {/* Secondary Action */}
            {secondaryActionText && onSecondaryActionClick && (
              <Button
                variant="outline"
                onClick={onSecondaryActionClick}
                size={size === "sm" ? "sm" : "default"}
                className="border-gray-600 text-gray-400 hover:text-gray-300 hover:bg-gray-800"
              >
                {secondaryActionText}
              </Button>
            )}
          </div>
        )}

        {/* Custom Children */}
        {children && <div className="mt-4">{children}</div>}
      </div>
    </div>
  );
};

export default EmptyState;
