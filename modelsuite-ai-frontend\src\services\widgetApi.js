import axiosInstance from "@/config/axiosInstance";

// Widget API service following ModelSuite patterns
export const widgetApi = {
  // Fetch weather widget data
  getWeatherData: async () => {
    try {
      const response = await axiosInstance.get("/api/v1/widgets/weather");
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.message || "Failed to fetch weather data"
      );
    }
  },

  // Generic widget data fetcher
  getWidgetData: async (widgetType) => {
    try {
      const response = await axiosInstance.get(`/api/v1/widgets/${widgetType}`);
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.message || `Failed to fetch ${widgetType} data`
      );
    }
  },

  // Update widget preferences (future)
  updateWidgetPreferences: async (preferences) => {
    try {
      const response = await axiosInstance.put(
        "/api/v1/widgets/preferences",
        preferences
      );
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.message || "Failed to update widget preferences"
      );
    }
  },
};

export default widgetApi;
