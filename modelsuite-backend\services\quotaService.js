import mongoose from "mongoose";
import { CAPTION_CONFIG } from "../config/captionConfig.js";

/**
 * QuotaService - Handles quota management for different features
 * Supports caption, persona, and other feature quotas
 */
class QuotaService {
  /**
   * Get quota configuration for different features
   */
  static getQuotaConfig(quotaType) {
    const configs = {
      caption: {
        dailyLimit: CAPTION_CONFIG.DAILY_QUOTA || 5,
        model: "CaptionQuota",
        collection: "captionquotas",
      },
      persona: {
        dailyLimit: parseInt(process.env.PERSONA_DAILY_QUOTA) || 3,
        model: "PersonaQuota",
        collection: "personaquotas",
      },
    };

    return configs[quotaType] || null;
  }

  /**
   * Check if user can generate content based on quota
   */
  static async checkQuota(userId, quotaType) {
    try {
      const config = this.getQuotaConfig(quotaType);
      if (!config) {
        throw new Error(`Unknown quota type: ${quotaType}`);
      }

      const today = new Date().toISOString().split("T")[0];

      // For persona quotas, create the collection dynamically
      if (quotaType === "persona") {
        const db = mongoose.connection.db;
        const collection = db.collection(config.collection);

        const quota = await collection.findOne({
          agencyId: new mongoose.Types.ObjectId(userId),
          date: today,
        });

        const usageCount = quota ? quota.usageCount : 0;
        const canGenerate = usageCount < config.dailyLimit;

        return {
          canGenerate,
          usageCount,
          dailyLimit: config.dailyLimit,
          remaining: config.dailyLimit - usageCount,
          message: canGenerate
            ? `${config.dailyLimit - usageCount} generations remaining today`
            : `Daily limit of ${config.dailyLimit} reached. Try again tomorrow.`,
        };
      }

      // For caption quotas, use existing model
      if (quotaType === "caption") {
        const CaptionQuota = mongoose.model("CaptionQuota");
        const quota = await CaptionQuota.findOne({
          modelId: userId,
          date: today,
        });

        const usageCount = quota ? quota.usageCount : 0;
        const canGenerate = usageCount < config.dailyLimit;

        return {
          canGenerate,
          usageCount,
          dailyLimit: config.dailyLimit,
          remaining: config.dailyLimit - usageCount,
          message: canGenerate
            ? `${config.dailyLimit - usageCount} generations remaining today`
            : `Daily limit of ${config.dailyLimit} reached. Try again tomorrow.`,
        };
      }

      throw new Error(`Quota check not implemented for type: ${quotaType}`);
    } catch (error) {
      console.error(`Quota check failed for ${quotaType}:`, error);
      throw error;
    }
  }

  /**
   * Update quota usage
   */
  static async updateQuota(userId, quotaType, increment = 1) {
    try {
      const config = this.getQuotaConfig(quotaType);
      if (!config) {
        throw new Error(`Unknown quota type: ${quotaType}`);
      }

      const today = new Date().toISOString().split("T")[0];

      // For persona quotas
      if (quotaType === "persona") {
        const db = mongoose.connection.db;
        const collection = db.collection(config.collection);

        const result = await collection.findOneAndUpdate(
          {
            agencyId: new mongoose.Types.ObjectId(userId),
            date: today,
          },
          {
            $inc: { usageCount: increment },
            $setOnInsert: {
              agencyId: new mongoose.Types.ObjectId(userId),
              date: today,
              maxUsage: config.dailyLimit,
              createdAt: new Date(),
            },
            $set: { updatedAt: new Date() },
          },
          {
            upsert: true,
            returnDocument: "after",
          },
        );

        return result.value;
      }

      // For caption quotas, use existing model
      if (quotaType === "caption") {
        const CaptionQuota = mongoose.model("CaptionQuota");

        const result = await CaptionQuota.findOneAndUpdate(
          {
            modelId: userId,
            date: today,
          },
          {
            $inc: { usageCount: increment },
          },
          {
            upsert: true,
            new: true,
            setDefaultsOnInsert: true,
          },
        );

        return result;
      }

      throw new Error(`Quota update not implemented for type: ${quotaType}`);
    } catch (error) {
      console.error(`Quota update failed for ${quotaType}:`, error);
      throw error;
    }
  }

  /**
   * Get quota status for a user
   */
  static async getQuotaStatus(userId, quotaType) {
    try {
      const config = this.getQuotaConfig(quotaType);
      if (!config) {
        throw new Error(`Unknown quota type: ${quotaType}`);
      }

      const today = new Date().toISOString().split("T")[0];

      // For persona quotas
      if (quotaType === "persona") {
        const db = mongoose.connection.db;
        const collection = db.collection(config.collection);

        const quota = await collection.findOne({
          agencyId: new mongoose.Types.ObjectId(userId),
          date: today,
        });

        const usageCount = quota ? quota.usageCount : 0;

        return {
          quotaType,
          date: today,
          usageCount,
          dailyLimit: config.dailyLimit,
          remaining: config.dailyLimit - usageCount,
          canGenerate: usageCount < config.dailyLimit,
          resetTime: this.getNextResetTime(),
        };
      }

      // For caption quotas
      if (quotaType === "caption") {
        const CaptionQuota = mongoose.model("CaptionQuota");
        const quota = await CaptionQuota.findOne({
          modelId: userId,
          date: today,
        });

        const usageCount = quota ? quota.usageCount : 0;

        return {
          quotaType,
          date: today,
          usageCount,
          dailyLimit: config.dailyLimit,
          remaining: config.dailyLimit - usageCount,
          canGenerate: usageCount < config.dailyLimit,
          resetTime: this.getNextResetTime(),
        };
      }

      throw new Error(`Quota status not implemented for type: ${quotaType}`);
    } catch (error) {
      console.error(`Get quota status failed for ${quotaType}:`, error);
      throw error;
    }
  }

  /**
   * Get next quota reset time (midnight UTC)
   */
  static getNextResetTime() {
    const tomorrow = new Date();
    tomorrow.setUTCDate(tomorrow.getUTCDate() + 1);
    tomorrow.setUTCHours(0, 0, 0, 0);
    return tomorrow.toISOString();
  }

  /**
   * Reset quota for a specific user and type (admin function)
   */
  static async resetQuota(userId, quotaType, date = null) {
    try {
      const config = this.getQuotaConfig(quotaType);
      if (!config) {
        throw new Error(`Unknown quota type: ${quotaType}`);
      }

      const targetDate = date || new Date().toISOString().split("T")[0];

      if (quotaType === "persona") {
        const db = mongoose.connection.db;
        const collection = db.collection(config.collection);

        const result = await collection.deleteOne({
          agencyId: new mongoose.Types.ObjectId(userId),
          date: targetDate,
        });

        return { deleted: result.deletedCount > 0 };
      }

      if (quotaType === "caption") {
        const CaptionQuota = mongoose.model("CaptionQuota");
        const result = await CaptionQuota.deleteOne({
          modelId: userId,
          date: targetDate,
        });

        return { deleted: result.deletedCount > 0 };
      }

      throw new Error(`Quota reset not implemented for type: ${quotaType}`);
    } catch (error) {
      console.error(`Quota reset failed for ${quotaType}:`, error);
      throw error;
    }
  }

  /**
   * Get quota analytics for admin dashboard
   */
  static async getQuotaAnalytics(quotaType, startDate, endDate) {
    try {
      const config = this.getQuotaConfig(quotaType);
      if (!config) {
        throw new Error(`Unknown quota type: ${quotaType}`);
      }

      const start =
        startDate ||
        new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          .toISOString()
          .split("T")[0];
      const end = endDate || new Date().toISOString().split("T")[0];

      if (quotaType === "persona") {
        const db = mongoose.connection.db;
        const collection = db.collection(config.collection);

        const analytics = await collection
          .aggregate([
            {
              $match: {
                date: { $gte: start, $lte: end },
              },
            },
            {
              $group: {
                _id: "$date",
                totalUsage: { $sum: "$usageCount" },
                uniqueUsers: { $addToSet: "$agencyId" },
                avgUsage: { $avg: "$usageCount" },
              },
            },
            {
              $project: {
                date: "$_id",
                totalUsage: 1,
                uniqueUsers: { $size: "$uniqueUsers" },
                avgUsage: { $round: ["$avgUsage", 2] },
              },
            },
            { $sort: { date: 1 } },
          ])
          .toArray();

        return analytics;
      }

      throw new Error(`Quota analytics not implemented for type: ${quotaType}`);
    } catch (error) {
      console.error(`Quota analytics failed for ${quotaType}:`, error);
      throw error;
    }
  }
}

export default QuotaService;
