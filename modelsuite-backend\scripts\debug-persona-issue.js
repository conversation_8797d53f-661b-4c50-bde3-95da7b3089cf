import dotenv from "dotenv";
import mongoose from "mongoose";
import PersonaProfile from "./models/persona/PersonaProfile.js";
import Model from "./models/model.js";
import Agency from "./models/agency.js";

// Load environment variables
dotenv.config();

// Connect to MongoDB
const DB_URI = process.env.MONGO_URI || "mongodb://localhost:27017/modelsuite";

console.log("🔧 Environment check:");
console.log(`DB_URI: ${DB_URI ? "Set" : "Not set"}`);
console.log(`NODE_ENV: ${process.env.NODE_ENV || "Not set"}`);

async function debugPersonaIssue() {
  try {
    console.log("🔌 Connecting to MongoDB...");
    await mongoose.connect(DB_URI);
    console.log("✅ Connected to MongoDB");

    // 1. Check if there are any personas in the database
    const totalPersonas = await PersonaProfile.countDocuments();
    console.log(`📊 Total personas in database: ${totalPersonas}`);

    if (totalPersonas > 0) {
      console.log("\n📋 Existing personas:");
      const personas = await PersonaProfile.find({})
        .select("_id modelId agencyId personaText tags version createdAt")
        .limit(5);
      personas.forEach((persona, index) => {
        console.log(`${index + 1}. ID: ${persona._id}`);
        console.log(`   Model: ${persona.modelId}`);
        console.log(`   Agency: ${persona.agencyId}`);
        console.log(`   Text: ${persona.personaText?.substring(0, 50)}...`);
        console.log(`   Tags: ${persona.tags?.join(", ")}`);
        console.log(`   Version: ${persona.version}`);
        console.log(`   Created: ${persona.createdAt}`);
        console.log("");
      });
    }

    // 2. Check agencies and models
    const totalAgencies = await Agency.countDocuments();
    const totalModels = await Model.countDocuments();
    console.log(`📊 Total agencies: ${totalAgencies}`);
    console.log(`📊 Total models: ${totalModels}`);

    // 3. Check the specific agency from the logs
    const agencyId = "68723da81257ef228b214073";
    const agency = await Agency.findById(agencyId);
    if (agency) {
      console.log(`\n✅ Found Stellar Models Agency: ${agency.agencyName}`);

      // Check models for this agency
      const agencyModels = await Model.find({ agencyId: agencyId });
      console.log(`📊 Models for this agency: ${agencyModels.length}`);

      if (agencyModels.length > 0) {
        console.log("\n👥 Agency models:");
        agencyModels.forEach((model, index) => {
          console.log(
            `${index + 1}. ${model.firstName} ${model.lastName} (ID: ${
              model._id
            })`,
          );
        });

        // Check personas for this agency
        const agencyPersonas = await PersonaProfile.find({
          agencyId: agencyId,
        });
        console.log(`\n🎭 Personas for this agency: ${agencyPersonas.length}`);

        // Check personas for each model
        for (const model of agencyModels) {
          const modelPersonas = await PersonaProfile.find({
            modelId: model._id,
          });
          console.log(
            `🎭 Personas for ${model.firstName}: ${modelPersonas.length}`,
          );
        }
      }
    } else {
      console.log(`❌ Agency with ID ${agencyId} not found`);
    }

    // 4. Try to create a test persona to see if there are validation issues
    console.log("\n🧪 Testing persona creation...");

    if (totalModels > 0) {
      const testModel = await Model.findOne({});
      console.log(
        `Using test model: ${testModel.firstName} ${testModel.lastName}`,
      );

      const testPersonaData = {
        modelId: testModel._id,
        agencyId: testModel.agencyId,
        createdBy: testModel.agencyId, // Use agency ID as creator
        createdByModel: "Agency", // Required enum value
        aiEngineUsed: "manual", // Required enum value
        version: 1,
        personaText:
          "This is a test persona for a fitness and lifestyle model who focuses on health, wellness, and inspiring others to live their best lives.",
        tags: ["fitness", "lifestyle", "wellness"],
        scoring: {
          completeness: 85,
          consistency: 90,
          uniqueness: 80,
        },
        communicationStyle: {
          tone: "Inspirational", // Valid enum value
          emojiUsage: "Moderate emoji usage",
          hashtagStrategy: "Niche-specific", // Valid enum value
        },
        demographics: {
          age: "25-30",
          gender: "female",
          location: "Urban areas",
        },
        interests: ["fitness", "nutrition", "yoga", "outdoor activities"],
        goals: ["Promote healthy living", "Build fitness community"],
        psychographics: {
          values: ["Authentic", "Creative", "Balanced"], // Valid enum values
          personality: ["energetic", "positive"],
        },
      };

      try {
        console.log("📝 Creating test persona...");
        const testPersona = new PersonaProfile(testPersonaData);
        await testPersona.save();
        console.log("✅ Test persona created successfully!");
        console.log(`   ID: ${testPersona._id}`);

        // Try to fetch it back
        const fetchedPersona = await PersonaProfile.findById(testPersona._id);
        if (fetchedPersona) {
          console.log("✅ Test persona can be fetched back from database");

          // Clean up - delete test persona
          await PersonaProfile.findByIdAndDelete(testPersona._id);
          console.log("🧹 Test persona cleaned up");
        } else {
          console.log("❌ Test persona was not saved properly");
        }
      } catch (error) {
        console.log("❌ Error creating test persona:");
        console.error(error.message);
        if (error.errors) {
          Object.keys(error.errors).forEach((field) => {
            console.error(`  ${field}: ${error.errors[field].message}`);
          });
        }
      }
    }
  } catch (error) {
    console.error("❌ Error during debugging:", error);
  } finally {
    await mongoose.disconnect();
    console.log("🔌 Disconnected from MongoDB");
  }
}

debugPersonaIssue();
