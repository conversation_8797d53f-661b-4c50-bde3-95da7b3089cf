import * as React from "react";
import { cn } from "@/lib/utils";

const StatCard = ({
  title,
  value,
  subtitle,
  icon: Icon,
  trend,
  className,
  onClick,
  loading = false,
  ...props
}) => {
  // Trend color based on positive/negative
  const getTrendColor = (trendValue) => {
    if (!trendValue) return "";
    return trendValue > 0 ? "text-green-400" : "text-red-400";
  };

  return (
    <div
      className={cn(
        "bg-[#1a1a1a] border border-gray-800 rounded-lg p-6 hover:border-gray-700 transition-colors cursor-pointer h-full flex flex-col justify-between min-h-[120px]",
        className
      )}
      onClick={onClick}
      {...props}
    >
      {/* Header with title and icon */}
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-xs font-medium text-gray-400 uppercase tracking-wide">
          {title}
        </h3>
        {Icon && <Icon className="h-4 w-4 text-gray-500" />}
      </div>

      {/* Main value */}
      <div className="space-y-1">
        {loading ? (
          <div className="h-8 w-24 bg-gray-700 animate-pulse rounded" />
        ) : (
          <p className="text-lg font-medium text-white">{value || "---"}</p>
        )}

        {/* Subtitle */}
        {subtitle && <p className="text-xs text-gray-500">{subtitle}</p>}
      </div>

      {/* Trend indicator */}
      {trend && (
        <div className="flex items-center gap-1 mt-2">
          <span className={cn("text-sm font-semibold", getTrendColor(trend))}>
            {trend > 0 ? "+" : ""}
            {trend}%
          </span>
          <span className="text-sm text-gray-500">vs last period</span>
        </div>
      )}
    </div>
  );
};

export default StatCard;
