import Agency from "../models/agency.js";
import Employee from "../models/Employee/Employee.js";
import ModelUser from "../models/model.js";

export const loginUserProfile = async (req, res) => {
  const { _id, role } = req.user;

  try {
    let user = null;

    if (role === "agency") {
      user = await Agency.findById(_id).lean();
    } else if (role === "model") {
      user = await ModelUser.findById(_id).lean();
    } else if (role === "member") {
      user = await Employee.findById(_id).lean();
    }

    if (!user) return res.status(404).json({ error: "User not found" });

    // Optionally remove sensitive info
    // delete user.password;

    res.json({ user });
  } catch (err) {
    console.error("Error in /me:", err);
    res.status(500).json({ error: "Server error" });
  }
};
