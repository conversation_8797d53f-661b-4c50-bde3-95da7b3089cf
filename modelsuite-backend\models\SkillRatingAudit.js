import mongoose from "mongoose";

const skillRatingAuditSchema = new mongoose.Schema(
  {
    user_skill_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "UserSkill",
      required: true,
    },
    old_level: {
      type: Number,
      required: true,
    },
    new_level: {
      type: Number,
      required: true,
    },
    rated_by: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "UserProfile",
      required: true,
    },
    rating_reason: {
      type: String,
    },
    timestamp: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  },
);

export default mongoose.model("SkillRatingAudit", skillRatingAuditSchema);
