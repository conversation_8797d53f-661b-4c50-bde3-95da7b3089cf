import bcrypt from "bcryptjs";
import crypto from "crypto";
import Employee from "../../models/Employee/Employee.js";
import EmployeeInvite from "../../models/Employee/EmployeeInvite.js";
import { generateRandomPassword } from "../../utils/randomPass.js";
import {
  sendEmployeeWelcomeEmail,
  sendEmployeeInviteEmail,
} from "../../utils/sendOtpToEmail.js";
import {
  getAvailableRoles,
  isValidPermission,
  ALL_PERMISSIONS,
  ROLE_PERMISSIONS,
} from "../../config/permissions.js";

export const inviteEmployee = async (req, res) => {
  try {
    const { email, role, customPermissions = [] } = req.body;

    // Validate input
    if (!email || !role) {
      return res.status(400).json({ message: "Email and role are required" });
    }

    // Validate role (new permission system)
    const availableRoles = getAvailableRoles();
    const employeeRole = role;
    if (!availableRoles.includes(employeeRole)) {
      return res.status(400).json({ message: "Invalid role" });
    }

    // Validate custom permissions
    if (customPermissions.length > 0) {
      const invalidPermissions = customPermissions.filter(
        (permission) => !isValidPermission(permission),
      );
      if (invalidPermissions.length > 0) {
        return res.status(400).json({
          message: `Invalid permissions: ${invalidPermissions.join(", ")}`,
        });
      }
    }

    // Get agency ID based on the authenticated user
    const agencyId =
      req.user.role === "employee" ? req.user.agencyId : req.user._id;

    // Check for existing invite
    const existingInvite = await EmployeeInvite.findOne({
      email: email.toLowerCase(),
      agencyId,
      status: "pending",
    });

    if (existingInvite) {
      return res.status(400).json({
        message: "An invite has already been sent to this email",
      });
    }

    // Generate secure token and expiry
    const token = crypto.randomBytes(32).toString("hex");
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours from now

    // Create new invite
    const invite = await EmployeeInvite.create({
      email: email.toLowerCase(),
      role: employeeRole,
      customPermissions,
      token,
      expiresAt,
      status: "pending",
      agencyId,
    });

    // Send invite email
    const activationLink = `${process.env.FRONTEND_HOSTING_BASEURL}/employee/activate?token=${token}`;
    await sendEmployeeInviteEmail(email, activationLink);

    res.status(201).json({
      message: "Invite sent successfully",
      email: invite.email,
      role: employeeRole,
      customPermissions,
    });
  } catch (err) {
    console.error("inviteEmployee error:", err);
    res.status(500).json({ message: "Failed to send invite" });
  }
};

export const activateEmployee = async (req, res) => {
  try {
    const { token, name, password } = req.body;

    if (!token || !name || !password) {
      return res.status(400).json({ message: "All fields are required" });
    }

    const invite = await EmployeeInvite.findOne({
      token,
      status: "pending",
      expiresAt: { $gt: new Date() },
    });

    if (!invite) {
      return res.status(400).json({
        message: "Invalid or expired invitation link",
      });
    }

    // Check if email is already registered
    const existingEmployee = await Employee.findOne({ email: invite.email });
    if (existingEmployee) {
      return res.status(400).json({
        message: "An account with this email already exists",
      });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create employee account
    const newEmployee = await Employee.create({
      name,
      email: invite.email,
      password: hashedPassword,
      userType: "Employee", // Set userType to 'Employee'
      role: invite.role,
      customPermissions: invite.customPermissions || [],
      agencyId: invite.agencyId,
      status: "active",
    });

    // Mark invite as used
    invite.status = "used";
    await invite.save();

    // Generate tokens for immediate login with permissions
    const { accessToken, refreshToken } =
      await newEmployee.generateAccessAndRefreshTokens();

    res.status(201).json({
      message: "Account activated successfully",
      accessToken,
      refreshToken,
      user: {
        _id: newEmployee._id,
        name: newEmployee.name,
        email: newEmployee.email,
        role: newEmployee.role,
      },
    });
  } catch (err) {
    console.error("activateEmployee error:", err);
    res.status(500).json({ message: "Failed to activate account" });
  }
};

// Get all employees for an agency
export const getEmployees = async (req, res) => {
  try {
    const agencyId =
      req.user.role === "employee" ? req.user.agencyId : req.user._id;

    const employees = await Employee.find({ agencyId })
      .select("name email role status createdAt")
      .sort({ createdAt: -1 });

    res.status(200).json(employees);
  } catch (err) {
    console.error("getEmployees error:", err);
    res.status(500).json({ message: "Failed to fetch employees" });
  }
};

// Search employees
export const searchEmployees = async (req, res) => {
  try {
    const { query } = req.query;
    const agencyId =
      req.user.role === "employee" ? req.user.agencyId : req.user._id;

    if (!query) {
      return res.status(400).json({ message: "Search query is required" });
    }

    const searchRegex = new RegExp(query, "i");

    const employees = await Employee.find({
      agencyId,
      $or: [
        { name: searchRegex },
        { email: searchRegex },
        { role: searchRegex },
      ],
    })
      .select("name email role status createdAt")
      .sort({ createdAt: -1 })
      .limit(10);

    res.status(200).json(employees);
  } catch (err) {
    console.error("searchEmployees error:", err);
    res.status(500).json({ message: "Failed to search employees" });
  }
};

// Create employee directly (for admin use)
export const createEmployee = async (req, res) => {
  try {
    const { name, email, role, customPermissions } = req.body;

    // Validate input
    if (!name || !email || !role) {
      return res
        .status(400)
        .json({ message: "Name, email, and role are required" });
    }

    // Validate role
    const validRoles = [
      "creator",
      "manager",
      "viewer",
      "admin",
      "financial_manager",
      "content_moderator",
      "support_agent",
      "auditor",
    ];
    if (!validRoles.includes(role)) {
      return res.status(400).json({ message: "Invalid role" });
    }

    // Validate custom permissions if provided
    if (customPermissions && Array.isArray(customPermissions)) {
      const invalidPermissions = customPermissions.filter(
        (permission) => !isValidPermission(permission),
      );
      if (invalidPermissions.length > 0) {
        return res.status(400).json({
          message: `Invalid permissions: ${invalidPermissions.join(", ")}`,
        });
      }
    }

    // Get agency ID
    const agencyId =
      req.user.role === "employee" ? req.user.agencyId : req.user._id;

    // Check if employee already exists
    const existingEmployee = await Employee.findOne({
      email: email.toLowerCase(),
    });
    if (existingEmployee) {
      return res
        .status(400)
        .json({ message: "Employee with this email already exists" });
    }

    // Generate random password
    const randomPassword = generateRandomPassword();
    const hashedPassword = await bcrypt.hash(randomPassword, 12);

    // Create employee
    const newEmployee = await Employee.create({
      name,
      email: email.toLowerCase(),
      password: hashedPassword,
      userType: "Employee", // Set userType to 'Employee'
      role,
      agencyId,
      customPermissions: customPermissions || [], // Use customPermissions instead of permissions
      status: "active",
    });

    // Send welcome email with credentials
    try {
      await sendEmployeeWelcomeEmail(email, name, randomPassword);
    } catch (emailError) {
      console.error("Failed to send welcome email:", emailError);
      // Don't fail the creation if email fails
    }

    res.status(201).json({
      message: "Employee created successfully",
      employee: {
        _id: newEmployee._id,
        name: newEmployee.name,
        email: newEmployee.email,
        role: newEmployee.role,
        status: newEmployee.status,
      },
    });
  } catch (err) {
    console.error("createEmployee error:", err);
    res.status(500).json({ message: "Failed to create employee" });
  }
};

export const deleteEmployee = async (req, res) => {
  try {
    const { id } = req.params;

    const employee = await Employee.findById(id);
    if (!employee) {
      return res.status(404).json({ message: "Employee not found" });
    }

    // Optional: ensure agency owns the employee
    const agencyId =
      req.user.role === "employee" ? req.user.agencyId : req.user._id;
    if (String(employee.agencyId) !== String(agencyId)) {
      return res.status(403).json({ message: "Unauthorized" });
    }

    // Delete the employee
    await employee.deleteOne();

    // 💥 Also delete the invite (if any) to unblock re-invites
    await EmployeeInvite.findOneAndDelete({
      email: employee.email,
      agencyId: employee.agencyId,
    });

    res
      .status(200)
      .json({ message: "Employee and invite deleted successfully" });
  } catch (err) {
    console.error("Delete Employee Error:", err);
    res.status(500).json({ message: "Server error" });
  }
};

export const deletePendingInvite = async (req, res) => {
  try {
    const { email } = req.params;

    const invite = await EmployeeInvite.findOneAndDelete({
      email,
      agencyId: req.user.role === "employee" ? req.user.agencyId : req.user._id,
      status: "pending",
    });

    if (!invite) {
      return res.status(404).json({ message: "No pending invite found" });
    }

    res.status(200).json({ message: "Invite deleted successfully" });
  } catch (err) {
    console.error("Delete Invite Error:", err);
    res.status(500).json({ message: "Server error" });
  }
};

// Get available roles and permissions for frontend
export const getPermissionsAndRoles = async (req, res) => {
  try {
    const availableRoles = getAvailableRoles();

    // Group permissions by category for better UI organization
    const permissionCategories = {
      "Task Management": [
        {
          key: "tasks.view",
          label: "View Tasks",
          description: ALL_PERMISSIONS["tasks.view"],
        },
        {
          key: "tasks.create",
          label: "Create Tasks",
          description: ALL_PERMISSIONS["tasks.create"],
        },
        {
          key: "tasks.edit",
          label: "Edit Tasks",
          description: ALL_PERMISSIONS["tasks.edit"],
        },
        {
          key: "tasks.delete",
          label: "Delete Tasks",
          description: ALL_PERMISSIONS["tasks.delete"],
        },
        {
          key: "tasks.assign",
          label: "Assign Tasks",
          description: ALL_PERMISSIONS["tasks.assign"],
        },
        {
          key: "tasks.approve",
          label: "Approve Tasks",
          description: ALL_PERMISSIONS["tasks.approve"],
        },
      ],
      "Content Management": [
        {
          key: "uploads.view",
          label: "View Uploads",
          description: ALL_PERMISSIONS["uploads.view"],
        },
        {
          key: "uploads.create",
          label: "Create Uploads",
          description: ALL_PERMISSIONS["uploads.create"],
        },
        {
          key: "uploads.edit",
          label: "Edit Uploads",
          description: ALL_PERMISSIONS["uploads.edit"],
        },
        {
          key: "uploads.delete",
          label: "Delete Uploads",
          description: ALL_PERMISSIONS["uploads.delete"],
        },
        {
          key: "uploads.review",
          label: "Review Uploads",
          description: ALL_PERMISSIONS["uploads.review"],
        },
      ],
      "Calendar Management": [
        {
          key: "calendar.view",
          label: "View Calendar",
          description: ALL_PERMISSIONS["calendar.view"],
        },
        {
          key: "calendar.create",
          label: "Create Calendar Events",
          description: ALL_PERMISSIONS["calendar.create"],
        },
        {
          key: "calendar.edit",
          label: "Edit Calendar Events",
          description: ALL_PERMISSIONS["calendar.edit"],
        },
      ],
      Communication: [
        {
          key: "messages.view",
          label: "View Messages",
          description: ALL_PERMISSIONS["messages.view"],
        },
        {
          key: "messages.create",
          label: "Create Messages",
          description: ALL_PERMISSIONS["messages.create"],
        },
        {
          key: "messages.edit",
          label: "Edit Messages",
          description: ALL_PERMISSIONS["messages.edit"],
        },
        {
          key: "messages.delete",
          label: "Delete Messages",
          description: ALL_PERMISSIONS["messages.delete"],
        },
        {
          key: "messages.send",
          label: "Send Messages",
          description: ALL_PERMISSIONS["messages.send"],
        },
        {
          key: "messages.manage",
          label: "Manage Messages",
          description: ALL_PERMISSIONS["messages.manage"],
        },
      ],
      "Notes Management": [
        {
          key: "notes.view",
          label: "View Notes",
          description: ALL_PERMISSIONS["notes.view"],
        },
        {
          key: "notes.create",
          label: "Create Notes",
          description: ALL_PERMISSIONS["notes.create"],
        },
        {
          key: "notes.edit",
          label: "Edit Notes",
          description: ALL_PERMISSIONS["notes.edit"],
        },
        {
          key: "notes.delete",
          label: "Delete Notes",
          description: ALL_PERMISSIONS["notes.delete"],
        },
      ],
      "Campaign Management": [
        {
          key: "campaign.view",
          label: "View Campaigns",
          description: ALL_PERMISSIONS["campaign.view"],
        },
        {
          key: "campaign.assign",
          label: "Assign Campaigns",
          description: ALL_PERMISSIONS["campaign.assign"],
        },
      ],
      Financial: [
        {
          key: "earnings.view",
          label: "View Earnings",
          description: ALL_PERMISSIONS["earnings.view"],
        },
        {
          key: "earnings.manage",
          label: "Manage Earnings",
          description: ALL_PERMISSIONS["earnings.manage"],
        },
        {
          key: "contracts.view",
          label: "View Contracts",
          description: ALL_PERMISSIONS["contracts.view"],
        },
        {
          key: "contracts.create",
          label: "Create Contracts",
          description: ALL_PERMISSIONS["contracts.create"],
        },
        {
          key: "contracts.manage",
          label: "Manage Contracts",
          description: ALL_PERMISSIONS["contracts.manage"],
        },
      ],
      "Support System": [
        {
          key: "support.view",
          label: "View Support Tickets",
          description: ALL_PERMISSIONS["support.view"],
        },
        {
          key: "support.create",
          label: "Create Support Tickets",
          description: ALL_PERMISSIONS["support.create"],
        },
        {
          key: "support.edit",
          label: "Edit Support Tickets",
          description: ALL_PERMISSIONS["support.edit"],
        },
        {
          key: "support.delete",
          label: "Delete Support Tickets",
          description: ALL_PERMISSIONS["support.delete"],
        },
      ],
      "Questionnaire Management": [
        {
          key: "questionnaire.view",
          label: "View Questionnaires",
          description: ALL_PERMISSIONS["questionnaire.view"],
        },
        {
          key: "questionnaire.create",
          label: "Create Questionnaires",
          description: ALL_PERMISSIONS["questionnaire.create"],
        },
        {
          key: "questionnaire.edit",
          label: "Edit Questionnaires",
          description: ALL_PERMISSIONS["questionnaire.edit"],
        },
        {
          key: "questionnaire.delete",
          label: "Delete Questionnaires",
          description: ALL_PERMISSIONS["questionnaire.delete"],
        },
        {
          key: "questionnaire.assign",
          label: "Assign Questionnaires",
          description: ALL_PERMISSIONS["questionnaire.assign"],
        },
      ],
      "Social Media": [
        {
          key: "viral_trends.view",
          label: "View Viral Trends",
          description: ALL_PERMISSIONS["viral_trends.view"],
        },
        {
          key: "viral_trends.manage",
          label: "Manage Viral Trends",
          description: ALL_PERMISSIONS["viral_trends.manage"],
        },
        {
          key: "social_media.view",
          label: "View Social Media",
          description: ALL_PERMISSIONS["social_media.view"],
        },
        {
          key: "social_media.manage",
          label: "Manage Social Media",
          description: ALL_PERMISSIONS["social_media.manage"],
        },
      ],
      "Profile Management": [
        {
          key: "profile.view",
          label: "View Profiles",
          description: ALL_PERMISSIONS["profile.view"],
        },
        {
          key: "profile.create",
          label: "Create Profiles",
          description: ALL_PERMISSIONS["profile.create"],
        },
        {
          key: "profile.edit",
          label: "Edit Profiles",
          description: ALL_PERMISSIONS["profile.edit"],
        },
      ],
      Administration: [
        {
          key: "employee.view",
          label: "View Employees",
          description: ALL_PERMISSIONS["employee.view"],
        },
        {
          key: "employee.invite",
          label: "Invite Employees",
          description: ALL_PERMISSIONS["employee.invite"],
        },
        {
          key: "employee.edit",
          label: "Edit Employees",
          description: ALL_PERMISSIONS["employee.edit"],
        },
        {
          key: "employee.delete",
          label: "Delete Employees",
          description: ALL_PERMISSIONS["employee.delete"],
        },
        {
          key: "performance.view",
          label: "View Performance",
          description: ALL_PERMISSIONS["performance.view"],
        },
        {
          key: "accesslog.view",
          label: "View Access Logs",
          description: ALL_PERMISSIONS["accesslog.view"],
        },
      ],
      Authentication: [
        {
          key: "auth.login",
          label: "Login Access",
          description: ALL_PERMISSIONS["auth.login"],
        },
      ],
    };

    // Role information with descriptions
    const roleInfo = {
      viewer: { label: "Viewer", description: "Can view content only" },
      creator: {
        label: "Creator",
        description: "Can create and manage content",
      },
      manager: { label: "Manager", description: "Can manage team and content" },
      admin: { label: "Admin", description: "Full administrative access" },
      financial_manager: {
        label: "Financial Manager",
        description: "Manages financial aspects",
      },
      content_moderator: {
        label: "Content Moderator",
        description: "Moderates and reviews content",
      },
      support_agent: {
        label: "Support Agent",
        description: "Handles customer support",
      },
      auditor: {
        label: "Auditor",
        description: "Audits and reviews activities",
      },
    };

    res.status(200).json({
      roles: availableRoles.map((role) => ({
        value: role,
        label: roleInfo[role]?.label || role,
        description: roleInfo[role]?.description || "",
        defaultPermissions: ROLE_PERMISSIONS[role] || [],
      })),
      permissionCategories,
      allPermissions: ALL_PERMISSIONS,
    });
  } catch (err) {
    console.error("getPermissionsAndRoles error:", err);
    res.status(500).json({ message: "Failed to fetch permissions and roles" });
  }
};
