import { configureStore } from "@reduxjs/toolkit";
import authSlice from "./features/auth/loginSlice";
import dashboardSlice from "./features/dashboard/dashboardSlice";
import fetchModelListSlice from "./features/models/fetchModelListSlice";
import inviteModelSlice from "./features/models/inviteModelSlice";
import searchAllModelsSlice from "./features/models/searchAllModelsSlice";
import questionnaireSlice from "./features/questionnaire/questionnaireSlice";
import modelsSlice from "./features/models/modelsSlice";
import notesSlice from "./features/notes/notesSlice";
import widgetSidebarSlice from "./features/widgetSidebar/widgetSidebarSlice";

export const store = configureStore({
  reducer: {
    authReducer: authSlice,
    dashboardReducer: dashboardSlice,
    fetchModelListReducer: fetchModelListSlice,
    inviteModelReducer: inviteModelSlice,
    searchAllModelsReducer: searchAllModelsSlice,
    questionnaireReducer: questionnaireSlice,
    modelsReducer: modelsSlice,
    notesReducer: notesSlice,
    widgetSidebarReducer: widgetSidebarSlice,
  },
});
