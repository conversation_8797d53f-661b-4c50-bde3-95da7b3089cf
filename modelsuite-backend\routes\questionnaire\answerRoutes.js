import express from "express";
import {
  submitTemplateAnswers,
  getModelAnswers,
  getMyAnswers,
  getTemplateAnswers,
} from "../../controllers/questionnaire/answerController.js";
import { verifyToken } from "../../middlewares/authMiddleware.js";
import checkPermission, {
  checkLegacyPermission,
} from "../../middlewares/permissionCheck.js";

const router = express.Router();

// Model submits answers
router.post(
  "/",
  verifyToken,
  checkLegacyPermission("questionnaire", "create"),
  submitTemplateAnswers,
);

// Model fetches their own answers for a template
router.get(
  "/template/:templateId",
  verifyToken,
  checkLegacyPermission("questionnaire", "view"),
  getMyAnswers,
);

// Agency gets all answers for a template (for analytics)
router.get(
  "/analytics/:templateId",
  verifyToken,
  checkLegacyPermission("questionnaire", "view"),
  getTemplateAnswers,
);

// Agency views a model's answers for a template
router.get(
  "/:modelId/:templateId",
  verifyToken,
  checkLegacyPermission("questionnaire", "view"),
  getModelAnswers,
);

export default router;
