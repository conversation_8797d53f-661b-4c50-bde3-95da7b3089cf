import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axiosInstance from "@/config/axiosInstance";

// Login thunk for both agency and model users.
export const loginUser = createAsyncThunk(
  "loginUser",
  async ({ identifier, password, userType }, { rejectWithValue }) => {
    try {
      const endpoint = userType === "agency" ? "/agency/login" : "/model/login";

      const response = await axiosInstance.post(endpoint, {
        identifier,
        password,
      });

      // Save token only in localStorage
      localStorage.setItem("token", response.data.token);

      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.error || "Login failed");
    }
  }
);

// OTP verification thunk - verifies OTP and stores token.
export const verifyOTP = createAsyncThunk(
  "verifyOTP",
  async ({ identifier, otp, userType }, { rejectWithValue }) => {
    try {
      const endpoint =
        userType === "agency"
          ? "/agency/login/verify-otp"
          : "/model/login/verify-otp";

      const response = await axiosInstance.post(endpoint, {
        identifier,
        otp,
      });

      // Save token only in localStorage
      localStorage.setItem("token", response.data.token);

      return response.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.error || "OTP verification failed"
      );
    }
  }
);

// Fetch current user using token from localStorage.
export const fetchMe = createAsyncThunk(
  "fetchMe",
  async (_, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.get("/me/");
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.error || "Failed to fetch user"
      );
    }
  }
);

const loadInitialState = () => {
  const token = localStorage.getItem("token");

  return {
    user: null,
    token: token || null,
    isAuthenticated: !!token,
    permissions: [],
    role: null,
    loading: false,
    successMsg: null,
    error: null,
    otpRequired: false,
    pendingData: null,
    lastPermissionUpdate: null,
  };
};

const authSlice = createSlice({
  name: "auth",
  initialState: loadInitialState(),
  reducers: {
    // Logs out user and clears localStorage + Redux state.
    logout: (state) => {
      state.user = null;
      state.token = null;
      state.isAuthenticated = false;
      state.permissions = [];
      state.role = null;
      state.error = null;
      state.otpRequired = false;
      state.pendingData = null;

      localStorage.removeItem("token");
    },

    clearError: (state) => {
      state.error = null;
    },

    setOTPRequired: (state, action) => {
      state.otpRequired = true;
      state.pendingData = action.payload;
    },

    clearOTPState: (state) => {
      state.otpRequired = false;
      state.pendingData = null;
    },

    forcePermissionUpdate: (state) => {
      state.lastPermissionUpdate = Date.now();
      if (state.user) {
        state.permissions =
          state.user.effectivePermissions || state.user.permissions || [];
      }
    },
  },

  extraReducers: (builder) => {
    builder

      .addCase(loginUser.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.loading = false;

        if (!action.payload.token || !action.payload.user) {
          state.otpRequired = true;
          state.pendingData = { identifier: action.meta.arg.identifier };
          return;
        }
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.isAuthenticated = true;
        state.otpRequired = false;
        state.pendingData = null;
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // OTP Verification
      .addCase(verifyOTP.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(verifyOTP.fulfilled, (state, action) => {
        state.loading = false;
        state.token = action.payload.token;
        state.isAuthenticated = true;
        state.otpRequired = false;
        state.pendingData = null;
      })
      .addCase(verifyOTP.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch Me
      .addCase(fetchMe.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchMe.fulfilled, (state, action) => {
        state.loading = false;
        state.user = action.payload.user;
        state.isAuthenticated = true;
        state.permissions =
          action.payload.user?.effectivePermissions ||
          action.payload.user?.permissions ||
          [];
        state.role = action.payload.user?.role;
        state.successMsg = "Login Success";
      })
      .addCase(fetchMe.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.user = null;
        state.token = null;
        state.isAuthenticated = false;
        state.permissions = [];
        state.role = null;
        localStorage.removeItem("token");
      });
  },
});

export const {
  logout,
  clearError,
  setOTPRequired,
  clearOTPState,
  forcePermissionUpdate,
} = authSlice.actions;

export default authSlice.reducer;
