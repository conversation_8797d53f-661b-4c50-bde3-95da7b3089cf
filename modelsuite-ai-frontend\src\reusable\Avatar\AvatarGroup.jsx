import * as React from "react";
import { cn } from "@/lib/utils";
import { User, Plus } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  <PERSON>ltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

const AvatarGroup = ({
  avatars = [],
  max = 5,
  size = "default", // "xs", "sm", "default", "lg", "xl"
  showAddButton = false,
  showTooltips = true,
  showCounter = true,
  spacing = "default", // "tight", "default", "loose"
  direction = "horizontal", // "horizontal", "vertical"
  onAddClick,
  onAvatarClick,
  className,
  avatarClassName,
  counterClassName,
  addButtonClassName,
  ...props
}) => {
  // Size configurations
  const sizeConfig = {
    xs: {
      avatar: "h-6 w-6",
      text: "text-xs",
      fallback: "text-xs",
      addButton: "h-6 w-6 text-xs",
      counter: "h-6 w-6 text-xs",
    },
    sm: {
      avatar: "h-8 w-8",
      text: "text-sm",
      fallback: "text-sm",
      addButton: "h-8 w-8 text-sm",
      counter: "h-8 w-8 text-sm",
    },
    default: {
      avatar: "h-10 w-10",
      text: "text-sm",
      fallback: "text-sm",
      addButton: "h-10 w-10 text-sm",
      counter: "h-10 w-10 text-sm",
    },
    lg: {
      avatar: "h-12 w-12",
      text: "text-base",
      fallback: "text-base",
      addButton: "h-12 w-12 text-base",
      counter: "h-12 w-12 text-base",
    },
    xl: {
      avatar: "h-16 w-16",
      text: "text-lg",
      fallback: "text-lg",
      addButton: "h-16 w-16 text-lg",
      counter: "h-16 w-16 text-lg",
    },
  };

  const sizes = sizeConfig[size] || sizeConfig.default;

  // Spacing configurations
  const spacingConfig = {
    tight: direction === "horizontal" ? "-space-x-2" : "-space-y-2",
    default: direction === "horizontal" ? "-space-x-1" : "-space-y-1",
    loose: direction === "horizontal" ? "space-x-1" : "space-y-1",
  };

  const spacingClass = spacingConfig[spacing] || spacingConfig.default;

  // Calculate visible avatars and remaining count
  const visibleAvatars = avatars.slice(0, max);
  const remainingCount = Math.max(0, avatars.length - max);

  // Generate fallback initials
  const getInitials = (name) => {
    if (!name) return "U";
    return name
      .split(" ")
      .map((part) => part.charAt(0))
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  // Generate avatar colors based on name
  const getAvatarColor = (name, index) => {
    const colors = [
      "bg-gray-800 text-gray-300",
      "bg-gray-700 text-gray-300",
      "bg-gray-600 text-gray-300",
      "bg-gray-800 text-gray-400",
      "bg-gray-700 text-gray-400",
    ];

    if (name) {
      const charCode = name.charCodeAt(0);
      return colors[charCode % colors.length];
    }

    return colors[index % colors.length];
  };

  // Handle avatar click
  const handleAvatarClick = (avatar, index) => {
    if (onAvatarClick) {
      onAvatarClick(avatar, index);
    }
  };

  // Render individual avatar
  const renderAvatar = (avatar, index) => {
    const avatarElement = (
      <Avatar
        key={avatar.id || index}
        className={cn(
          sizes.avatar,
          "ring-2 ring-[#0f0f0f] cursor-pointer transition-transform hover:scale-105",
          "hover:z-10 relative",
          onAvatarClick && "cursor-pointer",
          avatarClassName
        )}
        onClick={() => handleAvatarClick(avatar, index)}
      >
        <AvatarImage
          src={avatar.src || avatar.image || avatar.avatar}
          alt={avatar.name || avatar.alt || `Avatar ${index + 1}`}
        />
        <AvatarFallback
          className={cn(getAvatarColor(avatar.name, index), sizes.fallback)}
        >
          {avatar.fallback || getInitials(avatar.name) || (
            <User className="h-1/2 w-1/2" />
          )}
        </AvatarFallback>
      </Avatar>
    );

    // Wrap with tooltip if enabled
    if (showTooltips && (avatar.name || avatar.tooltip)) {
      return (
        <Tooltip key={avatar.id || index}>
          <TooltipTrigger asChild>{avatarElement}</TooltipTrigger>
          <TooltipContent className="bg-[#1a1a1a] border-gray-700 text-gray-300">
            <p>{avatar.tooltip || avatar.name || `User ${index + 1}`}</p>
            {avatar.status && (
              <p className="text-xs text-gray-500">{avatar.status}</p>
            )}
          </TooltipContent>
        </Tooltip>
      );
    }

    return avatarElement;
  };

  // Render remaining count
  const renderCounter = () => {
    if (!showCounter || remainingCount <= 0) return null;

    const counterElement = (
      <div
        className={cn(
          "flex items-center justify-center rounded-full ring-2 ring-[#0f0f0f]",
          "bg-gray-800 text-gray-300 font-medium",
          sizes.counter,
          sizes.text,
          counterClassName
        )}
      >
        +{remainingCount}
      </div>
    );

    if (showTooltips) {
      const remainingAvatars = avatars.slice(max);
      const tooltipContent = remainingAvatars
        .map((avatar) => avatar.name || avatar.tooltip || "User")
        .join(", ");

      return (
        <Tooltip>
          <TooltipTrigger asChild>{counterElement}</TooltipTrigger>
          <TooltipContent className="bg-[#1a1a1a] border-gray-700 text-gray-300">
            <p className="max-w-xs">{tooltipContent}</p>
          </TooltipContent>
        </Tooltip>
      );
    }

    return counterElement;
  };

  // Render add button
  const renderAddButton = () => {
    if (!showAddButton) return null;

    return (
      <Button
        variant="outline"
        size="sm"
        className={cn(
          "rounded-full border-2 border-dashed border-gray-600",
          "bg-transparent hover:bg-gray-800 text-gray-500 hover:text-gray-300",
          "hover:border-gray-500 transition-colors",
          sizes.addButton,
          addButtonClassName
        )}
        onClick={onAddClick}
      >
        <Plus className="h-1/2 w-1/2" />
      </Button>
    );
  };

  if (!avatars.length && !showAddButton) {
    return null;
  }

  const containerClass = cn(
    "flex items-center",
    direction === "vertical" ? "flex-col" : "flex-row",
    spacingClass,
    className
  );

  return (
    <TooltipProvider>
      <div className={containerClass} {...props}>
        {/* Visible Avatars */}
        {visibleAvatars.map((avatar, index) => renderAvatar(avatar, index))}

        {/* Remaining Count */}
        {renderCounter()}

        {/* Add Button */}
        {renderAddButton()}
      </div>
    </TooltipProvider>
  );
};

export default AvatarGroup;
