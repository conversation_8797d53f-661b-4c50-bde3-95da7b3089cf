// Centralized permissions configuration
// This file defines all available permissions and default role mappings

// All available permissions in the system
export const ALL_PERMISSIONS = {
  // Task Management
  "tasks.view": "View tasks",
  "tasks.create": "Create tasks",
  "tasks.edit": "Edit tasks",
  "tasks.delete": "Delete tasks",
  "tasks.assign": "Assign tasks",
  "tasks.approve": "Approve tasks",

  // Model Management
  "models.view": "View models",
  "models.create": "Create models",
  "models.edit": "Edit models",
  "models.delete": "Delete models",

  // Content Upload Management
  "uploads.view": "View uploads",
  "uploads.create": "Create uploads",
  "uploads.edit": "Edit uploads",
  "uploads.delete": "Delete uploads",
  "uploads.review": "Review uploads",

  // Calendar Management
  "calendar.view": "View calendar",
  "calendar.create": "Create calendar events",
  "calendar.edit": "Edit calendar events",
  "calendar.delete": "Delete calendar events",

  // Message Management
  "messages.view": "View messages",
  "messages.create": "Create messages",
  "messages.edit": "Edit messages",
  "messages.delete": "Delete messages",
  "messages.send": "Send messages",
  "messages.manage": "Manage messages",

  // Financial Management
  "earnings.view": "View earnings",
  "earnings.manage": "Manage earnings",

  // Campaign Management
  "campaign.view": "View campaigns",
  "campaign.assign": "Assign campaigns",

  // Notes Management
  "notes.view": "View notes",
  "notes.create": "Create notes",
  "notes.edit": "Edit notes",
  "notes.delete": "Delete notes",

  // Performance Analytics
  "performance.view": "View performance",

  // Access Logs
  "accesslog.view": "View access logs",

  // Support System
  "support.view": "View support tickets",
  "support.create": "Create support tickets",
  "support.edit": "Edit support tickets",
  "support.delete": "Delete support tickets",

  // Questionnaire Management
  "questionnaire.view": "View questionnaires",
  "questionnaire.create": "Create questionnaires",
  "questionnaire.edit": "Edit questionnaires",
  "questionnaire.delete": "Delete questionnaires",
  "questionnaire.assign": "Assign questionnaires",

  // Viral Trends
  "viral_trends.view": "View viral trends",
  "viral_trends.manage": "Manage viral trends",

  // Social Media
  "social_media.view": "View social media",
  "social_media.manage": "Manage social media",

  // Contracts
  "contracts.view": "View contracts",
  "contracts.create": "Create contracts",
  "contracts.manage": "Manage contracts",

  // Profile Management
  "profile.view": "View profiles",
  "profile.create": "Create profiles",
  "profile.edit": "Edit profiles",

  // Employee Management
  "employee.view": "View employees",
  "employee.invite": "Invite employees",
  "employee.edit": "Edit employees",
  "employee.delete": "Delete employees",

  // Persona Management
  "persona.view": "View personas",
  "persona.create": "Create personas",
  "persona.edit": "Edit personas",
  "persona.delete": "Delete personas",

  // Shift Management
  shift_management: "Manage shifts",
  "shifts.view": "View shifts",
  "shifts.create": "Create shifts",
  "shifts.edit": "Edit shifts",
  "shifts.delete": "Delete shifts",
  "shifts.assign": "Assign shifts",
  "shifts.stats": "View shift statistics",

  // Shift Report Management
  "shift_reports.view": "View shift reports",
  "shift_reports.create": "Create shift reports",
  "shift_reports.edit": "Edit shift reports",
  "shift_reports.delete": "Delete shift reports",
  "shift_reports.submit": "Submit shift reports",
  "shift_reports.time_tracking": "Use time tracking",

  // Authentication
  "auth.login": "Login access",
};

// Default permissions for each role
export const ROLE_PERMISSIONS = {
  viewer: [
    "tasks.view",
    "models.view",
    "uploads.view",
    "calendar.view",
    "messages.view",
    "notes.view",
    "performance.view",
    "profile.view",
    "shifts.view",
    "shift_reports.view",
    "shift_reports.create",
    "shift_reports.submit",
    "shift_reports.time_tracking",
    "auth.login",
  ],

  creator: [
    "tasks.view",
    "tasks.create",
    "tasks.edit",
    "models.view",
    "models.create",
    "models.edit",
    "uploads.view",
    "uploads.create",
    "uploads.edit",
    "calendar.view",
    "calendar.create",
    "calendar.edit",
    "calendar.delete",
    "messages.view",
    "messages.create",
    "messages.send",
    "notes.view",
    "notes.create",
    "notes.edit",
    "performance.view",
    "profile.view",
    "profile.edit",
    "shifts.view",
    "shift_reports.view",
    "shift_reports.create",
    "shift_reports.submit",
    "shift_reports.time_tracking",
    "auth.login",
  ],

  manager: [
    "tasks.view",
    "tasks.create",
    "tasks.edit",
    "tasks.assign",
    "tasks.approve",
    "models.view",
    "models.create",
    "models.edit",
    "models.delete",
    "uploads.view",
    "uploads.create",
    "uploads.edit",
    "uploads.review",
    "calendar.view",
    "calendar.create",
    "calendar.edit",
    "calendar.delete",
    "messages.view",
    "messages.create",
    "messages.edit",
    "messages.send",
    "messages.manage",
    "campaign.view",
    "campaign.assign",
    "notes.view",
    "notes.create",
    "notes.edit",
    "notes.delete",
    "performance.view",
    "support.view",
    "support.create",
    "support.edit",
    "questionnaire.view",
    "questionnaire.create",
    "questionnaire.edit",
    "questionnaire.assign",
    "profile.view",
    "profile.create",
    "profile.edit",
    "employee.view",
    "shift_management",
    "shifts.view",
    "shifts.create",
    "shifts.edit",
    "shifts.delete",
    "shifts.assign",
    "shifts.stats",
    "shift_reports.view",
    "shift_reports.create",
    "shift_reports.edit",
    "shift_reports.delete",
    "shift_reports.submit",
    "shift_reports.time_tracking",
    "auth.login",
  ],

  admin: [
    // Admin has all permissions
    ...Object.keys(ALL_PERMISSIONS),
  ],

  financial_manager: [
    "tasks.view",
    "models.view",
    "uploads.view",
    "calendar.view",
    "messages.view",
    "earnings.view",
    "earnings.manage",
    "campaign.view",
    "notes.view",
    "notes.create",
    "notes.edit",
    "performance.view",
    "contracts.view",
    "contracts.create",
    "contracts.manage",
    "profile.view",
    "profile.edit",
    "shifts.view",
    "shift_reports.view",
    "shift_reports.create",
    "shift_reports.submit",
    "shift_reports.time_tracking",
    "auth.login",
  ],

  content_moderator: [
    "tasks.view",
    "models.view",
    "models.edit",
    "uploads.view",
    "uploads.edit",
    "uploads.review",
    "calendar.view",
    "messages.view",
    "messages.edit",
    "messages.manage",
    "notes.view",
    "notes.create",
    "notes.edit",
    "performance.view",
    "social_media.view",
    "social_media.manage",
    "viral_trends.view",
    "viral_trends.manage",
    "profile.view",
    "profile.edit",
    "shifts.view",
    "shift_reports.view",
    "shift_reports.create",
    "shift_reports.submit",
    "shift_reports.time_tracking",
    "auth.login",
  ],

  support_agent: [
    "tasks.view",
    "models.view",
    "uploads.view",
    "calendar.view",
    "messages.view",
    "messages.create",
    "messages.send",
    "notes.view",
    "notes.create",
    "notes.edit",
    "support.view",
    "support.create",
    "support.edit",
    "support.delete",
    "questionnaire.view",
    "profile.view",
    "profile.edit",
    "shifts.view",
    "shift_reports.view",
    "shift_reports.create",
    "shift_reports.submit",
    "shift_reports.time_tracking",
    "auth.login",
  ],

  auditor: [
    "tasks.view",
    "models.view",
    "uploads.view",
    "calendar.view",
    "messages.view",
    "earnings.view",
    "campaign.view",
    "notes.view",
    "performance.view",
    "accesslog.view",
    "support.view",
    "questionnaire.view",
    "contracts.view",
    "profile.view",
    "employee.view",
    "shifts.view",
    "shift_reports.view",
    "shift_reports.time_tracking",
    "auth.login",
  ],
};

// Helper function to get default permissions for a role
export const getDefaultPermissions = (role) => {
  return ROLE_PERMISSIONS[role] || [];
};

// Helper function to check if a permission exists
export const isValidPermission = (permission) => {
  // Handle negative permissions (denied permissions)
  if (permission.startsWith("-")) {
    const basePermission = permission.substring(1);
    return Object.keys(ALL_PERMISSIONS).includes(basePermission);
  }
  return Object.keys(ALL_PERMISSIONS).includes(permission);
};

// Helper function to get all available roles
export const getAvailableRoles = () => {
  return Object.keys(ROLE_PERMISSIONS);
};

// System now uses dot notation consistently - no conversion functions needed
