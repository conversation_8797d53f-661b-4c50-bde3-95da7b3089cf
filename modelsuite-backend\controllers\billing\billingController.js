import Invoice from "../../models/billing/invoice.js";
import fs from "fs";
import csv from "fast-csv";
import {
  sendInvoiceEmailToClient,
  sendPaymentStatusEmail,
} from "../../utils/sendOtpToEmail.js"; // update path as per your structure

export const uploadInvoice = async (req, res) => {
  try {
    const { client, campaign, amount, currency, dueDate, status, note } =
      req.body;

    // ✅ Create downloadable file URL
    const fileUrl = req.file.path.replace("/upload/", "/upload/fl_attachment/");

    // ✅ Send fast response immediately
    res.status(201).json({
      success: true,
      message: "Invoice uploaded successfully.",
    });

    // ✅ Do all heavy tasks in background
    process.nextTick(async () => {
      try {
        const invoice = await new Invoice({
          client,
          campaign,
          amount,
          currency,
          dueDate,
          status,
          note,
          fileUrl,
          uploadedBy:
            req.user.role === "employee" ? req.user.agencyId : req.user._id,
        }).save();

        const populatedInvoice = await Invoice.findById(invoice._id).populate([
          {
            path: "client",
            select: "fullName email profilePhoto username _id",
          },
          {
            path: "uploadedBy",
            select: "agencyName agencyEmail username _id",
          },
        ]);

        const { email, fullName } = populatedInvoice.client || {};

        if (email && fullName) {
          await sendInvoiceEmailToClient({
            clientEmail: email,
            clientName: fullName,
            invoiceFileUrl: populatedInvoice.fileUrl,
          });
        }
      } catch (bgError) {
        console.error(
          "❌ Background Task Error (invoice/save/email):",
          bgError,
        );
      }
    });
  } catch (err) {
    console.error("🔥 Upload Invoice Error:", err);
    res.status(500).json({
      success: false,
      message: "Server error while uploading invoice.",
    });
  }
};

export const getInvoices = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const agencyId =
      req.user.role === "employee" ? req.user.agencyId : req.user._id;
    const filter = { uploadedBy: agencyId };

    // ✅ Optional status filter
    if (req.query.status) {
      filter.status = req.query.status; // should be "Paid", "Unpaid", or "Overdue"
    }

    const totalInvoices = await Invoice.countDocuments(filter);

    const invoices = await Invoice.find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate({
        path: "client",
        select: "fullName profilePhoto username _id",
      })
      .populate({
        path: "uploadedBy",
        select: "agencyName agencyEmail username _id",
      });

    res.status(200).json({
      success: true,
      page,
      totalPages: Math.ceil(totalInvoices / limit),
      totalInvoices,
      invoices,
    });
  } catch (err) {
    console.error("Get Invoices Error:", err);
    res.status(500).json({
      success: false,
      message: "Server error while fetching invoices.",
    });
  }
};
export const updateInvoice = async (req, res) => {
  try {
    const invoiceId = req.params.id;
    const agencyId =
      req.user.role === "employee" ? req.user.agencyId : req.user._id; // Logged-in user's ID

    // Allowed fields to update
    const allowedUpdates = [
      "client",
      "campaign",
      "amount",
      "currency",
      "dueDate",
      "status",
      "note",
    ];
    const updateData = {};

    // Only include fields present in request
    allowedUpdates.forEach((field) => {
      if (req.body[field] !== undefined) {
        updateData[field] = req.body[field];
      }
    });

    // Now perform secure update
    const updatedInvoice = await Invoice.findOneAndUpdate(
      { _id: invoiceId, uploadedBy: agencyId }, // Only update if agency matches
      updateData,
      { new: true }, // Return updated document (optional, can be false if you don't need it)
    );

    if (!updatedInvoice) {
      return res.status(404).json({
        success: false,
        message: "Invoice not found or unauthorized.",
      });
    }

    res.status(200).json({
      success: true,
      message: "Invoice updated successfully.",
    });
  } catch (error) {
    console.error("Update Invoice Error:", error);
    res.status(500).json({
      success: false,
      message: "Something went wrong while updating invoice.",
    });
  }
};

export const deleteInvoice = async (req, res) => {
  try {
    const invoiceId = req.params.id;
    const userId =
      req.user.role === "employee" ? req.user.agencyId : req.user._id;

    const deletedInvoice = await Invoice.findOneAndDelete({
      _id: invoiceId,
      uploadedBy: userId,
    });

    if (!deletedInvoice) {
      return res.status(404).json({
        success: false,
        message: "Invoice not found or not authorized to delete.",
      });
    }

    res.status(200).json({
      success: true,
      message: "Invoice deleted successfully.",
    });
  } catch (error) {
    console.error("Delete Invoice Error:", error);
    res.status(500).json({
      success: false,
      message: "Something went wrong while deleting invoice.",
    });
  }
};

export const getInvoiceById = async (req, res) => {
  try {
    const invoiceId = req.params.id;
    const userId =
      req.user.role === "employee" ? req.user.agencyId : req.user._id; // From auth middleware

    const invoice = await Invoice.findOne({
      _id: invoiceId,
      uploadedBy: userId,
    })
      .populate({
        path: "client",
        select: "fullName profilePhoto username _id",
      })
      .populate({
        path: "uploadedBy",
        select: "agencyName agencyEmail username _id",
      });

    if (!invoice) {
      return res.status(404).json({
        success: false,
        message: "Invoice not found or not authorized to access.",
      });
    }

    res.status(200).json({
      success: true,
      data: invoice,
    });
  } catch (error) {
    console.error("Get Invoice Error:", error);
    res.status(500).json({
      success: false,
      message: "Something went wrong while fetching invoice.",
    });
  }
};

export const exportInvoices = async (req, res) => {
  try {
    const userId =
      req.user.role === "employee" ? req.user.agencyId : req.user._id;

    const invoices = await Invoice.find({ uploadedBy: userId })
      .populate({
        path: "client",
        select: "fullName profilePhoto username _id phone email",
      })
      .populate({
        path: "uploadedBy",
        select: "agencyName agencyEmail username _id agencyPhone",
      });

    const csvStream = csv.format({ headers: true });
    const exportDir = "public/files/export";

    if (!fs.existsSync(exportDir)) {
      fs.mkdirSync(exportDir, { recursive: true });
    }

    const filePath = `${exportDir}/invoices.csv`;
    const writableStream = fs.createWriteStream(filePath);
    csvStream.pipe(writableStream);

    invoices.forEach((inv) => {
      const firstAttempt =
        inv.paymentAttempts && inv.paymentAttempts.length > 0
          ? inv.paymentAttempts[0]
          : null;

      csvStream.write({
        InvoiceID: inv._id,
        ClientName: inv.client?.fullName || "-",
        ClientUsername: inv.client?.username || "-",
        ClientEmail: inv.client?.email || "-",
        Campaign: inv.campaign || "-",
        Amount: `${inv.currency}${inv.amount}` || "-",
        Status: inv.status || "-",
        DueDate: inv.dueDate ? new Date(inv.dueDate).toLocaleDateString() : "-",
        UploadedBy: inv.uploadedBy?.agencyName || "-",
        UploaderEmail: inv.uploadedBy?.agencyEmail || "-",
        CreatedAt: inv.createdAt
          ? new Date(inv.createdAt).toLocaleDateString()
          : "-",
        UpdatedAt: inv.updatedAt
          ? new Date(inv.updatedAt).toLocaleDateString()
          : "-",
        AttemptNumber:
          inv.status === "Paid" ? "-" : inv.paymentAttempts?.length || 0,
        AttemptAt:
          inv.status === "Paid"
            ? "-"
            : firstAttempt
              ? new Date(firstAttempt.attemptAt).toLocaleString()
              : "-",
        Success:
          inv.status === "Paid"
            ? "-"
            : firstAttempt
              ? firstAttempt.success
                ? "Yes"
                : "No"
              : "-",
        FailureReason:
          inv.status === "Paid"
            ? "-"
            : firstAttempt && !firstAttempt.success
              ? firstAttempt.reason || "N/A"
              : "-",
      });
    });

    csvStream.end();
    writableStream.on("finish", () => {
      const baseUrl = process.env.SERVER_HOSTING_BASEURL.replace(
        /\/api\/v1\/?$/,
        "",
      );

      res.status(200).json({
        success: true,
        downloadUrl: `${baseUrl}/files/export/invoices.csv`,
      });
    });
  } catch (err) {
    console.error("CSV Export Error:", err);
    res.status(500).json({
      success: false,
      message: "Failed to export invoices",
    });
  }
};

export const mass_payment_status = async (req, res) => {
  try {
    const updates = JSON.parse(req.body.updates);
    const files = req.files;

    const updatedInvoices = [];

    for (const update of updates) {
      const {
        invoiceId,
        transactionMethod,
        transactionStatus,
        screenshotName,
        reason, // ✅ Accept reason from frontend
      } = update;

      // ✅ Validate reason if status is Failed
      if (transactionStatus === "Failed" && (!reason || reason.trim() === "")) {
        return res.status(400).json({
          message: `Failure reason is required for failed transaction on invoice ${invoiceId}`,
        });
      }

      const file = files.find((f) => f.originalname === screenshotName);

      // ✅ Build update object
      const updateData = {
        status: transactionStatus,
        transactionMethod,
        screenshotUrl: file ? file.path : undefined,
        $push: {
          paymentAttempts: {
            success: transactionStatus !== "Failed",
            reason: transactionStatus === "Failed" ? reason : undefined,
            attemptAt: new Date(),
          },
        },
      };

      // ✅ Update invoice
      await Invoice.findByIdAndUpdate(invoiceId, updateData);

      // ✅ Fetch populated invoice
      const populatedInvoice = await Invoice.findById(invoiceId).populate([
        {
          path: "client",
          select: "fullName email profilePhoto username _id",
        },
        {
          path: "uploadedBy",
          select: "agencyName agencyEmail username _id",
        },
      ]);

      updatedInvoices.push(populatedInvoice);

      // ✅ Send email alert in background
      if (populatedInvoice?.client?.email) {
        sendPaymentStatusEmail({
          email: populatedInvoice.client.email,
          name: populatedInvoice.client.fullName,
          status: transactionStatus,
          method: transactionMethod,
          reason,
          invoiceId,
          fileUrl: populatedInvoice.fileUrl,
          screenshotUrl: file ? file.path : undefined,
        }).catch((err) => console.error("Email alert failed:", err.message));
      }
    }

    res.status(200).json({
      message: "Mass update successful",
      data: updatedInvoices,
    });
  } catch (err) {
    console.error("Mass update error:", err);
    res.status(500).json({ message: "Something went wrong" });
  }
};

export const updateSingleInvoiceWithFile = async (req, res) => {
  try {
    const invoiceId = req.params.id;
    const agencyId =
      req.user.role === "employee" ? req.user.agencyId : req.user._id;

    const allowedUpdates = [
      "client",
      "campaign",
      "amount",
      "currency",
      "dueDate",
      "status",
      "note",
      "transactionMethod",
    ];

    const updateData = {};

    allowedUpdates.forEach((field) => {
      if (req.body[field] !== undefined) {
        updateData[field] = req.body[field];
      }
    });

    // Optional screenshot
    if (req.file) {
      updateData.screenshotUrl = req.file.path;
    }

    const updatedInvoice = await Invoice.findOneAndUpdate(
      { _id: invoiceId, uploadedBy: agencyId },
      updateData,
      { new: true },
    );

    if (!updatedInvoice) {
      return res.status(404).json({
        success: false,
        message: "Invoice not found or unauthorized.",
      });
    }

    res.status(200).json({
      success: true,
      message: "Invoice updated successfully.",
      data: updatedInvoice,
    });
  } catch (error) {
    console.error("Update Invoice Error:", error);
    res.status(500).json({
      success: false,
      message: "Something went wrong while updating invoice.",
    });
  }
};

export const getInvoices_dash = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 1000;
    const skip = (page - 1) * limit;

    const filter = { uploadedBy: req.user.id };

    if (req.query.status) {
      filter.status = req.query.status;
    }

    const totalInvoices = await Invoice.countDocuments(filter);

    const invoices = await Invoice.find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate({
        path: "client",
        select: "fullName profilePhoto username _id",
      })
      .populate({
        path: "uploadedBy",
        select: "agencyName agencyEmail username _id",
      });

    // 👉 Exchange rates map
    const exchangeRates = {
      INR: 0.012,
      USD: 1,
      EUR: 1.09,
      GBP: 1.27,
      AED: 0.27,
    };

    // 👉 Append USD-converted amount
    const convertedInvoices = invoices.map((invoice) => {
      const rate = exchangeRates[invoice.currency] || 1;
      const amountInUSD = invoice.amount * rate;

      return {
        ...invoice._doc,
        amountInUSD: Number(amountInUSD.toFixed(2)), // USD converted value
      };
    });

    res.status(200).json({
      success: true,
      page,
      totalPages: Math.ceil(totalInvoices / limit),
      totalInvoices,
      invoices: convertedInvoices,
    });
  } catch (err) {
    console.error("Get Invoices Error:", err);
    res.status(500).json({
      success: false,
      message: "Server error while fetching invoices.",
    });
  }
};

const conversionRateMap = {
  USD: 1,
  INR: 0.012, // 1 INR = 0.012 USD
  EUR: 1.1, // 1 EUR = 1.1 USD
  GBP: 1.3, // 1 GBP = 1.3 USD
};

export const getModelDashboard = async (req, res) => {
  const { modelId } = req.params;

  try {
    const invoices = await Invoice.find({ client: modelId })
      .sort({ createdAt: -1 })
      .populate([
        {
          path: "uploadedBy",
          select: "agencyName agencyEmail",
        },
      ]);

    const paidInvoices = invoices.filter((inv) => inv.status === "Paid");

    const unpaidInvoices = invoices.filter(
      (inv) => inv.status === "Unpaid" || inv.status === "Failed",
    );

    const convertToUSD = (amount, currency) =>
      amount * (conversionRateMap[currency] || 1);

    const thisMonthEarnings = paidInvoices.reduce(
      (sum, inv) => sum + convertToUSD(inv.amount, inv.currency),
      0,
    );

    const pendingPayout = unpaidInvoices.reduce(
      (sum, inv) => sum + convertToUSD(inv.amount, inv.currency),
      0,
    );

    const lastPaidInvoice =
      paidInvoices.length > 0
        ? new Date(paidInvoices[0].createdAt).toDateString()
        : "No payouts yet";

    const nextMethod = paidInvoices[0]?.transactionMethod || "PayPal";

    const summary = {
      thisMonthEarnings,
      pendingPayout,
      lastPayout: lastPaidInvoice,
      nextMethod,
    };

    const taxStatus = {
      status: "Approved",
      docName: "W-8BEN.pdf",
    };

    const activities = invoices.slice(0, 3).map((inv) => ({
      date: inv.createdAt.toDateString(),
      action: `Invoice uploaded for “${inv.campaign}”`,
    }));

    const enrichedInvoices = invoices.map((inv) => {
      let failReason = null;
      if (inv.status === "Failed" && inv.paymentAttempts?.length > 0) {
        failReason = inv.paymentAttempts[0].reason || "Unknown";
      }

      return {
        ...inv._doc,
        uploadedBy: inv.uploadedBy,
        failReason,
      };
    });

    res.json({
      invoices: enrichedInvoices,
      summary: {
        ...summary,
        thisMonthEarnings: `$${thisMonthEarnings.toFixed(2)}`,
        pendingPayout: `$${pendingPayout.toFixed(2)}`,
      },
      taxStatus,
      activities,
    });
  } catch (error) {
    console.error("[Dashboard Error]", error);
    res.status(500).json({ message: "Internal Server Error" });
  }
};

// ⚡ Optimized Get Invoices by Agency
export const getInvoicesByAgency = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const agencyId =
      req.user.role === "employee" ? req.user.agencyId : req.user._id;

    // ✅ Fetch only required fields, skip invoices with no client
    const invoices = await Invoice.find({ uploadedBy: agencyId })
      .select(
        "client campaign amount currency dueDate status fileUrl transactionMethod",
      )
      .populate({
        path: "client",
        select: "fullName email profilePhoto username _id",
      })
      .lean();

    const clientMap = new Map();

    for (const invoice of invoices) {
      if (!invoice.client) {
        console.warn(`⚠ Invoice ${invoice._id} has no client, skipping.`);
        continue; // Skip if client is missing
      }

      const { _id, fullName, email, username, profilePhoto } = invoice.client;
      const clientId = _id.toString();

      if (!clientMap.has(clientId)) {
        clientMap.set(clientId, {
          client: { _id, fullName, email, username, profilePhoto },
          monthlyEarning: 0,
          nextPayment: "Scheduled",
          modelInvoices: [],
          paidTransactionMethods: new Set(),
        });
      }

      const clientData = clientMap.get(clientId);

      clientData.modelInvoices.push({
        _id: invoice._id,
        campaign: invoice.campaign,
        amount: invoice.amount,
        currency: invoice.currency,
        dueDate: invoice.dueDate,
        status: invoice.status,
        fileUrl: invoice.fileUrl,
        transactionMethod: invoice.transactionMethod,
      });

      if (invoice.status === "Paid") {
        const usdAmount =
          invoice.currency === "USD" ? invoice.amount : invoice.amount / 80;
        clientData.monthlyEarning += usdAmount;

        if (invoice.transactionMethod) {
          clientData.paidTransactionMethods.add(invoice.transactionMethod);
        }
      }
    }

    const fullResult = [];
    for (const data of clientMap.values()) {
      data.monthlyEarning = `$${data.monthlyEarning.toFixed(2)}`;
      if (data.paidTransactionMethods.size > 0) {
        data.nextPayment = [...data.paidTransactionMethods][0];
      }
      delete data.paidTransactionMethods;
      fullResult.push(data);
    }

    const totalClients = fullResult.length;
    const paginatedClients = fullResult.slice(skip, skip + limit);

    return res.status(200).json({
      success: true,
      page,
      totalPages: Math.ceil(totalClients / limit),
      totalClients,
      clients: paginatedClients,
    });
  } catch (err) {
    console.error("🔥 GetInvoices Error:", err);
    return res.status(500).json({ error: "Internal Server Error" });
  }
};

export const single_payment_status = async (req, res) => {
  const { invoiceId, transactionMethod, transactionStatus, reason, client } =
    req.body;

  const file = req.file;

  // ✅ Parse client object if sent as string
  let parsedClient = null;
  try {
    parsedClient = typeof client === "string" ? JSON.parse(client) : client;
  } catch (err) {
    console.error("❌ Failed to parse client object:", err);
    return res.status(400).json({ message: "Invalid client object format" });
  }

  // ✅ 1. Fast validation
  if (!invoiceId || !transactionMethod || !transactionStatus) {
    return res.status(400).json({ message: "Missing required fields" });
  }

  if (transactionStatus === "Failed" && (!reason || reason.trim() === "")) {
    return res.status(400).json({
      message: `Failure reason is required for failed transaction on invoice ${invoiceId}`,
    });
  }

  try {
    // ✅ 2. Build update object
    const updateData = {
      status: transactionStatus,
      transactionMethod,
      ...(file && { screenshotUrl: file.path }),
      $push: {
        paymentAttempts: {
          success: transactionStatus !== "Failed",
          ...(transactionStatus === "Failed" && { reason }),
          attemptAt: Date.now(),
        },
      },
    };

    // ✅ 3. Update invoice in DB
    await Invoice.findByIdAndUpdate(invoiceId, updateData);

    // ✅ 4. Respond to client immediately
    res.status(200).json({ message: "Invoice updated successfully" });

    // ✅ 5. Run background task with passed client data
    setImmediate(() =>
      runBackgroundTask({
        client: parsedClient,
        transactionStatus,
        transactionMethod,
        reason,
        invoiceId,
        screenshotUrl: file?.path,
      }),
    );
  } catch (err) {
    console.error("❌ Payment Status Error:", err);
    res.status(500).json({ message: "Something went wrong" });
  }
};

// ✅ Background logic uses passed client info
const runBackgroundTask = async ({
  client,
  transactionStatus,
  transactionMethod,
  reason,
  invoiceId,
  screenshotUrl,
}) => {
  try {
    const invoice = await Invoice.findById(invoiceId).lean();

    if (!client?.email) {
      console.warn("⚠️ No client email found. Skipping email.");
      return;
    }

    await sendPaymentStatusEmail({
      email: client.email,
      name: client.fullName ?? "Client",
      status: transactionStatus,
      method: transactionMethod,
      reason,
      invoiceId,
      fileUrl: invoice?.fileUrl,
      screenshotUrl,
    });

    console.log(`📨 Email sent to ${client.email} for invoice ${invoiceId}`);
  } catch (err) {
    console.error("🔥 Background task error:", err);
  }
};
