import express from "express";
import <PERSON><PERSON><PERSON><PERSON>roller from "../../controllers/persona/personaController.js";
import { verifyToken } from "../../middlewares/authMiddleware.js";
import {
  checkAgency,
  requireAdminOrAgency,
} from "../../middlewares/roleCheck.js";
import { checkPermission } from "../../middlewares/permissionMiddleware.js";

const router = express.Router();

// Helper middleware to check if user is agency or employee with agency access
const checkAgencyAccess = (req, res, next) => {
  if (
    req.user &&
    (req.user.role === "agency" ||
      (req.user.role === "employee" && req.user.agencyId) ||
      (req.user.role === "model" && req.user.agencyId))
  ) {
    next();
  } else {
    return res
      .status(403)
      .json({ message: "Access denied: Agency access required" });
  }
};

// All routes require authentication and agency access
router.use(verifyToken);
router.use(checkAgencyAccess);

// Generate persona - requires persona.create permission
router.post(
  "/generate",
  checkPermission("persona.create"),
  PersonaController.generatePersona,
);

// Save persona - requires persona.create permission
router.post(
  "/",
  checkPermission("persona.create"),
  PersonaController.savePersona,
);

// Get all personas for agency - requires persona.view permission
router.get("/", checkPermission("persona.view"), PersonaController.getPersonas);

// Get persona analytics - requires persona.view permission
router.get(
  "/analytics",
  checkPermission("persona.view"),
  PersonaController.getAnalytics,
);

// Get quota status - requires persona.view permission
router.get(
  "/quota",
  checkPermission("persona.view"),
  PersonaController.getQuotaStatus,
);

// Get specific persona by ID - requires persona.view permission
router.get(
  "/:personaId",
  checkPermission("persona.view"),
  PersonaController.getPersonaById,
);

// Update persona - requires persona.edit permission
router.put(
  "/:personaId",
  checkPermission("persona.edit"),
  PersonaController.updatePersona,
);

// Delete persona - requires persona.delete permission
router.delete(
  "/:personaId",
  checkPermission("persona.delete"),
  PersonaController.deletePersona,
);

// Export persona as PDF - requires persona.view permission
router.get(
  "/:personaId/export",
  checkPermission("persona.view"),
  PersonaController.exportPersona,
);

// Get analytics for specific persona - requires persona.view permission
router.get(
  "/:personaId/analytics",
  checkPermission("persona.view"),
  PersonaController.getAnalytics,
);

// ==== Phase 3: Enhanced Persona Features ====

// Feedback endpoints
router.post(
  "/:personaId/feedback",
  checkPermission("persona.edit"),
  PersonaController.submitPersonaFeedback,
);

router.get(
  "/:personaId/feedback",
  checkPermission("persona.view"),
  PersonaController.getPersonaFeedback,
);

// Version management endpoints
router.get(
  "/:personaId/versions",
  checkPermission("persona.view"),
  PersonaController.getPersonaVersions,
);

router.post(
  "/:personaId/regenerate",
  checkPermission("persona.create"),
  PersonaController.regeneratePersonaWithVersion,
);

router.put(
  "/:personaId/versions/:versionId/activate",
  checkPermission("persona.edit"),
  PersonaController.activatePersonaVersion,
);

router.delete(
  "/:personaId/versions/:versionId",
  checkPermission("persona.delete"),
  PersonaController.deletePersonaVersion,
);

export default router;
