import Agency from "../models/agency.js";
import ModelUser from "../models/model.js";
import jwt from "jsonwebtoken";
import Employee from "../models/Employee/Employee.js";

const options = {
  httpOnly: true,
  secure: true,
  sameSite: "strict", // or 'lax' for usability
  maxAge: 1000 * 60 * 60, // 1 hour
};

export const ganerateAccessTokens = async (userId, role) => {
  try {
    let user;
    if (role === "model") {
      user = await ModelUser.findById(userId);
    } else if (role === "agency") {
      user = await Agency.findById(userId);
    } else if (role === "employee") {
      user = await Employee.findById(userId);
    }

    const accessToken = user.generateAccessToken();

    return { accessToken };
  } catch (error) {
    console.error("Error generating tokens:", error);
    throw new Error("Something went wrong while generating Access Tokens.");
  }
};

export const refreshAccessToken = async (req, res) => {
  const incomingRefreshToken =
    req.cookies.refreshToken || req.body.refreshToken;

  if (!incomingRefreshToken)
    return res.status(401).json({ message: "Unauthorized Request." });

  try {
    const decodedToken = jwt.verify(
      incomingRefreshToken,
      process.env.LOGIN_REFRESH_TOKEN_SECRET,
    );

    let user = await ModelUser.findById(decodedToken?._id);
    let userRole = "model";

    if (!user) {
      user = await Agency.findById(decodedToken?._id);
      userRole = "agency";
    }
    if (!user) {
      user = await Employee.findById(decodedToken._id);
      userRole = "employee";
    }

    if (!user)
      return res.status(401).json({ message: "Unauthorized request." });

    if (incomingRefreshToken !== user?.loginRefreshToken)
      return res.status(401).json({ message: "Refresh Token is expired." });

    const { accessToken } = await ganerateAccessTokens(user._id, userRole);

    res.cookie("accessToken", accessToken, options);

    // ✅ Prepare safeUser (remove password and sensitive fields)
    const safeUser = {
      _id: user._id,
      username: user.username,
      role: user.role,
    };

    return res.status(200).json({
      message: "Login successful",
      user: safeUser,
      token: accessToken,
    });
  } catch (error) {
    return res.status(401).json({
      message:
        error?.message ||
        "Something went wrong while generating new Access Token.",
    });
  }
};
