import mongoose from "mongoose";

/**
 * AutoScheduleLog Schema - Tracks auto-scheduling activities and calendar sync
 * Logs scheduling decisions, conflicts, and calendar integration results
 */
const autoScheduleLogSchema = new mongoose.Schema(
  {
    // Associated model and agency
    modelId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ModelUser",
      required: true,
      index: true,
    },

    agencyId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      required: true,
      index: true,
    },

    // Planning context
    planId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ContentPlan",
      index: true,
    },

    // Date range for scheduling
    dateRange: {
      startDate: {
        type: Date,
        required: true,
      },
      endDate: {
        type: Date,
        required: true,
      },
    },

    // Scheduling algorithm results
    slotSuggestions: [
      {
        datetime: {
          type: Date,
          required: true,
        },
        confidence: {
          type: Number,
          min: 0,
          max: 100,
          required: true,
        },
        reasoning: {
          type: String,
          maxlength: 500,
        },
        platform: {
          type: String,
          enum: ["instagram", "tiktok", "facebook", "twitter", "linkedin"],
        },
        contentType: {
          type: String,
          enum: ["image", "video", "carousel", "reel", "story"],
        },
        estimatedReach: {
          type: Number,
          min: 0,
        },
        estimatedEngagement: {
          type: Number,
          min: 0,
        },
      },
    ],

    // User selections
    selectedSlots: [
      {
        datetime: {
          type: Date,
          required: true,
        },
        taskId: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "ContentTask",
        },
        platform: {
          type: String,
          enum: ["instagram", "tiktok", "facebook", "twitter", "linkedin"],
        },
        approved: {
          type: Boolean,
          default: false,
        },
        approvedAt: {
          type: Date,
        },
        rejectedReason: {
          type: String,
          maxlength: 500,
        },
      },
    ],

    // Scheduling conflicts and issues
    conflicts: [
      {
        type: {
          type: String,
          enum: [
            "calendar_conflict",
            "too_frequent",
            "platform_limit",
            "content_overlap",
            "time_zone_issue",
            "model_unavailable",
            "approval_pending",
          ],
          required: true,
        },
        description: {
          type: String,
          required: true,
          maxlength: 500,
        },
        severity: {
          type: String,
          enum: ["low", "medium", "high", "critical"],
          default: "medium",
        },
        affectedSlots: [
          {
            type: Date,
          },
        ],
        resolved: {
          type: Boolean,
          default: false,
        },
        resolution: {
          type: String,
          maxlength: 500,
        },
        resolvedAt: {
          type: Date,
        },
      },
    ],

    // Calendar synchronization
    calendarSyncStatus: {
      type: String,
      enum: ["pending", "syncing", "synced", "failed", "partial"],
      default: "pending",
      index: true,
    },

    calendarAccount: {
      email: {
        type: String,
        trim: true,
      },
      provider: {
        type: String,
        enum: ["google", "outlook", "apple"],
        default: "google",
      },
      accountId: {
        type: String,
      },
      timezone: {
        type: String,
        default: "UTC",
      },
    },

    // Sync results
    syncResults: {
      totalSlots: {
        type: Number,
        default: 0,
      },
      successfulSyncs: {
        type: Number,
        default: 0,
      },
      failedSyncs: {
        type: Number,
        default: 0,
      },
      calendarEventsCreated: [
        {
          eventId: {
            type: String,
            required: true,
          },
          taskId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "ContentTask",
          },
          datetime: {
            type: Date,
            required: true,
          },
          title: {
            type: String,
            required: true,
          },
          status: {
            type: String,
            enum: ["created", "updated", "deleted", "failed"],
            default: "created",
          },
        },
      ],
      errors: [
        {
          slot: {
            type: Date,
            required: true,
          },
          error: {
            type: String,
            required: true,
          },
          errorCode: {
            type: String,
          },
          timestamp: {
            type: Date,
            default: Date.now,
          },
        },
      ],
    },

    // AI algorithm metadata
    algorithmVersion: {
      type: String,
      default: "1.0",
    },

    algorithmParams: {
      timeZone: {
        type: String,
        default: "UTC",
      },
      preferredTimes: [
        {
          hour: { type: Number, min: 0, max: 23 },
          minute: { type: Number, min: 0, max: 59 },
          weight: { type: Number, min: 0, max: 1, default: 1 },
        },
      ],
      avoidTimes: [
        {
          hour: { type: Number, min: 0, max: 23 },
          minute: { type: Number, min: 0, max: 59 },
        },
      ],
      minGapBetweenPosts: {
        type: Number, // hours
        default: 4,
      },
      maxPostsPerDay: {
        type: Number,
        default: 3,
      },
      platformSpecificTiming: {
        type: Boolean,
        default: true,
      },
      audienceAnalysis: {
        type: Boolean,
        default: true,
      },
    },

    // Performance tracking
    performance: {
      processingTime: {
        type: Number, // milliseconds
      },
      suggestionAccuracy: {
        type: Number, // percentage
        min: 0,
        max: 100,
      },
      userSatisfaction: {
        rating: {
          type: Number,
          min: 1,
          max: 5,
        },
        feedback: {
          type: String,
          maxlength: 1000,
        },
        submittedAt: {
          type: Date,
        },
      },
    },

    // Execution status
    status: {
      type: String,
      enum: ["pending", "processing", "completed", "failed", "cancelled"],
      default: "pending",
      index: true,
    },

    // Error tracking
    errors: [
      {
        stage: {
          type: String,
          enum: [
            "analysis",
            "suggestion",
            "selection",
            "calendar_sync",
            "notification",
          ],
          required: true,
        },
        message: {
          type: String,
          required: true,
        },
        stackTrace: {
          type: String,
        },
        timestamp: {
          type: Date,
          default: Date.now,
        },
      },
    ],

    // User interaction
    userActions: [
      {
        action: {
          type: String,
          enum: ["view", "accept", "reject", "modify", "reschedule", "approve"],
          required: true,
        },
        target: {
          type: String, // slot datetime or other identifier
          required: true,
        },
        details: {
          type: mongoose.Schema.Types.Mixed,
        },
        timestamp: {
          type: Date,
          default: Date.now,
        },
      },
    ],

    // Notification tracking
    notifications: {
      sent: [
        {
          type: {
            type: String,
            enum: ["email", "push", "in_app"],
            required: true,
          },
          recipient: {
            type: String,
            required: true,
          },
          subject: {
            type: String,
          },
          sentAt: {
            type: Date,
            default: Date.now,
          },
          delivered: {
            type: Boolean,
            default: false,
          },
          opened: {
            type: Boolean,
            default: false,
          },
        },
      ],
      scheduled: [
        {
          type: {
            type: String,
            enum: ["reminder", "confirmation", "conflict_alert"],
            required: true,
          },
          scheduledFor: {
            type: Date,
            required: true,
          },
          content: {
            type: String,
            required: true,
          },
          sent: {
            type: Boolean,
            default: false,
          },
        },
      ],
    },

    // Ownership tracking
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
    },

    createdByType: {
      type: String,
      required: true,
      enum: ["Agency", "ModelUser", "Employee"],
    },

    // Additional metadata
    metadata: {
      requestSource: {
        type: String,
        enum: ["manual", "auto", "api", "bulk"],
        default: "manual",
      },
      batchId: {
        type: String, // For bulk operations
      },
      clientInfo: {
        userAgent: String,
        ipAddress: String,
      },
    },

    // Archival
    isArchived: {
      type: Boolean,
      default: false,
      index: true,
    },

    archivedAt: {
      type: Date,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  },
);

// Compound indexes for efficient queries
autoScheduleLogSchema.index({ modelId: 1, agencyId: 1, createdAt: -1 });
autoScheduleLogSchema.index({ planId: 1, status: 1 });
autoScheduleLogSchema.index({ calendarSyncStatus: 1, createdAt: -1 });
autoScheduleLogSchema.index({
  "dateRange.startDate": 1,
  "dateRange.endDate": 1,
});
autoScheduleLogSchema.index({ status: 1, createdAt: -1 });

// Virtual for success rate
autoScheduleLogSchema.virtual("successRate").get(function () {
  if (this.syncResults.totalSlots === 0) return 0;
  return Math.round(
    (this.syncResults.successfulSyncs / this.syncResults.totalSlots) * 100,
  );
});

// Virtual for conflict resolution rate
autoScheduleLogSchema.virtual("conflictResolutionRate").get(function () {
  if (this.conflicts.length === 0) return 100;
  const resolved = this.conflicts.filter(
    (conflict) => conflict.resolved,
  ).length;
  return Math.round((resolved / this.conflicts.length) * 100);
});

// Virtual for total slots processed
autoScheduleLogSchema.virtual("totalSlotsProcessed").get(function () {
  return this.slotSuggestions.length;
});

// Virtual for user acceptance rate
autoScheduleLogSchema.virtual("acceptanceRate").get(function () {
  if (this.slotSuggestions.length === 0) return 0;
  const accepted = this.selectedSlots.filter((slot) => slot.approved).length;
  return Math.round((accepted / this.slotSuggestions.length) * 100);
});

// Pre-save middleware
autoScheduleLogSchema.pre("save", function (next) {
  // Update sync results totals
  this.syncResults.totalSlots = this.selectedSlots.length;
  this.syncResults.successfulSyncs =
    this.syncResults.calendarEventsCreated.filter(
      (event) => event.status === "created",
    ).length;
  this.syncResults.failedSyncs = this.syncResults.errors.length;

  // Auto-update status based on results
  if (this.status === "processing") {
    if (this.errors.length > 0) {
      this.status = "failed";
    } else if (
      this.syncResults.totalSlots > 0 &&
      this.syncResults.successfulSyncs === this.syncResults.totalSlots
    ) {
      this.status = "completed";
    }
  }

  next();
});

// Static methods
autoScheduleLogSchema.statics.findRecentLogs = function (
  agencyId,
  modelId,
  limit = 10,
) {
  const query = { agencyId, isArchived: false };
  if (modelId) query.modelId = modelId;

  return this.find(query)
    .sort({ createdAt: -1 })
    .limit(limit)
    .populate("modelId", "username fullName")
    .populate("planId", "name weekStart")
    .select(
      "dateRange status calendarSyncStatus syncResults performance createdAt",
    );
};

autoScheduleLogSchema.statics.getSchedulingAnalytics = function (
  agencyId,
  timeframe = 30,
) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - timeframe);

  return this.aggregate([
    {
      $match: {
        agencyId: new mongoose.Types.ObjectId(agencyId),
        createdAt: { $gte: startDate },
        isArchived: false,
      },
    },
    {
      $group: {
        _id: null,
        totalSchedulingRuns: { $sum: 1 },
        successfulRuns: {
          $sum: { $cond: [{ $eq: ["$status", "completed"] }, 1, 0] },
        },
        totalSlotsProcessed: { $sum: { $size: "$slotSuggestions" } },
        totalSlotsSelected: { $sum: { $size: "$selectedSlots" } },
        totalCalendarEvents: {
          $sum: { $size: "$syncResults.calendarEventsCreated" },
        },
        avgProcessingTime: { $avg: "$performance.processingTime" },
        avgAcceptanceRate: { $avg: "$performance.suggestionAccuracy" },
        totalConflicts: { $sum: { $size: "$conflicts" } },
        resolvedConflicts: {
          $sum: {
            $size: {
              $filter: {
                input: "$conflicts",
                cond: { $eq: ["$$this.resolved", true] },
              },
            },
          },
        },
      },
    },
  ]);
};

autoScheduleLogSchema.statics.findConflictsByType = function (
  agencyId,
  timeframe = 30,
) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - timeframe);

  return this.aggregate([
    {
      $match: {
        agencyId: new mongoose.Types.ObjectId(agencyId),
        createdAt: { $gte: startDate },
      },
    },
    { $unwind: "$conflicts" },
    {
      $group: {
        _id: "$conflicts.type",
        count: { $sum: 1 },
        resolved: {
          $sum: { $cond: [{ $eq: ["$conflicts.resolved", true] }, 1, 0] },
        },
        avgSeverity: {
          $avg: {
            $switch: {
              branches: [
                { case: { $eq: ["$conflicts.severity", "low"] }, then: 1 },
                { case: { $eq: ["$conflicts.severity", "medium"] }, then: 2 },
                { case: { $eq: ["$conflicts.severity", "high"] }, then: 3 },
                { case: { $eq: ["$conflicts.severity", "critical"] }, then: 4 },
              ],
              default: 2,
            },
          },
        },
      },
    },
    { $sort: { count: -1 } },
  ]);
};

// Instance methods
autoScheduleLogSchema.methods.addConflict = function (conflictData) {
  this.conflicts.push({
    ...conflictData,
    timestamp: new Date(),
  });
  return this.save();
};

autoScheduleLogSchema.methods.resolveConflict = function (
  conflictId,
  resolution,
) {
  const conflict = this.conflicts.id(conflictId);
  if (conflict) {
    conflict.resolved = true;
    conflict.resolution = resolution;
    conflict.resolvedAt = new Date();
  }
  return this.save();
};

autoScheduleLogSchema.methods.addCalendarEvent = function (eventData) {
  this.syncResults.calendarEventsCreated.push(eventData);
  this.calendarSyncStatus = "synced";
  return this.save();
};

autoScheduleLogSchema.methods.recordUserAction = function (
  action,
  target,
  details = {},
) {
  this.userActions.push({
    action,
    target,
    details,
    timestamp: new Date(),
  });
  return this.save();
};

autoScheduleLogSchema.methods.updatePerformance = function (performanceData) {
  this.performance = {
    ...this.performance,
    ...performanceData,
  };
  return this.save();
};

autoScheduleLogSchema.methods.generateReport = function () {
  return {
    logId: this._id,
    summary: {
      dateRange: this.dateRange,
      status: this.status,
      slotsProcessed: this.totalSlotsProcessed,
      acceptanceRate: this.acceptanceRate,
      successRate: this.successRate,
      conflictResolutionRate: this.conflictResolutionRate,
    },
    suggestions: this.slotSuggestions.length,
    selections: this.selectedSlots.length,
    calendarEvents: this.syncResults.calendarEventsCreated.length,
    conflicts: {
      total: this.conflicts.length,
      resolved: this.conflicts.filter((c) => c.resolved).length,
      byType: this.conflicts.reduce((acc, conflict) => {
        acc[conflict.type] = (acc[conflict.type] || 0) + 1;
        return acc;
      }, {}),
    },
    performance: this.performance,
    createdAt: this.createdAt,
  };
};

const AutoScheduleLog = mongoose.model(
  "AutoScheduleLog",
  autoScheduleLogSchema,
);
export default AutoScheduleLog;
