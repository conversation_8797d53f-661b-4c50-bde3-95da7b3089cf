import ContentUpload from "../../models/contentupload/ContentUpload.js";
import ContentCategory from "../../models/contentupload/ContentCategory.js";
import { ApiError } from "../../utils/ApiError.js";
import { ApiResponse } from "../../utils/ApiResponse.js";

/**
 * Service for managing content upload status transitions and workflow
 * Handles the status lifecycle: pending → uploaded → approved/rejected → archived
 */
class StatusTrackingService {
  /**
   * Valid status transitions mapping
   */
  static VALID_TRANSITIONS = {
    pending: ["uploaded", "rejected", "archived"],
    uploaded: ["approved", "rejected", "archived"],
    approved: ["archived"],
    rejected: ["uploaded", "archived"],
    archived: [], // Terminal state
  };

  /**
   * Status priorities for workflow management
   */
  static STATUS_PRIORITY = {
    pending: 1,
    uploaded: 2,
    approved: 3,
    rejected: 2,
    archived: 4,
  };

  /**
   * Validate if a status transition is allowed
   * @param {string} currentStatus - Current upload status
   * @param {string} newStatus - Desired new status
   * @returns {boolean} - Whether transition is valid
   */
  static isValidTransition(currentStatus, newStatus) {
    if (!currentStatus || !newStatus) return false;
    return this.VALID_TRANSITIONS[currentStatus]?.includes(newStatus) || false;
  }

  /**
   * Update upload status with validation and logging
   * @param {string} uploadId - Upload document ID
   * @param {string} newStatus - New status to set
   * @param {string} userId - User performing the action
   * @param {string} userRole - Role of the user (agency/model)
   * @param {Object} options - Additional options (reason, comment)
   * @returns {Object} - Updated upload document
   */
  static async updateStatus(
    uploadId,
    newStatus,
    userId,
    userRole,
    options = {},
  ) {
    try {
      const upload = await ContentUpload.findById(uploadId)
        .populate("categoryId")
        .populate("modelId", "username")
        .populate("agencyId", "name");

      if (!upload) {
        throw new ApiError(404, "Upload not found");
      }

      if (upload.isDeleted) {
        throw new ApiError(400, "Cannot update status of deleted upload");
      }

      // Validate transition
      if (!this.isValidTransition(upload.status, newStatus)) {
        throw new ApiError(
          400,
          `Invalid status transition from '${upload.status}' to '${newStatus}'`,
        );
      }

      // Role-based authorization for status changes
      if (!this.canUserChangeStatus(userRole, upload.status, newStatus)) {
        throw new ApiError(
          403,
          `${userRole} users cannot change status from '${upload.status}' to '${newStatus}'`,
        );
      }

      // Perform status-specific actions
      await this.performStatusAction(
        upload,
        newStatus,
        userId,
        userRole,
        options,
      );

      // Log status change
      await this.logStatusChange(upload, newStatus, userId, userRole, options);

      return upload;
    } catch (error) {
      console.error("Status update failed:", error);
      throw error;
    }
  }

  /**
   * Check if user role can perform specific status transition
   * @param {string} userRole - User role (agency/model)
   * @param {string} currentStatus - Current status
   * @param {string} newStatus - Desired new status
   * @returns {boolean} - Whether user can perform transition
   */
  static canUserChangeStatus(userRole, currentStatus, newStatus) {
    // Agency users can perform most transitions
    if (userRole === "agency") {
      return true;
    }

    // Model users have limited permissions
    if (userRole === "model") {
      // Models can only upload (pending → uploaded)
      if (currentStatus === "pending" && newStatus === "uploaded") {
        return true;
      }
      // Models can archive their own rejected uploads
      if (currentStatus === "rejected" && newStatus === "archived") {
        return true;
      }
    }

    return false;
  }

  /**
   * Perform status-specific actions
   * @param {Object} upload - Upload document
   * @param {string} newStatus - New status
   * @param {string} userId - User ID
   * @param {string} userRole - User role
   * @param {Object} options - Additional options
   */
  static async performStatusAction(
    upload,
    newStatus,
    userId,
    userRole,
    options,
  ) {
    switch (newStatus) {
      case "approved":
        await upload.approve(userId, options.comment);
        break;

      case "rejected":
        if (!options.reason) {
          throw new ApiError(400, "Rejection reason is required");
        }
        await upload.reject(userId, options.reason, options.comment);
        break;

      case "archived":
        upload.status = "archived";
        upload.deletedAt = new Date();
        upload.deletedBy = userId;
        if (options.comment) {
          await upload.addComment(
            userId,
            userRole === "model" ? "ModelUser" : "Agency",
            options.comment,
          );
        }
        await upload.save();
        break;

      case "uploaded":
        upload.status = "uploaded";
        if (options.comment) {
          await upload.addComment(
            userId,
            userRole === "model" ? "ModelUser" : "Agency",
            options.comment,
          );
        }
        await upload.save();
        break;

      default:
        upload.status = newStatus;
        await upload.save();
    }
  }

  /**
   * Log status change for audit trail
   * @param {Object} upload - Upload document
   * @param {string} newStatus - New status
   * @param {string} userId - User ID
   * @param {string} userRole - User role
   * @param {Object} options - Additional options
   */
  static async logStatusChange(upload, newStatus, userId, userRole, options) {
    const logEntry = {
      uploadId: upload._id,
      previousStatus: upload.status,
      newStatus,
      changedBy: userId,
      changedByRole: userRole,
      timestamp: new Date(),
      reason: options.reason || null,
      comment: options.comment || null,
    };

    console.log("Status change logged:", logEntry);
    // In a production environment, you might want to store this in a separate audit log collection
  }

  /**
   * Get uploads by status with filtering and pagination
   * @param {string} agencyId - Agency ID
   * @param {string} status - Status to filter by
   * @param {Object} filters - Additional filters
   * @param {Object} pagination - Pagination options
   * @returns {Object} - Filtered uploads with pagination
   */
  static async getUploadsByStatus(
    agencyId,
    status,
    filters = {},
    pagination = {},
  ) {
    const query = {
      agencyId,
      status,
      isDeleted: false,
      ...filters,
    };

    const {
      page = 1,
      limit = 20,
      sortBy = "createdAt",
      sortOrder = "desc",
    } = pagination;
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sortOptions = { [sortBy]: sortOrder === "desc" ? -1 : 1 };

    const [uploads, total] = await Promise.all([
      ContentUpload.find(query)
        .populate("categoryId", "label platform")
        .populate("modelId", "username profilePhoto")
        .populate("approvedBy", "username")
        .populate("rejectedBy", "username")
        .sort(sortOptions)
        .skip(skip)
        .limit(parseInt(limit)),
      ContentUpload.countDocuments(query),
    ]);

    return {
      uploads,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        hasMore: skip + uploads.length < total,
        totalPages: Math.ceil(total / parseInt(limit)),
      },
    };
  }

  /**
   * Get status workflow summary for agency dashboard
   * @param {string} agencyId - Agency ID
   * @param {Object} dateRange - Optional date range filter
   * @returns {Object} - Status workflow summary
   */
  static async getWorkflowSummary(agencyId, dateRange = {}) {
    const query = { agencyId, isDeleted: false };

    if (dateRange.startDate || dateRange.endDate) {
      query.createdAt = {};
      if (dateRange.startDate)
        query.createdAt.$gte = new Date(dateRange.startDate);
      if (dateRange.endDate) query.createdAt.$lte = new Date(dateRange.endDate);
    }

    const [statusCounts, overdue, recentActivity] = await Promise.all([
      ContentUpload.aggregate([
        { $match: query },
        { $group: { _id: "$status", count: { $sum: 1 } } },
      ]),
      ContentUpload.findOverdue(agencyId),
      ContentUpload.find(query)
        .sort({ updatedAt: -1 })
        .limit(10)
        .populate("modelId", "username")
        .populate("categoryId", "label"),
    ]);

    const statusBreakdown = statusCounts.reduce((acc, item) => {
      acc[item._id] = item.count;
      return acc;
    }, {});

    return {
      statusBreakdown,
      overdueCount: overdue.length,
      recentActivity,
      workflowHealth: this.calculateWorkflowHealth(
        statusBreakdown,
        overdue.length,
      ),
    };
  }

  /**
   * Calculate workflow health score based on status distribution
   * @param {Object} statusBreakdown - Count of uploads by status
   * @param {number} overdueCount - Number of overdue uploads
   * @returns {Object} - Workflow health metrics
   */
  static calculateWorkflowHealth(statusBreakdown, overdueCount) {
    const total = Object.values(statusBreakdown).reduce(
      (sum, count) => sum + count,
      0,
    );

    if (total === 0) {
      return {
        score: 100,
        status: "excellent",
        message: "No uploads to process",
      };
    }

    const pending = statusBreakdown.pending || 0;
    const approved = statusBreakdown.approved || 0;
    const rejected = statusBreakdown.rejected || 0;

    // Calculate health score (0-100)
    let score = 100;

    // Deduct points for high pending ratio
    const pendingRatio = pending / total;
    if (pendingRatio > 0.3) score -= 20;
    else if (pendingRatio > 0.2) score -= 10;

    // Deduct points for high rejection ratio
    const rejectionRatio = rejected / total;
    if (rejectionRatio > 0.2) score -= 15;
    else if (rejectionRatio > 0.1) score -= 8;

    // Deduct points for overdue uploads
    const overdueRatio = overdueCount / total;
    if (overdueRatio > 0.1) score -= 25;
    else if (overdueRatio > 0.05) score -= 15;

    // Determine status and message
    let status, message;
    if (score >= 90) {
      status = "excellent";
      message = "Workflow is running smoothly";
    } else if (score >= 75) {
      status = "good";
      message = "Workflow is performing well";
    } else if (score >= 60) {
      status = "fair";
      message = "Some workflow issues need attention";
    } else {
      status = "poor";
      message = "Workflow requires immediate attention";
    }

    return { score: Math.max(0, score), status, message };
  }
}

export default StatusTrackingService;
