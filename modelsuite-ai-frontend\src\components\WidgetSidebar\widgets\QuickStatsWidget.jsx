import React from "react";
import { TrendingUp, Users, DollarSign, Activity } from "lucide-react";

const QuickStatsWidget = ({ data }) => {
  // Mock data for development
  const statsData = data?.data?.stats || {
    earnings: { value: "$12,450", change: "+8%", trend: "up" },
    modelsOnline: { value: "18", change: "+2", trend: "up" },
    engagement: { value: "625.6K", change: "+12%", trend: "up" },
    activity: { value: "89%", change: "-3%", trend: "down" },
  };

  const statItems = [
    {
      label: "Earnings",
      value: statsData.earnings.value,
      change: statsData.earnings.change,
      trend: statsData.earnings.trend,
      icon: DollarSign,
      color: "text-green-400",
    },
    {
      label: "Models Online",
      value: statsData.modelsOnline.value,
      change: statsData.modelsOnline.change,
      trend: statsData.modelsOnline.trend,
      icon: Users,
      color: "text-blue-400",
    },
    {
      label: "Engagement",
      value: statsData.engagement.value,
      change: statsData.engagement.change,
      trend: statsData.engagement.trend,
      icon: TrendingUp,
      color: "text-purple-400",
    },
    {
      label: "Activity",
      value: statsData.activity.value,
      change: statsData.activity.change,
      trend: statsData.activity.trend,
      icon: Activity,
      color: "text-orange-400",
    },
  ];

  return (
    <div className="space-y-2">
      {statItems.map((item, index) => {
        const IconComponent = item.icon;
        return (
          <div
            key={index}
            className="flex items-center justify-between bg-gray-700/30 rounded p-2"
          >
            <div className="flex items-center space-x-2">
              <IconComponent className={`h-4 w-4 ${item.color}`} />
              <div>
                <p className="text-xs text-gray-400">{item.label}</p>
                <p className="text-sm font-medium text-white">{item.value}</p>
              </div>
            </div>
            <div
              className={`text-xs ${
                item.trend === "up" ? "text-green-400" : "text-red-400"
              }`}
            >
              {item.change}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default QuickStatsWidget;
