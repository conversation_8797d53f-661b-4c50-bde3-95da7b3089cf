import dotenv from "dotenv";
import QuestionSection from "../models/voice/QuestionSection.js";
import QuestionTemplate from "../models/voice/QuestionTemplate.js";
import connectDB from "../db.js";

// Load environment variables
dotenv.config();

const checkAndFixTags = async () => {
  try {
    console.log("🔍 Checking question tags in database...");

    // Check existing questions
    const allQuestions = await QuestionTemplate.find({}).populate("sectionId");
    console.log(`📊 Found ${allQuestions.length} total questions`);

    // Check how many have tags
    const questionsWithTags = allQuestions.filter(
      (q) => q.tags && q.tags.length > 0,
    );
    const questionsWithoutTags = allQuestions.filter(
      (q) => !q.tags || q.tags.length === 0,
    );

    console.log(`✅ Questions with tags: ${questionsWithTags.length}`);
    console.log(`❌ Questions without tags: ${questionsWithoutTags.length}`);

    if (questionsWithTags.length > 0) {
      console.log("\n📝 Sample questions with tags:");
      questionsWithTags.slice(0, 3).forEach((q) => {
        console.log(
          `  - "${q.text.substring(0, 50)}..." | Tags: [${q.tags.join(", ")}]`,
        );
      });
    }

    // Force update ALL questions to fix the duplicate tag issue
    console.log("\n🔧 Updating ALL questions with diverse tag combinations...");

    // Define section-to-tags mapping with DIFFERENT tag combinations for variety
    const sectionTagMapping = {
      "Good Morning (Flirty/Sensual)": [
        ["good_morning", "flirty"],
        ["good_morning", "sensual"],
        ["good_morning", "intimate"],
        ["good_morning", "naughty"],
        ["flirty", "sensual"],
        ["intimate", "playful"],
        ["sensual", "teasing"],
        ["good_morning", "romantic"],
      ],
      "Good Morning (Affectionate, Non-Sexual)": [
        ["good_morning", "affectionate"],
        ["good_morning", "non_sexual"],
        ["good_morning", "loving"],
        ["affectionate", "caring"],
        ["non_sexual", "sweet"],
        ["loving", "soft_spoken"],
        ["good_morning", "sleepy"],
        ["caring", "energetic"],
      ],
      "Good Night (Affectionate, Non-Sexual)": [
        ["good_night", "affectionate"],
        ["good_night", "loving"],
        ["good_night", "caring"],
        ["good_night", "sleepy"],
        ["affectionate", "soft_spoken"],
        ["loving", "sweet"],
        ["caring", "non_sexual"],
        ["good_night", "romantic"],
      ],
      "Daytime Conversation Starter (Mixed Tone)": [
        ["conversation_starter", "mixed_tone"],
        ["conversation_starter", "casual"],
        ["conversation_starter", "playful"],
        ["mixed_tone", "caring"],
        ["casual", "sweet"],
        ["playful", "energetic"],
        ["conversation_starter", "romantic"],
        ["mixed_tone", "affectionate"],
      ],
    };

    let fixedCount = 0;

    // Group ALL questions by section to assign different tag combinations
    const questionsBySection = {};
    allQuestions.forEach((question) => {
      if (question.sectionId && question.sectionId.title) {
        const sectionTitle = question.sectionId.title;
        if (!questionsBySection[sectionTitle]) {
          questionsBySection[sectionTitle] = [];
        }
        questionsBySection[sectionTitle].push(question);
      }
    });

    // Assign different tag combinations to questions in each section
    for (const [sectionTitle, questions] of Object.entries(
      questionsBySection,
    )) {
      const tagCombinations = sectionTagMapping[sectionTitle];

      if (tagCombinations) {
        for (let i = 0; i < questions.length; i++) {
          const question = questions[i];
          // Cycle through different tag combinations
          const tags = tagCombinations[i % tagCombinations.length];

          await QuestionTemplate.findByIdAndUpdate(
            question._id,
            { tags: tags },
            { new: true },
          );
          fixedCount++;
          console.log(
            `  ✅ Updated question ${i + 1} in section "${sectionTitle}" with tags: [${tags.join(", ")}]`,
          );
        }
      }
    }

    console.log(
      `\n🎉 Updated tags for ${fixedCount} questions with diverse combinations!`,
    );

    // Final verification
    const updatedQuestions = await QuestionTemplate.find({});
    const finalWithTags = updatedQuestions.filter(
      (q) => q.tags && q.tags.length > 0,
    );
    console.log(
      `\n📊 Final count: ${finalWithTags.length}/${updatedQuestions.length} questions have tags`,
    );
  } catch (error) {
    console.error("❌ Error checking/fixing tags:", error);
    throw error;
  }
};

// Run the script
(async () => {
  try {
    console.log("📡 Connecting to database...");
    await connectDB();
    console.log("🔍 Running tag check and fix...");
    await checkAndFixTags();
    console.log("✅ Tag check completed successfully!");
    process.exit(0);
  } catch (error) {
    console.error("❌ Failed to check/fix tags:", error);
    process.exit(1);
  }
})();
