import ModelPortfolio from "../models/modelPortfolio.js";
import slugify from "slugify";
import bcrypt from "bcryptjs";
import ModelUser from "../models/model.js";
import Agency from "../models/agency.js";
import EditorialPortfolio from "../models/editorialPortfolio.js";
import mongoose from "mongoose";
import nodemailer from "nodemailer";

//Helper Functions
function convertHeightToCm(height) {
  if (height === "") return "";
  if (typeof height === "number") return height;
  if (typeof height === "string") {
    // Handle formats like "5'9\"" or "175cm"
    if (height.includes("'")) {
      const [feet, inches] = height.replace('"', "").split("'");
      return Math.round(parseInt(feet) * 30.48 + parseInt(inches || 0) * 2.54);
    }
    return parseInt(height.replace(/[^\d]/g, ""));
  }
  return null;
}

const transporter = nodemailer.createTransport({
  service: "gmail", // or "ethereal", "mailtrap", etc.
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
});

function convertWeightToKg(weight) {
  if (weight === "") return "";
  if (typeof weight === "number") return weight;
  if (typeof weight === "string") {
    const num = parseFloat(weight);
    if (weight.toLowerCase().includes("lbs")) {
      return Math.round(num * 0.453592);
    }
    return num;
  }
  return null;
}

function countCanvasImagesFromFiles(uploadedFiles) {
  try {
    // Filter files that are canvas images (not cover image)
    const canvasImageFiles = uploadedFiles.filter(
      (file) => file.fieldname && file.fieldname.startsWith("canvasImage"),
    );

    return canvasImageFiles.length;
  } catch (error) {
    console.error("Error counting canvas images from files:", error);
    return 0;
  }
}

//classic
export const createCompCard = async (req, res) => {
  const {
    modelName,
    measurements,
    contact,
    visibility = "public",
    password,
    template = "classic",
    modelId,
    description,
  } = req.body;
  const agencyId = req.user.id;
  const coverImage = req.files.find((f) => f.fieldname === "coverImage")?.path;

  if (!coverImage) {
    return res.status(400).json({
      success: false,
      error: "Cover image is required",
    });
  }

  // Get all images that are not coverImage
  const uploadedImages = req.files
    .filter((f) => f.fieldname.startsWith("image"))
    .map((f) => f.path);

  // Get template and required image count dynamically
  const requiredImageCountPerTemplate = {
    classic: 4,
    modern: 5,
  };

  const requiredImageCount = requiredImageCountPerTemplate[template];

  if (uploadedImages.length < requiredImageCount) {
    return res.status(400).json({
      success: false,
      error: `At least ${requiredImageCount} images are required`,
    });
  }

  if (!modelId) {
    return res.status(400).json({
      success: false,
      error: "Model ID is required",
    });
  }

  if (!modelName || !measurements || !contact || !agencyId) {
    return res.status(400).json({
      error: "Missing required fields",
      required: ["modelName", "measurements", "contact", "agencyId"],
    });
  }
  try {
    const existingPortfolio = await ModelPortfolio.findOne({ model: modelId });
    if (existingPortfolio) {
      return res.status(400).json({
        success: false,
        error: "Portfolio already exists for this model",
        existingPortfolio: {
          id: existingPortfolio._id,
          slug: existingPortfolio.slug,
        },
      });
    }

    let baseSlug = slugify(`model-${modelName}`, { lower: true, strict: true });
    let slug = baseSlug;
    let counter = 1;

    while (await ModelPortfolio.findOne({ slug })) {
      slug = `${baseSlug}-${counter}`;
      counter++;
    }

    // Hash password if visibility is protected
    if (visibility === "protected" && !password) {
      return res.status(400).json({
        success: false,
        error: "Password is required for protected portfolios",
      });
    }
    let passwordHash = null;
    if (visibility === "protected" && password) {
      passwordHash = await bcrypt.hash(password, 10);
    }

    const normalizedMeasurements = {
      heightCm: convertHeightToCm(measurements.height),
      weightKg: convertWeightToKg(measurements.weight),
      bust: measurements.bust?.toString().trim(),
      waist: measurements.waist?.toString().trim(),
      hips: measurements.hips?.toString().trim(),
      dressSize: measurements.dressSize?.toString().trim(),
      shoeSize: measurements.shoeSize?.toString().trim(),
      eyeColor: measurements.eyeColor?.trim(),
      hairColor: measurements.hairColor?.trim(),
    };

    const portfolio = new ModelPortfolio({
      name: modelName.trim(),
      slug,
      measurements: normalizedMeasurements,
      contact: {
        email: contact.email?.toLowerCase().trim(),
        phone: contact.phone?.trim(),
        instagram: contact?.instagram,
        website: contact?.website,
      },
      images: uploadedImages,
      coverImage: coverImage,
      agency: agencyId,
      model: modelId,
      visibility,
      password: passwordHash,
      template,
      description,
    });

    await portfolio.save();

    const model = await ModelUser.findById(modelId);

    model.portfolio = portfolio._id;
    await model.save();

    const agency = await Agency.findById(agencyId);
    agency.portfolios.push(portfolio._id);
    await agency.save();

    await portfolio.populate([
      { path: "agency", select: "agencyName agencyEmail" },
      { path: "model", select: "fullName" },
    ]);

    // Return success response (exclude password hash)
    const responsePortfolio = portfolio.toObject();
    delete responsePortfolio.password;

    res.status(201).json({
      success: true,
      portfolio: responsePortfolio,
      message: "Portfolio created successfully",
      url: `${slug}`,
    });
  } catch (error) {
    console.error("Create portfolio error:", error);

    // Handle MongoDB validation errors
    if (error.name === "ValidationError") {
      const validationErrors = Object.keys(error.errors).map((key) => ({
        field: key,
        message: error.errors[key].message,
      }));

      return res.status(400).json({
        success: false,
        error: "Validation failed",
        details: validationErrors,
      });
    }

    // Handle duplicate key errors
    if (error.code === 11000) {
      const field = Object.keys(error.keyPattern)[0];
      return res.status(400).json({
        success: false,
        error: `${field} already exists`,
        field,
      });
    }

    // Handle cast errors (invalid ObjectId)
    if (error.name === "CastError") {
      return res.status(400).json({
        success: false,
        error: `Invalid ${error.path} format`,
      });
    }

    res.status(500).json({
      success: false,
      error: "Failed to create portfolio",
      details:
        process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

//Editorial
export const createEditorialCompCard = async (req, res) => {
  try {
    const agencyId = req.user.id;
    const portfolioData = req.body;

    const uploadedFiles = req.files;
    const { password } = req.body;

    if (!agencyId) {
      return res.status(400).json({ message: "Agency Id is required!" });
    }

    if (!portfolioData.measurements || !portfolioData.contact) {
      return res.status(400).json({ message: "All fields are required!" });
    }

    if (portfolioData.description && portfolioData.description.length > 500) {
      return res.status(400).json({
        message: "Description must be less than 500 characters",
      });
    }

    // Check if portfolio already exists
    const existingPortfolio = await EditorialPortfolio.findOne({
      model: portfolioData.modelId,
    });

    if (existingPortfolio) {
      return res.status(400).json({ message: "Compcard already exists!!" });
    }

    // Generate unique slug
    let baseSlug = slugify(`editorial-${portfolioData.modelName}`, {
      lower: true,
      strict: true,
    });
    let slug = baseSlug;
    let counter = 1;

    while (await EditorialPortfolio.findOne({ slug })) {
      slug = `${baseSlug}-${counter}`;
      counter++;
    }

    // Parse canvas data with better error handling
    let canvasData;
    try {
      if (typeof portfolioData.canvasData === "string") {
        canvasData = JSON.parse(portfolioData.canvasData);
      } else if (typeof portfolioData.canvasData === "object") {
        canvasData = portfolioData.canvasData;
      } else {
        throw new Error("Invalid canvas data type");
      }
    } catch (parseError) {
      console.error("Canvas data parsing error:", parseError);
      return res.status(400).json({
        message: "Invalid canvas data format!",
        error: parseError.message,
      });
    }

    if (!canvasData || !Array.isArray(canvasData.elements)) {
      return res.status(400).json({
        message: "Canvas data must contain a valid elements array!",
      });
    }

    // Validate cover image
    const coverImage = uploadedFiles.find(
      (file) => file.fieldname === "coverImage",
    );

    if (!coverImage) {
      return res.status(400).json({ message: "Cover Image is required!!" });
    }

    // Validate canvas image count
    const canvasImageCount = countCanvasImagesFromFiles(uploadedFiles);

    if (canvasImageCount < 3) {
      return res.status(400).json({ message: "Images should be more than 3" });
    }

    if (canvasImageCount > 6) {
      return res.status(400).json({ message: "Images should be less than 6" });
    }

    // Create image mapping from uploaded files
    const imageMapping = {};
    uploadedFiles.forEach((file) => {
      if (file.fieldname.startsWith("canvasImage")) {
        const imageIndex = parseInt(file.fieldname.replace("canvasImage", ""));
        imageMapping[imageIndex] = {
          url: file.path,
          filename: file.filename,
          originalName: file.originalname,
          size: file.size,
        };
      }
    });

    // Process canvas elements and attach image URLs
    const processedElements = canvasData.elements.map((element, index) => {
      if (element.type === "image" && element.imageIndex !== undefined) {
        const imageData = imageMapping[element.imageIndex];
        const processedElement = {
          id: element.id,
          type: element.type,
          x: element.x,
          y: element.y,
          width: element.width,
          height: element.height,
          rotation: element.rotation,
          zIndex: element.zIndex,
          imageIndex: element.imageIndex,
          imageUrl: imageData?.url || null,
          imageFilename: imageData?.filename || null,
          originalImageName: imageData?.originalName || null,
        };
        return processedElement;
      } else if (element.type === "text") {
        const processedElement = {
          id: element.id,
          type: element.type,
          x: element.x,
          y: element.y,
          rotation: element.rotation,
          zIndex: element.zIndex,
          content: element.content,
          fontSize: element.fontSize,
          fontFamily: element.fontFamily,
          color: element.color,
          bold: element.bold,
          italic: element.italic,
          underline: element.underline,
        };
        return processedElement;
      }

      return element;
    });

    // Handle password for protected portfolios
    if (portfolioData.visibility === "protected" && !password) {
      return res.status(400).json({
        message: "Password is required for protected CompCards",
      });
    }

    let passwordHash = null;
    if (portfolioData.visibility === "protected" && password) {
      passwordHash = await bcrypt.hash(password, 10);
    }

    const modelId = new mongoose.Types.ObjectId(portfolioData.modelId);

    // Construct final portfolio object
    const portfolioDocument = {
      model: modelId,
      modelName: portfolioData.modelName,
      slug: slug,
      template: portfolioData.template,
      visibility: portfolioData.visibility,
      measurements: {
        ...portfolioData.measurements,
        height: convertHeightToCm(portfolioData.measurements.height),
        weight: convertWeightToKg(portfolioData.measurements.weight),
      },
      contact: portfolioData.contact,
      canvas: {
        backgroundColor: canvasData.backgroundColor,
        elements: processedElements,
      },
      agency: agencyId,
      password: passwordHash,
      coverImage: {
        url: coverImage.path,
        filename: coverImage.filename,
        originalName: coverImage.originalname,
      },
      description: portfolioData.description,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Validate the document structure before saving
    try {
      const portfolio = new EditorialPortfolio(portfolioDocument);
      const validationError = portfolio.validateSync();
      if (validationError) {
        console.error("Validation error:", validationError);
        return res.status(400).json({
          success: false,
          message: "Data validation failed",
          error: validationError.message,
        });
      }
    } catch (validationError) {
      console.error("Pre-save validation error:", validationError);
      return res.status(400).json({
        success: false,
        message: "Data validation failed",
        error: validationError.message,
      });
    }

    // Save to database
    const savedPortfolio = await EditorialPortfolio.create(portfolioDocument);

    const model = await ModelUser.findById(modelId);

    model.editorialPortfolio = savedPortfolio._id;
    await model.save();

    const agency = await Agency.findById(agencyId);
    agency.portfolios.push(savedPortfolio._id);
    await agency.save();

    return res.status(201).json({
      success: true,
      message: "CompCard created successfully",
      portfolioId: savedPortfolio._id,
      slug: savedPortfolio.slug,
      data: savedPortfolio,
    });
  } catch (error) {
    console.error("Error saving portfolio:", error);

    // Provide more specific error information
    if (error.name === "ValidationError") {
      const validationErrors = Object.values(error.errors).map((err) => ({
        field: err.path,
        message: err.message,
        value: err.value,
      }));

      return res.status(400).json({
        success: false,
        message: "Validation failed",
        errors: validationErrors,
      });
    }

    return res.status(500).json({
      success: false,
      message: "Failed to save portfolio",
      error: error.message,
    });
  }
};

export const getPortfolioBySlug = async (req, res) => {
  const { slug } = req.params;

  if (!slug) {
    return res.status(400).json({
      success: false,
      error: "Slug is required",
    });
  }
  try {
    let portfolio;

    if (slug.startsWith("model-")) {
      portfolio = await ModelPortfolio.findOne({ slug })
        .populate("agency")
        .populate("model");
    } else if (slug.startsWith("editorial-")) {
      portfolio = await EditorialPortfolio.findOne({ slug })
        .populate("agency")
        .populate("model");
    }
    if (!portfolio) {
      return res.status(404).json({
        success: false,
        error: "Portfolio not found",
      });
    }

    if (portfolio.visibility === "private" && !req.user.id) {
      return res.status(403).json({
        success: false,
        error: "This portfolio is private. Please log in to view it.",
      });
    }

    const responsePortfolio = portfolio.toObject();
    delete responsePortfolio.password;

    return res.status(200).json({
      success: true,
      portfolio: responsePortfolio,
    });
  } catch (err) {
    console.error("Error fetching portfolio by slug:", err);
    return res.status(500).json({
      success: false,
      error: "Failed to fetch portfolio",
      details: process.env.NODE_ENV === "development" ? err.message : undefined,
    });
  }
};

export const getPublicPortfolioBySlug = async (req, res) => {
  const { slug } = req.params;

  if (!slug) {
    return res.status(400).json({
      success: false,
      error: "Slug is required",
    });
  }

  try {
    let portfolio;

    if (slug.startsWith("model-")) {
      portfolio = await ModelPortfolio.findOne({ slug })
        .populate("agency")
        .populate("model");
    } else if (slug.startsWith("editorial-")) {
      portfolio = await EditorialPortfolio.findOne({ slug })
        .populate("agency")
        .populate("model");
    }

    if (!portfolio) {
      return res.status(404).json({
        success: false,
        error: "Portfolio not found",
      });
    }

    if (portfolio.visibility !== "public") {
      return res.status(403).json({
        success: false,
        error: "This portfolio is not public",
      });
    }

    const responsePortfolio = portfolio.toObject();
    delete responsePortfolio.password;

    return res.status(200).json({
      success: true,
      portfolio: responsePortfolio,
    });
  } catch (err) {
    console.error("Error fetching public portfolio:", err);
    return res.status(500).json({
      success: false,
      error: "Failed to fetch portfolio",
      details: process.env.NODE_ENV === "development" ? err.message : undefined,
    });
  }
};

export const getAllModelsForAnAgency = async (req, res) => {
  const agencyId = req.user.id;

  try {
    const agency = await Agency.findById(agencyId).populate({
      path: "modelUsers",
      select: "-password",
      populate: [
        {
          path: "portfolio",
          select: "-password",
        },
        {
          path: "editorialPortfolio",
          select: "-password",
        },
      ],
    });

    if (!agency) {
      return res.status(404).json({
        success: false,
        error: "Agency not found",
      });
    }

    const models = agency.modelUsers;

    return res.status(200).json({
      success: true,
      models,
    });
  } catch (err) {
    console.error("Error fetching models for portfolio:", err);
    return res.status(500).json({
      success: false,
      error: "Failed to fetch models",
      details: process.env.NODE_ENV === "development" ? err.message : undefined,
    });
  }
};

export const verifyPassword = async (req, res) => {
  const { password, slug } = req.body;

  if (!password || !slug) {
    return res.status(400).json({
      accessGranted: false,
      message: "Slug and password are required!",
    });
  }

  try {
    let portfolio;

    if (slug.startsWith("model-")) {
      portfolio = await ModelPortfolio.findOne({ slug });
    } else if (slug.startsWith("editorial-")) {
      portfolio = await EditorialPortfolio.findOne({ slug });
    }

    if (!portfolio) {
      return res.status(404).json({
        accessGranted: false,
        message: "Portfolio not found",
      });
    }

    const isMatch = await bcrypt.compare(password, portfolio.password);
    if (!isMatch) {
      return res.status(403).json({
        accessGranted: false,
        message: "Incorrect password",
      });
    }

    return res.status(200).json({
      accessGranted: true,
      message: "Password verified successfully",
    });
  } catch (err) {
    console.error("Error verifying password:", err);
    return res.status(500).json({
      accessGranted: false,
      message: "Internal Server Error",
    });
  }
};

export const generateResetPasswordOTP = async (req, res) => {
  const agencyId = req.user.id;
  const { slug } = req.params;

  if (!slug) {
    return res.status(400).json({
      success: false,
      error: "Slug is required",
    });
  }

  try {
    const agency = await Agency.findById(agencyId);
    if (!agency) {
      return res.status(404).json({ message: "Agency not found" });
    }

    // Generate a 6-digit OTP
    const otp = Math.floor(100000 + Math.random() * 900000).toString();

    if (slug.startsWith("model-")) {
      const portfolio = await ModelPortfolio.findOne({ slug });
      if (!portfolio)
        return res.status(404).json({ message: "Portfolio not found" });

      portfolio.resetPasswordOTP.code = otp;
      portfolio.resetPasswordOTP.expiresAt = Date.now() + 10 * 60 * 1000;
      await portfolio.save();
    } else if (slug.startsWith("editorial-")) {
      const editorial = await EditorialPortfolio.findOne({ slug });
      if (!editorial)
        return res.status(404).json({ message: "Portfolio not found" });

      editorial.resetPasswordOTP.code = otp;
      editorial.resetPasswordOTP.expiresAt = Date.now() + 10 * 60 * 1000;
      await editorial.save();
    }

    const mailOptions = {
      from: `<${process.env.EMAIL_USER}>`,
      to: agency.agencyEmail,
      subject: "Reset Password OTP",
      text: `Your OTP to reset your password is: ${otp}`,
      html: `<p>Your OTP to reset your password is: <b>${otp}</b></p>`,
    };

    await transporter.sendMail(mailOptions);

    return res.status(200).json({ message: "OTP sent to registered email" });
  } catch (err) {
    console.error("Error sending OTP:", err);
    return res.status(500).json({ message: "Internal Server Error" });
  }
};

export const verifyOTP = async (req, res) => {
  const { slug } = req.params;
  const { otp } = req.body;

  if (!slug || !otp) {
    return res.status(400).json({
      success: false,
      error: "Slug and OTP are required",
    });
  }

  try {
    let portfolio;

    if (slug.startsWith("model-")) {
      portfolio = await ModelPortfolio.findOne({ slug });
    } else if (slug.startsWith("editorial-")) {
      portfolio = await EditorialPortfolio.findOne({ slug });
    } else {
      return res.status(400).json({ message: "Invalid slug format" });
    }

    if (!portfolio) {
      return res.status(404).json({ message: "Portfolio not found" });
    }

    const storedOTP = portfolio.resetPasswordOTP?.code;
    const otpExpiry = portfolio.resetPasswordOTP?.expiresAt;

    if (!storedOTP || !otpExpiry) {
      return res
        .status(400)
        .json({ message: "No OTP found. Please request a new one." });
    }

    if (storedOTP !== otp) {
      return res.status(401).json({ message: "Invalid OTP" });
    }

    if (Date.now() > otpExpiry) {
      return res.status(410).json({ message: "OTP has expired" });
    }

    // Optional: clear OTP after successful verification
    portfolio.resetPasswordOTP = undefined;
    portfolio.resetPasswordOTPVerified = true;
    await portfolio.save();

    return res.status(200).json({ message: "OTP verified successfully" });
  } catch (err) {
    console.error("Error verifying OTP:", err);
    return res.status(500).json({ message: "Internal Server Error" });
  }
};

export const resetPassword = async (req, res) => {
  const { slug } = req.params;
  const { newPassword } = req.body;

  if (!slug || !newPassword) {
    return res
      .status(400)
      .json({ message: "Slug and new password are required" });
  }

  try {
    let portfolio;

    if (slug.startsWith("model-")) {
      portfolio = await ModelPortfolio.findOne({ slug });
    } else if (slug.startsWith("editorial-")) {
      portfolio = await EditorialPortfolio.findOne({ slug });
    } else {
      return res.status(400).json({ message: "Invalid slug format" });
    }

    if (!portfolio) {
      return res.status(404).json({ message: "Portfolio not found" });
    }

    if (!portfolio.resetPasswordOTPVerified) {
      return res.status(403).json({ message: "OTP verification required" });
    }

    const hashedPassword = await bcrypt.hash(newPassword, 10);

    portfolio.password = hashedPassword;
    portfolio.resetPasswordOTPVerified = false;
    await portfolio.save();

    return res.status(200).json({ message: "Password reset successfully" });
  } catch (err) {
    console.error("Error resetting password:", err);
    return res.status(500).json({ message: "Internal server error" });
  }
};

//Get all models along with their portfolio status
