import mongoose from "mongoose";

const userSkillSchema = new mongoose.Schema(
  {
    user_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "UserProfile",
      required: true,
    },
    skill_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Skill",
      required: true,
    },
    level: {
      type: Number,
      required: true,
      min: 0,
      max: 5,
    },
    source: {
      type: String,
      enum: ["manual", "self", "auto", "peer"],
      default: "self",
    },
    evidence_url: {
      type: String,
    },
    evidence_file: {
      type: String,
    },
    endorsed_by: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "UserProfile",
      },
    ],
    updated_by: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "UserProfile",
    },
    updated_at: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  },
);

// Compound index to ensure one skill rating per user
userSkillSchema.index({ user_id: 1, skill_id: 1 }, { unique: true });

export default mongoose.model("UserSkill", userSkillSchema);
