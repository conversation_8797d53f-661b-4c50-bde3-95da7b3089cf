import express from "express";
import {
  createTool,
  getTools,
  getToolById,
  updateTool,
  deleteTool,
} from "../controllers/toolCatalogController.js";
import { verifyToken, verifyRole } from "../middlewares/authMiddleware.js";

const router = express.Router();

// Test route to verify endpoint is working
router.get("/test", (req, res) => {
  res.json({ message: "Tool catalog endpoint is working!" });
});

// Apply authentication middleware to all routes
router.use(verifyToken);
router.use(verifyRole("agency"));

// Create a new tool entry
router.post("/", createTool);

// Get all tool entries (optionally filter by agency)
router.get("/", getTools);

// Get a single tool entry by ID
router.get("/:id", getToolById);

// Update a tool entry by ID
router.put("/:id", updateTool);

// Delete a tool entry by ID
router.delete("/:id", deleteTool);

export default router;
