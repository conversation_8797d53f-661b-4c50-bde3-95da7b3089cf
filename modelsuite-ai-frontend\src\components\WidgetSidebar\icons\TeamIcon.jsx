import React from "react";

const TeamIcon = ({ className }) => {
  return (
    <svg
      width="100%"
      height="100%"
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <defs>
        {/* Background gradient */}
        <radialGradient id="teamBgGrad" cx="50%" cy="40%" r="75%">
          <stop offset="0%" stopColor="#0f112a" />
          <stop offset="60%" stopColor="#0a0b21" />
          <stop offset="100%" stopColor="#07081a" />
        </radialGradient>

        {/* Center stroke gradient (magenta -> violet) */}
        <linearGradient id="teamGradCenter" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" stopColor="#FF31D6" />
          <stop offset="50%" stopColor="#C34BFF" />
          <stop offset="100%" stopColor="#7C4DFF" />
        </linearGradient>

        {/* Left stroke gradient (cyan) */}
        <linearGradient id="teamGradLeft" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" stopColor="#38E5FF" />
          <stop offset="100%" stopColor="#00A4FF" />
        </linearGradient>

        {/* Right stroke gradient (blue) */}
        <linearGradient id="teamGradRight" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" stopColor="#45B2FF" />
          <stop offset="100%" stopColor="#1772FF" />
        </linearGradient>

        {/* Neon glow filter */}
        <filter id="teamGlow" x="-50%" y="-50%" width="200%" height="200%">
          <feGaussianBlur stdDeviation="8" result="blur" />
          <feMerge>
            <feMergeNode in="blur" />
            <feMergeNode in="SourceGraphic" />
          </feMerge>
        </filter>
      </defs>

      {/* Rounded square background */}
      <rect
        x="32"
        y="32"
        width="960"
        height="960"
        rx="160"
        ry="160"
        fill="url(#teamBgGrad)"
      />

      {/* Left person */}
      <g transform="translate(268, 362) scale(0.86)">
        {/* head */}
        <circle
          cx="0"
          cy="-32"
          r="92"
          fill="none"
          stroke="url(#teamGradLeft)"
          strokeWidth="28"
          strokeLinecap="round"
          strokeLinejoin="round"
          filter="url(#teamGlow)"
        />
        {/* body/shoulders */}
        <path
          d="M-166,160
             C-166,104 -118,78 -64,66
             C-18,56 18,56 64,66
             C118,78 166,104 166,160"
          fill="none"
          stroke="url(#teamGradLeft)"
          strokeWidth="28"
          strokeLinecap="round"
          strokeLinejoin="round"
          filter="url(#teamGlow)"
        />
      </g>

      {/* Right person */}
      <g transform="translate(756, 362) scale(0.86)">
        {/* head */}
        <circle
          cx="0"
          cy="-32"
          r="92"
          fill="none"
          stroke="url(#teamGradRight)"
          strokeWidth="28"
          strokeLinecap="round"
          strokeLinejoin="round"
          filter="url(#teamGlow)"
        />
        {/* body/shoulders */}
        <path
          d="M-166,160
             C-166,104 -118,78 -64,66
             C-18,56 18,56 64,66
             C118,78 166,104 166,160"
          fill="none"
          stroke="url(#teamGradRight)"
          strokeWidth="28"
          strokeLinecap="round"
          strokeLinejoin="round"
          filter="url(#teamGlow)"
        />
      </g>

      {/* Center person */}
      <g transform="translate(512, 424)">
        {/* head (slightly larger) */}
        <circle
          cx="0"
          cy="-60"
          r="120"
          fill="none"
          stroke="url(#teamGradCenter)"
          strokeWidth="34"
          strokeLinecap="round"
          strokeLinejoin="round"
          filter="url(#teamGlow)"
        />
        {/* "ear/comma" accents */}
        <path
          d="M-120,32
             c-18,22 -18,44 0,66"
          fill="none"
          stroke="url(#teamGradCenter)"
          strokeWidth="24"
          strokeLinecap="round"
          strokeLinejoin="round"
          filter="url(#teamGlow)"
        />
        <path
          d="M120,32
             c18,22 18,44 0,66"
          fill="none"
          stroke="url(#teamGradCenter)"
          strokeWidth="24"
          strokeLinecap="round"
          strokeLinejoin="round"
          filter="url(#teamGlow)"
        />
        {/* torso/shoulders */}
        <path
          d="M-224,280
             C-224,184 -150,146 -76,130
             C-20,118 20,118 76,130
             C150,146 224,184 224,280"
          fill="none"
          stroke="url(#teamGradCenter)"
          strokeWidth="34"
          strokeLinecap="round"
          strokeLinejoin="round"
          filter="url(#teamGlow)"
        />
      </g>
    </svg>
  );
};

export default TeamIcon;
