import Agency from "../models/agency.js";
import handleChatSockets from "./messanger/chatSocket.js";
import handleChannelSockets from "./messanger/channelSocket.js";
import handleDmSockets from "./messanger/dmSockets.js";
import handleGroupSockets from "./messanger/groupSockets.js";
import Chatfrommodeltostaff from "../models/supportsystem/chatfrommodeltostaff.js";
import Notificationfromstafftomodel from "../models/supportsystem/notifications.js";

const registerSocketHandlers = (io, socket, connectedUsers) => {
  let userId = null;
  let sessionStartTime = null;

  // Handle user connection
  socket.on("user:connect", async (data) => {
    try {
      userId = data.userId;
      sessionStartTime = new Date();

      // Update the user's session start time
      await Agency.findByIdAndUpdate(userId, {
        lastOnline: new Date(),
        sessionStartTime: sessionStartTime,
      });
    } catch (error) {
      console.error("Error in user:connect:", error);
    }
  });

  // Handle periodic activity updates (every minute)
  socket.on("user:activity", async () => {
    try {
      if (userId && sessionStartTime) {
        const now = new Date();

        // Update the total active minutes and last online time
        await Agency.findByIdAndUpdate(userId, {
          lastOnline: now,
          $inc: { totalActiveMinutes: 1 }, // Increment by 1 minute
        });
      }
    } catch (error) {
      console.error("Error in user:activity:", error);
    }
  });

  // Handle disconnection
  socket.on("disconnect", async () => {
    try {
      if (userId && sessionStartTime) {
        const now = new Date();
        const activeMinutes = Math.floor((now - sessionStartTime) / 60000);

        // Update final activity count
        await Agency.findByIdAndUpdate(userId, {
          lastOnline: now,
          sessionStartTime: null,
          $inc: { totalActiveMinutes: activeMinutes },
        });
      }
    } catch (error) {
      console.error("Error in disconnect:", error);
    }
  });
  socket.on("join", (userId) => {
    socket.join(userId); // joining a room with user's ID
    console.log(`User ${userId} joined`);
  });
  socket.on("send_message", async ({ senderId, receiverId, message }) => {
    try {
      const chat = await Chatfrommodeltostaff.create({
        senderId,
        receiverId,
        message,
      });

      io.to(receiverId).emit("receive_message", chat);
    } catch (error) {
      console.error("Chat error:", error);
    }
  });

  socket.on(
    "send_notification",
    async ({ sender, receiver, message, type }) => {
      try {
        const notification = await Notificationfromstafftomodel.create({
          sender,
          receiver,
          message,
          type,
        });
        io.to(receiver).emit("receive_notification", notification);
      } catch (error) {
        console.error("Notification error:", error);
      }
    },
  );

  // Register messanger socket handlers
  handleChatSockets(io, socket, connectedUsers);
  handleChannelSockets(io, socket, connectedUsers);
  handleDmSockets(io, socket, connectedUsers);
  handleGroupSockets(io, socket, connectedUsers);

  // resiter meeting socket handlers
};

export default registerSocketHandlers;
