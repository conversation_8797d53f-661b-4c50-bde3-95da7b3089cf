import axios from "axios";

const OPENWEATHER_API_KEY = process.env.OPENWEATHER_API_KEY;
const WEATHER_CACHE = new Map();
const CACHE_DURATION = 10 * 60 * 1000; // 10 minutes

export const getWeatherData = async (req, res) => {
  try {
    const { lat, lon, units = "metric", type = "current" } = req.query;

    if (!lat || !lon) {
      return res.status(400).json({
        success: false,
        message: "Latitude and longitude are required",
      });
    }

    const cacheKey = `${type}_${lat}_${lon}_${units}`;
    const cached = WEATHER_CACHE.get(cacheKey);

    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return res.status(200).json({
        success: true,
        data: cached.data,
        message: "Weather data retrieved from cache",
      });
    }

    const endpoint =
      type === "forecast"
        ? "https://api.openweathermap.org/data/2.5/forecast"
        : "https://api.openweathermap.org/data/2.5/weather";

    const response = await axios.get(endpoint, {
      params: {
        lat,
        lon,
        units,
        appid: OPENWEATHER_API_KEY,
      },
    });

    const weatherData = {
      ...response.data,
      cached: false,
      timestamp: new Date().toISOString(),
    };

    WEATHER_CACHE.set(cacheKey, {
      data: weatherData,
      timestamp: Date.now(),
    });

    res.status(200).json({
      success: true,
      data: weatherData,
      message: "Weather data retrieved successfully",
    });
  } catch (error) {
    console.error("Weather API Error:", error.response?.data || error.message);

    res.status(500).json({
      success: false,
      message: "Failed to fetch weather data",
      error: error.response?.data?.message || error.message,
    });
  }
};

export const getLocationSuggestions = async (req, res) => {
  try {
    const { q, limit = 5 } = req.query;

    if (!q || q.trim().length < 2) {
      return res.status(400).json({
        success: false,
        message: "Search query must be at least 2 characters long",
      });
    }

    const response = await axios.get(
      "http://api.openweathermap.org/geo/1.0/direct",
      {
        params: {
          q: q.trim(),
          limit: Math.min(limit, 10), // Prevent too many results
          appid: OPENWEATHER_API_KEY,
        },
      }
    );

    const locations = response.data.map((location) => ({
      name: location.name,
      country: location.country,
      state: location.state,
      lat: location.lat,
      lon: location.lon,
      displayName: `${location.name}${
        location.state ? `, ${location.state}` : ""
      }, ${location.country}`,
    }));

    res.status(200).json({
      success: true,
      data: locations,
      message: "Location suggestions retrieved successfully",
    });
  } catch (error) {
    console.error(
      "Location Search Error:",
      error.response?.data || error.message
    );

    res.status(500).json({
      success: false,
      message: "Failed to search locations",
      error: error.response?.data?.message || error.message,
    });
  }
};

// Clean up old cache entries every hour
setInterval(() => {
  const now = Date.now();
  for (const [key, value] of WEATHER_CACHE.entries()) {
    if (now - value.timestamp > CACHE_DURATION) {
      WEATHER_CACHE.delete(key);
    }
  }
}, 60 * 60 * 1000); // 1 hour
