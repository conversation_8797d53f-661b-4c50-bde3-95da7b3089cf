import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { Plus, Search, Filter, X, ArrowLeft, ChevronDown } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DataTable } from "@/reusable/table/DataTable";
import StatusBadge from "@/reusable/Status/StatusBadge";
import AssignmentModal from "./AssignmentModal";

import { fetchAssignments } from "@/redux/features/questionnaire/questionnaireSlice";

export default function AssignmentsPage() {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { assignments, assignmentLoading } = useSelector(
    (state) => state.questionnaireReducer
  );

  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 5, // Show only 5 items initially
  });

  const [displayCount, setDisplayCount] = useState(5); // Track how many items to show
  const [searchTerm, setSearchTerm] = useState("");
  const [showAssignmentModal, setShowAssignmentModal] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    status: "",
    template: "",
  });

  useEffect(() => {
    dispatch(fetchAssignments());
  }, [dispatch]);

  const columns = [
    {
      accessorKey: "templateId.title",
      header: "Template",
      cell: ({ row }) => (
        <span className="font-medium text-white">
          {row.original.templateId?.title || "Unknown Template"}
        </span>
      ),
    },
    {
      accessorKey: "modelId.name",
      header: "Model",
      cell: ({ row }) => (
        <span className="text-gray-300">
          {row.original.modelId?.name || "Unknown Model"}
        </span>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const statusVariants = {
          pending: "warning",
          in_progress: "default",
          completed: "success",
          overdue: "destructive",
        };
        return (
          <StatusBadge
            status={row.original.status}
            variant={statusVariants[row.original.status] || "secondary"}
          />
        );
      },
    },
    {
      accessorKey: "assignedAt",
      header: "Assigned",
      cell: ({ row }) => (
        <span className="text-sm text-gray-400">
          {new Date(row.original.assignedAt).toLocaleDateString()}
        </span>
      ),
    },
    {
      accessorKey: "dueDate",
      header: "Due Date",
      cell: ({ row }) => (
        <span className="text-sm text-gray-400">
          {row.original.dueDate
            ? new Date(row.original.dueDate).toLocaleDateString()
            : "No due date"}
        </span>
      ),
    },
  ];

  const handleNewAssignment = () => {
    setShowAssignmentModal(true);
  };

  const handleLoadMore = () => {
    setDisplayCount((prev) => prev + 5);
  };

  const handleGoBack = () => {
    navigate("/agency/questionnaires");
  };

  const handleFilterChange = (key, value) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const clearFilters = () => {
    setFilters({
      status: "",
      template: "",
    });
  };

  const filteredAssignments =
    assignments?.filter((assignment) => {
      const matchesSearch =
        assignment.templateId?.title
          ?.toLowerCase()
          .includes(searchTerm.toLowerCase()) ||
        assignment.modelId?.name
          ?.toLowerCase()
          .includes(searchTerm.toLowerCase());

      const matchesStatus =
        !filters.status || assignment.status === filters.status;
      const matchesTemplate =
        !filters.template || assignment.templateId?._id === filters.template;

      return matchesSearch && matchesStatus && matchesTemplate;
    }) || [];

  // Get assignments to display based on displayCount
  const displayedAssignments = filteredAssignments.slice(0, displayCount);
  const hasMoreAssignments = filteredAssignments.length > displayCount;

  // Get unique templates for filter dropdown
  const uniqueTemplates = [
    ...new Map(
      assignments?.map((assignment) => [
        assignment.templateId?._id,
        assignment.templateId,
      ])
    ).values(),
  ].filter(Boolean);

  return (
    <div className="p-4">
      <div className="max-w-7xl mx-auto space-y-4">
        {/* Back Button */}
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            onClick={handleGoBack}
            className="text-gray-300 hover:text-white hover:bg-[#27272a] p-2"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Templates
          </Button>
        </div>

        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold tracking-tight text-white">
              Questionnaire Assignments
            </h1>
            <p className="text-gray-400">
              Track and manage questionnaire assignments to your models
            </p>
          </div>
          <Button
            className="bg-[#1a1a1a] hover:bg-[#0f0f0f] text-white"
            onClick={handleNewAssignment}
          >
            <Plus className="mr-2 h-4 w-4" />
            New Assignment
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card className="bg-[#18181b] border-[#27272a]">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-300">
                Total Assignments
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                {assignments?.length || 0}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-[#18181b] border-[#27272a]">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-300">
                Pending
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                {assignments?.filter((a) => a.status === "pending")?.length ||
                  0}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-[#18181b] border-[#27272a]">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-300">
                In Progress
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                {assignments?.filter((a) => a.status === "in_progress")
                  ?.length || 0}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-[#18181b] border-[#27272a]">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-300">
                Completed
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                {assignments?.filter((a) => a.status === "completed")?.length ||
                  0}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Assignments Table */}
        <Card className="bg-[#18181b] border-[#27272a]">
          <CardHeader>
            <CardTitle className="text-white">All Assignments</CardTitle>
            <CardDescription className="text-gray-400">
              View and manage questionnaire assignments
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Search and Filters */}
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <div className="relative flex-1 max-w-sm">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search assignments..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-8 bg-[#27272a] border-[#3f3f46] text-white"
                  />
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowFilters(!showFilters)}
                  className="bg-[#27272a] border-[#3f3f46] text-white hover:bg-[#3f3f46] hover:text-white"
                >
                  <Filter className="mr-2 h-4 w-4" />
                  Filters
                </Button>
              </div>

              {/* Filter Panel */}
              {showFilters && (
                <div className="p-4 bg-[#27272a] border border-[#3f3f46] rounded-lg space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-medium text-white">
                      Filter Assignments
                    </h3>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowFilters(false)}
                      className="text-gray-400 hover:text-white hover:bg-[#3f3f46]"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="grid gap-4 md:grid-cols-3">
                    <div className="space-y-2">
                      <label className="text-sm text-gray-300">Status</label>
                      <Select
                        value={filters.status}
                        onValueChange={(value) =>
                          handleFilterChange("status", value)
                        }
                      >
                        <SelectTrigger className="bg-[#18181b] border-[#3f3f46] text-white">
                          <SelectValue placeholder="All statuses" />
                        </SelectTrigger>
                        <SelectContent className="bg-[#18181b] border-[#27272a]">
                          <SelectItem
                            value="pending"
                            className="text-white hover:bg-[#27272a]"
                          >
                            Pending
                          </SelectItem>
                          <SelectItem
                            value="in_progress"
                            className="text-white hover:bg-[#27272a]"
                          >
                            In Progress
                          </SelectItem>
                          <SelectItem
                            value="completed"
                            className="text-white hover:bg-[#27272a]"
                          >
                            Completed
                          </SelectItem>
                          <SelectItem
                            value="overdue"
                            className="text-white hover:bg-[#27272a]"
                          >
                            Overdue
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm text-gray-300">Template</label>
                      <Select
                        value={filters.template}
                        onValueChange={(value) =>
                          handleFilterChange("template", value)
                        }
                      >
                        <SelectTrigger className="bg-[#18181b] border-[#3f3f46] text-white">
                          <SelectValue placeholder="All templates" />
                        </SelectTrigger>
                        <SelectContent className="bg-[#18181b] border-[#27272a]">
                          {uniqueTemplates.map((template) => (
                            <SelectItem
                              key={template._id}
                              value={template._id}
                              className="text-white hover:bg-[#27272a]"
                            >
                              {template.title}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex items-end">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={clearFilters}
                        className="bg-[#18181b] border-[#3f3f46] text-white hover:bg-[#3f3f46] hover:text-white"
                      >
                        Clear Filters
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <DataTable
              columns={columns}
              data={displayedAssignments}
              pagination={pagination}
              setPagination={setPagination}
              loading={assignmentLoading}
              disableHover={true}
            />

            {/* Load More Button */}
            {hasMoreAssignments && (
              <div className="flex justify-center pt-4 border-t border-[#27272a]">
                <Button
                  variant="outline"
                  onClick={handleLoadMore}
                  className="bg-[#27272a] border-[#3f3f46] text-white hover:bg-[#3f3f46] hover:text-white"
                  disabled={assignmentLoading}
                >
                  <ChevronDown className="mr-2 h-4 w-4" />
                  Load More ({filteredAssignments.length - displayCount}{" "}
                  remaining)
                </Button>
              </div>
            )}

            {/* Show all count when filtered */}
            {filteredAssignments.length > 0 && (
              <div className="text-center text-sm text-gray-400 pt-2">
                Showing {displayedAssignments.length} of{" "}
                {filteredAssignments.length} assignments
              </div>
            )}
          </CardContent>
        </Card>

        {/* Assignment Modal */}
        <AssignmentModal
          isOpen={showAssignmentModal}
          onClose={() => setShowAssignmentModal(false)}
        />
      </div>
    </div>
  );
}
