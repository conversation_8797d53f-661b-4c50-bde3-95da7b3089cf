# Dashboard Development Guide

Welcome to the documentation for the **modelsuite-ai-frontend** dashboard. This guide covers design standards, reusable components, technical patterns, and best practices to help all team members contribute effectively.

---

## 1. Design System & Theme

### Font Family
- **Poppins** is the primary font. Set globally in `App.css`:
  ```css
  * {
    font-family: 'poppins', sans-serif;
  }
  ```

### Colors & Variables
- #0f0f0f
- #1a1a1a
- Use these variables for backgrounds, borders.

### Font Sizes
- Use Tailwind or shadcn/ui defaults for text sizes unless otherwise specified.
  at momemnt,we are using 18px(header),14px(sub-header and text),12px(table-header)

### Icons
- Use [Lucide](https://lucide.dev/) icons for all icons.

---

## 2. UI Library
- **Only use [`shadcn/ui`](https://ui.shadcn.com/) components.**
- No other UI frameworks (e.g., MUI, Chakra, Antd) are allowed.
- For inspiration, visit [<PERSON><PERSON><PERSON>](https://dribbble.com/) and [<PERSON><PERSON><PERSON>](https://www.behance.net/).

---

## 3. Reusable Components
All custom reusable components are in `src/reusable/`. Example usage and props:

### InputFormField
`src/reusable/Input/InputFormField.jsx`
```jsx
<InputFormField
  name="email"
  label="Email"
  control={control}
  type="email"
  placeholder="Enter your email"
/>
```
- Uses shadcn/ui Form and Input under the hood.

### ButtonLoader
`src/reusable/Loader/ButtonLoader.jsx`
```jsx
<ButtonLoader />
```
- Shows a spinning loader (uses lucide-react Loader icon).

### DataTable & DataTablePagination
`src/reusable/table/DataTable.jsx`
```jsx
<DataTable
  columns={columns}
  data={data}
  pagination={pagination}
  setPagination={setPagination}
  hasClick={true}
  onClickRoute={handleRowClick}
/>
```
- Uses shadcn/ui Table components internally.
- `DataTablePagination` provides pagination controls:
```jsx
<DataTablePagination table={tableInstance} data={data} />
```

---

## 4. shadcn/ui Components
All custom UI components are in `src/components/ui/`.

- **Button** (`button.jsx`):
  - Variants: `default`, `destructive`, `outline`, `secondary`, `ghost`, `link`.
  - Sizes: `default`, `sm`, `lg`, `icon`.
  - Usage:
    ```jsx
    <Button variant="outline" size="sm">Click me</Button>
    ```

- **Card** (`card.jsx`):
  - Compound components: `Card`, `CardHeader`, `CardTitle`, `CardDescription`, `CardContent`, `CardFooter`.
  - Usage:
    ```jsx
    <Card>
      <CardHeader>
        <CardTitle>Dashboard</CardTitle>
        <CardDescription>Overview</CardDescription>
      </CardHeader>
      <CardContent>...</CardContent>
      <CardFooter>...</CardFooter>
    </Card>
    ```

- Many more: Accordion, Dialog, DropdownMenu, Input, Label, Separator, Sheet, Sidebar, Skeleton, Table, Tabs, Tooltip, etc.

---

## 5. State Management (Redux Slices)
Slices are in `src/redux/features/`.

Example (from `auth/loginSlice.jsx`):
```js
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axiosInstance from "@/config/axiosInstance";

export const loginUser = createAsyncThunk("loginUser", async ({ identifier, password, userType }, { rejectWithValue }) => {
  try {
    const endpoint = userType === "agency" ? "/agency/login" : "/model/login";
    const response = await axiosInstance.post(endpoint, { identifier, password });
    localStorage.setItem("token", response.data.token);
    return response.data;
  } catch (error) {
    return rejectWithValue(error.response?.data?.error || "Login failed");
  }
});

const authSlice = createSlice({
  name: "auth",
  initialState: {},
  reducers: {
    logout(state) { localStorage.removeItem("token"); },
    // ...
  },
  extraReducers: (builder) => { /* ... */ },
});
```
- Use `createAsyncThunk` for async logic.
- Always use the shared `axiosInstance` (see below).

---

## 6. API Calls (axiosInstance)
Shared instance in `src/config/axiosInstance.js`:
```js
import axios from "axios";

const axiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  withCredentials: true,
});

axiosInstance.interceptors.request.use((config) => {
  const token = localStorage.getItem("token");
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

axiosInstance.interceptors.response.use(
  (res) => res,
  (err) => {
    if (err.response?.status === 401) {
      localStorage.removeItem("token");
      console.warn("Unauthorized. Logging out...");
    }
    return Promise.reject(err);
  }
);

export default axiosInstance;
```
- Always import and use this instance for all API calls.

---

## 7. Collaboration & Best Practices
- **Ask questions in the general chat on Discord.**
- **UI inspiration:** Refer to Dribbble and Behance for modern dashboard ideas.
- **Stick to shadcn/ui** for all UI work, no other UI frameworks.
- **Keep reusable components generic** and well-documented.

---

## 8. Screenshots
![Dashboard](Screenshot 2025-08-09 113951.png)
![AgencySettings](Screenshot 2025-08-09 114032.png)

---

## 9. References
- [shadcn/ui Docs](https://ui.shadcn.com/)
- [Dribbble](https://dribbble.com/) (UI inspiration)
- [Behance](https://www.behance.net/) (UI inspiration)

---

**Happy building!** For any doubts, reach out in Discord general chat. Keep this guide up to date as the project evolves.
