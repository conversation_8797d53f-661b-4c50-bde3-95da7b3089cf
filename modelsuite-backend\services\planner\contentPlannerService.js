import ContentPlan from "../../models/planner/ContentPlan.js";
import ContentTask from "../../models/planner/ContentTask.js";
import ContentGoal from "../../models/planner/ContentGoal.js";
import AutoScheduleLog from "../../models/planner/AutoScheduleLog.js";
import PersonaProfile from "../../models/persona/PersonaProfile.js";
import ModelUser from "../../models/model.js";
import Agency from "../../models/agency.js";
import { ApiError } from "../../utils/ApiError.js";
import aiContentService from "./aiContentService.js";

/**
 * Content Planner Service
 * Handles content planning, goal setting, and task management
 */
class ContentPlannerService {
  /**
   * Create a new content plan for a model
   */
  async createPlan(planData, createdBy, createdByType) {
    try {
      // Validate model and agency exist
      const model = await ModelUser.findById(planData.modelId);
      if (!model) {
        throw new ApiError(404, "Model not found");
      }

      const agency = await Agency.findById(planData.agencyId);
      if (!agency) {
        throw new ApiError(404, "Agency not found");
      }

      // Check for existing plan in the same week
      const existingPlan = await ContentPlan.findOne({
        modelId: planData.modelId,
        agencyId: planData.agencyId,
        weekStart: planData.weekStart,
        isArchived: false,
      });

      if (existingPlan) {
        throw new ApiError(400, "A plan already exists for this week");
      }

      // Create the plan
      const plan = new ContentPlan({
        ...planData,
        createdBy,
        createdByType,
        version: 1,
      });

      await plan.save();

      // Populate related data
      await plan.populate([
        { path: "modelId", select: "username fullName" },
        { path: "goals", select: "type targetAudience contentTone" },
      ]);

      return plan;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      throw new ApiError(
        500,
        `Failed to create content plan: ${error.message}`,
      );
    }
  }

  /**
   * Add a task to an existing plan
   */
  async addTask(taskData, createdBy, createdByType) {
    try {
      // Validate plan exists
      const plan = await ContentPlan.findById(taskData.planId);
      if (!plan) {
        throw new ApiError(404, "Content plan not found");
      }

      // Validate user has access to this plan
      if (
        createdByType === "Agency" &&
        plan.agencyId.toString() !== createdBy.toString()
      ) {
        throw new ApiError(403, "Access denied to this plan");
      }

      if (
        createdByType === "ModelUser" &&
        plan.modelId.toString() !== createdBy.toString()
      ) {
        throw new ApiError(403, "Access denied to this plan");
      }

      // Create the task
      const task = new ContentTask({
        ...taskData,
        agencyId: plan.agencyId,
        modelId: plan.modelId,
        createdBy,
        createdByType,
      });

      await task.save();

      // Add task to plan
      await plan.addTask(task._id);

      // Update plan distributions
      await this.updatePlanDistributions(plan._id);

      // Populate task data
      await task.populate([
        { path: "planId", select: "name weekStart" },
        { path: "goalIds", select: "type targetAudience" },
      ]);

      return task;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      throw new ApiError(500, `Failed to add task: ${error.message}`);
    }
  }

  /**
   * Get weekly plan for a model
   */
  async getWeeklyPlan(modelId, weekStart, agencyId) {
    try {
      // Parse week start date
      const startDate = new Date(weekStart);
      const endDate = new Date(startDate);
      endDate.setDate(endDate.getDate() + 6);

      // Find the plan
      const plan = await ContentPlan.findOne({
        modelId,
        agencyId,
        weekStart: {
          $gte: startDate,
          $lt: new Date(startDate.getTime() + 7 * 24 * 60 * 60 * 1000),
        },
        isArchived: false,
      })
        .populate("goals", "type targetAudience contentTone status progress")
        .populate({
          path: "tasks",
          populate: {
            path: "goalIds",
            select: "type targetAudience",
          },
        })
        .populate("modelId", "username fullName")
        .populate("createdBy", "fullName email");

      if (!plan) {
        return null;
      }

      // Get tasks for this week
      const tasks = await ContentTask.find({
        planId: plan._id,
        date: { $gte: startDate, $lte: endDate },
        isArchived: false,
      })
        .populate("goalIds", "type targetAudience")
        .sort({ date: 1, scheduledTime: 1 });

      // Update task completion count
      await plan.updateTaskCompletion();

      return {
        plan,
        tasks,
        summary: {
          totalTasks: tasks.length,
          completedTasks: tasks.filter((t) => t.status === "published").length,
          scheduledTasks: tasks.filter((t) => t.status === "scheduled").length,
          draftTasks: tasks.filter((t) => t.status === "draft").length,
          weekProgress: plan.completionPercentage,
          healthScore: plan.healthScore,
        },
      };
    } catch (error) {
      throw new ApiError(500, `Failed to get weekly plan: ${error.message}`);
    }
  }

  /**
   * Edit an existing plan
   */
  async editPlan(planId, updateData, userId, userType) {
    try {
      const plan = await ContentPlan.findById(planId);
      if (!plan) {
        throw new ApiError(404, "Content plan not found");
      }

      // Check permissions
      if (
        userType === "Agency" &&
        plan.agencyId.toString() !== userId.toString()
      ) {
        throw new ApiError(403, "Access denied to this plan");
      }

      if (
        userType === "ModelUser" &&
        plan.modelId.toString() !== userId.toString()
      ) {
        throw new ApiError(403, "Access denied to this plan");
      }

      // Create backup version before updating
      plan.version += 1;

      // Update plan data
      Object.keys(updateData).forEach((key) => {
        if (key !== "_id" && key !== "createdBy" && key !== "createdByType") {
          plan[key] = updateData[key];
        }
      });

      await plan.save();

      // Update distributions if tasks or goals changed
      if (updateData.tasks || updateData.goals) {
        await this.updatePlanDistributions(planId);
      }

      await plan.populate([
        { path: "goals", select: "type targetAudience contentTone" },
        { path: "tasks", select: "title date status mediaType" },
        { path: "modelId", select: "username fullName" },
      ]);

      return plan;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      throw new ApiError(500, `Failed to edit plan: ${error.message}`);
    }
  }

  /**
   * Add goal to a plan
   */
  async addGoalToPlan(planId, goalData, createdBy, createdByType) {
    try {
      const plan = await ContentPlan.findById(planId);
      if (!plan) {
        throw new ApiError(404, "Content plan not found");
      }

      // Validate persona exists
      if (goalData.personaId) {
        const persona = await PersonaProfile.findById(goalData.personaId);
        if (!persona) {
          throw new ApiError(404, "Persona not found");
        }
      }

      // Create the goal
      const goal = new ContentGoal({
        ...goalData,
        agencyId: plan.agencyId,
        modelId: plan.modelId,
        createdBy,
        createdByType,
      });

      await goal.save();

      // Add goal to plan
      await plan.addGoal(goal._id);

      await goal.populate("personaId", "personaText tags");

      return goal;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      throw new ApiError(500, `Failed to add goal to plan: ${error.message}`);
    }
  }

  /**
   * Get plans by date range
   */
  async getPlansByDateRange(agencyId, startDate, endDate, modelId = null) {
    try {
      const query = {
        agencyId,
        weekStart: { $gte: new Date(startDate), $lte: new Date(endDate) },
        isArchived: false,
      };

      if (modelId) {
        query.modelId = modelId;
      }

      const plans = await ContentPlan.find(query)
        .populate("modelId", "username fullName")
        .populate("goals", "type status progress")
        .populate("tasks", "status date")
        .sort({ weekStart: 1 });

      return plans.map((plan) => ({
        ...plan.toObject(),
        summary: {
          tasksCount: plan.tasks.length,
          completionRate: plan.completionPercentage,
          healthScore: plan.healthScore,
        },
      }));
    } catch (error) {
      throw new ApiError(500, `Failed to get plans: ${error.message}`);
    }
  }

  /**
   * Update task status
   */
  async updateTaskStatus(
    taskId,
    status,
    userId,
    userType,
    additionalData = {},
  ) {
    try {
      const task = await ContentTask.findById(taskId);
      if (!task) {
        throw new ApiError(404, "Task not found");
      }

      // Check permissions
      if (
        userType === "Agency" &&
        task.agencyId.toString() !== userId.toString()
      ) {
        throw new ApiError(403, "Access denied to this task");
      }

      if (
        userType === "ModelUser" &&
        task.modelId.toString() !== userId.toString()
      ) {
        throw new ApiError(403, "Access denied to this task");
      }

      // Update task
      task.status = status;

      // Handle additional data based on status
      if (status === "scheduled" && additionalData.scheduledTime) {
        task.scheduledTime = additionalData.scheduledTime;
      }

      if (status === "published" && additionalData.platforms) {
        additionalData.platforms.forEach((platformData) => {
          const platform = task.platforms.find(
            (p) => p.name === platformData.name,
          );
          if (platform) {
            platform.published = true;
            platform.publishedAt = new Date();
            platform.postId = platformData.postId;
            if (platformData.metrics) {
              platform.metrics = {
                ...platform.metrics,
                ...platformData.metrics,
              };
            }
          }
        });
      }

      await task.save();

      // Update plan completion
      const plan = await ContentPlan.findById(task.planId);
      if (plan) {
        await plan.updateTaskCompletion();
      }

      return task;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      throw new ApiError(500, `Failed to update task status: ${error.message}`);
    }
  }

  /**
   * Get agency analytics
   */
  async getAgencyAnalytics(agencyId, timeframe = 30) {
    try {
      const [planAnalytics, taskMetrics] = await Promise.all([
        ContentPlan.getAgencyAnalytics(agencyId, timeframe),
        this.getTaskAnalytics(agencyId, timeframe),
      ]);

      const analytics = {
        plans: planAnalytics[0] || {
          totalPlans: 0,
          activePlans: 0,
          completedPlans: 0,
          avgCompletionRate: 0,
          totalTasks: 0,
          totalTasksCompleted: 0,
          avgEngagementRate: 0,
        },
        tasks: taskMetrics,
        timeline: await this.getTimelineAnalytics(agencyId, timeframe),
      };

      return analytics;
    } catch (error) {
      throw new ApiError(500, `Failed to get analytics: ${error.message}`);
    }
  }

  /**
   * Helper method to update plan distributions
   */
  async updatePlanDistributions(planId) {
    try {
      const plan = await ContentPlan.findById(planId).populate("tasks");

      if (!plan) return;

      // Reset distributions
      const platformDist = {
        instagram: 0,
        tiktok: 0,
        facebook: 0,
        twitter: 0,
        linkedin: 0,
      };
      const contentTypeDist = {
        image: 0,
        video: 0,
        carousel: 0,
        reel: 0,
        story: 0,
      };

      // Count distributions
      plan.tasks.forEach((task) => {
        // Count platforms
        task.platforms.forEach((platform) => {
          if (platformDist.hasOwnProperty(platform.name)) {
            platformDist[platform.name]++;
          }
        });

        // Count content types
        if (contentTypeDist.hasOwnProperty(task.mediaType)) {
          contentTypeDist[task.mediaType]++;
        }
      });

      // Update plan
      plan.platformDistribution = platformDist;
      plan.contentTypeDistribution = contentTypeDist;

      await plan.save();
    } catch (error) {
      console.error("Error updating plan distributions:", error);
    }
  }

  /**
   * Helper method to get task analytics
   */
  async getTaskAnalytics(agencyId, timeframe) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - timeframe);

      const endDate = new Date();

      const metrics = await ContentTask.getTaskMetrics(agencyId, {
        startDate,
        endDate,
      });

      return (
        metrics[0] || {
          totalTasks: 0,
          totalEngagement: 0,
          avgEngagementRate: 0,
          platformBreakdown: [],
        }
      );
    } catch (error) {
      console.error("Error getting task analytics:", error);
      return {
        totalTasks: 0,
        totalEngagement: 0,
        avgEngagementRate: 0,
        platformBreakdown: [],
      };
    }
  }

  /**
   * Helper method to get timeline analytics
   */
  async getTimelineAnalytics(agencyId, timeframe) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - timeframe);

      const tasks = await ContentTask.find({
        agencyId,
        createdAt: { $gte: startDate },
        isArchived: false,
      })
        .select("createdAt status date")
        .sort({ createdAt: 1 });

      // Group by day
      const timeline = {};
      tasks.forEach((task) => {
        const day = task.createdAt.toISOString().split("T")[0];
        if (!timeline[day]) {
          timeline[day] = { total: 0, published: 0, scheduled: 0, draft: 0 };
        }
        timeline[day].total++;
        timeline[day][task.status]++;
      });

      return Object.keys(timeline).map((date) => ({
        date,
        ...timeline[date],
      }));
    } catch (error) {
      console.error("Error getting timeline analytics:", error);
      return [];
    }
  }

  /**
   * Create plan from template
   */
  async createFromTemplate(templateId, planData, createdBy, createdByType) {
    try {
      const template = await ContentPlan.findById(templateId);
      if (!template || !template.isTemplate) {
        throw new ApiError(404, "Template not found");
      }

      // Create new plan based on template
      const newPlan = new ContentPlan({
        ...planData,
        name:
          planData.name ||
          `${template.name} - ${new Date().toISOString().split("T")[0]}`,
        themes: [...template.themes],
        schedulingPreferences: { ...template.schedulingPreferences },
        platformDistribution: { ...template.platformDistribution },
        contentTypeDistribution: { ...template.contentTypeDistribution },
        basedOnTemplate: templateId,
        createdBy,
        createdByType,
      });

      await newPlan.save();

      return newPlan;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      throw new ApiError(
        500,
        `Failed to create plan from template: ${error.message}`,
      );
    }
  }

  /**
   * Archive plan
   */
  async archivePlan(planId, userId, userType) {
    try {
      const plan = await ContentPlan.findById(planId);
      if (!plan) {
        throw new ApiError(404, "Plan not found");
      }

      // Check permissions
      if (
        userType === "Agency" &&
        plan.agencyId.toString() !== userId.toString()
      ) {
        throw new ApiError(403, "Access denied to this plan");
      }

      plan.isArchived = true;
      plan.archivedAt = new Date();
      plan.archivedBy = userId;

      await plan.save();

      return { message: "Plan archived successfully" };
    } catch (error) {
      if (error instanceof ApiError) throw error;
      throw new ApiError(500, `Failed to archive plan: ${error.message}`);
    }
  }

  // ===== AI-POWERED METHODS =====

  /**
   * Generate AI content strategy for a model
   */
  async generateAIStrategy(modelId, agencyId, preferences = {}) {
    try {
      return await aiContentService.generateContentStrategy(
        modelId,
        agencyId,
        preferences,
      );
    } catch (error) {
      if (error instanceof ApiError) throw error;
      throw new ApiError(
        500,
        `Failed to generate AI strategy: ${error.message}`,
      );
    }
  }

  /**
   * Get AI content suggestions for a plan
   */
  async getAIContentSuggestions(planId, platform, count = 5) {
    try {
      return await aiContentService.generateContentSuggestions(
        planId,
        platform,
        count,
      );
    } catch (error) {
      if (error instanceof ApiError) throw error;
      throw new ApiError(500, `Failed to get AI suggestions: ${error.message}`);
    }
  }

  /**
   * Analyze optimal posting times using AI
   */
  async analyzeOptimalTiming(modelId, platform, weekData = {}) {
    try {
      return await aiContentService.analyzeOptimalTiming(
        modelId,
        platform,
        weekData,
      );
    } catch (error) {
      if (error instanceof ApiError) throw error;
      throw new ApiError(500, `Failed to analyze timing: ${error.message}`);
    }
  }

  /**
   * Generate AI theme suggestions
   */
  async generateAIThemes(modelId, agencyId, preferences = {}) {
    try {
      return await aiContentService.generateThemeSuggestions(
        modelId,
        agencyId,
        preferences,
      );
    } catch (error) {
      if (error instanceof ApiError) throw error;
      throw new ApiError(500, `Failed to generate themes: ${error.message}`);
    }
  }

  /**
   * Create AI-powered content plan with suggestions
   */
  async createAIPoweredPlan(
    planData,
    createdBy,
    createdByType,
    aiPreferences = {},
  ) {
    try {
      // First create the basic plan
      const plan = await this.createPlan(planData, createdBy, createdByType);

      // Generate AI strategy if requested
      if (aiPreferences.generateStrategy) {
        const strategy = await this.generateAIStrategy(
          planData.modelId,
          planData.agencyId,
          aiPreferences,
        );

        // Update plan with AI insights
        plan.aiRecommendations = {
          strategy: strategy.strategy,
          generatedAt: new Date(),
          provider: strategy.provider,
        };

        // Update weekly theme if AI suggested one
        if (
          strategy.strategy.weeklyThemes &&
          strategy.strategy.weeklyThemes.length > 0
        ) {
          plan.weeklyTheme = strategy.strategy.weeklyThemes[0];
        }

        await plan.save();
      }

      // Generate initial content suggestions if requested
      if (aiPreferences.generateSuggestions) {
        const platforms = planData.targetPlatforms || ["instagram"];
        const suggestions = {};

        for (const platform of platforms) {
          try {
            const platformSuggestions = await this.getAIContentSuggestions(
              plan._id,
              platform,
              aiPreferences.suggestionCount || 3,
            );
            suggestions[platform] = platformSuggestions.suggestions;
          } catch (error) {
            console.error(
              `Failed to generate suggestions for ${platform}:`,
              error,
            );
            suggestions[platform] = [];
          }
        }

        plan.aiRecommendations = {
          ...plan.aiRecommendations,
          contentSuggestions: suggestions,
        };

        await plan.save();
      }

      return plan;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      throw new ApiError(
        500,
        `Failed to create AI-powered plan: ${error.message}`,
      );
    }
  }

  /**
   * Optimize existing plan with AI insights
   */
  async optimizePlanWithAI(planId, optimizationOptions = {}) {
    try {
      const plan = await ContentPlan.findById(planId)
        .populate("modelId")
        .populate("agencyId");

      if (!plan) {
        throw new ApiError(404, "Plan not found");
      }

      const optimizations = {};

      // Analyze timing if requested
      if (optimizationOptions.analyzeTiming) {
        const platforms = plan.targetPlatforms || ["instagram"];
        const timingAnalysis = {};

        for (const platform of platforms) {
          try {
            const analysis = await this.analyzeOptimalTiming(
              plan.modelId._id,
              platform,
              { weekStart: plan.weekStart },
            );
            timingAnalysis[platform] = analysis.analysis;
          } catch (error) {
            console.error(`Failed to analyze timing for ${platform}:`, error);
          }
        }

        optimizations.timingAnalysis = timingAnalysis;
      }

      // Generate additional content suggestions if requested
      if (optimizationOptions.generateMoreSuggestions) {
        const platforms = plan.targetPlatforms || ["instagram"];
        const newSuggestions = {};

        for (const platform of platforms) {
          try {
            const suggestions = await this.getAIContentSuggestions(
              planId,
              platform,
              optimizationOptions.suggestionCount || 5,
            );
            newSuggestions[platform] = suggestions.suggestions;
          } catch (error) {
            console.error(
              `Failed to generate new suggestions for ${platform}:`,
              error,
            );
          }
        }

        optimizations.newContentSuggestions = newSuggestions;
      }

      // Update plan with optimizations
      plan.aiRecommendations = {
        ...plan.aiRecommendations,
        optimizations,
        lastOptimized: new Date(),
      };

      await plan.save();

      return {
        planId,
        optimizations,
        optimizedAt: new Date(),
      };
    } catch (error) {
      if (error instanceof ApiError) throw error;
      throw new ApiError(500, `Failed to optimize plan: ${error.message}`);
    }
  }
}

export default new ContentPlannerService();
