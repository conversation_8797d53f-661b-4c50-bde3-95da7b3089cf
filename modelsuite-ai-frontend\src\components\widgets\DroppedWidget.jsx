import React, { useState, useRef, useEffect, useCallback } from "react";
import { useDispatch } from "react-redux";
import {
  updateWidgetPosition,
  removeWidget,
} from "@/redux/features/dashboard/dashboardSlice";
import { widgetRegistry } from "@/components/WidgetSidebar/widgetRegistry.jsx";

const DroppedWidget = ({ widget }) => {
  const dispatch = useDispatch();
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const widgetRef = useRef(null);

  const registeredWidget = widgetRegistry[widget.type];

  const handleMouseDown = useCallback((e) => {
    // Allow dragging from anywhere on the widget
    setIsDragging(true);
    const rect = widgetRef.current.getBoundingClientRect();
    setDragOffset({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    });

    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDoubleClick = useCallback(
    (e) => {
      e.preventDefault();
      e.stopPropagation();

      // Show confirmation and remove widget
      const widgetName = registeredWidget?.title || widget.type;
      if (window.confirm(`Remove ${widgetName} widget from dashboard?`)) {
        dispatch(removeWidget(widget.id));
      }
    },
    [dispatch, widget.id, widget.type, registeredWidget]
  );

  const handleMouseMove = useCallback(
    (e) => {
      if (!isDragging || !widgetRef.current) return;

      const dashboardRect =
        widgetRef.current.parentElement.getBoundingClientRect();
      const newPosition = {
        x: e.clientX - dashboardRect.left - dragOffset.x,
        y: e.clientY - dashboardRect.top - dragOffset.y,
      };

      // Get widget dimensions
      const widgetWidth = registeredWidget?.defaultSize?.width || 300;
      const widgetHeight = registeredWidget?.defaultSize?.height || 200;

      // Constrain to dashboard bounds
      const constrainedPosition = {
        x: Math.max(
          0,
          Math.min(newPosition.x, dashboardRect.width - widgetWidth)
        ),
        y: Math.max(
          0,
          Math.min(newPosition.y, dashboardRect.height - widgetHeight)
        ),
      };

      dispatch(
        updateWidgetPosition({
          widgetId: widget.id,
          position: constrainedPosition,
        })
      );
    },
    [isDragging, dragOffset, widget.id, dispatch, registeredWidget]
  );

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Global mouse event listeners for dragging
  useEffect(() => {
    if (isDragging) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
      document.body.style.userSelect = "none"; // Prevent text selection

      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
        document.body.style.userSelect = "";
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  if (!registeredWidget) {
    // Fallback for unknown widget types
    return (
      <div
        ref={widgetRef}
        className="absolute bg-red-100 border-2 border-red-300 rounded-lg p-4 shadow-lg z-10"
        style={{
          left: widget.position.x,
          top: widget.position.y,
          width: 300,
          height: 200,
        }}
      >
        <p className="text-red-500 text-sm">
          Widget type "{widget.type}" not found
        </p>
      </div>
    );
  }

  const WidgetComponent = registeredWidget.component;
  const widgetWidth = registeredWidget.defaultSize?.width || 300;
  const widgetHeight = registeredWidget.defaultSize?.height || 200;

  return (
    <div
      ref={widgetRef}
      className={`
        absolute bg-gray-900 border border-gray-700 rounded-lg shadow-2xl overflow-hidden
        cursor-move backdrop-blur-sm
        ${
          isDragging ? "shadow-2xl scale-105 z-50 ring-2 ring-blue-500" : "z-10"
        }
        transition-all duration-200
        hover:shadow-2xl hover:border-gray-600
      `}
      style={{
        left: widget.position.x,
        top: widget.position.y,
        width: widgetWidth,
        height: widgetHeight,
      }}
      onMouseDown={handleMouseDown}
      onDoubleClick={handleDoubleClick}
      title="Drag to move • Double-click to remove"
    >
      {/* Widget Content */}
      <div className="p-3 h-full overflow-auto">
        <WidgetComponent />
      </div>
    </div>
  );
};

export default DroppedWidget;
