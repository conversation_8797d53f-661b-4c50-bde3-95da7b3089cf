import React from "react";

const SettingsIcon = ({ className }) => {
  return (
    <svg
      width="100%"
      height="100%"
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <defs>
        {/* Background gradients to match look */}
        <radialGradient id="settingsBgRadial" cx="66%" cy="22%" r="92%">
          <stop offset="0%" stopColor="#3B1282" />
          <stop offset="48%" stopColor="#18143A" />
          <stop offset="100%" stopColor="#0A0B18" />
        </radialGradient>
        <radialGradient id="settingsVioletHalo" cx="66%" cy="22%" r="70%">
          <stop offset="0%" stopColor="#7E2CFF" stopOpacity="0.32" />
          <stop offset="100%" stopColor="#000000" stopOpacity="0" />
        </radialGradient>

        {/* Stroke gradient (pink -> violet -> blue) */}
        <linearGradient
          id="settingsStrokeGrad"
          x1="15%"
          y1="15%"
          x2="85%"
          y2="85%"
        >
          <stop offset="0%" stopColor="#FF3CF6" />
          <stop offset="55%" stopColor="#A43CFF" />
          <stop offset="100%" stopColor="#0AA3FF" />
        </linearGradient>

        {/* Inner ring gradient */}
        <linearGradient
          id="settingsInnerGrad"
          x1="30%"
          y1="0%"
          x2="70%"
          y2="100%"
        >
          <stop offset="0%" stopColor="#FF3CF6" />
          <stop offset="100%" stopColor="#0AA3FF" />
        </linearGradient>

        {/* Neon glow */}
        <filter id="settingsGlow" x="-60%" y="-60%" width="220%" height="220%">
          <feGaussianBlur stdDeviation="14" result="blur" />
          <feMerge>
            <feMergeNode in="blur" />
            <feMergeNode in="SourceGraphic" />
          </feMerge>
        </filter>
      </defs>

      {/* Rounded square background */}
      <rect
        x="64"
        y="64"
        width="896"
        height="896"
        rx="200"
        fill="url(#settingsBgRadial)"
      />
      <rect
        x="64"
        y="64"
        width="896"
        height="896"
        rx="200"
        fill="url(#settingsVioletHalo)"
      />

      {/* Gear outline */}
      <g fill="none" strokeLinecap="round" strokeLinejoin="round">
        {/* glow pass */}
        <path
          d="M 774.09,447.11 L 798.89,424.29 L 818.02,418.44 L 818.02,605.56 L 798.89,599.71 L 774.09,576.89 L 743.21,651.44 L 776.88,652.84 L 794.54,662.23 L 662.23,794.54 L 652.84,776.88 L 651.44,743.21 L 576.89,774.09 L 599.71,798.89 L 605.56,818.02 L 418.44,818.02 L 424.29,798.89 L 447.11,774.09 L 372.56,743.21 L 371.16,776.88 L 361.77,794.54 L 229.46,662.23 L 247.12,652.84 L 280.79,651.44 L 249.91,576.89 L 225.11,599.71 L 205.98,605.56 L 205.98,418.44 L 225.11,424.29 L 249.91,447.11 L 280.79,372.56 L 247.12,371.16 L 229.46,361.77 L 361.77,229.46 L 371.16,247.12 L 372.56,280.79 L 447.11,249.91 L 424.29,225.11 L 418.44,205.98 L 605.56,205.98 L 599.71,225.11 L 576.89,249.91 L 651.44,280.79 L 652.84,247.12 L 662.23,229.46 L 794.54,361.77 L 776.88,371.16 L 743.21,372.56 Z"
          stroke="url(#settingsStrokeGrad)"
          strokeWidth="36"
          filter="url(#settingsGlow)"
        />
        {/* crisp pass */}
        <path
          d="M 774.09,447.11 L 798.89,424.29 L 818.02,418.44 L 818.02,605.56 L 798.89,599.71 L 774.09,576.89 L 743.21,651.44 L 776.88,652.84 L 794.54,662.23 L 662.23,794.54 L 652.84,776.88 L 651.44,743.21 L 576.89,774.09 L 599.71,798.89 L 605.56,818.02 L 418.44,818.02 L 424.29,798.89 L 447.11,774.09 L 372.56,743.21 L 371.16,776.88 L 361.77,794.54 L 229.46,662.23 L 247.12,652.84 L 280.79,651.44 L 249.91,576.89 L 225.11,599.71 L 205.98,605.56 L 205.98,418.44 L 225.11,424.29 L 249.91,447.11 L 280.79,372.56 L 247.12,371.16 L 229.46,361.77 L 361.77,229.46 L 371.16,247.12 L 372.56,280.79 L 447.11,249.91 L 424.29,225.11 L 418.44,205.98 L 605.56,205.98 L 599.71,225.11 L 576.89,249.91 L 651.44,280.79 L 652.84,247.12 L 662.23,229.46 L 794.54,361.77 L 776.88,371.16 L 743.21,372.56 Z"
          stroke="url(#settingsStrokeGrad)"
          strokeWidth="20"
        />
      </g>

      {/* Inner ring */}
      <g fill="none">
        <circle
          cx="512"
          cy="512"
          r="160"
          stroke="url(#settingsInnerGrad)"
          strokeWidth="32"
          filter="url(#settingsGlow)"
        />
        <circle
          cx="512"
          cy="512"
          r="160"
          stroke="url(#settingsInnerGrad)"
          strokeWidth="18"
        />
      </g>
    </svg>
  );
};

export default SettingsIcon;
