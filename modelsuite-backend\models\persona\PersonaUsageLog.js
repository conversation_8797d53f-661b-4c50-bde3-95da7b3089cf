import mongoose from "mongoose";

/**
 * PersonaUsageLog Schema - Tracks how and where personas are used
 * Enables analytics and impact scoring for persona effectiveness
 */
const personaUsageLogSchema = new mongoose.Schema(
  {
    // Reference to the persona being used
    personaId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "PersonaProfile",
      required: true,
    },

    // Where the persona was used
    usageType: {
      type: String,
      enum: [
        "caption_generation",
        "bio_creation",
        "content_planning",
        "trend_analysis",
        "export_pdf",
        "other",
      ],
      required: true,
    },

    // Context of usage
    context: {
      // Related entity ID (e.g., caption ID, bio ID)
      entityId: {
        type: mongoose.Schema.Types.ObjectId,
      },

      // Additional context data
      metadata: {
        type: mongoose.Schema.Types.Mixed,
        default: {},
      },
    },

    // User who triggered the usage
    usedBy: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      refPath: "usedByModel",
    },

    usedByModel: {
      type: String,
      required: true,
      enum: ["Agency", "Employee"],
    },

    // Agency context
    agencyId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      required: true,
    },

    // Model context
    modelId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ModelUser",
      required: true,
    },

    // Success metrics
    success: {
      type: Boolean,
      default: true,
    },

    // Error information if usage failed
    error: {
      type: String,
      trim: true,
    },
  },
  {
    timestamps: true,
  },
);

// Indexes for analytics queries
personaUsageLogSchema.index({ personaId: 1, createdAt: -1 });
personaUsageLogSchema.index({ agencyId: 1, usageType: 1, createdAt: -1 });
personaUsageLogSchema.index({ modelId: 1, createdAt: -1 });
personaUsageLogSchema.index({ usageType: 1, createdAt: -1 });

// Static method to get usage analytics
personaUsageLogSchema.statics.getUsageAnalytics = function (
  agencyId,
  options = {},
) {
  const pipeline = [
    { $match: { agencyId: new mongoose.Types.ObjectId(agencyId) } },
  ];

  // Add date filter if provided
  if (options.startDate || options.endDate) {
    const dateMatch = {};
    if (options.startDate) dateMatch.$gte = new Date(options.startDate);
    if (options.endDate) dateMatch.$lte = new Date(options.endDate);
    pipeline[0].$match.createdAt = dateMatch;
  }

  // Group by persona and usage type
  pipeline.push({
    $group: {
      _id: {
        personaId: "$personaId",
        usageType: "$usageType",
      },
      count: { $sum: 1 },
      lastUsed: { $max: "$createdAt" },
      successRate: {
        $avg: { $cond: ["$success", 1, 0] },
      },
    },
  });

  // Lookup persona details
  pipeline.push({
    $lookup: {
      from: "personaprofiles",
      localField: "_id.personaId",
      foreignField: "_id",
      as: "persona",
    },
  });

  // Unwind and project
  pipeline.push(
    { $unwind: "$persona" },
    {
      $project: {
        personaId: "$_id.personaId",
        usageType: "$_id.usageType",
        count: 1,
        lastUsed: 1,
        successRate: 1,
        personaText: "$persona.personaText",
        tags: "$persona.tags",
        modelId: "$persona.modelId",
      },
    },
  );

  return this.aggregate(pipeline);
};

// Static method to get top used personas
personaUsageLogSchema.statics.getTopPersonas = function (agencyId, limit = 5) {
  return this.aggregate([
    { $match: { agencyId: new mongoose.Types.ObjectId(agencyId) } },
    {
      $group: {
        _id: "$personaId",
        totalUsage: { $sum: 1 },
        lastUsed: { $max: "$createdAt" },
        usageTypes: { $addToSet: "$usageType" },
      },
    },
    {
      $lookup: {
        from: "personaprofiles",
        localField: "_id",
        foreignField: "_id",
        as: "persona",
      },
    },
    { $unwind: "$persona" },
    {
      $project: {
        personaId: "$_id",
        totalUsage: 1,
        lastUsed: 1,
        usageTypes: 1,
        personaText: "$persona.personaText",
        tags: "$persona.tags",
        modelId: "$persona.modelId",
      },
    },
    { $sort: { totalUsage: -1 } },
    { $limit: limit },
  ]);
};

// Static method to log persona usage
personaUsageLogSchema.statics.logUsage = async function (data) {
  try {
    const log = new this(data);
    await log.save();

    // Update persona usage count
    await mongoose.model("PersonaProfile").findByIdAndUpdate(data.personaId, {
      $inc: { usageCount: 1 },
      $set: { lastUsedAt: new Date() },
    });

    return log;
  } catch (error) {
    console.error("Failed to log persona usage:", error);
    throw error;
  }
};

const PersonaUsageLog = mongoose.model(
  "PersonaUsageLog",
  personaUsageLogSchema,
);

export default PersonaUsageLog;
