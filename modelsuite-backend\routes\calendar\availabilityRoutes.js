import express from "express";
import {
  createAbsence,
  getSelfAbsences,
  getModelAbsences,
  updateAbsence,
  deleteAbsence,
} from "../../controllers/calendar/availabilityController.js";
import { verifyToken, verifyRole } from "../../middlewares/authMiddleware.js";
import checkPermission, {
  checkLegacyPermission,
} from "../../middlewares/permissionCheck.js";

const router = express.Router();

// All routes require authentication
router.use(verifyToken);

// Create new absence (ModelUser)
router.post(
  "/create",
  checkLegacyPermission("calendar", "create"),
  createAbsence,
);

// Get own absences (ModelUser)
router.get("/self", checkLegacyPermission("calendar", "view"), getSelfAbsences);

// Get absences for specific model (Agency)
router.get(
  "/model/:modelId",
  checkLegacyPermission("calendar", "view"),
  getModelAbsences,
);

// Update own absence (ModelUser)
router.put(
  "/update/:id",
  checkLegacyPermission("calendar", "edit"),
  updateAbsence,
);

// Delete own absence (ModelUser)
router.delete(
  "/delete/:id",
  checkLegacyPermission("calendar", "edit"),
  deleteAbsence,
);

export default router;
