import mongoose from "mongoose";

async function checkCaptionIndexes() {
  try {
    console.log("🔍 Connecting to database...");
    await mongoose.connect(
      "mongodb+srv://devanand:<EMAIL>/modelsuite?retryWrites=true&w=majority&appName=Cluster0",
      {
        serverSelectionTimeoutMS: 10000, // 10 second timeout
      },
    );

    const db = mongoose.connection.db;
    const collection = db.collection("captionquotas");

    console.log("📊 Checking captionquotas collection...");

    // Check indexes
    const indexes = await collection.indexes();
    console.log("\nCurrent indexes:");
    indexes.forEach((index, i) => {
      console.log(
        `${i + 1}. ${index.name}: ${JSON.stringify(index.key)} ${
          index.unique ? "(UNIQUE)" : ""
        }`,
      );
    });

    // Count documents with null userId
    const nullCount = await collection.countDocuments({ userId: null });
    console.log(`\nDocuments with userId: null = ${nullCount}`);

    // Count total documents
    const totalCount = await collection.countDocuments({});
    console.log(`Total documents = ${totalCount}`);

    console.log("\n✅ Check completed successfully");
  } catch (error) {
    console.error("❌ Error:", error.message);
  } finally {
    await mongoose.disconnect();
    console.log("🔌 Disconnected from database");
  }
}

checkCaptionIndexes();
