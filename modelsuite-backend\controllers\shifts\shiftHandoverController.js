import ShiftHandover from "../../models/shifts/ShiftHandover.js";
import WeeklySchedule from "../../models/shifts/WeeklySchedule.js";
import Employee from "../../models/Employee/Employee.js";
import { ApiError } from "../../utils/ApiError.js";
import { ApiResponse } from "../../utils/ApiResponse.js";
import { asyncHandler } from "../../utils/asyncHandler.js";

// Create a new shift handover request
export const createHandoverRequest = asyncHandler(async (req, res) => {
  const { weeklyScheduleId, shiftId, reason } = req.body;
  const requestedBy = req.user._id;
  const agencyId =
    req.user.role === "employee" ? req.user.agencyId : req.user._id;

  // Find the weekly schedule and specific shift
  const weeklySchedule = await WeeklySchedule.findById(weeklyScheduleId);
  if (!weeklySchedule) {
    throw new ApiError(404, "Weekly schedule not found");
  }

  // Find the specific shift
  const shift = weeklySchedule.shifts.id(shiftId);
  if (!shift) {
    throw new ApiError(404, "Shift not found");
  }

  // Verify the requesting employee is assigned to this shift
  if (!shift.employeeId || !shift.employeeId.equals(requestedBy)) {
    throw new ApiError(
      403,
      "You can only request handover for your own shifts",
    );
  }

  // Create shift start datetime by combining date and start time
  const shiftDate = new Date(shift.date);
  const [startHour, startMinute] = shift.startTime.split(":").map(Number);
  const shiftStartDateTime = new Date(shiftDate);
  shiftStartDateTime.setHours(startHour, startMinute, 0, 0);

  const now = new Date();

  // Check if shift start time is in the future and at least 1 hour away
  const oneHourFromNow = new Date(now.getTime() + 60 * 60 * 1000);
  if (shiftStartDateTime <= oneHourFromNow) {
    throw new ApiError(
      400,
      "Cannot request handover for shifts starting within 1 hour or in the past",
    );
  }

  // Check if there's already an active handover request for this shift
  const existingHandover = await ShiftHandover.findOne({
    "originalShift.weeklyScheduleId": weeklyScheduleId,
    "originalShift.shiftId": shiftId,
    status: "pending",
  });

  if (existingHandover) {
    throw new ApiError(
      400,
      "There is already an active handover request for this shift",
    );
  }

  // Set expiration time (1 hour before shift starts)
  const expiresAt = new Date(shiftStartDateTime.getTime() - 60 * 60 * 1000);
  if (expiresAt <= now) {
    throw new ApiError(
      400,
      "Too late to request handover for this shift (less than 1 hour remaining)",
    );
  }

  // Create handover request
  const handoverRequest = new ShiftHandover({
    agencyId,
    originalShift: {
      weeklyScheduleId,
      shiftId,
      day: shift.day,
      date: shift.date,
      startTime: shift.startTime,
      endTime: shift.endTime,
      position: shift.position,
    },
    requestedBy,
    reason,
    expiresAt,
  });

  await handoverRequest.save();

  const populatedHandover = await ShiftHandover.findById(handoverRequest._id)
    .populate("requestedBy", "firstName lastName email")
    .populate("originalShift.weeklyScheduleId");

  res
    .status(201)
    .json(
      new ApiResponse(
        201,
        populatedHandover,
        "Handover request created successfully",
      ),
    );
});

// Get available employees for a shift handover
export const getAvailableEmployees = asyncHandler(async (req, res) => {
  const { handoverId } = req.params;
  const agencyId =
    req.user.role === "employee" ? req.user.agencyId : req.user._id;

  // Find the handover request
  const handover = await ShiftHandover.findById(handoverId);
  if (!handover) {
    throw new ApiError(404, "Handover request not found");
  }

  if (!handover.agencyId.equals(agencyId)) {
    throw new ApiError(403, "Access denied");
  }

  const { date, startTime, endTime } = handover.originalShift;

  // Find all employees in the agency
  const allEmployees = await Employee.find({ agencyId, isActive: true }).select(
    "firstName lastName email position",
  );

  // Find employees who are already scheduled during this time
  const busyEmployees = await WeeklySchedule.aggregate([
    {
      $match: {
        agencyId,
        "shifts.date": date,
      },
    },
    {
      $unwind: "$shifts",
    },
    {
      $match: {
        "shifts.date": date,
        "shifts.employeeId": { $ne: null },
        $or: [
          {
            $and: [
              { "shifts.startTime": { $lte: startTime } },
              { "shifts.endTime": { $gt: startTime } },
            ],
          },
          {
            $and: [
              { "shifts.startTime": { $lt: endTime } },
              { "shifts.endTime": { $gte: endTime } },
            ],
          },
          {
            $and: [
              { "shifts.startTime": { $gte: startTime } },
              { "shifts.endTime": { $lte: endTime } },
            ],
          },
        ],
      },
    },
    {
      $group: {
        _id: null,
        busyEmployeeIds: { $addToSet: "$shifts.employeeId" },
      },
    },
  ]);

  const busyEmployeeIds =
    busyEmployees.length > 0 ? busyEmployees[0].busyEmployeeIds : [];

  // Filter out busy employees and the requesting employee
  const availableEmployees = allEmployees.filter(
    (employee) =>
      !busyEmployeeIds.some((busyId) => busyId.equals(employee._id)) &&
      !employee._id.equals(handover.requestedBy),
  );

  res
    .status(200)
    .json(
      new ApiResponse(
        200,
        availableEmployees,
        "Available employees retrieved successfully",
      ),
    );
});

// Accept a handover request
export const acceptHandover = asyncHandler(async (req, res) => {
  const { handoverId } = req.params;
  const acceptedBy = req.user._id;
  const agencyId =
    req.user.role === "employee" ? req.user.agencyId : req.user._id;

  const handover = await ShiftHandover.findById(handoverId);
  if (!handover) {
    throw new ApiError(404, "Handover request not found");
  }

  if (!handover.agencyId.equals(agencyId)) {
    throw new ApiError(403, "Access denied");
  }

  if (!handover.canEmployeeAccept(acceptedBy)) {
    throw new ApiError(400, "Cannot accept this handover request");
  }

  // Check if employee is available for this shift
  const { date, startTime, endTime } = handover.originalShift;
  const conflictingShift = await WeeklySchedule.findOne({
    agencyId,
    "shifts.date": date,
    "shifts.employeeId": acceptedBy,
    $or: [
      {
        $and: [
          { "shifts.startTime": { $lte: startTime } },
          { "shifts.endTime": { $gt: startTime } },
        ],
      },
      {
        $and: [
          { "shifts.startTime": { $lt: endTime } },
          { "shifts.endTime": { $gte: endTime } },
        ],
      },
      {
        $and: [
          { "shifts.startTime": { $gte: startTime } },
          { "shifts.endTime": { $lte: endTime } },
        ],
      },
    ],
  });

  if (conflictingShift) {
    throw new ApiError(400, "You have a conflicting shift at this time");
  }

  // Update the shift assignment in WeeklySchedule
  const weeklySchedule = await WeeklySchedule.findById(
    handover.originalShift.weeklyScheduleId,
  );
  if (!weeklySchedule) {
    throw new ApiError(404, "Weekly schedule not found");
  }

  const shift = weeklySchedule.shifts.id(handover.originalShift.shiftId);
  if (!shift) {
    throw new ApiError(404, "Shift not found in weekly schedule");
  }

  // Update the shift's employee assignment
  shift.employeeId = acceptedBy;
  await weeklySchedule.save();

  // Update handover status
  handover.status = "accepted";
  handover.acceptedBy = acceptedBy;
  handover.acceptedAt = new Date();
  await handover.save();

  const populatedHandover = await ShiftHandover.findById(handover._id)
    .populate("requestedBy", "firstName lastName email")
    .populate("acceptedBy", "firstName lastName email")
    .populate("originalShift.weeklyScheduleId");

  res
    .status(200)
    .json(
      new ApiResponse(
        200,
        populatedHandover,
        "Handover completed successfully. Shift has been reassigned.",
      ),
    );
});

// Reject a handover request
export const rejectHandover = asyncHandler(async (req, res) => {
  const { handoverId } = req.params;
  const rejectedBy = req.user._id;
  const agencyId =
    req.user.role === "employee" ? req.user.agencyId : req.user._id;

  const handover = await ShiftHandover.findById(handoverId);
  if (!handover) {
    throw new ApiError(404, "Handover request not found");
  }

  if (!handover.agencyId.equals(agencyId)) {
    throw new ApiError(403, "Access denied");
  }

  if (
    handover.status !== "accepted" ||
    !handover.acceptedBy.equals(rejectedBy)
  ) {
    throw new ApiError(400, "Cannot reject this handover request");
  }

  // Update handover status
  handover.status = "rejected";
  handover.rejectedBy = rejectedBy;
  handover.rejectedAt = new Date();
  handover.acceptedBy = null;
  handover.acceptedAt = null;
  await handover.save();

  const populatedHandover = await ShiftHandover.findById(handover._id)
    .populate("requestedBy", "firstName lastName email")
    .populate("rejectedBy", "firstName lastName email")
    .populate("originalShift.weeklyScheduleId");

  res
    .status(200)
    .json(
      new ApiResponse(
        200,
        populatedHandover,
        "Handover request rejected successfully",
      ),
    );
});

// Cancel a handover request (by the original requester)
export const cancelHandover = asyncHandler(async (req, res) => {
  const { handoverId } = req.params;
  const requestedBy = req.user._id;

  const handover = await ShiftHandover.findById(handoverId);
  if (!handover) {
    throw new ApiError(404, "Handover request not found");
  }

  if (!handover.requestedBy.equals(requestedBy)) {
    throw new ApiError(403, "You can only cancel your own handover requests");
  }

  if (handover.status !== "pending" && handover.status !== "accepted") {
    throw new ApiError(400, "Cannot cancel this handover request");
  }

  handover.status = "cancelled";
  await handover.save();

  const populatedHandover = await ShiftHandover.findById(handover._id)
    .populate("requestedBy", "firstName lastName email")
    .populate("acceptedBy", "firstName lastName email")
    .populate("originalShift.weeklyScheduleId");

  res
    .status(200)
    .json(
      new ApiResponse(
        200,
        populatedHandover,
        "Handover request cancelled successfully",
      ),
    );
});

// Get all handover requests for an agency
export const getAgencyHandovers = asyncHandler(async (req, res) => {
  const agencyId =
    req.user.role === "employee" ? req.user.agencyId : req.user._id;
  const { status, page = 1, limit = 10 } = req.query;

  const query = { agencyId };
  if (status) {
    query.status = status;
  }

  const skip = (page - 1) * limit;

  const handovers = await ShiftHandover.find(query)
    .populate("requestedBy", "firstName lastName email")
    .populate("acceptedBy", "firstName lastName email")
    .populate("originalShift.weeklyScheduleId")
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(parseInt(limit));

  const total = await ShiftHandover.countDocuments(query);

  res.status(200).json(
    new ApiResponse(
      200,
      {
        handovers,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit),
        },
      },
      "Handover requests retrieved successfully",
    ),
  );
});

// Get handover requests for an employee
export const getEmployeeHandovers = asyncHandler(async (req, res) => {
  const employeeId = req.user._id;
  const { status } = req.query;

  const handovers = await ShiftHandover.findByEmployee(employeeId, status);

  res
    .status(200)
    .json(
      new ApiResponse(
        200,
        handovers,
        "Employee handover requests retrieved successfully",
      ),
    );
});

// Add a cleanup function to handle expired handovers
export const cleanupExpiredHandovers = asyncHandler(async (req, res) => {
  const now = new Date();

  const expiredHandovers = await ShiftHandover.updateMany(
    {
      status: "pending",
      expiresAt: { $lte: now },
    },
    {
      status: "cancelled",
    },
  );

  res
    .status(200)
    .json(
      new ApiResponse(
        200,
        { modifiedCount: expiredHandovers.modifiedCount },
        "Expired handovers cleaned up successfully",
      ),
    );
});
