import express from "express";
import {
  startTimeSession,
  endTimeSession,
  getActiveSessions,
  submitShiftReport,
  getMyReports,
  deleteAllReportsByAgencyEmail,
} from "../../controllers/shiftReport/shiftReportController.js";
import { verifyRole, verifyToken } from "../../middlewares/authMiddleware.js";

const router = express.Router();

// uncommentted after login
router.use(verifyToken);

// Time Tracking Routes
router.post("/time/start", startTimeSession);
router.post("/time/end", endTimeSession);
router.get("/time/active", getActiveSessions);

// Shift Report Routes
router.post("/report/submit", submitShiftReport);
router.get("/report/my-reports", getMyReports);
router.delete("/temp/delete-by-agency", deleteAllReportsByAgencyEmail);
export default router;
