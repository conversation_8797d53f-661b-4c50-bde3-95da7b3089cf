import BioProfile from "../../models/bio/BioProfile.js";
import BioGenerationLog from "../../models/bio/BioGenerationLog.js";
import BioPromptBuilder from "./bioPromptBuilder.js";
import PersonaProfile from "../../models/persona/PersonaProfile.js";
import ModelUser from "../../models/model.js";
import { ApiError } from "../../utils/ApiError.js";
import OpenAI from "openai";

/**
 * Bio Service - Core business logic for bio generation and management
 */
class BioService {
  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    this.defaultConfig = {
      model: "gpt-4",
      temperature: 0.7,
      maxTokens: 1500,
    };
  }

  /**
   * Generate bios for a model using persona
   * @param {string} modelId - Model ID
   * @param {string} personaId - Persona ID
   * @param {Object} options - Generation options
   * @returns {Promise<Object>} Generated bios and metadata
   */
  async generateBios(modelId, personaId, options = {}) {
    const startTime = Date.now();
    let logEntry = null;

    try {
      // Validate inputs
      if (!modelId || !personaId) {
        throw new ApiError(400, "Model ID and Persona ID are required");
      }

      // Start timing data preparation
      const dataPrepStart = Date.now();

      // Fetch model and persona data
      const [modelProfile, personaProfile] = await Promise.all([
        ModelUser.findById(modelId),
        PersonaProfile.findById(personaId),
      ]);

      if (!modelProfile) {
        throw new ApiError(404, "Model not found");
      }

      if (!personaProfile) {
        throw new ApiError(404, "Persona not found");
      }

      // Validate that persona belongs to same agency as model
      if (
        modelProfile.agencyId.toString() !== personaProfile.agencyId.toString()
      ) {
        throw new ApiError(
          403,
          "Persona and model must belong to the same agency",
        );
      }

      // Validate prompt data
      const validation = BioPromptBuilder.validatePromptData(
        modelProfile,
        personaProfile,
      );
      if (!validation.isValid) {
        throw new ApiError(
          400,
          `Invalid data: ${validation.errors.join(", ")}`,
        );
      }

      // Build prompt
      const prompt = BioPromptBuilder.buildPrompt(
        modelProfile,
        personaProfile,
        options,
      );
      const dataPrepTime = Date.now() - dataPrepStart;

      // Create generation log entry
      logEntry = new BioGenerationLog({
        modelId,
        agencyId: modelProfile.agencyId,
        personaSnapshot: {
          personaId,
          name: personaProfile.name,
          content: personaProfile.personaText || personaProfile.content,
          tags: personaProfile.tags || [],
          communicationStyle: personaProfile.communicationStyle || {},
        },
        modelSnapshot: {
          username: modelProfile.username,
          fullName: modelProfile.fullName,
          profileDescription: modelProfile.profileDescription,
          interests: modelProfile.interests || [],
          specialties: modelProfile.specialties || [],
        },
        promptUsed: prompt,
        aiService: options.aiService || this.defaultConfig.model,
        aiConfig: {
          temperature: options.temperature || this.defaultConfig.temperature,
          maxTokens: options.maxTokens || this.defaultConfig.maxTokens,
          model: options.aiService || this.defaultConfig.model,
        },
        timing: {
          dataPrepTime,
        },
        generationMode: options.regeneration
          ? "regeneration"
          : "persona_enhanced",
        requestMetadata: {
          userId: options.userId,
          userAgent: options.userAgent,
          ipAddress: options.ipAddress,
        },
        success: false, // Will be updated on success
      });

      await logEntry.save();

      // Generate bios using AI
      const aiStart = Date.now();
      const generatedBios = await this.callAIService(prompt, options);
      const aiTime = Date.now() - aiStart;

      // Process and validate generated content
      const processedBios = this.processBioContent(generatedBios);

      // Update log with success
      await logEntry.markAsSuccessful(processedBios, {
        dataPrepTime,
        aiTime,
        total: Date.now() - startTime,
      });

      // Estimate and track costs
      const costData = this.estimateCost(
        prompt,
        processedBios,
        options.aiService,
      );
      await logEntry.updateCost(
        costData.inputTokens,
        costData.outputTokens,
        costData.costPerToken,
      );

      return {
        success: true,
        data: {
          bios: processedBios,
          metadata: {
            modelId,
            personaId,
            generationId: logEntry._id,
            timing: {
              dataPrepTime,
              aiTime,
              total: Date.now() - startTime,
            },
            warnings: validation.warnings,
          },
        },
      };
    } catch (error) {
      // Log the error
      if (logEntry) {
        const errorType = this.categorizeError(error);
        await logEntry.markAsFailed(errorType, error.message);
      }

      console.error("Bio generation failed:", error);
      throw error;
    }
  }

  /**
   * Regenerate bios with improvements
   * @param {string} modelId - Model ID
   * @param {string} personaId - Persona ID
   * @param {Object} feedback - Feedback on current bios
   * @param {Object} options - Generation options
   * @returns {Promise<Object>} Regenerated bios
   */
  async regenerateBios(modelId, personaId, feedback = {}, options = {}) {
    try {
      // Get current bios
      const currentBio = await BioProfile.getLatestForModel(modelId);

      if (!currentBio) {
        throw new ApiError(404, "No existing bio found to regenerate");
      }

      // Fetch model and persona data
      const [modelProfile, personaProfile] = await Promise.all([
        ModelUser.findById(modelId),
        PersonaProfile.findById(personaId),
      ]);

      // Build regeneration prompt
      const prompt = BioPromptBuilder.buildRegenerationPrompt(
        modelProfile,
        personaProfile,
        {
          shortBio: currentBio.shortBio,
          mediumBio: currentBio.mediumBio,
          longBio: currentBio.longBio,
        },
        feedback,
      );

      // Generate with regeneration flag
      return await this.generateBios(modelId, personaId, {
        ...options,
        regeneration: true,
        customPrompt: prompt,
      });
    } catch (error) {
      console.error("Bio regeneration failed:", error);
      throw error;
    }
  }

  /**
   * Save generated bios to database
   * @param {Object} bioData - Bio data to save
   * @param {string} userId - User ID who created the bio
   * @returns {Promise<Object>} Saved bio profile
   */
  async saveBios(bioData, userId) {
    try {
      const {
        modelId,
        agencyId,
        personaId,
        shortBio,
        mediumBio,
        longBio,
        metadata = {},
        createdBy,
        createdByType,
      } = bioData;

      // Debug logging
      console.log("Received bioData:", bioData);
      console.log("createdByType:", createdByType);

      // Validate required fields
      if (
        !modelId ||
        !agencyId ||
        !personaId ||
        !shortBio ||
        !mediumBio ||
        !longBio
      ) {
        throw new ApiError(400, "All bio fields are required");
      }

      // Validate createdByType
      if (!createdByType) {
        throw new ApiError(400, "createdByType is required");
      }

      // Archive existing bio if it exists
      const existingBio = await BioProfile.getLatestForModel(modelId);
      if (existingBio) {
        await existingBio.archive();
      }

      // Create new bio profile
      const bioProfileData = {
        modelId,
        agencyId,
        personaId,
        shortBio: shortBio.trim(),
        mediumBio: mediumBio.trim(),
        longBio: longBio.trim(),
        createdBy: createdBy || userId,
        createdByType: createdByType || "Agency",
        metadata: {
          ...metadata,
          aiModel: metadata.aiModel || this.defaultConfig.model,
        },
      };

      console.log("Creating BioProfile with data:", bioProfileData);
      console.log("createdByType value:", bioProfileData.createdByType);

      const bioProfile = new BioProfile(bioProfileData);

      console.log("BioProfile instance created:");
      console.log("createdByType on instance:", bioProfile.createdByType);
      console.log("Full bio profile:", bioProfile.toObject());

      const savedBio = await bioProfile.save();

      // Populate references for response (excluding createdBy due to mixed references)
      await savedBio.populate([
        { path: "modelId", select: "username fullName" },
        { path: "personaId", select: "name" },
      ]);

      return savedBio;
    } catch (error) {
      console.error("Bio save failed:", error);
      throw error;
    }
  }

  /**
   * Get latest bios for a model
   * @param {string} modelId - Model ID
   * @param {boolean} includeArchived - Include archived bios
   * @returns {Promise<Object>} Latest bio data
   */
  async getLatestBios(modelId, includeArchived = false) {
    try {
      const bio = await BioProfile.getLatestForModel(modelId, includeArchived);

      if (!bio) {
        return null;
      }

      await bio.populate([
        { path: "modelId", select: "username fullName" },
        { path: "personaId", select: "name tags" },
      ]);

      return bio;
    } catch (error) {
      console.error("Failed to get latest bios:", error);
      throw error;
    }
  }

  /**
   * Get bio generation logs for a model
   * @param {string} modelId - Model ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Generation logs
   */
  async getGenerationLogs(modelId, options = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        successOnly = false,
        dateFrom = null,
        dateTo = null,
      } = options;

      const query = { modelId };

      if (successOnly) {
        query.success = true;
      }

      if (dateFrom || dateTo) {
        query.createdAt = {};
        if (dateFrom) query.createdAt.$gte = new Date(dateFrom);
        if (dateTo) query.createdAt.$lte = new Date(dateTo);
      }

      const logs = await BioGenerationLog.find(query)
        .sort({ createdAt: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit)
        .select("-promptUsed -personaSnapshot.content") // Exclude large fields
        .lean();

      const total = await BioGenerationLog.countDocuments(query);

      return {
        logs,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      console.error("Failed to get generation logs:", error);
      throw error;
    }
  }

  /**
   * Call AI service for bio generation
   * @param {string} prompt - Generated prompt
   * @param {Object} options - AI options
   * @returns {Promise<Object>} AI response
   */
  async callAIService(prompt, options = {}) {
    const aiService = options.aiService || this.defaultConfig.model;
    const customPrompt = options.customPrompt || prompt;

    try {
      switch (aiService) {
        case "gpt-4":
        case "gpt-3.5-turbo":
          return await this.callOpenAI(customPrompt, options);
        case "gemini-pro":
          return await this.callGemini(customPrompt, options);
        default:
          throw new ApiError(400, `Unsupported AI service: ${aiService}`);
      }
    } catch (error) {
      console.error(`AI service call failed (${aiService}):`, error);
      throw error;
    }
  }

  /**
   * Call OpenAI API
   * @param {string} prompt - Prompt text
   * @param {Object} options - Options
   * @returns {Promise<Object>} OpenAI response
   */
  async callOpenAI(prompt, options = {}) {
    try {
      const response = await this.openai.chat.completions.create({
        model: options.aiService || this.defaultConfig.model,
        messages: [
          {
            role: "system",
            content:
              "You are a professional bio writer specializing in creating compelling, authentic bios for content creators and models.",
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        temperature: options.temperature || this.defaultConfig.temperature,
        max_tokens: options.maxTokens || this.defaultConfig.maxTokens,
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new ApiError(500, "No content received from OpenAI");
      }

      // Parse JSON response
      const parsedBios = JSON.parse(content);

      return {
        short: parsedBios.short,
        medium: parsedBios.medium,
        long: parsedBios.long,
        usage: response.usage,
      };
    } catch (error) {
      if (error.message.includes("JSON")) {
        throw new ApiError(500, "Failed to parse AI response as JSON");
      }
      throw error;
    }
  }

  /**
   * Call Gemini API (placeholder - implement based on your Gemini setup)
   * @param {string} prompt - Prompt text
   * @param {Object} options - Options
   * @returns {Promise<Object>} Gemini response
   */
  async callGemini(prompt, options = {}) {
    // Implement Gemini API call here
    throw new ApiError(501, "Gemini integration not implemented yet");
  }

  /**
   * Process and validate bio content
   * @param {Object} rawBios - Raw AI-generated bios
   * @returns {Object} Processed bio data
   */
  processBioContent(rawBios) {
    const processed = {
      short: {
        content: rawBios.short?.trim() || "",
        wordCount: 0,
        characterCount: 0,
      },
      medium: {
        content: rawBios.medium?.trim() || "",
        wordCount: 0,
        characterCount: 0,
      },
      long: {
        content: rawBios.long?.trim() || "",
        wordCount: 0,
        characterCount: 0,
      },
    };

    // Calculate metrics for each bio
    Object.keys(processed).forEach((type) => {
      const content = processed[type].content;
      processed[type].wordCount = content
        .split(/\s+/)
        .filter((word) => word.length > 0).length;
      processed[type].characterCount = content.length;
    });

    // Validate length constraints
    if (processed.short.characterCount > 200) {
      console.warn("Short bio exceeds 200 characters");
    }
    if (processed.medium.characterCount > 500) {
      console.warn("Medium bio exceeds 500 characters");
    }
    if (processed.long.characterCount > 1000) {
      console.warn("Long bio exceeds 1000 characters");
    }

    return processed;
  }

  /**
   * Estimate AI service costs
   * @param {string} prompt - Input prompt
   * @param {Object} response - AI response
   * @param {string} aiService - AI service used
   * @returns {Object} Cost estimation
   */
  estimateCost(prompt, response, aiService = "gpt-4") {
    const inputTokens = Math.ceil(prompt.length / 4); // Rough estimate
    const outputText = Object.values(response)
      .map((bio) => bio.content)
      .join(" ");
    const outputTokens = Math.ceil(outputText.length / 4);

    // Cost per 1000 tokens (approximate)
    const costs = {
      "gpt-4": 0.03,
      "gpt-3.5-turbo": 0.002,
      "gemini-pro": 0.001,
    };

    const costPerToken = (costs[aiService] || costs["gpt-4"]) / 1000;

    return {
      inputTokens,
      outputTokens,
      costPerToken,
      totalCost: (inputTokens + outputTokens) * costPerToken,
    };
  }

  /**
   * Categorize errors for analytics
   * @param {Error} error - Error object
   * @returns {string} Error category
   */
  categorizeError(error) {
    if (error.status === 429) return "rate_limit";
    if (error.status === 402) return "quota_exceeded";
    if (error.status >= 500) return "ai_service_error";
    if (error.message.includes("JSON")) return "parsing_error";
    if (error.message.includes("content")) return "content_filter";
    if (error.message.includes("network")) return "network_error";
    if (error.message.includes("validation")) return "validation_error";
    return "ai_service_error";
  }
}

export default new BioService();
