import React from "react";
import {
  CloudIcon,
  TrendingUpIcon,
  MessageCircleIcon,
  BellIcon,
  BarChartIcon,
  MapPinIcon,
  CalendarIcon,
} from "lucide-react";
import CustomCalendarIcon from "@/components/icons/CustomCalendarIcon";

// Widget icon mapping
const WIDGET_ICONS = {
  weather: CloudIcon,
  clock: CustomCalendarIcon,
  calendar: CalendarIcon,
  analytics: TrendingUpIcon,
  messages: MessageCircleIcon,
  notifications: BellIcon,
  metrics: BarChartIcon,
  location: MapPinIcon,
};

// Widget icon component
const WidgetIcon = ({
  type,
  size = 24,
  className = "",
  isDragging = false,
  ...props
}) => {
  const IconComponent = WIDGET_ICONS[type];

  if (!IconComponent) {
    // Fallback for unknown widget types
    return (
      <div
        className={`
          w-${size} h-${size} 
          bg-gray-200 rounded-md 
          flex items-center justify-center
          text-gray-500 text-xs
          ${className}
          ${isDragging ? "opacity-50" : ""}
        `}
        {...props}
      >
        ?
      </div>
    );
  }

  return (
    <IconComponent
      size={size}
      className={`
        ${className}
        ${isDragging ? "opacity-50" : ""}
      `}
      {...props}
    />
  );
};

export default WidgetIcon;
