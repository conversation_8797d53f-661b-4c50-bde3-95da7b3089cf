import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Plus, Trash2, ArrowLeft } from "lucide-react";
import {
  fetchTemplateById,
  updateTemplate,
} from "@/redux/features/questionnaire/questionnaireSlice";
import ButtonLoader from "@/reusable/Loader/ButtonLoader";

const EditTemplatePage = () => {
  const { templateId } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { selectedTemplate, templateLoading } = useSelector(
    (state) => state.questionnaireReducer
  );

  const [formData, setFormData] = useState({
    title: "",
    description: "",
    isActive: true,
    sections: [
      {
        title: "",
        questions: [
          {
            label: "",
            type: "text",
            required: false,
            helpText: "",
            options: [],
          },
        ],
      },
    ],
  });

  useEffect(() => {
    if (templateId) {
      dispatch(fetchTemplateById(templateId));
    }
  }, [dispatch, templateId]);

  useEffect(() => {
    if (selectedTemplate) {
      setFormData({
        title: selectedTemplate.title || "",
        description: selectedTemplate.description || "",
        isActive:
          selectedTemplate.isActive !== undefined
            ? selectedTemplate.isActive
            : true,
        sections:
          selectedTemplate.sections?.length > 0
            ? selectedTemplate.sections
            : [
                {
                  title: "",
                  questions: [
                    {
                      label: "",
                      type: "text",
                      required: false,
                      helpText: "",
                      options: [],
                    },
                  ],
                },
              ],
      });
    }
  }, [selectedTemplate]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const addSection = () => {
    setFormData((prev) => ({
      ...prev,
      sections: [
        ...prev.sections,
        {
          title: "",
          questions: [
            {
              label: "",
              type: "text",
              required: false,
              helpText: "",
              options: [],
            },
          ],
        },
      ],
    }));
  };

  const removeSection = (sectionIndex) => {
    setFormData((prev) => ({
      ...prev,
      sections: prev.sections.filter((_, index) => index !== sectionIndex),
    }));
  };

  const updateSection = (sectionIndex, field, value) => {
    setFormData((prev) => ({
      ...prev,
      sections: prev.sections.map((section, index) =>
        index === sectionIndex ? { ...section, [field]: value } : section
      ),
    }));
  };

  const addQuestion = (sectionIndex) => {
    setFormData((prev) => ({
      ...prev,
      sections: prev.sections.map((section, index) =>
        index === sectionIndex
          ? {
              ...section,
              questions: [
                ...section.questions,
                {
                  label: "",
                  type: "text",
                  required: false,
                  helpText: "",
                  options: [],
                },
              ],
            }
          : section
      ),
    }));
  };

  const removeQuestion = (sectionIndex, questionIndex) => {
    setFormData((prev) => ({
      ...prev,
      sections: prev.sections.map((section, index) =>
        index === sectionIndex
          ? {
              ...section,
              questions: section.questions.filter(
                (_, qIndex) => qIndex !== questionIndex
              ),
            }
          : section
      ),
    }));
  };

  const updateQuestion = (sectionIndex, questionIndex, field, value) => {
    setFormData((prev) => ({
      ...prev,
      sections: prev.sections.map((section, sIndex) =>
        sIndex === sectionIndex
          ? {
              ...section,
              questions: section.questions.map((question, qIndex) =>
                qIndex === questionIndex
                  ? { ...question, [field]: value }
                  : question
              ),
            }
          : section
      ),
    }));
  };

  const handleSubmit = async () => {
    if (!formData.title.trim()) {
      toast.error("Please enter a template title");
      return;
    }

    const templateData = {
      title: formData.title.trim(),
      description: formData.description.trim(),
      isActive: formData.isActive,
      sections: formData.sections.filter(
        (section) =>
          section.title.trim() && section.questions.some((q) => q.label.trim())
      ),
    };

    try {
      await dispatch(updateTemplate({ templateId, templateData })).unwrap();
      toast.success("Template updated successfully!");
      navigate("/agency/questionnaires");
    } catch (error) {
      console.error("Failed to update template:", error);
      toast.error(
        error?.message || "Failed to update template. Please try again."
      );
    }
  };

  if (templateLoading && !selectedTemplate) {
    return (
      <div className="p-4 flex items-center justify-center">
        <div className="text-white">Loading template...</div>
      </div>
    );
  }

  return (
    <div className="p-4">
      <div className="max-w-7xl mx-auto space-y-4">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate("/agency/questionnaires")}
            className="text-gray-400 hover:text-white hover:bg-[#27272a]"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Templates
          </Button>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold tracking-tight text-white">
              Edit Template
            </h1>
            <p className="text-gray-400">
              Update your questionnaire template and its sections
            </p>
          </div>
        </div>

        {/* Basic Information */}
        <Card className="bg-[#18181b] border-[#27272a]">
          <CardHeader>
            <CardTitle className="text-white">Basic Information</CardTitle>
            <CardDescription className="text-gray-400">
              Update basic details about your questionnaire template
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title" className="text-gray-300">
                Template Title
              </Label>
              <Input
                id="title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                placeholder="Enter template title"
                className="bg-[#27272a] border-[#3f3f46] text-white placeholder:text-gray-500"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description" className="text-gray-300">
                Description
              </Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Enter template description (optional)"
                className="bg-[#27272a] border-[#3f3f46] text-white placeholder:text-gray-500"
                rows={3}
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="isActive"
                checked={formData.isActive}
                onCheckedChange={(checked) =>
                  setFormData((prev) => ({ ...prev, isActive: checked }))
                }
              />
              <Label htmlFor="isActive" className="text-gray-300">
                Active Template
              </Label>
              <span className="text-sm text-gray-500">
                Inactive templates cannot be assigned to models
              </span>
            </div>
          </CardContent>
        </Card>

        {/* Sections */}
        <Card className="bg-[#18181b] border-[#27272a]">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-white">Template Sections</CardTitle>
                <CardDescription className="text-gray-400">
                  Organize your questions into logical sections
                </CardDescription>
              </div>
              <Button
                onClick={addSection}
                className="bg-[#1a1a1a] hover:bg-[#0f0f0f] text-white"
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Section
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {formData.sections.map((section, sectionIndex) => (
              <div
                key={sectionIndex}
                className="space-y-4 p-4 border border-[#27272a] rounded-lg bg-[#18181b]"
              >
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-white">
                    Section {sectionIndex + 1}
                  </h3>
                  {formData.sections.length > 1 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeSection(sectionIndex)}
                      className="text-red-400 hover:text-red-300 hover:bg-red-900/20"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label className="text-gray-300">Section Title</Label>
                    <Input
                      value={section.title}
                      onChange={(e) =>
                        updateSection(sectionIndex, "title", e.target.value)
                      }
                      placeholder="General Information"
                      className="bg-[#27272a] border-[#3f3f46] text-white placeholder:text-gray-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label className="text-gray-300">
                      Section Description (Optional)
                    </Label>
                    <Input
                      value={section.description || ""}
                      onChange={(e) =>
                        updateSection(
                          sectionIndex,
                          "description",
                          e.target.value
                        )
                      }
                      placeholder="Enter section description"
                      className="bg-[#27272a] border-[#3f3f46] text-white placeholder:text-gray-500"
                    />
                  </div>
                </div>

                {/* Questions */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-white">Questions</h4>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => addQuestion(sectionIndex)}
                      className="bg-[#27272a] border-[#27272a] text-white hover:bg-[#1a1a1a] hover:border-[#1a1a1a]"
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      Add Question
                    </Button>
                  </div>

                  {section.questions.map((question, questionIndex) => (
                    <div
                      key={questionIndex}
                      className="space-y-3 p-3 border border-[#3f3f46] rounded bg-[#0f0f0f]"
                    >
                      <div className="flex items-center justify-between">
                        <Label className="text-gray-300">
                          Question {questionIndex + 1}
                        </Label>
                        {section.questions.length > 1 && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() =>
                              removeQuestion(sectionIndex, questionIndex)
                            }
                            className="text-red-400 hover:text-red-300 hover:bg-red-900/20"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>

                      <div className="grid gap-3 md:grid-cols-2">
                        <div className="space-y-2">
                          <Label className="text-gray-300">Question Text</Label>
                          <Input
                            value={question.label}
                            onChange={(e) =>
                              updateQuestion(
                                sectionIndex,
                                questionIndex,
                                "label",
                                e.target.value
                              )
                            }
                            placeholder="Enter question text"
                            className="bg-[#27272a] border-[#3f3f46] text-white placeholder:text-gray-500"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label className="text-gray-300">Question Type</Label>
                          <Select
                            value={question.type}
                            onValueChange={(value) =>
                              updateQuestion(
                                sectionIndex,
                                questionIndex,
                                "type",
                                value
                              )
                            }
                          >
                            <SelectTrigger className="bg-[#27272a] border-[#3f3f46] text-white">
                              <SelectValue placeholder="Text Input" />
                            </SelectTrigger>
                            <SelectContent className="bg-[#18181b] border-[#27272a]">
                              <SelectItem
                                value="text"
                                className="text-white hover:bg-[#27272a]"
                              >
                                Text Input
                              </SelectItem>
                              <SelectItem
                                value="textarea"
                                className="text-white hover:bg-[#27272a]"
                              >
                                Long Text
                              </SelectItem>
                              <SelectItem
                                value="select"
                                className="text-white hover:bg-[#27272a]"
                              >
                                Multiple Choice
                              </SelectItem>
                              <SelectItem
                                value="radio"
                                className="text-white hover:bg-[#27272a]"
                              >
                                Single Choice
                              </SelectItem>
                              <SelectItem
                                value="checkbox"
                                className="text-white hover:bg-[#27272a]"
                              >
                                Checkboxes
                              </SelectItem>
                              <SelectItem
                                value="number"
                                className="text-white hover:bg-[#27272a]"
                              >
                                Number
                              </SelectItem>
                              <SelectItem
                                value="email"
                                className="text-white hover:bg-[#27272a]"
                              >
                                Email
                              </SelectItem>
                              <SelectItem
                                value="phone"
                                className="text-white hover:bg-[#27272a]"
                              >
                                Phone
                              </SelectItem>
                              <SelectItem
                                value="date"
                                className="text-white hover:bg-[#27272a]"
                              >
                                Date
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={question.required}
                            onCheckedChange={(checked) =>
                              updateQuestion(
                                sectionIndex,
                                questionIndex,
                                "required",
                                checked
                              )
                            }
                          />
                          <Label className="text-gray-300">
                            Required Question
                          </Label>
                          <span className="text-sm text-gray-500">
                            Users must answer this question
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex items-center justify-end gap-4">
          <Button
            variant="outline"
            onClick={() => navigate("/agency/questionnaires")}
            className="bg-[#27272a] border-[#27272a] text-white hover:bg-[#1a1a1a] hover:border-[#1a1a1a]"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={templateLoading || !formData.title}
            className="bg-[#1a1a1a] hover:bg-[#0f0f0f] text-white disabled:opacity-50"
          >
            {templateLoading ? <ButtonLoader /> : "Update Template"}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default EditTemplatePage;
