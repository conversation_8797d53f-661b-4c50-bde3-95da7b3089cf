import { ApiError } from "../../utils/ApiError.js";
import { ApiResponse } from "../../utils/ApiResponse.js";
import PersonaProfile from "../../models/persona/PersonaProfile.js";
import PersonaUsageLog from "../../models/persona/PersonaUsageLog.js";
import { asyncHandler } from "../../utils/asyncHandler.js";

/**
 * PersonaAnalyticsController - Handles persona analytics and performance metrics
 */
class PersonaAnalyticsController {
  /**
   * Get tone consistency analysis across personas
   */
  static getToneConsistencyAnalysis = asyncHandler(async (req, res) => {
    const { agencyId } = req.query;
    let { agencyId: userAgencyId, _id: userId, role } = req.user;

    if (role === "agency" && !userAgencyId) {
      userAgencyId = userId;
    }

    const targetAgencyId = agencyId || userAgencyId;

    // Get all personas for the agency
    const personas = await PersonaProfile.find({
      agencyId: targetAgencyId,
      isActive: true,
    }).populate("modelId", "username fullName");

    if (personas.length === 0) {
      return res
        .status(200)
        .json(
          new ApiResponse(
            200,
            { consistency: 100, analysis: "No personas found" },
            "Analysis complete",
          ),
        );
    }

    // Analyze tone consistency
    const toneAnalysis = this.analyzeToneConsistency(personas);

    res
      .status(200)
      .json(
        new ApiResponse(
          200,
          toneAnalysis,
          "Tone consistency analysis completed",
        ),
      );
  });

  /**
   * Get persona performance metrics
   */
  static getPersonaPerformanceMetrics = asyncHandler(async (req, res) => {
    const { timeframe = "30d", modelId } = req.query;
    let { agencyId, _id: userId, role } = req.user;

    if (role === "agency" && !agencyId) {
      agencyId = userId;
    }

    const timeframeDays = this.parseTimeframe(timeframe);
    const startDate = new Date(
      Date.now() - timeframeDays * 24 * 60 * 60 * 1000,
    );

    // Build query
    const query = {
      agencyId,
      createdAt: { $gte: startDate },
    };

    if (modelId) {
      query.modelId = modelId;
    }

    // Get usage statistics
    const usageStats = await this.getUsageStatistics(query, startDate);

    // Get performance metrics
    const performanceMetrics = await this.getPerformanceMetrics(query);

    // Get feedback statistics
    const feedbackStats = await this.getFeedbackStatistics(query);

    const metrics = {
      timeframe,
      period: {
        start: startDate,
        end: new Date(),
      },
      usage: usageStats,
      performance: performanceMetrics,
      feedback: feedbackStats,
    };

    res
      .status(200)
      .json(
        new ApiResponse(
          200,
          metrics,
          "Performance metrics retrieved successfully",
        ),
      );
  });

  /**
   * Get persona adoption analytics
   */
  static getPersonaAdoptionAnalytics = asyncHandler(async (req, res) => {
    let { agencyId, _id: userId, role } = req.user;

    if (role === "agency" && !agencyId) {
      agencyId = userId;
    }

    // Get adoption metrics
    const totalModels = await this.getTotalModelsCount(agencyId);
    const modelsWithPersonas = await this.getModelsWithPersonasCount(agencyId);
    const adoptionRate =
      totalModels > 0 ? (modelsWithPersonas / totalModels) * 100 : 0;

    // Get creation trend (last 30 days)
    const creationTrend = await this.getCreationTrend(agencyId);

    // Get most active personas
    const mostActivePersonas = await this.getMostActivePersonas(agencyId);

    const analytics = {
      adoption: {
        totalModels,
        modelsWithPersonas,
        adoptionRate: Math.round(adoptionRate * 100) / 100,
      },
      trend: creationTrend,
      mostActive: mostActivePersonas,
    };

    res
      .status(200)
      .json(
        new ApiResponse(
          200,
          analytics,
          "Adoption analytics retrieved successfully",
        ),
      );
  });

  /**
   * Get persona effectiveness analysis
   */
  static getPersonaEffectivenessAnalysis = asyncHandler(async (req, res) => {
    const { personaId } = req.params;

    const persona = await PersonaProfile.findById(personaId);
    if (!persona) {
      throw new ApiError(404, "Persona not found");
    }

    // Get effectiveness metrics
    const effectiveness = await this.calculateEffectiveness(personaId);

    res
      .status(200)
      .json(
        new ApiResponse(200, effectiveness, "Effectiveness analysis completed"),
      );
  });

  // Helper methods
  static analyzeToneConsistency(personas) {
    const tones = personas
      .map((p) => p.communicationStyle?.tone)
      .filter(Boolean);

    if (tones.length === 0) {
      return {
        consistency: 100,
        analysis: "No tone data available",
        distribution: {},
      };
    }

    const toneCount = {};
    tones.forEach((tone) => {
      toneCount[tone] = (toneCount[tone] || 0) + 1;
    });

    const dominantTone = Object.keys(toneCount).reduce((a, b) =>
      toneCount[a] > toneCount[b] ? a : b,
    );

    const consistency = (toneCount[dominantTone] / tones.length) * 100;

    return {
      consistency: Math.round(consistency * 100) / 100,
      dominantTone,
      distribution: toneCount,
      analysis:
        consistency > 80
          ? "High consistency"
          : consistency > 60
            ? "Moderate consistency"
            : "Low consistency",
    };
  }

  static parseTimeframe(timeframe) {
    switch (timeframe) {
      case "7d":
        return 7;
      case "30d":
        return 30;
      case "90d":
        return 90;
      case "1y":
        return 365;
      default:
        return 30;
    }
  }

  static async getUsageStatistics(query, startDate) {
    const totalUsage = await PersonaUsageLog.countDocuments({
      ...query,
      usageDate: { $gte: startDate },
    });

    const dailyUsage = await PersonaUsageLog.aggregate([
      {
        $match: {
          ...query,
          usageDate: { $gte: startDate },
        },
      },
      {
        $group: {
          _id: {
            $dateToString: { format: "%Y-%m-%d", date: "$usageDate" },
          },
          count: { $sum: 1 },
        },
      },
      { $sort: { _id: 1 } },
    ]);

    return {
      total: totalUsage,
      daily: dailyUsage,
    };
  }

  static async getPerformanceMetrics(query) {
    const personas = await PersonaProfile.find(query);

    const totalPersonas = personas.length;
    const avgUsageCount =
      totalPersonas > 0
        ? personas.reduce((sum, p) => sum + (p.usageCount || 0), 0) /
          totalPersonas
        : 0;

    return {
      totalPersonas,
      averageUsage: Math.round(avgUsageCount * 100) / 100,
    };
  }

  static async getFeedbackStatistics(query) {
    try {
      const PersonaFeedback = (
        await import("../../models/persona/PersonaFeedback.js")
      ).default;

      const feedbackStats = await PersonaFeedback.aggregate([
        {
          $lookup: {
            from: "personaprofiles",
            localField: "personaId",
            foreignField: "_id",
            as: "persona",
          },
        },
        {
          $match: {
            "persona.agencyId": query.agencyId,
          },
        },
        {
          $group: {
            _id: null,
            averageRating: { $avg: "$rating" },
            averageUsefulness: { $avg: "$usefulness" },
            totalFeedback: { $sum: 1 },
          },
        },
      ]);

      return (
        feedbackStats[0] || {
          averageRating: 0,
          averageUsefulness: 0,
          totalFeedback: 0,
        }
      );
    } catch (error) {
      return {
        averageRating: 0,
        averageUsefulness: 0,
        totalFeedback: 0,
      };
    }
  }

  static async getTotalModelsCount(agencyId) {
    const ModelUser = (await import("../../models/model.js")).default;
    return await ModelUser.countDocuments({ agencyId });
  }

  static async getModelsWithPersonasCount(agencyId) {
    const uniqueModels = await PersonaProfile.distinct("modelId", { agencyId });
    return uniqueModels.length;
  }

  static async getCreationTrend(agencyId) {
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

    return await PersonaProfile.aggregate([
      {
        $match: {
          agencyId: agencyId,
          createdAt: { $gte: thirtyDaysAgo },
        },
      },
      {
        $group: {
          _id: {
            $dateToString: { format: "%Y-%m-%d", date: "$createdAt" },
          },
          count: { $sum: 1 },
        },
      },
      { $sort: { _id: 1 } },
    ]);
  }

  static async getMostActivePersonas(agencyId) {
    return await PersonaProfile.find({ agencyId })
      .sort({ usageCount: -1 })
      .limit(5)
      .populate("modelId", "username fullName")
      .select("personaText usageCount lastUsedAt tags");
  }

  static async calculateEffectiveness(personaId) {
    const persona = await PersonaProfile.findById(personaId);

    // Calculate based on usage frequency, feedback, and recency
    const usageScore = Math.min((persona.usageCount || 0) / 10, 1) * 40; // Max 40 points
    const recencyScore = this.calculateRecencyScore(persona.lastUsedAt) * 30; // Max 30 points

    let feedbackScore = 15; // Default middle score
    try {
      const PersonaFeedback = (
        await import("../../models/persona/PersonaFeedback.js")
      ).default;
      const avgRating = await PersonaFeedback.aggregate([
        { $match: { personaId: persona._id } },
        { $group: { _id: null, avg: { $avg: "$rating" } } },
      ]);

      if (avgRating[0]) {
        feedbackScore = (avgRating[0].avg / 5) * 30; // Max 30 points
      }
    } catch (error) {
      // PersonaFeedback model might not exist
    }

    const totalScore = usageScore + recencyScore + feedbackScore;

    return {
      overall: Math.round(totalScore),
      breakdown: {
        usage: Math.round(usageScore),
        recency: Math.round(recencyScore),
        feedback: Math.round(feedbackScore),
      },
      rating: this.getEffectivenessRating(totalScore),
    };
  }

  static calculateRecencyScore(lastUsedAt) {
    if (!lastUsedAt) return 0;

    const daysSinceUse =
      (Date.now() - lastUsedAt.getTime()) / (1000 * 60 * 60 * 24);

    if (daysSinceUse <= 1) return 1;
    if (daysSinceUse <= 7) return 0.8;
    if (daysSinceUse <= 30) return 0.5;
    if (daysSinceUse <= 90) return 0.2;
    return 0;
  }

  static getEffectivenessRating(score) {
    if (score >= 80) return "Excellent";
    if (score >= 60) return "Good";
    if (score >= 40) return "Fair";
    if (score >= 20) return "Poor";
    return "Very Poor";
  }
}

export default PersonaAnalyticsController;
