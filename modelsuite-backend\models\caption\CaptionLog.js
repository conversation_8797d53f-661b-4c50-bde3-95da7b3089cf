import mongoose from "mongoose";

const captionLogSchema = new mongoose.Schema(
  {
    modelId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    mediaUrl: {
      type: String,
      required: true,
    },
    mediaType: {
      type: String,
      enum: ["image", "video"],
      required: true,
    },
    fileSize: {
      type: Number, // in bytes
    },
    prompt: {
      type: String,
      required: true,
    },
    captions: [
      {
        type: String,
      },
    ],
    aiService: {
      type: String,
      enum: ["gpt-4", "gemini-vision"],
      required: true,
    },
    processingTime: {
      type: Number, // milliseconds
    },
    nsfwScore: {
      type: Number,
      min: 0,
      max: 1,
      default: 0,
    },
    isNsfw: {
      type: Boolean,
      default: false,
    },

    // Phase 2: Persona Integration Fields
    linkedPersona: {
      personaId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "PersonaProfile",
      },
      version: {
        type: Number,
      },
      influenceScore: {
        type: Number, // 0-100 percentage of how much persona influenced generation
        min: 0,
        max: 100,
      },
    },

    personaInfluence: {
      toneInfluence: {
        type: Number,
        min: 0,
        max: 100,
        default: 0,
      },
      interestsInfluence: {
        type: Number,
        min: 0,
        max: 100,
        default: 0,
      },
      goalsInfluence: {
        type: Number,
        min: 0,
        max: 100,
        default: 0,
      },
      demographicsInfluence: {
        type: Number,
        min: 0,
        max: 100,
        default: 0,
      },
      behavioralInfluence: {
        type: Number,
        min: 0,
        max: 100,
        default: 0,
      },
    },

    generationMode: {
      type: String,
      enum: ["standard", "persona_enhanced"],
      default: "standard",
    },

    // Caption effectiveness tracking (Phase 3)
    effectiveness: {
      agencyRating: {
        type: String,
        enum: ["good", "neutral", "bad"],
      },
      ratedAt: {
        type: Date,
      },
      ratedBy: {
        type: mongoose.Schema.Types.ObjectId,
        refPath: "effectiveness.ratedByModel",
      },
      ratedByModel: {
        type: String,
        enum: ["Agency", "Employee"],
      },
    },

    metadata: {
      imageResolution: String,
      videoLength: Number, // seconds
      aiService: String, // AI service used
      modelVersion: String, // e.g., "gpt-4-vision-preview"
      tokenUsage: Number, // Token usage for cost tracking
      responseId: String, // AI response ID for debugging
      candidateCount: Number, // For Gemini responses
    },
  },
  {
    timestamps: true,
  },
);

// Index for efficient queries
captionLogSchema.index({ modelId: 1, createdAt: -1 });
captionLogSchema.index({ isNsfw: 1 });
captionLogSchema.index({ "linkedPersona.personaId": 1 }); // For persona-caption tracking
captionLogSchema.index({ generationMode: 1 }); // For performance comparison
captionLogSchema.index({ "effectiveness.agencyRating": 1 }); // For effectiveness analysis

export default mongoose.model("CaptionLog", captionLogSchema);
