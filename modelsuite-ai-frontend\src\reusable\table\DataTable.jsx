import React from "react";
import {
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from "@tanstack/react-table";
import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
  TableCell,
} from "../../components/ui/table";
import DataTablePagination from "./DataTablePagination";

export function DataTable({
  columns,
  hasClick,
  data,
  onClickRoute,
  pagination,
  setPagination,
  hasPagination = true,
  showPaginationControls = false, // New prop to control pagination UI visibility
}) {
  const table = useReactTable({
    data: data.results || data.result || data,
    columns,
    manualPagination: !showPaginationControls, // Use client-side pagination when controls are shown
    pageCount: showPaginationControls
      ? Math.ceil(
          (data.results || data.result || data).length /
            (pagination?.pageSize || 10)
        )
      : data.count && data.limit
      ? Math.ceil(data.count / data.limit)
      : 1,
    state: {
      pagination,
    },
    onPaginationChange: setPagination,
    getPaginationRowModel: getPaginationRowModel(),
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <div className="px-8 py-6">
      <Table className="w-full bg-[#18181b] rounded-xl overflow-hidden">
        <TableHeader className="bg-[#18181b]">
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header) => {
                return (
                  <TableHead
                    key={header.id}
                    className="text-white bg-[#18181b] px-4 py-3 text-xs font-semibold tracking-wider uppercase border-b border-[#232327] first:rounded-tl-xl last:rounded-tr-xl"
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                );
              })}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map((row) => (
              <TableRow
                key={row.id}
                data-state={row.getIsSelected() && "selected"}
                onClick={() => hasClick && onClickRoute(row.original)}
                className="border-b border-[#232327] hover:bg-[#232327] transition-colors"
              >
                {row.getVisibleCells().map((cell) => (
                  <TableCell
                    key={cell.id}
                    className="px-4 py-2 text-sm text-gray-200"
                  >
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell
                colSpan={columns.length}
                className="h-24 text-center text-gray-400"
              >
                No results.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      {showPaginationControls && (
        <DataTablePagination table={table} data={data} />
      )}
    </div>
  );
}
