import * as React from "react";
import { cn } from "@/lib/utils";
import Spinner from "./Spinner";

const LoadingOverlay = ({
  isLoading = false,
  children,
  overlay = true,
  blur = true,
  spinner = true,
  message,
  size = "default",
  variant = "default",
  className,
  ...props
}) => {
  if (!isLoading) {
    return children;
  }

  return (
    <div className={cn("relative", className)} {...props}>
      {/* Content */}
      <div
        className={cn(
          "transition-all duration-200",
          isLoading && blur && "blur-sm",
          isLoading && "pointer-events-none select-none"
        )}
      >
        {children}
      </div>

      {/* Loading Overlay */}
      {overlay && (
        <div className="absolute inset-0 bg-[#0f0f0f]/80 backdrop-blur-sm flex items-center justify-center z-50 rounded-lg">
          <div className="text-center">
            {spinner && (
              <Spinner size={size} variant={variant} className="mb-2" />
            )}
            {message && <p className="text-sm text-gray-400 mt-2">{message}</p>}
          </div>
        </div>
      )}
    </div>
  );
};

export default LoadingOverlay;
