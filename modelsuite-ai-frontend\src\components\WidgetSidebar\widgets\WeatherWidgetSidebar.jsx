import React, { useState, useEffect } from "react";
import { Cloud, Sun, CloudRain, Snowflake, RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";

const WeatherWidgetSidebar = ({ data }) => {
  const [weatherData, setWeatherData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Mock weather data for development
  const mockWeatherData = React.useMemo(
    () => ({
      location: "New York",
      temperature: 24,
      condition: "sunny",
      humidity: 65,
      windSpeed: 12,
      description: "Partly cloudy",
    }),
    []
  );

  useEffect(() => {
    // Use provided data or fallback to mock data
    if (data?.data?.weather) {
      setWeatherData(data.data.weather);
    } else {
      // Simulate API delay
      setLoading(true);
      setTimeout(() => {
        setWeatherData(mockWeatherData);
        setLoading(false);
      }, 1000);
    }
  }, [data, mockWeatherData]);

  const getWeatherIcon = (condition) => {
    switch (condition?.toLowerCase()) {
      case "sunny":
      case "clear":
        return <Sun className="h-8 w-8 text-yellow-400" />;
      case "cloudy":
      case "partly cloudy":
        return <Cloud className="h-8 w-8 text-gray-400" />;
      case "rainy":
      case "rain":
        return <CloudRain className="h-8 w-8 text-blue-400" />;
      case "snowy":
      case "snow":
        return <Snowflake className="h-8 w-8 text-blue-200" />;
      default:
        return <Cloud className="h-8 w-8 text-gray-400" />;
    }
  };

  const handleRefresh = () => {
    setLoading(true);
    setError(null);
    // Simulate refresh
    setTimeout(() => {
      setWeatherData({
        ...mockWeatherData,
        temperature: Math.floor(Math.random() * 35) + 5,
      });
      setLoading(false);
    }, 800);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-4">
        <RefreshCw className="h-4 w-4 animate-spin text-gray-400" />
        <span className="ml-2 text-xs text-gray-400">Loading weather...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-3">
        <p className="text-xs text-red-400 mb-2">{error}</p>
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefresh}
          className="text-xs border-gray-600 text-gray-300 hover:bg-gray-700"
        >
          Retry
        </Button>
      </div>
    );
  }

  if (!weatherData) {
    return (
      <div className="text-center py-3 text-xs text-gray-500">
        No weather data available
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {/* Main Weather Display */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {getWeatherIcon(weatherData.condition)}
          <div>
            <p className="text-xl font-bold text-white">
              {weatherData.temperature}°C
            </p>
            <p className="text-xs text-gray-400">{weatherData.location}</p>
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleRefresh}
          className="text-gray-400 hover:text-white hover:bg-gray-700 h-7 w-7 p-0"
        >
          <RefreshCw className="h-3 w-3" />
        </Button>
      </div>

      {/* Weather Description */}
      <p className="text-xs text-gray-300 capitalize">
        {weatherData.description}
      </p>

      {/* Weather Details */}
      <div className="grid grid-cols-2 gap-2 text-xs">
        <div className="bg-gray-700/50 rounded p-2 text-center">
          <p className="text-gray-400">Humidity</p>
          <p className="text-white font-medium">{weatherData.humidity}%</p>
        </div>
        <div className="bg-gray-700/50 rounded p-2 text-center">
          <p className="text-gray-400">Wind</p>
          <p className="text-white font-medium">{weatherData.windSpeed} km/h</p>
        </div>
      </div>
    </div>
  );
};

export default WeatherWidgetSidebar;
