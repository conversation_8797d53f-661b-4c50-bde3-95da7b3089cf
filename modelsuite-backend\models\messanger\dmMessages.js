import mongoose from "mongoose";

const dmMessageSchema = new mongoose.Schema(
  {
    convoId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: "DmConversation",
      index: true, // Fast filtering by conversation
    },
    senderId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: "User",
      index: true, // Useful for querying messages by sender
    },
    receiverId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: "User",
    },

    text: { type: String, default: "" },

    attachments: [
      {
        fileId: { type: String },
        type: { type: String },
        name: { type: String },
        size: { type: Number },
        width: { type: Number },
        height: { type: Number },
        mimeType: { type: String },
        duration: { type: String, default: null },
        status: { type: String },
        progress: { type: Number },
        cloudinaryUrl: { type: String },
        publicId: { type: String },
      },
    ],

    type: {
      type: String,
      enum: ["textonly", "attachmentonly", "mixed", "system"],
      default: "textonly",
      index: true, // Optional: if you're filtering by message type
    },

    replyTo: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "DmMessage",
      default: null,
    },

    status: {
      type: String,
      enum: ["sent", "delivered", "seen", "error"],
      default: "sent",
      index: true, // Helps with unread/delivery filtering
    },

    deletedFor: [{ type: String, default: [] }],

    reactions: [
      {
        userId: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
        fullName: { type: String },
        avatar: { type: String },
        emoji: String,
      },
    ],

    edited: { type: Boolean, default: false },
    pinned: { type: Boolean, default: false },

    createdAt: {
      type: Date,
      default: Date.now,
      index: true, // Required for sorting by time in conversations
    },
  },
  {
    timestamps: true, // Adds createdAt and updatedAt
  },
);

// Compound index: frequently used in chat apps
dmMessageSchema.index({ convoId: 1, createdAt: -1 });

export default mongoose.model("DmMessage", dmMessageSchema);
