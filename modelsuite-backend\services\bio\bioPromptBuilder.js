/**
 * Bio Prompt Builder Service
 * Constructs AI prompts for bio generation using model and persona data
 */
class BioPromptBuilder {
  /**
   * Build a comprehensive prompt for bio generation
   * @param {Object} modelProfile - Model profile data
   * @param {Object} personaProfile - Persona profile data
   * @param {Object} options - Generation options
   * @returns {string} Formatted prompt for AI
   */
  static buildPrompt(modelProfile, personaProfile, options = {}) {
    const {
      tone = "professional",
      targetAudience = "general",
      includePersonality = true,
      includeSpecialties = true,
      language = "en",
    } = options;

    // Extract key information
    const modelData = this.extractModelData(modelProfile);
    const personaData = this.extractPersonaData(personaProfile);

    // Build the main prompt
    const prompt = this.constructPrompt({
      modelData,
      personaData,
      tone,
      targetAudience,
      includePersonality,
      includeSpecialties,
      language,
    });

    return prompt;
  }

  /**
   * Extract and clean model profile data
   * @param {Object} modelProfile - Raw model profile
   * @returns {Object} Cleaned model data
   */
  static extractModelData(modelProfile) {
    return {
      name: modelProfile.fullName || modelProfile.username || "Model",
      username: modelProfile.username || "",
      description: modelProfile.profileDescription || "",
      interests: Array.isArray(modelProfile.interests)
        ? modelProfile.interests.filter(Boolean).slice(0, 5)
        : [],
      specialties: Array.isArray(modelProfile.specialties)
        ? modelProfile.specialties.filter(Boolean).slice(0, 5)
        : [],
      location: modelProfile.location || "",
      age: modelProfile.age || null,
      experience: modelProfile.yearsOfExperience || null,
      achievements: Array.isArray(modelProfile.achievements)
        ? modelProfile.achievements.filter(Boolean).slice(0, 3)
        : [],
      uniqueTraits: modelProfile.uniqueTraits || "",
    };
  }

  /**
   * Extract and clean persona profile data
   * @param {Object} personaProfile - Raw persona profile
   * @returns {Object} Cleaned persona data
   */
  static extractPersonaData(personaProfile) {
    return {
      name: personaProfile.name || "Professional Persona",
      content: personaProfile.personaText || personaProfile.content || "",
      tone: personaProfile.communicationStyle?.tone || "professional",
      language: personaProfile.communicationStyle?.language || "en",
      formality: personaProfile.communicationStyle?.formality || "formal",
      tags: Array.isArray(personaProfile.tags)
        ? personaProfile.tags.filter(Boolean).slice(0, 8)
        : [],
      voiceCharacteristics: personaProfile.voiceCharacteristics || {},
      brandAlignment: personaProfile.brandAlignment || {},
    };
  }

  /**
   * Construct the actual AI prompt
   * @param {Object} data - All prompt data
   * @returns {string} Complete prompt
   */
  static constructPrompt(data) {
    const {
      modelData,
      personaData,
      tone,
      targetAudience,
      includePersonality,
      includeSpecialties,
      language,
    } = data;

    let prompt = `You are a professional bio writer creating compelling, authentic bios for a content creator. `;
    prompt += `Generate three versions of a bio (short, medium, long) that capture the essence of this person.\n\n`;

    // Model Information Section
    prompt += `=== SUBJECT INFORMATION ===\n`;
    prompt += `Name: ${modelData.name}\n`;
    if (modelData.username) prompt += `Username: @${modelData.username}\n`;
    if (modelData.description)
      prompt += `Current Description: ${modelData.description}\n`;
    if (modelData.location) prompt += `Location: ${modelData.location}\n`;
    if (modelData.age) prompt += `Age: ${modelData.age}\n`;
    if (modelData.experience)
      prompt += `Years of Experience: ${modelData.experience}\n`;

    if (includeSpecialties && modelData.specialties.length > 0) {
      prompt += `Specialties: ${modelData.specialties.join(", ")}\n`;
    }

    if (modelData.interests.length > 0) {
      prompt += `Interests: ${modelData.interests.join(", ")}\n`;
    }

    if (modelData.achievements.length > 0) {
      prompt += `Achievements: ${modelData.achievements.join(", ")}\n`;
    }

    if (modelData.uniqueTraits) {
      prompt += `Unique Traits: ${modelData.uniqueTraits}\n`;
    }

    // Persona Guidelines Section
    prompt += `\n=== PERSONA GUIDELINES ===\n`;
    prompt += `Persona Name: ${personaData.name}\n`;
    if (personaData.content) {
      prompt += `Persona Description: ${personaData.content}\n`;
    }
    prompt += `Communication Tone: ${personaData.tone}\n`;
    prompt += `Formality Level: ${personaData.formality}\n`;

    if (personaData.tags.length > 0) {
      prompt += `Key Characteristics: ${personaData.tags.join(", ")}\n`;
    }

    // Writing Instructions
    prompt += `\n=== WRITING INSTRUCTIONS ===\n`;
    prompt += `Target Audience: ${targetAudience}\n`;
    prompt += `Overall Tone: ${tone}\n`;
    prompt += `Language: ${language}\n`;

    if (includePersonality) {
      prompt += `Include personality traits that make this person relatable and engaging.\n`;
    }

    // Output Format Instructions
    prompt += `\n=== OUTPUT FORMAT ===\n`;
    prompt += `Generate exactly three bio versions with the following specifications:\n\n`;

    prompt += `SHORT BIO (150-200 characters):\n`;
    prompt += `- Perfect for social media profiles\n`;
    prompt += `- Catchy and memorable\n`;
    prompt += `- Include name and 1-2 key points\n`;
    prompt += `- Must be under 200 characters\n\n`;

    prompt += `MEDIUM BIO (300-500 characters):\n`;
    prompt += `- Suitable for platform profiles\n`;
    prompt += `- Include background and specialties\n`;
    prompt += `- Professional yet approachable\n`;
    prompt += `- Must be under 500 characters\n\n`;

    prompt += `LONG BIO (800-1000 characters):\n`;
    prompt += `- Comprehensive professional bio\n`;
    prompt += `- Include background, expertise, and personality\n`;
    prompt += `- Tell a story that connects with audience\n`;
    prompt += `- Must be under 1000 characters\n\n`;

    // Specific Requirements
    prompt += `=== SPECIFIC REQUIREMENTS ===\n`;
    prompt += `1. Write in third person unless persona specifically calls for first person\n`;
    prompt += `2. Avoid clichés and overused phrases\n`;
    prompt += `3. Make each bio authentic and unique\n`;
    prompt += `4. Ensure consistency with the persona's communication style\n`;
    prompt += `5. Include a call-to-action or hook where appropriate\n`;
    prompt += `6. Maintain professional credibility while being engaging\n`;
    prompt += `7. Use active voice and strong verbs\n`;
    prompt += `8. Ensure each bio can stand alone\n\n`;

    // Output Format
    prompt += `=== RESPONSE FORMAT ===\n`;
    prompt += `Respond with a JSON object containing:\n`;
    prompt += `{\n`;
    prompt += `  "short": "short bio text here",\n`;
    prompt += `  "medium": "medium bio text here",\n`;
    prompt += `  "long": "long bio text here"\n`;
    prompt += `}\n\n`;

    prompt += `Do not include any other text outside the JSON object.`;

    return prompt;
  }

  /**
   * Build a regeneration prompt for existing bios
   * @param {Object} modelProfile - Model profile data
   * @param {Object} personaProfile - Persona profile data
   * @param {Object} existingBios - Current bio versions
   * @param {Object} feedback - User feedback on current bios
   * @returns {string} Regeneration prompt
   */
  static buildRegenerationPrompt(
    modelProfile,
    personaProfile,
    existingBios,
    feedback = {},
  ) {
    let prompt = this.buildPrompt(modelProfile, personaProfile);

    prompt += `\n=== REGENERATION CONTEXT ===\n`;
    prompt += `You are improving existing bios based on feedback.\n\n`;

    prompt += `CURRENT BIOS:\n`;
    if (existingBios.shortBio) {
      prompt += `Current Short: ${existingBios.shortBio}\n`;
    }
    if (existingBios.mediumBio) {
      prompt += `Current Medium: ${existingBios.mediumBio}\n`;
    }
    if (existingBios.longBio) {
      prompt += `Current Long: ${existingBios.longBio}\n`;
    }

    if (feedback && Object.keys(feedback).length > 0) {
      prompt += `\nFEEDBACK TO ADDRESS:\n`;
      if (feedback.tooGeneric) prompt += `- Make more specific and unique\n`;
      if (feedback.wrongTone)
        prompt += `- Adjust tone to better match persona\n`;
      if (feedback.missingInfo)
        prompt += `- Include more relevant background information\n`;
      if (feedback.tooLong) prompt += `- Make more concise\n`;
      if (feedback.tooShort) prompt += `- Add more detail and personality\n`;
      if (feedback.customFeedback) prompt += `- ${feedback.customFeedback}\n`;
    }

    prompt += `\nGenerate improved versions that address the feedback while maintaining the persona guidelines.`;

    return prompt;
  }

  /**
   * Build a prompt for specific bio length only
   * @param {Object} modelProfile - Model profile data
   * @param {Object} personaProfile - Persona profile data
   * @param {string} bioType - 'short', 'medium', or 'long'
   * @param {Object} options - Generation options
   * @returns {string} Targeted prompt
   */
  static buildTargetedPrompt(
    modelProfile,
    personaProfile,
    bioType,
    options = {},
  ) {
    const basePrompt = this.buildPrompt(modelProfile, personaProfile, options);

    const typeSpecs = {
      short: {
        length: "150-200 characters",
        purpose: "social media profiles",
        focus: "name and 1-2 key points",
      },
      medium: {
        length: "300-500 characters",
        purpose: "platform profiles",
        focus: "background and specialties",
      },
      long: {
        length: "800-1000 characters",
        purpose: "comprehensive professional bio",
        focus: "background, expertise, and personality",
      },
    };

    const spec = typeSpecs[bioType];
    if (!spec) {
      throw new Error(`Invalid bio type: ${bioType}`);
    }

    let prompt = basePrompt;
    prompt += `\n=== FOCUSED GENERATION ===\n`;
    prompt += `Generate only a ${bioType.toUpperCase()} bio with these specifications:\n`;
    prompt += `- Length: ${spec.length}\n`;
    prompt += `- Purpose: ${spec.purpose}\n`;
    prompt += `- Focus: ${spec.focus}\n\n`;

    prompt += `Respond with only the bio text, no JSON formatting.`;

    return prompt;
  }

  /**
   * Validate prompt data before generation
   * @param {Object} modelProfile - Model profile data
   * @param {Object} personaProfile - Persona profile data
   * @returns {Object} Validation result
   */
  static validatePromptData(modelProfile, personaProfile) {
    const errors = [];
    const warnings = [];

    // Model validation
    if (!modelProfile) {
      errors.push("Model profile is required");
    } else {
      if (!modelProfile.fullName && !modelProfile.username) {
        errors.push("Model must have either fullName or username");
      }
      if (!modelProfile.profileDescription) {
        warnings.push(
          "Model profile description is missing - bio may be generic",
        );
      }
    }

    // Persona validation
    if (!personaProfile) {
      errors.push("Persona profile is required");
    } else {
      if (!personaProfile.personaText && !personaProfile.content) {
        warnings.push(
          "Persona content is missing - bio may not match intended style",
        );
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Get prompt statistics for analytics
   * @param {string} prompt - Generated prompt
   * @returns {Object} Prompt statistics
   */
  static getPromptStats(prompt) {
    return {
      totalLength: prompt.length,
      wordCount: prompt.split(/\s+/).length,
      sectionCount: (prompt.match(/===/g) || []).length / 2,
      estimatedTokens: Math.ceil(prompt.length / 4), // Rough estimate
    };
  }
}

export default BioPromptBuilder;
