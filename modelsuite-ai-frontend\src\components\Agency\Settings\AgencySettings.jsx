import React from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { ArrowLeft } from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import Profile from './Profile'
import SignAndSecurity from './SignAndSecurity'
import BillingAndSubscription from './BillingAndSubscription'

const AgencySettings = () => {
  const navigate = useNavigate();
  return (
    <>
      <div className="flex items-center px-8 py-4 bg-[#0f0f0f] border-b border-[#1a1a1a] h-16 relative">
        <button 
          onClick={() => navigate(-1)}
          className="flex items-center text-gray-400 hover:text-white mr-4"
        >
          <ArrowLeft size={20} />
        </button>
        <h1 className="text-lg font-semibold text-white">My Settings</h1>
      </div>

      <Tabs className="px-8 py-6" defaultValue="personalInfoTab">
        <TabsList className="bg-[#1a1a1a] p-1 rounded-lg">
          <TabsTrigger 
            className="px-4 py-2 text-sm data-[state=active]:bg-[#2a2a2a] data-[state=active]:text-white text-gray-400" 
            value="personalInfoTab"
          >
            Personal Info
          </TabsTrigger>
          <TabsTrigger 
            className="px-4 py-2 text-sm data-[state=active]:bg-[#2a2a2a] data-[state=active]:text-white text-gray-400" 
            value="accountSettingsTab"
          >
            Sign in & Security
          </TabsTrigger>
          <TabsTrigger 
            className="px-4 py-2 text-sm data-[state=active]:bg-[#2a2a2a] data-[state=active]:text-white text-gray-400" 
            value="securityTab"
          >
            Profile & Agency Branding
          </TabsTrigger>
          <TabsTrigger 
            className="px-4 py-2 text-sm data-[state=active]:bg-[#2a2a2a] data-[state=active]:text-white text-gray-400" 
            value="privacyTab"
          >
            Notification
          </TabsTrigger>
          <TabsTrigger 
            className="px-4 py-2 text-sm data-[state=active]:bg-[#2a2a2a] data-[state=active]:text-white text-gray-400" 
            value="termsTab"
          >
            Security & Login Option
          </TabsTrigger>
          <TabsTrigger 
            className="px-4 py-2 text-sm data-[state=active]:bg-[#2a2a2a] data-[state=active]:text-white text-gray-400" 
            value="BillingTab"
          >
            Billing & Subscription
          </TabsTrigger>
        </TabsList>
        <TabsContent className="mt-6 p-4 bg-[#0f0f0f] border border-[#1a1a1a] rounded-lg" value="personalInfoTab">
          <Profile />
        </TabsContent>
        <TabsContent className="mt-6 p-4 bg-[#0f0f0f] border border-[#1a1a1a] rounded-lg" value="accountSettingsTab">
          <SignAndSecurity />
        </TabsContent>
        <TabsContent className="mt-6 p-4 bg-[#0f0f0f] border border-[#1a1a1a] rounded-lg" value="securityTab">
          {/* <SignInAndSecurity /> */}
          <div className="text-gray-300">Sign in & Security content will go here</div>
        </TabsContent>
        <TabsContent className="mt-6 p-4 bg-[#0f0f0f] border border-[#1a1a1a] rounded-lg" value="privacyTab">
          {/* <Privacy/> */}
          <div className="text-gray-300">Privacy content will go here</div>
        </TabsContent>
        <TabsContent className="mt-6 p-4 bg-[#0f0f0f] border border-[#1a1a1a] rounded-lg" value="termsTab">
         {/* <Terms/> */}
         {/* <BillingAndSubscription /> */}
        </TabsContent>
        <TabsContent className="mt-6 p-4 bg-[#0f0f0f] border border-[#1a1a1a] rounded-lg" value="BillingTab">
         {/* <Terms/> */}
         <BillingAndSubscription />
        </TabsContent>
      </Tabs>
    </>
  )
}

export default AgencySettings