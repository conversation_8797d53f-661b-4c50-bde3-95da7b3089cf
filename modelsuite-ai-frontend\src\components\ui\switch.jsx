import * as React from "react";
import * as SwitchPrimitive from "@radix-ui/react-switch";

import { cn } from "@/lib/utils";

function Switch({ className, ...props }) {
  // Debug log to verify this component is being used
  React.useEffect(() => {
    console.log("Custom Switch component loaded with props:", props);
  }, [props.checked]);

  return (
    <SwitchPrimitive.Root
      data-slot="switch"
      className={cn(
        // Base styles with higher specificity
        "peer inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",
        // Checked state: active/on - very dark background (looks "on")
        "data-[state=checked]:bg-[#0f0f0f] data-[state=checked]:border-[#27272a] data-[state=checked]:shadow-sm",
        // Unchecked state: inactive/off - lighter gray background (looks "off")
        "data-[state=unchecked]:bg-[#52525b] data-[state=unchecked]:border-[#3f3f46]",
        // Focus states
        "focus-visible:border-[#27272a] focus-visible:ring-[#27272a]/50",
        // Force override any existing styles
        "!important",
        className
      )}
      {...props}
    >
      <SwitchPrimitive.Thumb
        data-slot="switch-thumb"
        className={cn(
          "pointer-events-none block size-4 rounded-full ring-0 transition-transform shadow-sm",
          // Checked state: bright white thumb on right (clearly "on")
          "data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=checked]:bg-white data-[state=checked]:shadow-md",
          // Unchecked state: very dark thumb on left (clearly "off")
          "data-[state=unchecked]:translate-x-0 data-[state=unchecked]:bg-[#18181b] data-[state=unchecked]:shadow-sm"
        )}
      />
    </SwitchPrimitive.Root>
  );
}
export { Switch };
