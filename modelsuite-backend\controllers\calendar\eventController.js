import Event from "../../models/calendar/Event.js";

export const createEvent = async (req, res) => {
  try {
    const {
      title,
      description,
      start,
      end,
      allDay,
      modelId,
      timezone,
      recurrence,
      recurrenceEndDate,
    } = req.body;

    const event = await Event.create({
      title,
      description,
      start,
      end,
      allDay: allDay || false,
      timezone: timezone || "UTC",
      recurrence: recurrence || "none",
      recurrenceEndDate: recurrenceEndDate || null,
      modelId,
      createdBy: req.user.role === "model" ? "ModelUser" : "Agency", // 'ModelUser' or 'Agency'
      createdById: req.user._id,
    });

    res.status(201).json({ success: true, event });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Failed to create event",
      error: error.message,
    });
  }
};

export const getEvents = async (req, res) => {
  try {
    const { modelId } = req.params;

    // Optional: filter by date range
    const { startDate, endDate } = req.query;

    const filter = { modelId };

    if (startDate && endDate) {
      filter.start = { $gte: new Date(startDate) };
      filter.end = { $lte: new Date(endDate) };
    }

    const events = await Event.find(filter).sort({ start: 1 });

    res.status(200).json({ success: true, events });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Failed to fetch events",
      error: error.message,
    });
  }
};

export const updateEvent = async (req, res) => {
  try {
    const { id } = req.params;

    const event = await Event.findById(id);
    if (!event)
      return res
        .status(404)
        .json({ success: false, message: "Event not found" });

    const isOwner = event.createdById.toString() === req.user._id.toString();
    const isAgency = req.user.role === "agency";

    if (!isOwner && !isAgency) {
      return res
        .status(403)
        .json({ success: false, message: "Not authorized to edit this event" });
    }

    const updatedEvent = await Event.findByIdAndUpdate(id, req.body, {
      new: true,
    });

    res.status(200).json({ success: true, event: updatedEvent });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Failed to update event",
      error: error.message,
    });
  }
};

export const deleteEvent = async (req, res) => {
  try {
    const { id } = req.params;

    const event = await Event.findById(id);
    if (!event)
      return res
        .status(404)
        .json({ success: false, message: "Event not found" });

    const isOwner = event.createdById.toString() === req.user._id.toString();
    const isAgency = req.user.role === "agency";

    if (!isOwner && !isAgency) {
      return res.status(403).json({
        success: false,
        message: "Not authorized to delete this event",
      });
    }

    await Event.findByIdAndDelete(id);

    res
      .status(200)
      .json({ success: true, message: "Event deleted successfully" });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Failed to delete event",
      error: error.message,
    });
  }
};
