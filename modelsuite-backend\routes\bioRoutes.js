import express from "express";
import BioController from "../controllers/bio/bioController.js";
import { verifyToken, verifyRole } from "../middlewares/authMiddleware.js";

const router = express.Router();

// Apply authentication middleware to all routes
router.use(verifyToken);

// Bio generation endpoints (accessible to agencies and models)
router.post(
  "/generate",
  verifyRole(["agency", "model"]),
  BioController.generateBio,
);
router.post("/save", verifyRole(["agency", "model"]), BioController.saveBio);
router.post(
  "/regenerate",
  verifyRole(["agency", "model"]),
  BioController.regenerateBio,
);

// Bio retrieval endpoints (accessible to agencies and models)
router.get(
  "/summary",
  verifyRole(["agency", "model"]),
  BioController.getBioSummary,
);
router.get("/analytics", verifyRole(["agency"]), BioController.getBioAnalytics); // Agency only
router.get(
  "/analytics/:modelId",
  verifyRole(["agency", "model"]),
  BioController.getModelBioAnalytics,
);
router.get(
  "/logs/:modelId",
  verifyRole(["agency", "model"]),
  BioController.getGenerationLogs,
);
router.get(
  "/:modelId",
  verifyRole(["agency", "model"]),
  BioController.getModelBios,
);

// Bio management endpoints (accessible to agencies and models)
router.put(
  "/:bioId/rating",
  verifyRole(["agency", "model"]),
  BioController.updateBioRating,
);
router.post(
  "/:bioId/usage",
  verifyRole(["agency", "model"]),
  BioController.trackBioUsage,
);
router.put(
  "/:bioId/archive",
  verifyRole(["agency", "model"]),
  BioController.archiveBio,
);

export default router;
