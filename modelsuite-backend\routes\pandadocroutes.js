import express from "express";
import {
  getTemplates,
  createDocument,
  getDocumentById,
  handleWebhook,
  getContractsByAgency,
  getContractsByModelAndAgency,
} from "../controllers/pandadoccontroller.js";
import { verifyToken } from "../middlewares/authMiddleware.js";
import checkPermission from "../middlewares/permissionCheck.js";

const router = express.Router();

router.get(
  "/templates",
  verifyToken,
  checkPermission("contracts.view"),
  getTemplates,
);
router.post(
  "/create-document",
  verifyToken,
  checkPermission("contracts.create"),
  createDocument,
);
router.get(
  "/documents/:id",
  verifyToken,
  checkPermission("contracts.view"),
  getDocumentById,
);
router.post(
  "/webhook",
  verifyToken,
  checkPermission("contracts.manage"),
  handleWebhook,
);
router.get(
  "/agency/:agencyId",
  verifyToken,
  checkPermission("contracts.view"),
  getContractsByAgency,
);
router.get(
  "/model/:modelId/:agencyId",
  verifyToken,
  getContractsByModelAndAgency,
);

export default router;
