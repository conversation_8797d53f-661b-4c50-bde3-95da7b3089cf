import mongoose from "mongoose";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

/**
 * Reset persona generation quotas for testing purposes
 * This script resets the persona generation quota for specified users or all users
 */
async function resetPersonaQuota() {
  try {
    console.log("🔄 Connecting to MongoDB...");

    // Get MongoDB URI from environment variable or use a default
    const mongoUri =
      process.env.MONGODB_URI ||
      "mongodb+srv://devanand:<EMAIL>/modelsuite?retryWrites=true&w=majority&appName=Cluster0";

    await mongoose.connect(mongoUri);
    console.log("✅ Connected to MongoDB");

    const db = mongoose.connection.db;
    const collection = db.collection("personaquotas");

    // Get current date in YYYY-MM-DD format
    const today = new Date().toISOString().split("T")[0];
    console.log(`🗓️ Resetting quotas for date: ${today}`);

    // If you need to reset for a specific agency, uncomment and update this line
    // const specificAgencyId = new mongoose.Types.ObjectId("YOUR_AGENCY_ID_HERE");

    // Option 1: Reset quota for a specific agency
    // const result = await collection.updateMany(
    //   { agencyId: specificAgencyId, date: today },
    //   { $set: { usageCount: 0 } }
    // );

    // Option 2: Reset quota for all agencies (for today's date)
    const result = await collection.updateMany(
      { date: today },
      { $set: { usageCount: 0 } },
    );

    console.log(
      `✅ Persona quotas reset: ${result.modifiedCount} document(s) updated`,
    );

    // Display current quota status
    const quotas = await collection.find({ date: today }).toArray();
    if (quotas.length > 0) {
      console.log("\n📊 Current Quota Status:");
      quotas.forEach((quota) => {
        console.log(
          `Agency ID: ${quota.agencyId}, Usage: ${quota.usageCount}, Date: ${quota.date}`,
        );
      });
    } else {
      console.log("\n📊 No quota records found for today");
    }

    console.log("\n🎉 Quota reset completed successfully!");
    await mongoose.disconnect();
    console.log("👋 MongoDB connection closed");
  } catch (error) {
    console.error("❌ Error resetting persona quotas:", error);
    process.exit(1);
  }
}

// Execute the reset function
resetPersonaQuota();
