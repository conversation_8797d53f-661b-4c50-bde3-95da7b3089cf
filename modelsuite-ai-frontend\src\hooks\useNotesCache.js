import { useMemo } from "react";
import { useSelector } from "react-redux";

/**
 * Custom hook for caching and filtering notes
 * Provides memoized filtered notes and statistics
 */
export const useNotesCache = (filters) => {
  const { notes, loading, error } = useSelector((state) => state.notes);

  // Memoized filtered notes based on current filters
  const filteredNotes = useMemo(() => {
    if (!notes || notes.length === 0) return [];

    return notes.filter((note) => {
      // Priority filter
      if (filters.priority && note.priority !== filters.priority) {
        return false;
      }

      // Category filter
      if (filters.category && note.category !== filters.category) {
        return false;
      }

      // Tags filter
      if (filters.tags && filters.tags.length > 0) {
        const hasMatchingTag = filters.tags.some(
          (tag) => note.tags && note.tags.includes(tag)
        );
        if (!hasMatchingTag) return false;
      }

      // Visibility filter
      if (filters.visibility && note.visibility !== filters.visibility) {
        return false;
      }

      // Status filter
      if (filters.status && note.status !== filters.status) {
        return false;
      }

      // Pinned filter
      if (filters.isPinned !== null && note.isPinned !== filters.isPinned) {
        return false;
      }

      // Date range filter
      if (filters.dateRange) {
        const noteDate = new Date(note.createdAt);

        if (filters.dateRange.from) {
          const fromDate = new Date(filters.dateRange.from);
          if (noteDate < fromDate) return false;
        }

        if (filters.dateRange.to) {
          const toDate = new Date(filters.dateRange.to);
          // Add 24 hours to include the entire end date
          toDate.setHours(23, 59, 59, 999);
          if (noteDate > toDate) return false;
        }
      }

      // Search query filter
      if (filters.searchQuery && filters.searchQuery.trim()) {
        const query = filters.searchQuery.toLowerCase().trim();
        const searchFields = [
          note.title || "",
          note.content || "",
          note.tags?.join(" ") || "",
          note.category || "",
        ];

        const hasMatch = searchFields.some((field) =>
          field.toLowerCase().includes(query)
        );

        if (!hasMatch) return false;
      }

      return true;
    });
  }, [notes, filters]);

  // Memoized statistics
  const statistics = useMemo(() => {
    if (!notes || notes.length === 0) {
      return {
        total: 0,
        filtered: 0,
        byPriority: {},
        byCategory: {},
        pinned: 0,
        recent: 0,
      };
    }

    // Calculate recent notes (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const stats = {
      total: notes.length,
      filtered: filteredNotes.length,
      byPriority: {},
      byCategory: {},
      pinned: 0,
      recent: 0,
    };

    // Calculate statistics from all notes
    notes.forEach((note) => {
      // Priority statistics
      if (note.priority) {
        stats.byPriority[note.priority] =
          (stats.byPriority[note.priority] || 0) + 1;
      }

      // Category statistics
      if (note.category) {
        stats.byCategory[note.category] =
          (stats.byCategory[note.category] || 0) + 1;
      }

      // Pinned count
      if (note.isPinned) {
        stats.pinned++;
      }

      // Recent count
      const noteDate = new Date(note.createdAt);
      if (noteDate >= sevenDaysAgo) {
        stats.recent++;
      }
    });

    return stats;
  }, [notes, filteredNotes]);

  // Memoized sorted notes
  const sortedNotes = useMemo(() => {
    if (!filteredNotes || filteredNotes.length === 0) return [];

    return [...filteredNotes].sort((a, b) => {
      // Primary sort: Pinned notes first
      if (a.isPinned && !b.isPinned) return -1;
      if (!a.isPinned && b.isPinned) return 1;

      // Secondary sort: Priority (critical > high > medium > low)
      const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
      const aPriority = priorityOrder[a.priority] || 0;
      const bPriority = priorityOrder[b.priority] || 0;

      if (aPriority !== bPriority) {
        return bPriority - aPriority;
      }

      // Tertiary sort: Most recent first
      return (
        new Date(b.updatedAt || b.createdAt) -
        new Date(a.updatedAt || a.createdAt)
      );
    });
  }, [filteredNotes]);

  // Cache invalidation helpers
  const invalidateCache = () => {
    // This would typically trigger a refetch
    // Implementation depends on your specific Redux setup
    console.log("Cache invalidated - consider refetching notes");
  };

  const isStale = useMemo(() => {
    // Consider data stale after 5 minutes
    if (!notes || notes.length === 0) return true;

    const lastUpdate = localStorage.getItem("notes_last_fetch");
    if (!lastUpdate) return true;

    const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
    return parseInt(lastUpdate) < fiveMinutesAgo;
  }, [notes]);

  return {
    // Data
    notes: sortedNotes,
    allNotes: notes,
    filteredCount: filteredNotes.length,
    totalCount: notes?.length || 0,

    // Statistics
    statistics,

    // State
    loading,
    error,
    isStale,

    // Cache management
    invalidateCache,

    // Utility functions
    getNoteById: (id) => notes?.find((note) => note._id === id),
    getNotesByModel: (modelId) =>
      notes?.filter((note) => note.modelId === modelId),
    getNotesByCategory: (category) =>
      notes?.filter((note) => note.category === category),
    getPinnedNotes: () => notes?.filter((note) => note.isPinned),
    getRecentNotes: (days = 7) => {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);
      return notes?.filter((note) => new Date(note.createdAt) >= cutoffDate);
    },
  };
};

export default useNotesCache;
