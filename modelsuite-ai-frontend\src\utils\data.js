export const globalSidebarNavigation = [
  { name: "Dashboard", href: "/agency/dashboard", icon: "MdDashboard" },
  {
    name: "Models",
    icon: "FaUsers",
    permission: "models.view",
    href: "/agency/models",
  },
  { name: "CompCards", icon: "FaLaptop", href: "/agency/createCompCard" },
  {
    name: "Team",
    href: "/agency/team",
    icon: "FaUsers",
    permission: "employee.view",
  },
  {
    name: "Skill Matrix",
    href: "/agency/skill-matrix",
    icon: "FaBullseye",
  },
  {
    name: "Questionnaires",
    icon: "FaClipboard",
    children: [
      {
        name: "Templates",
        href: "/agency/questionnaires",
      },
      {
        name: "Assignments",
        href: "/agency/questionnaires/assignments",
      },
    ],
  },
  {
    name: "Calendar",
    href: "/calendar/success",
    icon: "FaCalendarAlt",
    permission: "calendar.view",
  },
  {
    name: "Contracts",
    icon: "FaFileSignature",
    permission: "contracts.view",
    children: [
      {
        name: "Active Contracts",
        href: "/contracts/active",
        permission: "contracts.view",
      },
      {
        name: "Pending Approvals",
        href: "/contracts/pending",
        permission: "contracts.manage",
      },
    ],
  },
  {
    name: "Invoices",
    icon: "FaFileInvoiceDollar",
    permission: "earnings.view",
    children: [
      {
        name: "Sent Invoices",
        href: "/invoices/sent",
        permission: "earnings.view",
      },
      {
        name: "Received Invoices",
        href: "/invoices/received",
        permission: "earnings.view",
      },
    ],
  },
  {
    name: "Messages",
    href: "/messages",
    icon: "FaRegCommentDots",
    permission: "messages.view",
  },
  {
    name: "Notes",
    href: "/agency/notes",
    icon: "FaRegCommentDots",
    permission: "notes.view",
  },
  { name: "Voice Content", href: "/agency/voice", icon: "FaMicrophone" },
  {
    name: "Lyra AI",
    icon: "FaMagic",
    children: [
      { name: "Smart Caption Generator", href: "/agency/caption-generator" },
      { name: "Persona Builder", href: "/agency/lyra/persona-builder" },
    ],
  },
  {
    name: "Support Contact",
    href: "/support-contact-manager",
    icon: "FaHeadset",
  },
  {
    name: "Insights",
    icon: "FaChartBar",
    permission: "performance.view",
    children: [
      {
        name: "Performance",
        href: "/insights/performance",
        permission: "performance.view",
      },
      {
        name: "Engagement",
        href: "/insights/engagement",
        permission: "performance.view",
      },
    ],
  },
];

export const modelPanelMenu = [
  { icon: "FaRegCommentDots", label: "Messenger" },
  { icon: "FaCalendarAlt", label: "Calendar" },
  { icon: "FaCheckSquare", label: "Tasks" },
  { icon: "FaRegCommentDots", label: "Notes" },
  { icon: "FaChartLine", label: "Traffic & Analytics" },
  { icon: "FaFileAlt", label: "Postings & Content" },
  { icon: "FaBullhorn", label: "Viral Trends & Insights" },
  { icon: "FaUsers", label: "Team Members" },
  { icon: "FaShieldAlt", label: "Leak Protection" },
  { icon: "FaDollarSign", label: "Billing & Finance" },
  { icon: "FaGift", label: "Paid Platforms" },
  { icon: "FaGift", label: "Rewards & Gamification" },
  { icon: "FaBook", label: "Content Library" },
];
