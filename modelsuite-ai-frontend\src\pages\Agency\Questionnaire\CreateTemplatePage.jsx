import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import {
  Plus,
  Trash2,
  ArrowLeft,
  ChevronDown,
  ChevronRight,
  Check,
  Copy,
} from "lucide-react";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import {
  createTemplate,
  fetchTemplates,
} from "@/redux/features/questionnaire/questionnaireSlice";
import ButtonLoader from "@/reusable/Loader/ButtonLoader";

export default function CreateTemplatePage() {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { templateLoading, templates } = useSelector(
    (state) => state.questionnaireReducer
  );

  useEffect(() => {
    dispatch(fetchTemplates());
  }, [dispatch]);

  const [formData, setFormData] = useState({
    title: "",
    description: "",
    isActive: true,
    sections: [
      {
        id: `section-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        title: "General Information",
        description: "",
        questions: [
          {
            id: `question-${Date.now()}-${Math.random()
              .toString(36)
              .substr(2, 9)}`,
            text: "",
            type: "text",
            required: false,
          },
        ],
      },
    ],
  });

  // Template picker state
  const [showTemplatePicker, setShowTemplatePicker] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [expandedSections, setExpandedSections] = useState({});
  const [selectedSections, setSelectedSections] = useState({});
  const [selectedQuestions, setSelectedQuestions] = useState({});

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const addSection = () => {
    setFormData((prev) => ({
      ...prev,
      sections: [
        ...prev.sections,
        {
          id: `section-${Date.now()}-${Math.random()
            .toString(36)
            .substr(2, 9)}`,
          title: "",
          description: "",
          questions: [
            {
              id: `question-${Date.now()}-${Math.random()
                .toString(36)
                .substr(2, 9)}`,
              text: "",
              type: "text",
              required: false,
            },
          ],
        },
      ],
    }));
  };

  const removeSection = (sectionIndex) => {
    setFormData((prev) => ({
      ...prev,
      sections: prev.sections.filter((_, index) => index !== sectionIndex),
    }));
  };

  const updateSection = (sectionIndex, field, value) => {
    setFormData((prev) => ({
      ...prev,
      sections: prev.sections.map((section, index) =>
        index === sectionIndex ? { ...section, [field]: value } : section
      ),
    }));
  };

  const addQuestion = (sectionIndex) => {
    setFormData((prev) => ({
      ...prev,
      sections: prev.sections.map((section, index) =>
        index === sectionIndex
          ? {
              ...section,
              questions: [
                ...section.questions,
                {
                  id: `question-${Date.now()}-${Math.random()
                    .toString(36)
                    .substr(2, 9)}`,
                  text: "",
                  type: "text",
                  required: false,
                },
              ],
            }
          : section
      ),
    }));
  };

  const removeQuestion = (sectionIndex, questionIndex) => {
    setFormData((prev) => ({
      ...prev,
      sections: prev.sections.map((section, index) =>
        index === sectionIndex
          ? {
              ...section,
              questions: section.questions.filter(
                (_, qIndex) => qIndex !== questionIndex
              ),
            }
          : section
      ),
    }));
  };

  const updateQuestion = (sectionIndex, questionIndex, field, value) => {
    setFormData((prev) => ({
      ...prev,
      sections: prev.sections.map((section, index) =>
        index === sectionIndex
          ? {
              ...section,
              questions: section.questions.map((question, qIndex) =>
                qIndex === questionIndex
                  ? { ...question, [field]: value }
                  : question
              ),
            }
          : section
      ),
    }));
  };

  const handleSubmit = async () => {
    if (!formData.title) {
      toast.error("Please enter a template title");
      return;
    }

    try {
      await dispatch(createTemplate(formData)).unwrap();
      toast.success("Template created successfully!");
      navigate("/agency/questionnaires");
    } catch (error) {
      console.error("Failed to create template:", error);
      toast.error(
        error?.message || "Failed to create template. Please try again."
      );
    }
  };

  // Template picker functions
  const handleTemplateSelect = (template) => {
    setSelectedTemplate(template);
    setShowTemplatePicker(true);
    // Initialize all sections as unselected
    const sectionState = {};
    const questionState = {};
    template.sections?.forEach((section, sIndex) => {
      sectionState[sIndex] = false;
      section.questions?.forEach((_, qIndex) => {
        questionState[`${sIndex}-${qIndex}`] = false;
      });
    });
    setSelectedSections(sectionState);
    setSelectedQuestions(questionState);
  };

  const toggleSectionSelection = (sectionIndex) => {
    const newSelectedSections = {
      ...selectedSections,
      [sectionIndex]: !selectedSections[sectionIndex],
    };
    setSelectedSections(newSelectedSections);

    // Also toggle all questions in this section
    const newSelectedQuestions = { ...selectedQuestions };
    selectedTemplate.sections[sectionIndex].questions?.forEach((_, qIndex) => {
      newSelectedQuestions[`${sectionIndex}-${qIndex}`] =
        newSelectedSections[sectionIndex];
    });
    setSelectedQuestions(newSelectedQuestions);
  };

  const toggleQuestionSelection = (sectionIndex, questionIndex) => {
    const key = `${sectionIndex}-${questionIndex}`;
    const newSelectedQuestions = {
      ...selectedQuestions,
      [key]: !selectedQuestions[key],
    };
    setSelectedQuestions(newSelectedQuestions);

    // Check if all questions in section are selected to update section checkbox
    const sectionQuestions =
      selectedTemplate.sections[sectionIndex].questions || [];
    const allSelected = sectionQuestions.every(
      (_, qIndex) => newSelectedQuestions[`${sectionIndex}-${qIndex}`]
    );
    const noneSelected = sectionQuestions.every(
      (_, qIndex) => !newSelectedQuestions[`${sectionIndex}-${qIndex}`]
    );

    setSelectedSections((prev) => ({
      ...prev,
      [sectionIndex]: allSelected
        ? true
        : noneSelected
        ? false
        : "indeterminate",
    }));
  };

  const applySelectedTemplate = () => {
    if (!selectedTemplate) return;

    const newSections = [];
    selectedTemplate.sections?.forEach((section, sIndex) => {
      if (selectedSections[sIndex]) {
        // Include entire section with unique IDs
        newSections.push({
          ...section,
          // Add unique identifier to avoid key conflicts
          id: `${selectedTemplate._id}-section-${sIndex}-${Date.now()}`,
          questions:
            section.questions?.map((question, qIndex) => ({
              ...question,
              id: `${
                selectedTemplate._id
              }-question-${sIndex}-${qIndex}-${Date.now()}`,
            })) || [],
        });
      } else {
        // Check for individual questions
        const selectedQuestionsInSection = [];
        section.questions?.forEach((question, qIndex) => {
          if (selectedQuestions[`${sIndex}-${qIndex}`]) {
            selectedQuestionsInSection.push({
              ...question,
              id: `${
                selectedTemplate._id
              }-question-${sIndex}-${qIndex}-${Date.now()}`,
            });
          }
        });

        if (selectedQuestionsInSection.length > 0) {
          newSections.push({
            ...section,
            id: `${selectedTemplate._id}-section-${sIndex}-${Date.now()}`,
            questions: selectedQuestionsInSection,
          });
        }
      }
    });

    if (newSections.length === 0) {
      toast.error("Please select at least one section or question");
      return;
    }

    setFormData((prev) => ({
      ...prev,
      title: prev.title || `${selectedTemplate.title} (Copy)`,
      description: prev.description || selectedTemplate.description || "",
      sections: [...prev.sections, ...newSections],
    }));

    // Show success message
    const selectedSectionsCount =
      Object.values(selectedSections).filter(Boolean).length;
    const selectedQuestionsCount =
      Object.values(selectedQuestions).filter(Boolean).length;

    if (selectedSectionsCount > 0 && selectedQuestionsCount === 0) {
      toast.success(
        `Added ${selectedSectionsCount} section(s) to your template`
      );
    } else if (selectedSectionsCount === 0 && selectedQuestionsCount > 0) {
      toast.success(
        `Added ${selectedQuestionsCount} question(s) to your template`
      );
    } else {
      toast.success(
        `Added ${selectedSectionsCount} section(s) and ${selectedQuestionsCount} question(s) to your template`
      );
    }

    // Close picker
    setShowTemplatePicker(false);
    setSelectedTemplate(null);
    setSelectedSections({});
    setSelectedQuestions({});
    setExpandedSections({});
  };

  return (
    <div className="p-4">
      <div className="max-w-7xl mx-auto space-y-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              onClick={() => navigate("/agency/questionnaires")}
              className="text-gray-400 hover:text-white hover:bg-[#27272a]"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Templates
            </Button>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold tracking-tight text-white">
              Create Questionnaire Template
            </h1>
            <p className="text-gray-400">
              Build a custom questionnaire for your models
            </p>
          </div>
        </div>

        {/* Basic Information */}
        <Card className="bg-[#18181b] border-[#27272a]">
          <CardHeader>
            <CardTitle className="text-white">Basic Information</CardTitle>
            <CardDescription className="text-gray-400">
              Provide basic details about your questionnaire template
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title" className="text-gray-300">
                Template Title
              </Label>
              <Input
                id="title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                placeholder="Enter template title"
                className="bg-[#27272a] border-[#3f3f46] text-white placeholder:text-gray-500"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description" className="text-gray-300">
                Description
              </Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Enter template description (optional)"
                className="bg-[#27272a] border-[#3f3f46] text-white placeholder:text-gray-500"
                rows={3}
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="isActive"
                checked={formData.isActive}
                onCheckedChange={(checked) =>
                  setFormData((prev) => ({ ...prev, isActive: checked }))
                }
              />
              <Label htmlFor="isActive" className="text-gray-300">
                Active templates can be assigned to models
              </Label>
            </div>
          </CardContent>
        </Card>

        {/* Default Templates Section */}
        {templates?.filter((t) => !t.agencyId)?.length > 0 && (
          <Card className="bg-[#18181b] border-[#27272a]">
            <CardHeader>
              <CardTitle className="text-white">Start from Template</CardTitle>
              <CardDescription className="text-gray-400">
                Use a default template as a starting point for your
                questionnaire
              </CardDescription>
            </CardHeader>
            <CardContent>
              {!showTemplatePicker ? (
                <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-3">
                  {templates
                    .filter((t) => !t.agencyId)
                    .map((template) => (
                      <div
                        key={template._id}
                        className="p-4 border border-[#3f3f46] rounded-lg bg-[#27272a] hover:bg-[#3f3f46] transition-colors cursor-pointer"
                        onClick={() => handleTemplateSelect(template)}
                      >
                        <h4 className="font-medium text-white mb-2">
                          {template.title}
                        </h4>
                        <p className="text-sm text-gray-400 mb-3">
                          {template.description || "No description"}
                        </p>
                        <div className="flex items-center justify-between text-xs text-gray-500">
                          <span>{template.sections?.length || 0} sections</span>
                          <span>Click to customize</span>
                        </div>
                      </div>
                    ))}
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="text-lg font-medium text-white">
                      Select Sections & Questions
                    </h4>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setShowTemplatePicker(false);
                          setSelectedTemplate(null);
                        }}
                        className="border-[#27272a] bg-[#27272a] text-white hover:border-[#1a1a1a]"
                      >
                        Cancel
                      </Button>
                      <Button
                        size="sm"
                        onClick={applySelectedTemplate}
                        className="bg-[#1a1a1a] border border-[#27272a] text-white hover:bg-[#0f0f0f] hover:border-[#3f3f46]"
                      >
                        <Check className="mr-2 h-4 w-4" />
                        Add Selected
                      </Button>
                    </div>
                  </div>

                  <div className="text-sm text-gray-400 mb-4">
                    Choose entire sections or individual questions to add to
                    your template
                  </div>

                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {selectedTemplate?.sections?.map(
                      (section, sectionIndex) => (
                        <div
                          key={
                            section._id || `template-section-${sectionIndex}`
                          }
                          className="border border-[#3f3f46] rounded-lg bg-[#27272a]"
                        >
                          {/* Section Header */}
                          <div className="p-3 border-b border-[#3f3f46]">
                            <div className="flex items-center gap-3">
                              <Checkbox
                                checked={
                                  selectedSections[sectionIndex] === true
                                }
                                onCheckedChange={() =>
                                  toggleSectionSelection(sectionIndex)
                                }
                              />
                              <button
                                onClick={() => {
                                  setExpandedSections((prev) => ({
                                    ...prev,
                                    [sectionIndex]: !prev[sectionIndex],
                                  }));
                                }}
                                className="flex items-center gap-2 flex-1 text-left"
                              >
                                {expandedSections[sectionIndex] ? (
                                  <ChevronDown className="h-4 w-4 text-gray-400" />
                                ) : (
                                  <ChevronRight className="h-4 w-4 text-gray-400" />
                                )}
                                <div className="flex-1">
                                  <h5 className="font-medium text-white">
                                    {section.title}
                                  </h5>
                                  <p className="text-sm text-gray-400">
                                    {section.questions?.length || 0} questions
                                  </p>
                                </div>
                              </button>
                            </div>
                          </div>

                          {/* Questions */}
                          {expandedSections[sectionIndex] && (
                            <div className="p-3 space-y-2">
                              {section.questions?.map(
                                (question, questionIndex) => (
                                  <div
                                    key={
                                      question._id ||
                                      `template-question-${sectionIndex}-${questionIndex}`
                                    }
                                    className="flex items-center gap-3 p-2 hover:bg-[#3f3f46] rounded"
                                  >
                                    <Checkbox
                                      checked={
                                        selectedQuestions[
                                          `${sectionIndex}-${questionIndex}`
                                        ] || false
                                      }
                                      onCheckedChange={() =>
                                        toggleQuestionSelection(
                                          sectionIndex,
                                          questionIndex
                                        )
                                      }
                                    />
                                    <div className="flex-1">
                                      <p className="text-sm text-white">
                                        {question.text || question.label}
                                      </p>
                                      <div className="flex items-center gap-2 text-xs text-gray-500">
                                        <span className="bg-gray-800 px-1.5 py-0.5 rounded">
                                          {question.type}
                                        </span>
                                        {question.required && (
                                          <span className="text-red-400">
                                            Required
                                          </span>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                )
                              )}
                            </div>
                          )}
                        </div>
                      )
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Questionnaire Sections */}
        <Card className="bg-[#18181b] border-[#27272a]">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-white">
                  Questionnaire Sections
                </CardTitle>
                <CardDescription className="text-gray-400">
                  Organize your questions into logical sections
                </CardDescription>
              </div>
              <Button
                onClick={addSection}
                className="bg-[#1a1a1a] border border-[#3f3f46] hover:border-[#27272a] text-white"
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Section
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {formData.sections.map((section, sectionIndex) => (
              <div
                key={section.id || `section-${sectionIndex}`}
                className="space-y-4 p-4 border border-[#27272a] rounded-lg bg-[#18181b]"
              >
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-white">
                    Section {sectionIndex + 1}
                  </h3>
                  {formData.sections.length > 1 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeSection(sectionIndex)}
                      className="text-red-400 hover:text-red-300 hover:bg-red-900/20"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label className="text-gray-300">Section Title</Label>
                    <Input
                      value={section.title}
                      onChange={(e) =>
                        updateSection(sectionIndex, "title", e.target.value)
                      }
                      placeholder="General Information"
                      className="bg-[#27272a] border-[#3f3f46] text-white placeholder:text-gray-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label className="text-gray-300">
                      Section Description (Optional)
                    </Label>
                    <Input
                      value={section.description}
                      onChange={(e) =>
                        updateSection(
                          sectionIndex,
                          "description",
                          e.target.value
                        )
                      }
                      placeholder="Enter section description"
                      className="bg-[#27272a] border-[#3f3f46] text-white placeholder:text-gray-500"
                    />
                  </div>
                </div>

                {/* Questions */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-white">Questions</h4>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => addQuestion(sectionIndex)}
                      className="bg-[#1a1a1a] border border-[#3f3f46] hover:border-[#27272a] text-white"
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      Add Question
                    </Button>
                  </div>

                  {section.questions.map((question, questionIndex) => (
                    <div
                      key={
                        question.id ||
                        `question-${sectionIndex}-${questionIndex}`
                      }
                      className="space-y-3 p-3 border border-[#3f3f46] rounded bg-[#0f0f0f]"
                    >
                      <div className="flex items-center justify-between">
                        <Label className="text-gray-300">
                          Question {questionIndex + 1}
                        </Label>
                        {section.questions.length > 1 && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() =>
                              removeQuestion(sectionIndex, questionIndex)
                            }
                            className="text-red-400 hover:text-red-300 hover:bg-red-900/20"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>

                      <div className="grid gap-3 md:grid-cols-2">
                        <div className="space-y-2">
                          <Label className="text-gray-300">Question Text</Label>
                          <Input
                            value={question.text}
                            onChange={(e) =>
                              updateQuestion(
                                sectionIndex,
                                questionIndex,
                                "text",
                                e.target.value
                              )
                            }
                            placeholder="Enter question text"
                            className="bg-[#27272a] border-[#3f3f46] text-white placeholder:text-gray-500"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label className="text-gray-300">Question Type</Label>
                          <Select
                            value={question.type}
                            onValueChange={(value) =>
                              updateQuestion(
                                sectionIndex,
                                questionIndex,
                                "type",
                                value
                              )
                            }
                          >
                            <SelectTrigger className="bg-[#27272a] border-[#3f3f46] text-white">
                              <SelectValue placeholder="Text Input" />
                            </SelectTrigger>
                            <SelectContent className="bg-[#18181b] border-[#27272a]">
                              <SelectItem
                                value="text"
                                className="text-white hover:bg-[#27272a]"
                              >
                                Text Input
                              </SelectItem>
                              <SelectItem
                                value="textarea"
                                className="text-white hover:bg-[#27272a]"
                              >
                                Long Text
                              </SelectItem>
                              <SelectItem
                                value="select"
                                className="text-white hover:bg-[#27272a]"
                              >
                                Multiple Choice
                              </SelectItem>
                              <SelectItem
                                value="radio"
                                className="text-white hover:bg-[#27272a]"
                              >
                                Single Choice
                              </SelectItem>
                              <SelectItem
                                value="checkbox"
                                className="text-white hover:bg-[#27272a]"
                              >
                                Checkboxes
                              </SelectItem>
                              <SelectItem
                                value="number"
                                className="text-white hover:bg-[#27272a]"
                              >
                                Number
                              </SelectItem>
                              <SelectItem
                                value="email"
                                className="text-white hover:bg-[#27272a]"
                              >
                                Email
                              </SelectItem>
                              <SelectItem
                                value="phone"
                                className="text-white hover:bg-[#27272a]"
                              >
                                Phone
                              </SelectItem>
                              <SelectItem
                                value="date"
                                className="text-white hover:bg-[#27272a]"
                              >
                                Date
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={question.required}
                            onCheckedChange={(checked) =>
                              updateQuestion(
                                sectionIndex,
                                questionIndex,
                                "required",
                                checked
                              )
                            }
                          />
                          <Label className="text-gray-300">
                            Required Question
                          </Label>
                          <span className="text-sm text-gray-500">
                            Users must answer this question
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex items-center justify-end gap-4">
          <Button
            variant="outline"
            onClick={() => navigate("/agency/questionnaires")}
            className="border-[#27272a] bg-[#27272a] text-white hover:border-[#1a1a1a]"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={templateLoading || !formData.title}
            className="bg-[#1a1a1a] border border-[#27272a] text-white hover:bg-[#0f0f0f] hover:border-[#3f3f46] disabled:opacity-50"
          >
            {templateLoading ? <ButtonLoader /> : "Create Template"}
          </Button>
        </div>
      </div>
    </div>
  );
}
