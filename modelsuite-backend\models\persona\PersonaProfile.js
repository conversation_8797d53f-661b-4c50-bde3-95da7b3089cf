import mongoose from "mongoose";

/**
 * PersonaProfile Schema - Stores AI-generated persona profiles for models
 * Used by agencies to create targeted content and marketing strategies
 */
const personaProfileSchema = new mongoose.Schema(
  {
    // Core persona data
    personaText: {
      type: String,
      required: true,
      maxlength: 2000, // Updated to allow detailed persona descriptions
      trim: true,
    },

    // Descriptive tags for categorization
    tags: {
      type: [String],
      required: true,
      validate: {
        validator: function (tags) {
          return tags && tags.length >= 3;
        },
        message: "At least 3 descriptive tags are required",
      },
    },

    // Structured persona attributes
    demographics: {
      age: {
        type: String,
        trim: true,
      },
      location: {
        type: String,
        trim: true,
      },
    },

    interests: {
      type: [String],
      default: [],
    },

    goals: {
      type: [String],
      default: [],
    },

    painPoints: {
      type: [String],
      default: [],
    },

    // Enhanced persona attributes - Phase 1 additions
    psychographics: {
      values: {
        type: [String],
        default: [],
        enum: [
          "Adventurous",
          "Security-focused",
          "Ambitious",
          "Creative",
          "Traditional",
          "Independent",
          "Family-oriented",
          "Status-conscious",
          "Authentic",
          "Innovative",
          "Collaborative",
          "Competitive",
          "Balanced",
          "Risk-taker",
          "Conservative",
        ],
      },
      customValues: {
        type: [String],
        default: [], // For agency-added custom values
      },
      lifestyle: {
        type: String,
        trim: true,
      },
      personality: {
        type: [String],
        default: [],
      },
      motivations: {
        type: [String],
        default: [],
      },
    },

    behavioral: {
      postingStyle: {
        type: String,
        enum: [
          "Consistent",
          "Sporadic",
          "Trend-driven",
          "Seasonal",
          "Event-based",
        ],
        trim: true,
      },
      postingFrequency: {
        type: String,
        enum: ["Daily", "Weekly", "Bi-weekly", "Monthly", "As-needed"],
        trim: true,
      },
      contentPreferences: {
        type: [String],
        default: [],
      },
      engagementStyle: {
        type: String,
        enum: [
          "High-interaction",
          "Moderate",
          "Low-key",
          "Story-focused",
          "Visual-first",
        ],
        trim: true,
      },
      peakActivityTimes: {
        type: [String],
        default: [],
      },
    },

    communicationStyle: {
      tone: {
        type: String,
        enum: [
          "Professional",
          "Casual",
          "Playful",
          "Inspirational",
          "Educational",
          "Authentic",
        ],
        trim: true,
      },
      vocabulary: {
        type: String,
        enum: ["Simple", "Moderate", "Advanced", "Technical", "Trendy"],
        trim: true,
      },
      emojiUsage: {
        type: String,
        enum: [
          "Heavy emoji user",
          "Moderate emoji usage",
          "Minimal emoji usage",
          "No emojis",
        ],
        default: "Moderate emoji usage",
      },
      hashtagStrategy: {
        type: String,
        enum: [
          "Trending-focused",
          "Niche-specific",
          "Minimal",
          "Brand-consistent",
          "Mixed",
        ],
        trim: true,
      },
    },

    // Scoring and feedback metrics - Phase 1 additions
    scoring: {
      processingTime: {
        type: Number, // in milliseconds
        default: 0,
      },
      completeness: {
        type: Number, // percentage 0-100
        default: 0,
      },
      agencyRating: {
        type: Number, // 1-5 stars
        min: 1,
        max: 5,
      },
      lastRatedAt: {
        type: Date,
      },
    },

    // Version management - Phase 1 enhancements
    version: {
      type: Number,
      default: 1,
    },

    isArchived: {
      type: Boolean,
      default: false,
    },

    // AI generation metadata
    aiGeneratedFrom: {
      questionnaireAnswers: {
        type: mongoose.Schema.Types.Mixed,
        default: {},
      },
      profileData: {
        type: mongoose.Schema.Types.Mixed,
        default: {},
      },
    },

    aiEngineUsed: {
      type: String,
      enum: ["gpt-4", "gemini-vision", "manual"],
      required: true,
    },

    // Ownership and access control
    modelId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ModelUser",
      required: true,
    },

    agencyId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      required: true,
    },

    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      refPath: "createdByModel",
    },

    createdByModel: {
      type: String,
      required: true,
      enum: ["Agency", "Employee"],
    },

    // Status and versioning
    status: {
      type: String,
      enum: ["draft", "approved"],
      default: "approved",
    },

    // Version tracking
    versions: [
      {
        updatedAt: {
          type: Date,
          default: Date.now,
        },
        updatedBy: {
          type: mongoose.Schema.Types.ObjectId,
          required: true,
        },
        changes: {
          type: String,
          trim: true,
        },
      },
    ],

    // Usage tracking
    usageCount: {
      type: Number,
      default: 0,
    },

    lastUsedAt: {
      type: Date,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  },
);

// Indexes for performance
personaProfileSchema.index({ agencyId: 1, modelId: 1 });
personaProfileSchema.index({ agencyId: 1, createdAt: -1 });
personaProfileSchema.index({ tags: 1 });
personaProfileSchema.index({ usageCount: -1 });
personaProfileSchema.index({ version: -1, modelId: 1 }); // For version history
personaProfileSchema.index({ isArchived: 1 }); // For active personas
personaProfileSchema.index({ "scoring.agencyRating": -1 }); // For rating queries

// Virtual for persona age
personaProfileSchema.virtual("ageInDays").get(function () {
  return Math.floor((Date.now() - this.createdAt) / (1000 * 60 * 60 * 24));
});

// Pre-save validation
personaProfileSchema.pre("save", function (next) {
  // Validate persona text doesn't contain emojis or links
  const emojiRegex =
    /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu;
  const urlRegex = /(https?:\/\/[^\s]+)/g;

  if (emojiRegex.test(this.personaText) || urlRegex.test(this.personaText)) {
    return next(new Error("Persona text cannot contain emojis or links"));
  }

  next();
});

// Static method to find personas by agency
personaProfileSchema.statics.findByAgency = function (agencyId, options = {}) {
  const query = this.find({ agencyId });

  if (options.modelId) {
    query.where({ modelId: options.modelId });
  }

  if (options.tags && options.tags.length > 0) {
    query.where({ tags: { $in: options.tags } });
  }

  return query.populate("modelId", "fullName username").sort({ createdAt: -1 });
};

// Instance method to increment usage
personaProfileSchema.methods.incrementUsage = function () {
  this.usageCount += 1;
  this.lastUsedAt = new Date();
  return this.save();
};

// Instance method to add version
personaProfileSchema.methods.addVersion = function (updatedBy, changes) {
  this.versions.push({
    updatedBy,
    changes,
    updatedAt: new Date(),
  });
  return this;
};

// Instance method to calculate completeness score
personaProfileSchema.methods.calculateCompleteness = function () {
  let score = 0;
  const totalFields = 14; // Total scoreable fields

  // Core fields (4 points each)
  if (this.personaText && this.personaText.length > 50) score += 4;
  if (this.tags && this.tags.length >= 3) score += 4;
  if (
    this.demographics &&
    (this.demographics.age || this.demographics.location)
  )
    score += 4;
  if (this.interests && this.interests.length > 0) score += 4;

  // Enhanced fields (2 points each)
  if (
    this.psychographics &&
    this.psychographics.values &&
    this.psychographics.values.length > 0
  )
    score += 2;
  if (this.psychographics && this.psychographics.lifestyle) score += 2;
  if (
    this.psychographics &&
    this.psychographics.personality &&
    this.psychographics.personality.length > 0
  )
    score += 2;
  if (
    this.psychographics &&
    this.psychographics.motivations &&
    this.psychographics.motivations.length > 0
  )
    score += 2;
  if (this.behavioral && this.behavioral.postingStyle) score += 2;
  if (
    this.behavioral &&
    this.behavioral.contentPreferences &&
    this.behavioral.contentPreferences.length > 0
  )
    score += 2;
  if (this.communicationStyle && this.communicationStyle.tone) score += 2;
  if (this.communicationStyle && this.communicationStyle.vocabulary) score += 2;
  if (this.communicationStyle && this.communicationStyle.emojiUsage) score += 2;
  if (this.communicationStyle && this.communicationStyle.hashtagStrategy)
    score += 2;

  const percentage = Math.round((score / 32) * 100); // 32 is max possible score
  this.scoring.completeness = percentage;
  return percentage;
};

// Instance method to archive old versions
personaProfileSchema.methods.archiveOldVersions = async function () {
  const PersonaProfile = this.constructor;

  // Get all versions for this model, sorted by version desc
  const allVersions = await PersonaProfile.find({
    modelId: this.modelId,
    isArchived: false,
  }).sort({ version: -1 });

  // If more than 5 versions, archive the oldest ones
  if (allVersions.length > 5) {
    const versionsToArchive = allVersions.slice(5); // Keep top 5, archive rest

    for (const versionToArchive of versionsToArchive) {
      versionToArchive.isArchived = true;
      await versionToArchive.save();
    }
  }
};

// Static method to get latest version for model
personaProfileSchema.statics.getLatestVersion = function (modelId) {
  return this.findOne({
    modelId,
    isArchived: false,
  }).sort({ version: -1 });
};

// Static method to get version history
personaProfileSchema.statics.getVersionHistory = function (modelId, limit = 5) {
  return this.find({
    modelId,
  })
    .sort({ version: -1 })
    .limit(limit)
    .populate("createdBy", "fullName username");
};

const PersonaProfile = mongoose.model("PersonaProfile", personaProfileSchema);

export default PersonaProfile;
