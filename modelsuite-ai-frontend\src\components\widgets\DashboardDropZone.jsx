import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { addWidget } from "@/redux/features/dashboard/dashboardSlice";
import {
  endDrag,
  selectIsDragging,
} from "@/redux/features/widgetSidebar/widgetSidebarSlice";

const DashboardDropZone = ({ children, className = "" }) => {
  const dispatch = useDispatch();
  const isDragging = useSelector(selectIsDragging);
  const [isDragOver, setIsDragOver] = useState(false);

  const handleDragOver = (e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "copy";
  };

  const handleDragEnter = (e) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    // Only set dragOver to false if we're leaving the drop zone completely
    if (!e.currentTarget.contains(e.relatedTarget)) {
      setIsDragOver(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragOver(false);

    try {
      // Get the widget data from the drag event
      const widgetDataStr = e.dataTransfer.getData("application/json");
      if (!widgetDataStr) return;

      const widget = JSON.parse(widgetDataStr);

      // Calculate position relative to the dashboard
      const rect = e.currentTarget.getBoundingClientRect();
      const position = {
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      };

      // Ensure the widget doesn't go outside the bounds
      const adjustedPosition = {
        x: Math.max(0, Math.min(position.x, rect.width - 300)), // Assume widget width ~300px
        y: Math.max(0, Math.min(position.y, rect.height - 200)), // Assume widget height ~200px
      };

      // Add the widget to the dashboard
      dispatch(
        addWidget({
          widgetType: widget.type,
          position: adjustedPosition,
        })
      );

      // End the drag state
      dispatch(endDrag());
    } catch (error) {
      console.error("Error handling widget drop:", error);
      dispatch(endDrag());
    }
  };

  return (
    <div
      className={`
        relative min-h-full
        ${className}
        ${isDragging ? "transition-all duration-200" : ""}
        ${
          isDragOver && isDragging
            ? "bg-blue-50/50 ring-2 ring-blue-300 ring-dashed"
            : ""
        }
      `}
      onDragOver={handleDragOver}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      {/* Drop overlay when dragging */}
      {isDragging && isDragOver && (
        <div className="absolute inset-0 flex items-center justify-center bg-blue-100/20 border-2 border-dashed border-blue-400 rounded-lg z-10">
          <div className="text-blue-600 text-lg font-medium">
            Drop widget here
          </div>
        </div>
      )}

      {/* Regular dashboard content */}
      {children}
    </div>
  );
};

export default DashboardDropZone;
