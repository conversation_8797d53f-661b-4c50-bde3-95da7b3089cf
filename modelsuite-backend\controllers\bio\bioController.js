import { ApiError } from "../../utils/ApiError.js";
import { ApiResponse } from "../../utils/ApiResponse.js";
import { asyncHandler } from "../../utils/asyncHandler.js";
import bioService from "../../services/bio/bioService.js";
import bioAnalyticsService from "../../services/bio/bioAnalyticsService.js";

/**
 * Bio Controller - Handles bio generation and management endpoints
 */
class BioController {
  /**
   * Generate new bios for a model
   * POST /api/v1/bio/generate
   */
  static generateBio = asyncHandler(async (req, res) => {
    const { modelId, personaId, options = {} } = req.body;
    const { agencyId, _id: userId, role } = req.user;

    // Validate required fields
    if (!modelId || !personaId) {
      throw new ApiError(400, "Model ID and Persona ID are required");
    }

    // Add user context to options
    const generationOptions = {
      ...options,
      userId,
      userAgent: req.get("User-Agent"),
      ipAddress: req.ip,
    };

    try {
      // Generate bios
      const result = await bioService.generateBios(
        modelId,
        personaId,
        generationOptions,
      );

      res
        .status(200)
        .json(new ApiResponse(200, result.data, "Bios generated successfully"));
    } catch (error) {
      console.error("Bio generation failed:", error);
      throw error;
    }
  });

  /**
   * Save generated bios
   * POST /api/v1/bio/save
   */
  static saveBio = asyncHandler(async (req, res) => {
    const { modelId, personaId, shortBio, mediumBio, longBio, metadata } =
      req.body;
    const { agencyId, _id: userId, role } = req.user;

    // Debug logging
    console.log("User role:", role);
    console.log("User object:", { agencyId, userId, role });

    // Validate required fields
    if (!modelId || !personaId || !shortBio || !mediumBio || !longBio) {
      throw new ApiError(
        400,
        "Model ID, Persona ID, and all bio fields are required",
      );
    }

    // Map role to createdByType
    let createdByType;
    switch (role) {
      case "agency":
        createdByType = "Agency";
        break;
      case "model":
        createdByType = "ModelUser";
        break;
      case "employee":
        createdByType = "Employee";
        break;
      default:
        createdByType = "Agency"; // Default fallback
        console.warn("Unknown role:", role, "defaulting to Agency");
    }

    const bioData = {
      modelId,
      agencyId,
      personaId,
      shortBio,
      mediumBio,
      longBio,
      metadata,
      createdBy: userId,
      createdByType,
    };

    try {
      const savedBio = await bioService.saveBios(bioData, userId);

      res
        .status(201)
        .json(new ApiResponse(201, savedBio, "Bio saved successfully"));
    } catch (error) {
      console.error("Bio save failed:", error);
      throw error;
    }
  });

  /**
   * Regenerate bios with feedback
   * POST /api/v1/bio/regenerate
   */
  static regenerateBio = asyncHandler(async (req, res) => {
    const { modelId, personaId, feedback = {}, options = {} } = req.body;
    const { _id: userId } = req.user;

    // Validate required fields
    if (!modelId || !personaId) {
      throw new ApiError(400, "Model ID and Persona ID are required");
    }

    // Add user context to options
    const regenerationOptions = {
      ...options,
      userId,
      userAgent: req.get("User-Agent"),
      ipAddress: req.ip,
    };

    try {
      const result = await bioService.regenerateBios(
        modelId,
        personaId,
        feedback,
        regenerationOptions,
      );

      res
        .status(200)
        .json(
          new ApiResponse(200, result.data, "Bios regenerated successfully"),
        );
    } catch (error) {
      console.error("Bio regeneration failed:", error);
      throw error;
    }
  });

  /**
   * Get latest bios for a model
   * GET /api/v1/bio/:modelId
   */
  static getModelBios = asyncHandler(async (req, res) => {
    const { modelId } = req.params;
    const { includeArchived = false } = req.query;

    if (!modelId) {
      throw new ApiError(400, "Model ID is required");
    }

    try {
      const bio = await bioService.getLatestBios(
        modelId,
        includeArchived === "true",
      );

      if (!bio) {
        return res
          .status(404)
          .json(new ApiResponse(404, null, "No bios found for this model"));
      }

      res
        .status(200)
        .json(new ApiResponse(200, bio, "Bio retrieved successfully"));
    } catch (error) {
      console.error("Failed to get model bios:", error);
      throw error;
    }
  });

  /**
   * Get generation logs for a model
   * GET /api/v1/bio/logs/:modelId
   */
  static getGenerationLogs = asyncHandler(async (req, res) => {
    const { modelId } = req.params;
    const {
      page = 1,
      limit = 20,
      successOnly = false,
      dateFrom,
      dateTo,
    } = req.query;

    if (!modelId) {
      throw new ApiError(400, "Model ID is required");
    }

    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      successOnly: successOnly === "true",
      dateFrom: dateFrom ? new Date(dateFrom) : null,
      dateTo: dateTo ? new Date(dateTo) : null,
    };

    try {
      const logs = await bioService.getGenerationLogs(modelId, options);

      res
        .status(200)
        .json(
          new ApiResponse(200, logs, "Generation logs retrieved successfully"),
        );
    } catch (error) {
      console.error("Failed to get generation logs:", error);
      throw error;
    }
  });

  /**
   * Update bio rating
   * PUT /api/v1/bio/:bioId/rating
   */
  static updateBioRating = asyncHandler(async (req, res) => {
    const { bioId } = req.params;
    const { rating } = req.body;

    if (!bioId) {
      throw new ApiError(400, "Bio ID is required");
    }

    if (!rating || rating < 1 || rating > 5) {
      throw new ApiError(400, "Rating must be between 1 and 5");
    }

    try {
      const BioProfile = (await import("../../models/bio/BioProfile.js"))
        .default;
      const bio = await BioProfile.findById(bioId);

      if (!bio) {
        throw new ApiError(404, "Bio not found");
      }

      // Verify user has access to this bio
      if (req.user.agencyId.toString() !== bio.agencyId.toString()) {
        throw new ApiError(403, "Access denied to this bio");
      }

      await bio.rate(rating);

      res
        .status(200)
        .json(
          new ApiResponse(
            200,
            { rating: bio.performance.rating },
            "Bio rating updated successfully",
          ),
        );
    } catch (error) {
      console.error("Failed to update bio rating:", error);
      throw error;
    }
  });

  /**
   * Track bio usage
   * POST /api/v1/bio/:bioId/usage
   */
  static trackBioUsage = asyncHandler(async (req, res) => {
    const { bioId } = req.params;

    if (!bioId) {
      throw new ApiError(400, "Bio ID is required");
    }

    try {
      const BioProfile = (await import("../../models/bio/BioProfile.js"))
        .default;
      const bio = await BioProfile.findById(bioId);

      if (!bio) {
        throw new ApiError(404, "Bio not found");
      }

      // Verify user has access to this bio
      if (req.user.agencyId.toString() !== bio.agencyId.toString()) {
        throw new ApiError(403, "Access denied to this bio");
      }

      await bio.updateUsage();

      res.status(200).json(
        new ApiResponse(
          200,
          {
            usageCount: bio.performance.usageCount,
            lastUsed: bio.performance.lastUsed,
          },
          "Bio usage tracked successfully",
        ),
      );
    } catch (error) {
      console.error("Failed to track bio usage:", error);
      throw error;
    }
  });

  /**
   * Get bio analytics for agency
   * GET /api/v1/bio/analytics
   */
  static getBioAnalytics = asyncHandler(async (req, res) => {
    const { agencyId } = req.user;
    const { type = "overview", timeframe = 30, limit = 10 } = req.query;

    const options = {
      timeframe: parseInt(timeframe),
      limit: parseInt(limit),
    };

    try {
      let analytics;

      switch (type) {
        case "top-rated":
          analytics = await bioAnalyticsService.getTopRatedBios(
            agencyId,
            options,
          );
          break;
        case "tone-distribution":
          analytics = await bioAnalyticsService.analyzeToneDistribution(
            agencyId,
            options,
          );
          break;
        case "generation-stats":
          analytics = await bioAnalyticsService.getGenerationStats(
            agencyId,
            options,
          );
          break;
        case "usage":
          analytics = await bioAnalyticsService.getBioUsageAnalytics(
            agencyId,
            options,
          );
          break;
        default:
          // Get overview analytics
          const [topRated, toneDistribution, generationStats, usage] =
            await Promise.all([
              bioAnalyticsService.getTopRatedBios(agencyId, { limit: 5 }),
              bioAnalyticsService.analyzeToneDistribution(agencyId, options),
              bioAnalyticsService.getGenerationStats(agencyId, options),
              bioAnalyticsService.getBioUsageAnalytics(agencyId, options),
            ]);

          analytics = {
            overview: {
              topRated: topRated.slice(0, 3),
              toneDistribution: toneDistribution.distribution.slice(0, 5),
              performance: generationStats.performance,
              usage: usage.overview,
            },
            summary: {
              totalBios: usage.overview.totalBios,
              successRate: generationStats.overview.successRate,
              avgUsage: usage.metrics.avgUsagePerBio,
              engagementRate: usage.overview.engagementRate,
            },
          };
      }

      res
        .status(200)
        .json(
          new ApiResponse(
            200,
            analytics,
            "Bio analytics retrieved successfully",
          ),
        );
    } catch (error) {
      console.error("Failed to get bio analytics:", error);
      throw error;
    }
  });

  /**
   * Get model-specific bio analytics
   * GET /api/v1/bio/analytics/:modelId
   */
  static getModelBioAnalytics = asyncHandler(async (req, res) => {
    const { modelId } = req.params;
    const { timeframe = 90 } = req.query;

    if (!modelId) {
      throw new ApiError(400, "Model ID is required");
    }

    const options = {
      timeframe: parseInt(timeframe),
    };

    try {
      const analytics = await bioAnalyticsService.getModelBioAnalytics(
        modelId,
        options,
      );

      res
        .status(200)
        .json(
          new ApiResponse(
            200,
            analytics,
            "Model bio analytics retrieved successfully",
          ),
        );
    } catch (error) {
      console.error("Failed to get model bio analytics:", error);
      throw error;
    }
  });

  /**
   * Archive a bio
   * PUT /api/v1/bio/:bioId/archive
   */
  static archiveBio = asyncHandler(async (req, res) => {
    const { bioId } = req.params;

    if (!bioId) {
      throw new ApiError(400, "Bio ID is required");
    }

    try {
      const BioProfile = (await import("../../models/bio/BioProfile.js"))
        .default;
      const bio = await BioProfile.findById(bioId);

      if (!bio) {
        throw new ApiError(404, "Bio not found");
      }

      // Verify user has access to this bio
      if (req.user.agencyId.toString() !== bio.agencyId.toString()) {
        throw new ApiError(403, "Access denied to this bio");
      }

      await bio.archive();

      res
        .status(200)
        .json(
          new ApiResponse(
            200,
            { isArchived: bio.isArchived },
            "Bio archived successfully",
          ),
        );
    } catch (error) {
      console.error("Failed to archive bio:", error);
      throw error;
    }
  });

  /**
   * Get agency bio summary
   * GET /api/v1/bio/summary
   */
  static getBioSummary = asyncHandler(async (req, res) => {
    const { agencyId } = req.user;

    try {
      const BioProfile = (await import("../../models/bio/BioProfile.js"))
        .default;

      const summary = await BioProfile.aggregate([
        {
          $match: {
            agencyId: agencyId,
          },
        },
        {
          $group: {
            _id: null,
            totalBios: { $sum: 1 },
            activeBios: {
              $sum: { $cond: [{ $eq: ["$isArchived", false] }, 1, 0] },
            },
            avgRating: { $avg: "$performance.rating" },
            totalUsage: { $sum: "$performance.usageCount" },
            highRatedBios: {
              $sum: { $cond: [{ $gte: ["$performance.rating", 4] }, 1, 0] },
            },
          },
        },
      ]);

      const stats = summary[0] || {
        totalBios: 0,
        activeBios: 0,
        avgRating: 0,
        totalUsage: 0,
        highRatedBios: 0,
      };

      // Get recent activity
      const recentBios = await BioProfile.find({
        agencyId,
        isArchived: false,
      })
        .sort({ createdAt: -1 })
        .limit(5)
        .populate("modelId", "username fullName")
        .populate("personaId", "name");

      res.status(200).json(
        new ApiResponse(
          200,
          {
            stats: {
              ...stats,
              avgRating: Math.round((stats.avgRating || 0) * 10) / 10,
            },
            recentBios,
          },
          "Bio summary retrieved successfully",
        ),
      );
    } catch (error) {
      console.error("Failed to get bio summary:", error);
      throw error;
    }
  });
}

export default BioController;
