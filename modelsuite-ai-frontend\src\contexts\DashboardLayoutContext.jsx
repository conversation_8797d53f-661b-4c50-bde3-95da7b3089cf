import React, { createContext, useContext, useState } from "react";

const DashboardLayoutContext = createContext();

// eslint-disable-next-line react-refresh/only-export-components
export const useDashboardLayout = () => {
  const context = useContext(DashboardLayoutContext);
  if (!context) {
    throw new Error(
      "useDashboardLayout must be used within a DashboardLayoutProvider"
    );
  }
  return context;
};

export const DashboardLayoutProvider = ({ children }) => {
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const refreshDashboard = () => {
    setRefreshTrigger((prev) => prev + 1);
  };

  const value = {
    refreshTrigger,
    refreshDashboard,
  };

  return (
    <DashboardLayoutContext.Provider value={value}>
      {children}
    </DashboardLayoutContext.Provider>
  );
};
