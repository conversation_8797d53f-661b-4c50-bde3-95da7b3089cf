import mongoose from "mongoose";
const supportSystemSchema = new mongoose.Schema(
  {
    modelid: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ModelUser", // replace with your actual model name
      required: [true, "Model ID is required!"],
    },
    supportContact: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Employee", // replace with your employee model name
      required: [true, "Support contact ID is required!"],
    },
    agencyid: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency", // replace with your agency model name
      required: [true, "Agency ID is required!"],
    },
    profile_image: {
      type: String,
      default:
        "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face",
    },
    title: {
      type: String,
      required: [true, "Title is required"],
    },
    bio: {
      type: String,
      maxlength: 300,
      required: [true, "Bio is required!"],
    },
    internalDept: {
      type: String,
      default: "",
    },
    supportareas: [
      {
        type: String,
        required: [true, "Support area is required!"],
      },
    ],
    availability: {
      Monday: {
        start: String,
        end: String,
      },
      Tuesday: {
        start: String,
        end: String,
      },
      Wednesday: {
        start: String,
        end: String,
      },
      Thursday: {
        start: String,
        end: String,
      },
      Friday: {
        start: String,
        end: String,
      },
      Saturday: {
        start: String,
        end: String,
      },
      Sunday: {
        start: String,
        end: String,
      },
    },
    timezone: {
      type: String,
    },
    commChannel: {
      type: String,
      default: "No preference",
    },
    languages: [
      {
        type: String,
      },
    ],
  },
  { timestamps: true },
);

const SupportSystem = mongoose.model("SupportSystem", supportSystemSchema);
export default SupportSystem;
