import { Schema, model } from "mongoose";

const msgTemplateSchema = new Schema(
  {
    title: {
      type: String,
      required: [true, "Template title is required"],
      trim: true,
      maxlength: [200, "Title cannot exceed 200 characters"],
    },
    content: {
      type: String,
      required: [true, "Template content is required"],
      maxlength: [5000, "Content cannot exceed 5000 characters"],
    },
    category: {
      type: String,
      required: true,
      enum: ["Common", "DM", "Group", "Channel"],
      default: "Common",
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: false, // Make it optional if you want global templates
    },
  },
  {
    timestamps: true, // This adds createdAt and updatedAt fields automatically
  },
);

// Index for better query performance
msgTemplateSchema.index({ userId: 1, category: 1 });
msgTemplateSchema.index({ title: "text", content: "text" }); // For search functionality
msgTemplateSchema.index({ createdAt: -1 });

export default model("MsgTemplate", msgTemplateSchema);
