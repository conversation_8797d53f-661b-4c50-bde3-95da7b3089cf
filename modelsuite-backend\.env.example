# Cloudflare R2 Configuration
# Required for content upload functionality
CLO<PERSON><PERSON>LARE_ACCOUNT_ID=your_cloudflare_account_id
CLOUDFLARE_R2_ACCESS_KEY_ID=your_r2_access_key_id
CLOUDFLARE_R2_SECRET_ACCESS_KEY=your_r2_secret_access_key
CLOUDFLARE_R2_BUCKET_NAME=modelsuite-uploads
CLOUDFLARE_R2_ENDPOINT=https://your_account_id.r2.cloudflarestorage.com
CLOUDFLARE_R2_PUBLIC_DOMAIN=your_custom_domain.com

# OpenWeather API Configuration
# Required for weather widget functionality
OPENWEATHER_API_KEY=your_openweather_api_key_here

# Note: CLOUDFLARE_R2_PUBLIC_DOMAIN is optional
# If not provided, default R2 public URLs will be used