import { RewardService } from "../../utils/RewardService.js";
import { REWARD_CONFIGS } from "../../config/rewardConfig.js";

// Record login streak (called automatically from model login)
export const recordLogin = async (req, res) => {
  try {
    const userId = req.user._id;
    const userType = req.user.role || "model";

    if (!userId || !userType)
      return res
        .status(401)
        .json({ message: "Unauthorized access. Cannot record login streak." });

    const result = await RewardService.recordActivity(
      userId,
      userType,
      "login_streak",
    );

    return res.status(200).json({
      message: "Login streak recorded succcesfully.",
      data: result,
    });
  } catch (error) {
    console.error("Record login error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to record login streak",
      details: error.message,
    });
  }
};

// Record post creation (called when agency creates content)
export const recordPost = async (req, res) => {
  try {
    const userId = req.user._id;
    const userType = req.user.role || "agency";

    if (!userId || !userType)
      return res
        .status(401)
        .json({ message: "Unauthorized access. Cannot record new post." });

    // ✅ Only agencies can create posts
    if (userType !== "agency") {
      return res.status(403).json({
        message: "Only agencies can create posts for their models.",
      });
    }

    const { postCount = 1 } = req.body;

    // Validate postCount
    if (postCount < 1 || postCount > 100) {
      return res.status(400).json({
        success: false,
        error: "Invalid post count. Must be between 1 and 100.",
      });
    }

    const result = await RewardService.recordActivity(
      userId,
      userType,
      "post_count",
      { postCount },
    );

    res.status(200).json({
      message: "Post creation recorded successfully",
      data: result,
    });
  } catch (error) {
    console.error("Record post error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to record post creation",
      details: error.message,
    });
  }
};

// Record model signup (called when agency signs a model)
export const recordModelSignup = async (req, res) => {
  try {
    const userId = req.user._id;
    const userType = req.user.role || "agency";

    if (!userId || !userType)
      return res.status(401).json({
        message: "Unauthorized access. Cannot record new model signup.",
      });

    const { modelId, modelName } = req.body;

    // Validate required fields
    if (!modelId || !modelName) {
      return res.status(400).json({
        message:
          "modelId and modelName are required for recording new model signup",
      });
    }

    const result = await RewardService.recordActivity(
      userId,
      userType,
      "model_signup",
      {
        modelId,
        modelName,
      },
    );

    res.status(200).json({
      message: "Model signup recorded successfully",
      data: result,
    });
  } catch (error) {
    console.error("Record model signup error:", error);
    res.status(500).json({
      error: "Failed to record model signup",
      details: error.message,
    });
  }
};

// Get specific reward data for current user
export const getRewardData = async (req, res) => {
  try {
    const userId = req.user._id;
    const userType = req.user.role;
    const { rewardType } = req.params;

    // Validate rewardType
    const validRewardTypes = Object.keys(REWARD_CONFIGS);
    if (!validRewardTypes.includes(rewardType)) {
      return res.status(400).json({
        success: false,
        error: `Invalid reward type. Valid types: ${validRewardTypes.join(", ")}`,
      });
    }

    const data = await RewardService.getRewardData(
      userId,
      userType,
      rewardType,
    );

    res.status(200).json({
      data,
    });
  } catch (error) {
    console.error("Get reward data error:", error);
    res.status(500).json({
      error: "Failed to get reward data",
      details: error.message,
    });
  }
};

// Get all rewards for current user
export const getAllRewards = async (req, res) => {
  try {
    const userId = req.user._id;
    const userType = req.user.role;

    const data = await RewardService.getAllUserRewards(userId, userType);

    // Calculate summary stats
    const summary = {
      totalPoints: 0,
      totalBadges: 0,
      highestLevel: 1,
      activeStreaks: 0,
    };

    Object.values(data).forEach((reward) => {
      summary.totalPoints += reward.totalPoints || 0;
      summary.totalBadges += reward.badges?.length || 0;
      summary.highestLevel = Math.max(summary.highestLevel, reward.level || 1);
      if (reward.rewardType?.includes("streak") && reward.currentValue > 0) {
        summary.activeStreaks += 1;
      }
    });

    res.status(200).json({
      success: true,
      data: {
        rewards: data,
        summary,
      },
    });
  } catch (error) {
    console.error("Get all rewards error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to get user rewards",
      details: error.message,
    });
  }
};

// Get leaderboard for specific reward type
export const getLeaderboard = async (req, res) => {
  try {
    const { rewardType } = req.params;
    const userType = req.user.role;
    const limit = parseInt(req.query.limit) || 10;

    // Validate limit
    if (limit < 1 || limit > 100) {
      return res.status(400).json({
        success: false,
        error: "Limit must be between 1 and 100",
      });
    }

    // Validate rewardType
    const validRewardTypes = Object.keys(REWARD_CONFIGS);
    if (!validRewardTypes.includes(rewardType)) {
      return res.status(400).json({
        success: false,
        error: `Invalid reward type. Valid types: ${validRewardTypes.join(", ")}`,
      });
    }

    const leaderboard = await RewardService.getLeaderboard(
      rewardType,
      userType,
      limit,
    );

    // Find current user's position
    const currentUserId = req.user._id.toString();
    let userPosition = null;
    let userRank = null;

    leaderboard.forEach((entry, index) => {
      if (entry.userId._id.toString() === currentUserId) {
        userPosition = entry;
        userRank = index + 1;
      }
    });

    return res.status(200).json({
      data: {
        leaderboard,
        userPosition,
        userRank,
        totalEntries: leaderboard.length,
        rewardType,
        userType,
      },
    });
  } catch (error) {
    console.error("Get leaderboard error:", error);
    res.status(500).json({
      error: "Failed to get leaderboard",
      details: error.message,
    });
  }
};

// Get reward statistics (admin/overview endpoint)
export const getRewardStats = async (req, res) => {
  try {
    const userId = req.user;
    const userType = req.user.role;

    if (!userId || !userType)
      return res.status(401).json({ message: "Unauthorised access." });

    // Get stats for all reward types available to this user type
    const stats = {};

    for (const [rewardType, config] of Object.entries(REWARD_CONFIGS)) {
      if (config.userTypes.includes(userType)) {
        try {
          const leaderboard = await RewardService.getLeaderboard(
            rewardType,
            userType,
            1,
          );
          const topUser = leaderboard[0];

          stats[rewardType] = {
            description: config.description,
            topPerformer: topUser
              ? {
                  user: topUser.userId,
                  value: topUser.currentValue,
                  totalPoints: topUser.totalPoints,
                  level: topUser.level,
                }
              : null,
            availableBadges: Object.keys(config.badges).length,
          };
        } catch (error) {
          console.error(`Error getting stats for ${rewardType}:`, error);
          stats[rewardType] = {
            description: config.description,
            topPerformer: null,
            availableBadges: Object.keys(config.badges).length,
            error: "Failed to load stats",
          };
        }
      }
    }

    res.status(200).json({
      data: {
        userType,
        rewardTypes: stats,
        timestamp: new Date(),
      },
    });
  } catch (error) {
    console.error("Get reward stats error:", error);
    res.status(500).json({
      error: "Failed to get reward statistics",
      details: error.message,
    });
  }
};

// Get user's badge collection
export const getUserBadges = async (req, res) => {
  try {
    const userId = req.user._id;
    const userType = req.user.role;

    if (!userId || !userType)
      return res.status(401).json({ message: "Unauthorised access." });

    const allRewards = await RewardService.getAllUserRewards(userId, userType);

    // Collect all badges from all reward types
    const allBadges = [];
    let totalPoints = 0;

    Object.entries(allRewards).forEach(([rewardType, reward]) => {
      if (reward.badges && reward.badges.length > 0) {
        reward.badges.forEach((badge) => {
          allBadges.push({
            ...badge,
            rewardType,
            rewardDescription: REWARD_CONFIGS[rewardType]?.description,
          });
        });
      }
      totalPoints += reward.totalPoints || 0;
    });

    // Sort badges by rarity and date earned
    const rarityOrder = { legendary: 4, epic: 3, rare: 2, common: 1 };
    allBadges.sort((a, b) => {
      const rarityDiff =
        (rarityOrder[b.rarity] || 0) - (rarityOrder[a.rarity] || 0);
      if (rarityDiff !== 0) return rarityDiff;
      return new Date(b.earnedAt) - new Date(a.earnedAt);
    });

    res.status(200).json({
      success: true,
      data: {
        badges: allBadges,
        totalBadges: allBadges.length,
        totalPoints,
        rarityBreakdown: {
          legendary: allBadges.filter((b) => b.rarity === "legendary").length,
          epic: allBadges.filter((b) => b.rarity === "epic").length,
          rare: allBadges.filter((b) => b.rarity === "rare").length,
          common: allBadges.filter((b) => b.rarity === "common").length,
        },
      },
    });
  } catch (error) {
    console.error("Get user badges error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to get user badges",
      details: error.message,
    });
  }
};

// Manual reward recording (for testing/admin purposes)
// temporary func
export const manualRecordActivity = async (req, res) => {
  try {
    const { userId, userType, rewardType, options = {} } = req.body;

    // Basic validation
    if (!userId || !userType || !rewardType) {
      return res.status(400).json({
        success: false,
        error: "userId, userType, and rewardType are required",
      });
    }

    const result = await RewardService.recordActivity(
      userId,
      userType,
      rewardType,
      options,
    );

    res.status(200).json({
      success: true,
      message: "Activity recorded manually",
      data: result,
    });
  } catch (error) {
    console.error("Manual record activity error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to record activity manually",
      details: error.message,
    });
  }
};
