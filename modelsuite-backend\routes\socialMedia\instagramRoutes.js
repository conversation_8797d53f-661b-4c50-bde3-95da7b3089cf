import express from "express";
import { handleInstagramCallback } from "../../controllers/socialMedia/handleInstagramCallback.js";
import { verifyToken } from "../../middlewares/authMiddleware.js";
import checkPermission from "../../middlewares/permissionCheck.js";
import {
  disconnectInstagram,
  getInstagramAccountInfo,
  getInstagramDemographics,
  getInstagramInsights,
  getInstagramStoryInsights,
} from "../../controllers/socialMedia/insightsController.js";
import {
  getFacebookPageInfo,
  getFacebookPageInsights,
  getFacebookPagePosts,
  getFacebookPostInsights,
} from "../../controllers/socialMedia/facebookController.js";

const router = express.Router();

// 👇 Instagram OAuth callback route
router.get("/callback", handleInstagramCallback);
router.get(
  "/account-info/:modelId",
  verifyToken,
  checkPermission("social_media.view"),
  getInstagramAccountInfo,
);
router.get(
  "/insights/:modelId",
  verifyToken,
  checkPermission("social_media.view"),
  getInstagramInsights,
);
router.get(
  "/demographics/:modelId",
  verifyToken,
  checkPermission("social_media.view"),
  getInstagramDemographics,
);
router.get(
  "/story-insights/:modelId",
  verifyToken,
  checkPermission("social_media.view"),
  getInstagramStoryInsights,
);
router.delete(
  "/disconnect/:modelId",
  verifyToken,
  checkPermission("social_media.manage"),
  disconnectInstagram,
);
router.get(
  "/facebook/insights/:modelId",
  verifyToken,
  checkPermission("social_media.view"),
  getFacebookPageInsights,
);
router.get(
  "/facebook/page-info/:modelId",
  verifyToken,
  checkPermission("social_media.view"),
  getFacebookPageInfo,
);
router.get(
  "/facebook/posts/:modelId",
  verifyToken,
  checkPermission("social_media.view"),
  getFacebookPagePosts,
);
router.get(
  "/facebook/post/:postId/insights",
  verifyToken,
  getFacebookPostInsights,
);

export default router;
