import { google } from "googleapis";
import ModelUser from "../../models/model.js";
import jwt from "jsonwebtoken";

// Setup OAuth2 client
const oauth2Client = new google.auth.OAuth2(
  process.env.GOOGLE_CLIENT_ID,
  process.env.GOOGLE_CLIENT_SECRET,
  process.env.GOOGLE_REDIRECT_URI,
);

// STEP 1: Redirect to Google Login
export const generateGoogleAuthURL = (req, res) => {
  try {
    const token = req.query.state; // ✅ this is JWT from frontend

    const url = oauth2Client.generateAuthUrl({
      access_type: "offline",
      prompt: "consent",
      scope: [
        "https://www.googleapis.com/auth/calendar",
        "https://www.googleapis.com/auth/calendar.events",
      ],
      state: token, // ✅ this attaches JWT to OAuth flow
    });

    return res.redirect(url);
  } catch (error) {
    console.log(error.message);
    res
      .status(500)
      .json({ error: error.message || "Failed to authenticate with Google" });
  }
};

// STEP 2: Handle Google Callback
export const handleGoogleCallback = async (req, res) => {
  try {
    const { code, state: stateToken } = req.query;
    //  console.log(process.env.JWT_SECRET)

    if (!code)
      return res.status(400).json({ message: "Missing authorization code." });
    if (!stateToken)
      return res.status(401).json({ message: "Token missing in state param" });

    // 🔐 Verify JWT from state
    let decoded;
    try {
      decoded = jwt.verify(stateToken, process.env.LOGIN_ACCESS_TOKEN_SECRET);
    } catch (err) {
      console.error("❌ Failed to decode state token:", err.message);
      return res
        .status(401)
        .json({ message: "Invalid or expired token in state param" });
    }

    const modelId = decoded?._id;
    console.log("✅ Verified token for model ID:", modelId);

    const { tokens } = await oauth2Client.getToken(code);
    oauth2Client.setCredentials(tokens);

    console.log(tokens);

    await ModelUser.findByIdAndUpdate(modelId, {
      googleAccessToken: tokens.access_token,
      googleRefreshToken: tokens.refresh_token,
      tokenExpiryDate: tokens.expiry_date
        ? new Date(tokens.expiry_date)
        : undefined,
    });

    res.redirect(
      `${process.env.FRONTEND_HOSTING_BASEURL}/calendar/success?modelId=${modelId}`,
    );
  } catch (error) {
    console.log(error.message);
    res
      .status(500)
      .json({ error: error.message || "Failed to authenticate with Google" });
  }
};

export const checkGoogleConnectionStatus = async (req, res) => {
  try {
    const { modelId } = req.params;
    const model = await ModelUser.findById(modelId);

    if (!model) return res.status(404).json({ connected: false });

    const connected = !!model.googleAccessToken;
    res.status(200).json({ connected });
  } catch (err) {
    res.status(500).json({ connected: false });
  }
};

export const disconnectGoogleCalendar = async (req, res) => {
  try {
    const modelId = req.user._id; // assuming JWT middleware is used

    const model = await ModelUser.findById(modelId);
    if (!model) return res.status(404).json({ error: "Model not found" });

    // Optional: Revoke the token at Google
    if (model.googleAccessToken) {
      const oauth2Client = new google.auth.OAuth2();
      oauth2Client.setCredentials({ access_token: model.googleAccessToken });

      try {
        await oauth2Client.revokeCredentials(); // revoke access on Google's side
      } catch (err) {
        console.warn("Token revoke failed:", err.message); // don't fail the whole request
      }
    }

    // Clear tokens in DB
    model.googleAccessToken = undefined;
    model.googleRefreshToken = undefined;
    model.tokenExpiryDate = undefined;
    await model.save();

    res.json({ message: "Google Calendar disconnected successfully" });
  } catch (err) {
    console.error("Calendar disconnect error:", err.message);
    res.status(500).json({ error: "Failed to disconnect calendar" });
  }
};
