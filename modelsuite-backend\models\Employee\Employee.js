import mongoose from "mongoose";
import jwt from "jsonwebtoken";

const employeeSchema = new mongoose.Schema({
  agencyId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Agency",
    required: true,
  },

  userType: {
    type: String,
    default: "Employee",
  },
  role: {
    type: String,
    enum: [
      "viewer",
      "creator",
      "manager",
      "admin",
      "financial_manager",
      "content_moderator",
      "support_agent",
      "auditor",
    ],
    default: "viewer",
  },

  name: {
    type: String,
    required: true,
    trim: true,
  },

  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
  },

  password: {
    type: String,
    required: true,
    minlength: 6,
  },

  status: {
    type: String,
    enum: ["active", "inactive", "suspended"],
    default: "active",
  },

  loginRefreshToken: {
    type: String,
  },

  // Custom permissions that override default role permissions
  customPermissions: {
    type: [String], // Array of permission strings in dot notation (e.g., ['tasks.create', 'uploads.delete'])
    default: [],
  },

  profileImage: String,
  phone: String,

  createdAt: {
    type: Date,
    default: Date.now,
  },
});

employeeSchema.methods.generateAccessToken = async function () {
  // Get effective permissions for this employee
  const effectivePermissions = await this.getEffectivePermissions();

  return jwt.sign(
    {
      _id: this._id,
      role: "employee",
      email: this.email,
      name: this.name,
      agencyId: this.agencyId,
      effectivePermissions: effectivePermissions,
    },
    process.env.LOGIN_ACCESS_TOKEN_SECRET,
    {
      expiresIn: process.env.LOGIN_ACCESS_TOKEN_EXPIRY,
    },
  );
};

employeeSchema.methods.generateRefreshToken = function () {
  return jwt.sign(
    { _id: this._id, role: "employee" },
    process.env.LOGIN_REFRESH_TOKEN_SECRET,
    {
      expiresIn: process.env.LOGIN_REFRESH_TOKEN_EXPIRY,
    },
  );
};

// Method to calculate effective permissions (role + custom overrides)
employeeSchema.methods.getEffectivePermissions = async function () {
  const { getDefaultPermissions } = await import("../../config/permissions.js");

  // Get default permissions for the role
  const defaultPermissions = getDefaultPermissions(this.role);

  // Ensure customPermissions is an array (handle undefined/null cases)
  const customPermissions = this.customPermissions || [];

  // Separate positive and negative permissions from custom permissions
  const positiveCustomPermissions = customPermissions.filter(
    (p) => !p.startsWith("-"),
  );
  const negativeCustomPermissions = customPermissions
    .filter((p) => p.startsWith("-"))
    .map((p) => p.substring(1)); // Remove the '-' prefix

  // Start with default permissions
  let effectivePermissions = [...defaultPermissions];

  // Add positive custom permissions
  effectivePermissions = [
    ...new Set([...effectivePermissions, ...positiveCustomPermissions]),
  ];

  // Remove negative custom permissions (denied permissions)
  effectivePermissions = effectivePermissions.filter(
    (permission) => !negativeCustomPermissions.includes(permission),
  );

  return effectivePermissions;
};

employeeSchema.methods.generateAccessAndRefreshTokens = async function () {
  try {
    const effectivePermissions = await this.getEffectivePermissions();

    const accessToken = jwt.sign(
      {
        _id: this._id,
        email: this.email,
        name: this.name,
        role: "employee", // Main role is always "employee"
        subRole: this.role, // Sub-role (manager, viewer, etc.)
        agencyId: this.agencyId,
        effectivePermissions: effectivePermissions,
      },
      process.env.LOGIN_ACCESS_TOKEN_SECRET,
      {
        expiresIn: process.env.LOGIN_ACCESS_TOKEN_EXPIRY,
      },
    );

    const refreshToken = jwt.sign(
      {
        _id: this._id,
      },
      process.env.LOGIN_REFRESH_TOKEN_SECRET,
      {
        expiresIn: process.env.LOGIN_REFRESH_TOKEN_EXPIRY,
      },
    );

    this.loginRefreshToken = refreshToken;
    await this.save({ validateBeforeSave: false });

    return { accessToken, refreshToken };
  } catch (error) {
    throw new Error(
      "Something went wrong while generating refresh and access token",
    );
  }
};

// No pre-save hook needed - permissions are handled by getEffectivePermissions method

const Employee = mongoose.model("Employee", employeeSchema);
export default Employee;
