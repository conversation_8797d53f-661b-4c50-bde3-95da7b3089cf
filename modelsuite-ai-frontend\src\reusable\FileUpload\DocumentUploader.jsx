import * as React from "react";
import { cn } from "@/lib/utils";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  FileText,
  Upload,
  X,
  Download,
  Eye,
  FileCheck,
  AlertCircle,
  File,
} from "lucide-react";

const DocumentUploader = ({
  acceptedTypes = [".pdf", ".doc", ".docx", ".txt", ".rtf"],
  maxSize = 20 * 1024 * 1024, // 20MB default
  multiple = false,
  currentFiles = [],
  onUpload,
  onRemove,
  onPreview,
  disabled = false,
  showPreview = true,
  className,
  ...props
}) => {
  const [files, setFiles] = React.useState(currentFiles || []);
  const [dragActive, setDragActive] = React.useState(false);
  const [uploading, setUploading] = React.useState(false);
  const [errors, setErrors] = React.useState({});
  const fileInputRef = React.useRef(null);

  // File type icons mapping
  const getFileIcon = (fileName) => {
    const extension = fileName.toLowerCase().split(".").pop();
    switch (extension) {
      case "pdf":
        return <FileText className="h-5 w-5 text-red-500" />;
      case "doc":
      case "docx":
        return <FileText className="h-5 w-5 text-blue-500" />;
      case "txt":
      case "rtf":
        return <FileText className="h-5 w-5 text-gray-500" />;
      default:
        return <File className="h-5 w-5 text-muted-foreground" />;
    }
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // Get file type badge color
  const getFileTypeBadge = (fileName) => {
    const extension = fileName.toLowerCase().split(".").pop();
    switch (extension) {
      case "pdf":
        return { variant: "destructive", label: "PDF" };
      case "doc":
      case "docx":
        return { variant: "default", label: "DOC" };
      case "txt":
        return { variant: "secondary", label: "TXT" };
      case "rtf":
        return { variant: "outline", label: "RTF" };
      default:
        return {
          variant: "secondary",
          label: extension?.toUpperCase() || "FILE",
        };
    }
  };

  // Validate file
  const validateFile = (file) => {
    // Check file type
    const fileName = file.name.toLowerCase();
    const isValidType = acceptedTypes.some((type) =>
      fileName.endsWith(type.toLowerCase().replace(".", ""))
    );

    if (!isValidType) {
      return `Invalid file type. Accepted: ${acceptedTypes.join(", ")}`;
    }

    // Check file size
    if (file.size > maxSize) {
      return `File size exceeds ${formatFileSize(maxSize)}`;
    }

    return null;
  };

  // Process file upload
  const processFiles = async (fileList) => {
    const newFiles = Array.from(fileList).map((file) => ({
      id: Math.random().toString(36).substr(2, 9),
      file,
      name: file.name,
      size: file.size,
      type: file.type,
      status: "pending", // pending, uploading, success, error
      error: validateFile(file),
      uploadedAt: new Date(),
    }));

    // If not multiple, replace existing files
    if (!multiple) {
      setFiles(newFiles);
      setErrors({});
    } else {
      setFiles((prev) => [...prev, ...newFiles]);
    }

    // Upload valid files
    for (const fileData of newFiles) {
      if (!fileData.error && onUpload) {
        await uploadFile(fileData);
      }
    }
  };

  // Upload single file
  const uploadFile = async (fileData) => {
    setFiles((prev) =>
      prev.map((f) =>
        f.id === fileData.id ? { ...f, status: "uploading" } : f
      )
    );

    try {
      setUploading(true);
      const result = await onUpload(fileData.file);

      setFiles((prev) =>
        prev.map((f) =>
          f.id === fileData.id
            ? { ...f, status: "success", uploadUrl: result?.url }
            : f
        )
      );

      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[fileData.id];
        return newErrors;
      });
    } catch (error) {
      setFiles((prev) =>
        prev.map((f) => (f.id === fileData.id ? { ...f, status: "error" } : f))
      );

      setErrors((prev) => ({
        ...prev,
        [fileData.id]: error.message || "Upload failed",
      }));
    } finally {
      setUploading(false);
    }
  };

  // Remove file
  const removeFile = (fileId) => {
    setFiles((prev) => prev.filter((f) => f.id !== fileId));
    setErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[fileId];
      return newErrors;
    });

    if (onRemove) {
      onRemove(fileId);
    }
  };

  // Drag handlers
  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (disabled) return;

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      processFiles(e.dataTransfer.files);
    }
  };

  const handleInputChange = (e) => {
    if (e.target.files && e.target.files.length > 0) {
      processFiles(e.target.files);
    }
  };

  const openFileDialog = () => {
    if (fileInputRef.current && !disabled) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className={cn("space-y-4", className)} {...props}>
      {/* Drop Zone */}
      <Card
        className={cn(
          "relative overflow-hidden transition-all duration-200 cursor-pointer border-2 border-dashed bg-[#0f0f0f] border-gray-800",
          dragActive && "border-gray-600 bg-[#1a1a1a]",
          disabled && "cursor-not-allowed opacity-50",
          !disabled && "hover:border-gray-700"
        )}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={openFileDialog}
      >
        <CardContent className="p-8 text-center">
          <div className="space-y-4">
            <div className="flex justify-center">
              <FileText className="h-12 w-12 text-gray-400" />
            </div>
            <div>
              <p className="text-lg font-medium text-gray-300">
                {dragActive ? "Drop documents here" : "Upload Documents"}
              </p>
              <p className="text-sm text-gray-500 mt-1">
                Drag & drop documents or click to browse
              </p>
              <p className="text-xs text-gray-600 mt-1">
                Supported: {acceptedTypes.join(", ")} • Max:{" "}
                {formatFileSize(maxSize)}
                {multiple && " • Multiple files allowed"}
              </p>
            </div>
            <Button
              variant="outline"
              size="sm"
              disabled={disabled}
              className="border-gray-600 bg-[#1a1a1a] text-gray-400 hover:bg-gray-800 hover:text-gray-300 hover:border-gray-500"
            >
              <Upload className="h-4 w-4 mr-2" />
              Browse Documents
            </Button>
          </div>
        </CardContent>

        <input
          ref={fileInputRef}
          type="file"
          accept={acceptedTypes.join(",")}
          multiple={multiple}
          onChange={handleInputChange}
          className="hidden"
          disabled={disabled}
        />
      </Card>

      {/* File List */}
      {files.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-300">
            Documents ({files.length})
          </h4>
          <div className="space-y-2">
            {files.map((fileData) => {
              const typeBadge = getFileTypeBadge(fileData.name);
              const hasError = fileData.error || errors[fileData.id];

              return (
                <Card
                  key={fileData.id}
                  className={cn(
                    "p-3 bg-[#0f0f0f] border-gray-800",
                    hasError && "border-red-600 bg-red-900/20"
                  )}
                >
                  <div className="flex items-center gap-3">
                    {/* File Icon */}
                    <div className="flex-shrink-0">
                      {getFileIcon(fileData.name)}
                    </div>

                    {/* File Info */}
                    <div className="flex-1 min-w-0 space-y-1">
                      <div className="flex items-center gap-2">
                        <p className="text-sm font-medium truncate text-gray-300">
                          {fileData.name}
                        </p>
                        <Badge variant={typeBadge.variant} className="text-xs">
                          {typeBadge.label}
                        </Badge>
                      </div>

                      <div className="flex items-center gap-2 text-xs text-gray-400">
                        <span>{formatFileSize(fileData.size)}</span>
                        {fileData.uploadedAt && (
                          <span>
                            • {fileData.uploadedAt.toLocaleDateString()}
                          </span>
                        )}
                      </div>

                      {hasError && (
                        <p className="text-xs text-red-400 flex items-center gap-1">
                          <AlertCircle className="h-3 w-3" />
                          {fileData.error || errors[fileData.id]}
                        </p>
                      )}
                    </div>

                    {/* Status & Actions */}
                    <div className="flex items-center gap-2">
                      {/* Upload Status */}
                      {fileData.status === "uploading" && (
                        <div className="h-4 w-4 animate-spin rounded-full border-2 border-blue-400 border-t-transparent" />
                      )}
                      {fileData.status === "success" && (
                        <FileCheck className="h-4 w-4 text-green-400" />
                      )}
                      {fileData.status === "error" && (
                        <AlertCircle className="h-4 w-4 text-red-400" />
                      )}

                      {/* Action Buttons */}
                      <div className="flex items-center gap-1">
                        {showPreview &&
                          fileData.status === "success" &&
                          onPreview && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                onPreview(fileData);
                              }}
                              className="h-8 w-8 p-0 text-gray-500 hover:text-gray-300 hover:bg-[#1a1a1a]"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          )}

                        {fileData.uploadUrl && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              window.open(fileData.uploadUrl, "_blank");
                            }}
                            className="h-8 w-8 p-0 text-gray-500 hover:text-gray-300 hover:bg-[#1a1a1a]"
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                        )}

                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            removeFile(fileData.id);
                          }}
                          className="h-8 w-8 p-0 text-gray-500 hover:text-gray-300 hover:bg-[#1a1a1a]"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card>
              );
            })}
          </div>
        </div>
      )}

      {/* Upload Summary */}
      {files.length > 0 && (
        <div className="text-xs text-gray-400">
          {files.filter((f) => f.status === "success").length} of {files.length}{" "}
          documents uploaded
          {uploading && " • Uploading..."}
        </div>
      )}
    </div>
  );
};

export default DocumentUploader;
