import mongoose from "mongoose";

const portfolioSchema = new mongoose.Schema(
  {
    name: { type: String, required: true },
    slug: { type: String, required: true, unique: true },
    bio: String,
    category: String,
    measurements: {
      heightCm: Number,
      weightKg: Number,
      bust: String,
      waist: String,
      hips: String,
      dressSize: String,
      shoeSize: String,
      hairColor: String,
      eyeColor: String,
    },
    coverImage: String,
    contact: {
      email: String,
      phone: String,
      instagram: String,
      website: String,
    },
    images: [String],
    visibility: {
      type: String,
      enum: ["public", "private", "protected"],
      default: "public",
    },
    password: String,
    template: { type: String, enum: ["modern", "classic", "editorial"] },
    agency: { type: mongoose.Schema.Types.ObjectId, ref: "Agency" },
    model: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ModelUser",
      unique: true,
    },
    resetPasswordOTP: {
      code: {
        type: String,
      },
      expiresAt: {
        type: Date,
      },
    },
    resetPasswordOTPVerified: {
      type: Boolean,
      default: false,
    },
    description: {
      type: String,
      maxlength: 500,
      trim: true,
    },
  },
  { timestamps: true },
);

const ModelPortfolio = mongoose.model("ModelPortfolio", portfolioSchema);

export default ModelPortfolio;
