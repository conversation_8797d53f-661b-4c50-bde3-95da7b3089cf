import React from "react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { cn } from "@/lib/utils";

const DraggableCard = ({ id, children, className, ...props }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className={cn(
        "relative group cursor-grab active:cursor-grabbing",
        isDragging && "z-50 opacity-80",
        className
      )}
      {...props}
    >
      {/* Card Content with subtle drag indication */}
      <div
        className={cn(
          "transition-all duration-200 relative",
          isDragging && "shadow-2xl ring-2 ring-blue-500/50 scale-105",
          "hover:shadow-lg hover:scale-[1.02] hover:ring-1 hover:ring-gray-600/50"
        )}
      >
        {/* Subtle drag indicator - only shows on hover */}
        <div className="absolute inset-0 rounded-lg border-2 border-dashed border-transparent group-hover:border-gray-600/30 transition-all duration-200 pointer-events-none" />
        {children}
      </div>
    </div>
  );
};

export default DraggableCard;
