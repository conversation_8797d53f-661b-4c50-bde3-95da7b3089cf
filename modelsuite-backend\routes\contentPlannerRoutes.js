import express from "express";
import {
  createPlan,
  addTask,
  getWeeklyPlan,
  editPlan,
  addGoalToPlan,
  getPlansByDateRange,
  updateTaskStatus,
  suggestPostingSlots,
  checkCalendarAvailability,
  syncToCalendar,
  getSchedulingLogs,
  getAnalytics,
  getSchedulingAnalytics,
  createFromTemplate,
  archivePlan,
  getModelAnalytics,
  getPlanSummary,
  bulkUpdateTasks,
  // AI-powered endpoints
  generateAIStrategy,
  getAIContentSuggestions,
  analyzeOptimalTiming,
  generateAIThemes,
  createAIPoweredPlan,
  optimizePlanWithAI,
} from "../controllers/planner/contentPlannerController.js";

// Import existing middleware
import { verifyToken, verifyRole } from "../middlewares/authMiddleware.js";

const router = express.Router();

// Apply authentication to all routes
router.use(verifyToken);

// Plan Management Routes
router.post("/create-plan", verifyRole(["agency", "model"]), createPlan);

router.post("/add-task", verifyRole(["agency", "model"]), addTask);

router.get("/week/:modelId", verifyRole(["agency", "model"]), getWeeklyPlan);

router.put("/plan/:planId", verifyRole(["agency", "model"]), editPlan);

router.post(
  "/plan/:planId/goals",
  verifyRole(["agency", "model"]),
  addGoalToPlan,
);

router.get("/plans", verifyRole(["agency", "model"]), getPlansByDateRange);

router.post(
  "/from-template",
  verifyRole(["agency", "model"]),
  createFromTemplate,
);

router.put(
  "/plan/:planId/archive",
  verifyRole(["agency", "model"]),
  archivePlan,
);

router.get(
  "/plan/:planId/summary",
  verifyRole(["agency", "model"]),
  getPlanSummary,
);

// Task Management Routes
router.put(
  "/task/:taskId/status",
  verifyRole(["agency", "model"]),
  updateTaskStatus,
);

router.put(
  "/tasks/bulk-update",
  verifyRole(["agency", "model"]),
  bulkUpdateTasks,
);

// Auto-Scheduling Routes
router.post(
  "/auto-schedule",
  verifyRole(["agency", "model"]),
  suggestPostingSlots,
);

router.get(
  "/calendar/availability/:modelId",
  verifyRole(["agency", "model"]),
  checkCalendarAvailability,
);

router.post("/sync-calendar", verifyRole(["agency", "model"]), syncToCalendar);

// Logs and Analytics Routes
router.get(
  "/logs/:modelId",
  verifyRole(["agency", "model"]),
  getSchedulingLogs,
);

router.get("/analytics", verifyRole(["agency"]), getAnalytics);

router.get(
  "/analytics/scheduling",
  verifyRole(["agency"]),
  getSchedulingAnalytics,
);

router.get(
  "/analytics/:modelId",
  verifyRole(["agency", "model"]),
  getModelAnalytics,
);

// ===== AI-POWERED ROUTES =====

// AI Strategy Generation
router.post(
  "/ai/strategy/:modelId",
  verifyRole(["agency", "model"]),
  generateAIStrategy,
);

// AI Content Suggestions
router.get(
  "/ai/suggestions/:planId",
  verifyRole(["agency", "model"]),
  getAIContentSuggestions,
);

// AI Timing Analysis
router.post(
  "/ai/timing/:modelId",
  verifyRole(["agency", "model"]),
  analyzeOptimalTiming,
);

// AI Theme Generation
router.post(
  "/ai/themes/:modelId",
  verifyRole(["agency", "model"]),
  generateAIThemes,
);

// AI-Powered Plan Creation
router.post(
  "/ai/create-plan",
  verifyRole(["agency", "model"]),
  createAIPoweredPlan,
);

// AI Plan Optimization
router.post(
  "/ai/optimize/:planId",
  verifyRole(["agency", "model"]),
  optimizePlanWithAI,
);

export default router;
