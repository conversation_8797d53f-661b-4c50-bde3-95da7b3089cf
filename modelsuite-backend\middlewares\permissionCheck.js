// Enhanced permission check that supports both legacy and new permission systems
const checkPermission = (requiredPermission) => {
  return (req, res, next) => {
    if (!req.user) {
      return res
        .status(403)
        .json({ message: "Forbidden: User not authenticated." });
    }

    // Allow agency users full access (do NOT auto-allow model users)
    // Models should not be implicitly granted permissions here; controllers
    // enforce finer-grained access rules and employees use effectivePermissions.
    if (req.user.role === "agency") {
      return next();
    }

    if (req.user.role !== "employee") {
      return res.status(403).json({
        message: "Forbidden: Not an employee or user not authenticated.",
      });
    }

    // Check permissions using dot notation
    if (
      req.user.effectivePermissions &&
      Array.isArray(req.user.effectivePermissions)
    ) {
      if (req.user.effectivePermissions.includes(requiredPermission)) {
        return next();
      }
    }

    return res.status(403).json({
      message: `Forbidden: You do not have permission for ${requiredPermission}.`,
    });
  };
};

// Legacy function for backward compatibility
const checkLegacyPermission = (module, action) => {
  return checkPermission(`${module}.${action}`);
};

export default checkPermission;
export { checkLegacyPermission };
