import * as React from "react";
import { cn } from "@/lib/utils";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Copy, RotateCcw, Type } from "lucide-react";

const TextAreaField = ({
  control,
  name,
  label,
  placeholder = "Enter your text...",
  description,
  disabled = false,
  required = false,
  rows = 4,
  maxLength,
  minLength,
  resize = true, // true, false, "vertical", "horizontal"
  showWordCount = false,
  showCharCount = false,
  showCopy = false,
  showClear = false,
  autoResize = false,
  className,
  ...props
}) => {
  const textareaRef = React.useRef(null);

  // Auto-resize functionality
  React.useEffect(() => {
    if (autoResize && textareaRef.current) {
      const textarea = textareaRef.current;
      const adjustHeight = () => {
        textarea.style.height = "auto";
        textarea.style.height = `${textarea.scrollHeight}px`;
      };

      textarea.addEventListener("input", adjustHeight);
      adjustHeight(); // Initial adjustment

      return () => textarea.removeEventListener("input", adjustHeight);
    }
  }, [autoResize]);

  // Get resize class
  const getResizeClass = () => {
    if (!resize) return "resize-none";
    if (resize === "vertical") return "resize-y";
    if (resize === "horizontal") return "resize-x";
    return "resize";
  };

  // Count words
  const countWords = (text) => {
    if (!text) return 0;
    return text
      .trim()
      .split(/\s+/)
      .filter((word) => word.length > 0).length;
  };

  // Copy to clipboard
  const copyToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      // You could add a toast notification here
    } catch (err) {
      console.error("Failed to copy: ", err);
    }
  };

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className="space-y-2">
          {label && (
            <div className="flex items-center justify-between">
              <FormLabel className="text-sm font-medium text-white">
                {label}
                {required && <span className="text-red-400 ml-1">*</span>}
              </FormLabel>

              {/* Action Buttons */}
              {(showCopy || showClear) && field.value && (
                <div className="flex items-center gap-1">
                  {showCopy && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(field.value)}
                      className="h-6 w-6 p-0 text-gray-400 hover:text-white hover:bg-gray-700"
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                  )}
                  {showClear && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => field.onChange("")}
                      className="h-6 w-6 p-0 text-gray-400 hover:text-white hover:bg-gray-700"
                    >
                      <RotateCcw className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              )}
            </div>
          )}

          <FormControl>
            <div className="relative">
              <Textarea
                ref={textareaRef}
                placeholder={placeholder}
                disabled={disabled}
                rows={autoResize ? 1 : rows}
                maxLength={maxLength}
                minLength={minLength}
                className={cn(
                  "bg-gray-800/50 border-gray-700 text-white placeholder:text-gray-400",
                  "focus:border-blue-500 focus:ring-blue-500/20",
                  "transition-all duration-200",
                  getResizeClass(),
                  autoResize && "overflow-hidden",
                  className
                )}
                {...field}
                {...props}
              />

              {/* Character/Word Count */}
              {(showCharCount || showWordCount || maxLength) && (
                <div className="absolute bottom-2 right-2 flex items-center gap-2 text-xs text-gray-400">
                  {showWordCount && (
                    <div className="flex items-center gap-1">
                      <Type className="h-3 w-3" />
                      <span>{countWords(field.value)} words</span>
                    </div>
                  )}
                  {(showCharCount || maxLength) && (
                    <div
                      className={cn(
                        "px-2 py-1 rounded bg-gray-700/50",
                        maxLength &&
                          field.value?.length > maxLength * 0.9 &&
                          "text-yellow-400",
                        maxLength &&
                          field.value?.length >= maxLength &&
                          "text-red-400"
                      )}
                    >
                      {field.value?.length || 0}
                      {maxLength && `/${maxLength}`}
                    </div>
                  )}
                </div>
              )}
            </div>
          </FormControl>

          {description && (
            <FormDescription className="text-xs text-gray-400">
              {description}
            </FormDescription>
          )}

          <FormMessage className="text-xs text-red-400" />
        </FormItem>
      )}
    />
  );
};

export default TextAreaField;
