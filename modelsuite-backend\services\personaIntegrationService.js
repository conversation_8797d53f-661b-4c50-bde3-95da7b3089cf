import PersonaProfile from "../models/persona/PersonaProfile.js";
import { ApiError } from "../utils/ApiError.js";

/**
 * PersonaIntegrationService - Handles persona integration across different modules
 * Provides persona context for caption generation, bio creation, and other content tools
 */
class PersonaIntegrationService {
  /**
   * Extract persona context for caption generation
   * Returns key persona elements suitable for AI prompts
   */
  static async getPersonaContextForCaptions(modelId) {
    try {
      // Get the latest active persona for the model
      const persona = await PersonaProfile.getLatestVersion(modelId);

      if (!persona) {
        return null; // No persona available
      }

      // Extract key elements for caption generation
      const context = {
        personaId: persona._id,
        version: persona.version,

        // Core persona elements for caption context
        tone: persona.communicationStyle?.tone || null,
        vocabulary: persona.communicationStyle?.vocabulary || null,
        emojiUsage:
          persona.communicationStyle?.emojiUsage || "Moderate emoji usage",
        hashtagStrategy: persona.communicationStyle?.hashtagStrategy || null,

        // Top 3 interests for content relevance
        topInterests: persona.interests?.slice(0, 3) || [],

        // Communication preferences
        engagementStyle: persona.behavioral?.engagementStyle || null,
        postingStyle: persona.behavioral?.postingStyle || null,

        // Target audience context
        demographics: {
          age: persona.demographics?.age || null,
          location: persona.demographics?.location || null,
        },

        // Brand voice elements
        brandValues: persona.psychographics?.values?.slice(0, 3) || [],
        personality: persona.psychographics?.personality?.slice(0, 2) || [],

        // Goals for content alignment
        goals: persona.goals?.slice(0, 2) || [],

        // Brief persona summary for context
        personaSummary: persona.personaText?.substring(0, 200) || null,
      };

      return context;
    } catch (error) {
      console.error("Error fetching persona context for captions:", error);
      throw new ApiError(500, "Failed to retrieve persona context");
    }
  }

  /**
   * Build persona-enhanced prompt for caption generation
   * Integrates persona context into AI prompts
   */
  static buildPersonaEnhancedCaptionPrompt(
    basePrompt,
    personaContext,
    mediaType,
    style,
    captionCount,
  ) {
    if (!personaContext) {
      return basePrompt; // Return original prompt if no persona available
    }

    let enhancedPrompt = basePrompt;

    // Add persona context section
    enhancedPrompt += "\n\nPERSONA CONTEXT:";

    if (personaContext.personaSummary) {
      enhancedPrompt += `\nTarget Audience: ${personaContext.personaSummary}`;
    }

    if (personaContext.tone) {
      enhancedPrompt += `\nBrand Tone: ${personaContext.tone}`;
    }

    if (personaContext.topInterests.length > 0) {
      enhancedPrompt += `\nKey Interests: ${personaContext.topInterests.join(
        ", ",
      )}`;
    }

    if (personaContext.emojiUsage) {
      enhancedPrompt += `\nEmoji Style: ${personaContext.emojiUsage}`;
    }

    if (personaContext.hashtagStrategy) {
      enhancedPrompt += `\nHashtag Approach: ${personaContext.hashtagStrategy}`;
    }

    if (personaContext.brandValues.length > 0) {
      enhancedPrompt += `\nBrand Values: ${personaContext.brandValues.join(
        ", ",
      )}`;
    }

    if (personaContext.goals.length > 0) {
      enhancedPrompt += `\nContent Goals: ${personaContext.goals.join(", ")}`;
    }

    // Add persona-specific instructions
    enhancedPrompt += "\n\nINSTRUCTIONS:";
    enhancedPrompt +=
      "\n- Align captions with the target audience persona described above";
    enhancedPrompt +=
      "\n- Match the specified brand tone and emoji usage style";
    enhancedPrompt +=
      "\n- Incorporate relevant interests and brand values naturally";
    enhancedPrompt +=
      "\n- Ensure hashtag strategy aligns with the persona's approach";
    enhancedPrompt += "\n- Keep content goals in mind when crafting messaging";

    return enhancedPrompt;
  }

  /**
   * Extract persona influence metadata for tracking
   * Returns which persona fields influenced the generation
   */
  static extractPersonaInfluence(personaContext) {
    if (!personaContext) {
      return {
        toneInfluence: 0,
        interestsInfluence: 0,
        goalsInfluence: 0,
        demographicsInfluence: 0,
        behavioralInfluence: 0,
      };
    }

    const influence = {
      toneInfluence: 0,
      interestsInfluence: 0,
      goalsInfluence: 0,
      demographicsInfluence: 0,
      behavioralInfluence: 0,
    };

    // Calculate influence percentages based on available data
    if (
      personaContext.tone ||
      personaContext.emojiUsage ||
      personaContext.hashtagStrategy
    ) {
      influence.toneInfluence = 85; // High influence when tone data available
    }

    if (personaContext.topInterests.length > 0) {
      influence.interestsInfluence = personaContext.topInterests.length * 25; // 25% per interest, max 75%
    }

    if (personaContext.goals.length > 0) {
      influence.goalsInfluence = personaContext.goals.length * 35; // 35% per goal, max 70%
    }

    if (
      personaContext.demographics.age ||
      personaContext.demographics.location
    ) {
      influence.demographicsInfluence = 60; // Moderate influence
    }

    if (personaContext.engagementStyle || personaContext.postingStyle) {
      influence.behavioralInfluence = 70; // High influence when behavioral data available
    }

    return influence;
  }

  /**
   * Get persona selection options for campaigns
   * Returns available personas for a model with metadata
   */
  static async getPersonaSelectionOptions(modelId, agencyId) {
    try {
      console.log(
        "=== PersonaIntegrationService.getPersonaSelectionOptions ===",
      );
      console.log("Input params:", { modelId, agencyId });

      // Use the same logic as PersonaService.getPersonasForAgency to ensure consistency
      console.log("🔍 Calling PersonaService.getPersonasForAgency...");
      const PersonaService = (await import("./personaService.js")).default;
      const personas = await PersonaService.getPersonasForAgency(agencyId, {});

      console.log("✅ Found personas using PersonaService:", personas.length);

      if (personas.length === 0) {
        console.log("❌ No personas found for agency:", agencyId);
        return [];
      }

      // Transform personas for caption generator compatibility
      const result = personas.map((persona) => ({
        _id: persona._id,
        name: `${persona.model?.username || "Model"} Persona`,
        description: persona.content?.substring(0, 100) + "...",
        audienceType: persona.tags?.[0] || "General",
        content: persona.content, // Full content for caption generation
        tags: persona.tags || [],
        demographics: {
          age: persona.demographics?.age,
          gender: persona.demographics?.gender || "All",
        },
      }));

      console.log("📤 Returning transformed persona options:", result.length);
      return result;
    } catch (error) {
      console.error("❌ Error fetching persona selection options:", error);
      throw new ApiError(500, "Failed to retrieve persona options");
    }
  }

  /**
   * Validate persona for caption generation
   * Checks if persona has sufficient data for meaningful integration
   */
  static validatePersonaForCaption(personaContext) {
    if (!personaContext) {
      return { isValid: false, reason: "No persona available" };
    }

    const hasMinimumData =
      personaContext.tone ||
      personaContext.topInterests.length > 0 ||
      personaContext.emojiUsage;

    if (!hasMinimumData) {
      return {
        isValid: false,
        reason: "Persona lacks sufficient data for caption enhancement",
      };
    }

    return { isValid: true };
  }

  /**
   * Track persona usage in caption generation
   * Updates usage statistics for analytics
   */
  static async trackPersonaUsage(personaId, context = "caption_generation") {
    try {
      if (!personaId) return;

      const persona = await PersonaProfile.findById(personaId);
      if (persona) {
        await persona.incrementUsage();

        // Log usage for analytics if PersonaUsageLog exists
        try {
          const PersonaUsageLog = mongoose.model("PersonaUsageLog");
          await PersonaUsageLog.create({
            personaId,
            modelId: persona.modelId,
            agencyId: persona.agencyId,
            usageContext: context,
            usageDate: new Date(),
          });
        } catch (logError) {
          // PersonaUsageLog might not exist, continue without logging
          console.log("PersonaUsageLog not available:", logError.message);
        }
      }
    } catch (error) {
      console.error("Error tracking persona usage:", error);
      // Don't throw error, just log it
    }
  }
}

export default PersonaIntegrationService;
