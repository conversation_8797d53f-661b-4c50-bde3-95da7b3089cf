import React from "react";

const ContractsIcon = ({ className }) => {
  return (
    <svg
      width="100%"
      height="100%"
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <defs>
        {/* Background gradient - dark purple */}
        <radialGradient id="contractsBgGrad" cx="50%" cy="30%" r="80%">
          <stop offset="0%" stopColor="#2D1B69" />
          <stop offset="50%" stopColor="#1A1B4A" />
          <stop offset="100%" stopColor="#0F1B3C" />
        </radialGradient>

        {/* Document border gradient - cyan to magenta */}
        <linearGradient
          id="contractsDocGrad"
          x1="0%"
          y1="0%"
          x2="100%"
          y2="100%"
        >
          <stop offset="0%" stopColor="#00E5FF" />
          <stop offset="30%" stopColor="#2196F3" />
          <stop offset="70%" stopColor="#9C27B0" />
          <stop offset="100%" stopColor="#E91E63" />
        </linearGradient>

        {/* Text lines gradient - blue to purple */}
        <linearGradient
          id="contractsTextGrad"
          x1="0%"
          y1="0%"
          x2="100%"
          y2="0%"
        >
          <stop offset="0%" stopColor="#2196F3" />
          <stop offset="100%" stopColor="#9C27B0" />
        </linearGradient>

        {/* Pen gradient - purple to magenta */}
        <linearGradient
          id="contractsPenGrad"
          x1="0%"
          y1="0%"
          x2="100%"
          y2="100%"
        >
          <stop offset="0%" stopColor="#9C27B0" />
          <stop offset="100%" stopColor="#E91E63" />
        </linearGradient>

        {/* Signature gradient - purple */}
        <linearGradient
          id="contractsSignatureGrad"
          x1="0%"
          y1="0%"
          x2="100%"
          y2="100%"
        >
          <stop offset="0%" stopColor="#673AB7" />
          <stop offset="100%" stopColor="#9C27B0" />
        </linearGradient>

        {/* Neon glow filter */}
        <filter id="contractsGlow" x="-50%" y="-50%" width="200%" height="200%">
          <feGaussianBlur stdDeviation="8" result="blur" />
          <feMerge>
            <feMergeNode in="blur" />
            <feMergeNode in="SourceGraphic" />
          </feMerge>
        </filter>
      </defs>

      {/* Rounded square background */}
      <rect
        x="32"
        y="32"
        width="960"
        height="960"
        rx="180"
        ry="180"
        fill="url(#contractsBgGrad)"
      />

      {/* Document container */}
      <rect
        x="220"
        y="180"
        width="584"
        height="664"
        rx="40"
        ry="40"
        fill="none"
        stroke="url(#contractsDocGrad)"
        strokeWidth="20"
        filter="url(#contractsGlow)"
      />

      {/* Text lines in document - properly positioned */}
      <g filter="url(#contractsGlow)">
        {/* First text line */}
        <rect
          x="280"
          y="280"
          width="300"
          height="16"
          rx="8"
          fill="url(#contractsTextGrad)"
        />

        {/* Second text line */}
        <rect
          x="280"
          y="340"
          width="260"
          height="16"
          rx="8"
          fill="url(#contractsTextGrad)"
        />
      </g>

      {/* Pen/Stylus - positioned in bottom right */}
      <g
        transform="translate(620, 620) rotate(45)"
        filter="url(#contractsGlow)"
      >
        {/* Pen body */}
        <rect
          x="0"
          y="-12"
          width="120"
          height="24"
          rx="12"
          ry="12"
          fill="none"
          stroke="url(#contractsPenGrad)"
          strokeWidth="16"
        />

        {/* Pen tip */}
        <polygon
          points="120,-8 150,0 120,8"
          fill="none"
          stroke="url(#contractsPenGrad)"
          strokeWidth="12"
          strokeLinejoin="round"
        />

        {/* Pen clip */}
        <rect
          x="15"
          y="-16"
          width="6"
          height="32"
          rx="3"
          fill="none"
          stroke="url(#contractsPenGrad)"
          strokeWidth="6"
        />
      </g>

      {/* Signature line - positioned in bottom left */}
      <g transform="translate(280, 680)" filter="url(#contractsGlow)">
        {/* Wavy signature stroke */}
        <path
          d="M 0 0 Q 20 -12 40 0 T 80 0 Q 100 8 120 0"
          fill="none"
          stroke="url(#contractsSignatureGrad)"
          strokeWidth="12"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </svg>
  );
};

export default ContractsIcon;
