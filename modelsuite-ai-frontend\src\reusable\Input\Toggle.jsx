import React from "react";
import { cn } from "@/lib/utils";

const Toggle = ({
  checked = false,
  onChange,
  label,
  disabled = false,
  size = "default",
  variant = "default",
  className,
  ...props
}) => {
  const getSizeClasses = () => {
    switch (size) {
      case "sm":
        return "h-4 w-7";
      case "lg":
        return "h-6 w-11";
      default:
        return "h-5 w-9";
    }
  };

  const getThumbSizeClasses = () => {
    switch (size) {
      case "sm":
        return "size-3";
      case "lg":
        return "size-5";
      default:
        return "size-4";
    }
  };

  return (
    <div className="flex items-center gap-3">
      {label && (
        <span className="text-sm font-medium text-gray-200 select-none">
          {label}
        </span>
      )}
      <button
        type="button"
        role="switch"
        aria-checked={checked}
        disabled={disabled}
        onClick={() => !disabled && onChange?.(!checked)}
        className={cn(
          "inline-flex shrink-0 items-center rounded-full border-2 border-transparent transition-all duration-200 outline-none focus-visible:ring-2 focus-visible:ring-gray-400 focus-visible:ring-offset-2 focus-visible:ring-offset-[#0f0f0f]",
          getSizeClasses(),
          checked ? "bg-white" : "bg-gray-700 hover:bg-gray-600",
          disabled && "opacity-50 cursor-not-allowed",
          !disabled && "cursor-pointer",
          className
        )}
        {...props}
      >
        <span
          className={cn(
            "pointer-events-none block rounded-full transition-transform duration-200 ease-in-out",
            getThumbSizeClasses(),
            checked
              ? "translate-x-full bg-[#0f0f0f]"
              : "translate-x-0 bg-gray-300",
            checked && size === "sm" && "translate-x-4",
            checked && size === "default" && "translate-x-4",
            checked && size === "lg" && "translate-x-5"
          )}
        />
      </button>
    </div>
  );
};

export default Toggle;
