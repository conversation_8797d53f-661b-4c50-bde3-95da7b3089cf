import SupportSystem from "../../models/supportsystem/supportsystem.js";
import ModelUser from "../../models/model.js";
import Chatfrommodeltostaff from "../../models/supportsystem/chatfrommodeltostaff.js";
import Notificationfromstafftomodel from "../../models/supportsystem/notifications.js";
import { Server } from "socket.io";
import express from "express";
import mongoose from "mongoose";
export const createSupportStaff = async (req, res) => {
  try {
    // Transform schedule array into availability object
    const formatSchedule = (scheduleArray) => {
      const availability = {};
      scheduleArray.forEach(({ day, start, end }) => {
        availability[day] = { start, end };
      });
      return availability;
    };

    const {
      modelid,
      supportContact,
      agencyid,
      bio,
      customRole,
      internalDept,
      supportareas,
      languages,
      commChannel,
      timezone,
      schedule,
    } = req.body;

    const staff = await SupportSystem.create({
      modelid: modelid,
      supportContact: supportContact,
      agencyid,
      title: customRole,
      bio,
      internalDept,
      supportareas: supportareas,
      languages: languages,
      commChannel: commChannel,
      timezone,
      availability: formatSchedule(schedule),
    });

    return res.status(201).json({ success: true, staff });
  } catch (error) {
    return res.status(400).json({ success: false, message: error.message });
  }
};

export const deleteall = async (req, res) => {
  try {
  } catch (error) {}
};
export const updateSupportStaff = async (req, res) => {
  try {
    const { id } = req.params;
    const updated = await SupportSystem.findByIdAndUpdate(id, req.body, {
      new: true,
      runValidators: true,
    });
    if (!updated) return res.status(404).json({ message: "Staff not found" });
    return res.json({ success: true, staff: updated });
  } catch (error) {
    return res.status(400).json({ success: false, message: error.message });
  }
};
export const getAllSupportStaff = async (req, res) => {
  try {
    console.log("====================================");
    console.log("🚀 Fetching all support staff");
    console.log("====================================");
    const staffList = await SupportSystem.find();
    return res.json({
      count: staffList.length,
      success: true,
      staff: staffList,
    });
  } catch (error) {
    return res.status(500).json({ success: false, message: error.message });
  }
};
export const getSupportStaffByModelId = async (req, res) => {
  try {
    const { modelid } = req.params;

    console.log("====================================");
    console.log("🚀 Fetching support staff for modelid:", modelid);
    console.log("====================================");

    const staffList = await SupportSystem.find({ modelid });

    return res.json({
      count: staffList.length,
      success: true,
      staff: staffList,
    });
  } catch (error) {
    return res.status(500).json({ success: false, message: error.message });
  }
};
export const getSupportStaffById = async (req, res) => {
  try {
    const { id } = req.params;
    const staff = await SupportSystem.findById(id);
    if (!staff) return res.status(404).json({ message: "Staff not found" });
    return res.json({ success: true, staff });
  } catch (error) {
    return res.status(400).json({ success: false, message: error.message });
  }
};
export const deleteSupportStaff = async (req, res) => {
  try {
    const { id } = req.params;
    const deleted = await SupportSystem.findByIdAndDelete(id);
    if (!deleted) return res.status(404).json({ message: "Staff not found" });
    return res.json({ success: true, message: "Staff deleted successfully" });
  } catch (error) {
    return res.status(400).json({ success: false, message: error.message });
  }
};
export const filterBySupportArea = async (req, res) => {
  try {
    const { area } = req.query;
    const staff = await SupportSystem.find({ support_areas: area });
    return res.json({ success: true, staff });
  } catch (error) {
    return res.status(500).json({ success: false, message: error.message });
  }
};
export const getAllPrimarySupportAreas = async (req, res) => {
  try {
    const staffList = await SupportSystem.find({}, { support_areas: 1 });

    const primaryAreas = staffList
      .map((staff) => staff.support_areas?.[0])
      .filter(Boolean);

    return res.json({ success: true, primarySupportAreas: primaryAreas });
  } catch (error) {
    return res.status(500).json({ success: false, message: error.message });
  }
};
// ================= SOCKET HANDLER ===================
export const registerSocketHandlers = (io) => {
  io.on("connection", (socket) => {
    console.log("🔌 New client connected: ", socket.id);

    socket.on("send_message", async ({ senderId, receiverId, message }) => {
      try {
        const chat = await Chatfrommodeltostaff.create({
          senderId,
          receiverId,
          message,
        });
        io.to(receiverId).emit("receive_message", chat);
      } catch (error) {
        console.error("Chatfrommodeltostaff error:", error);
      }
    });

    socket.on(
      "send_notification",
      async ({ sender, receiver, message, type }) => {
        try {
          const notification = await Notificationfromstafftomodel.create({
            sender,
            receiver,
            message,
            type,
          });
          io.to(receiver).emit("receive_notification", notification);
        } catch (error) {
          console.error("Notificationfromstafftomodel error:", error);
        }
      },
    );
  });
};

// ================= CHAT CONTROLLERS ===================
export const getRecentChatWithModel = async (req, res) => {
  try {
    const { modelId, staffId } = req.params;
    const recent = await Chatfrommodeltostaff.findOne({
      $or: [
        { senderId: modelId, receiverId: staffId },
        { senderId: staffId, receiverId: modelId },
      ],
    })
      .sort({ timestamp: -1 })
      .limit(1);

    return res.json({ success: true, recent });
  } catch (error) {
    return res.status(500).json({ success: false, message: error.message });
  }
};

export const countUnseenMessages = async (req, res) => {
  try {
    const { fromModelId, toStaffId } = req.params;
    const count = await Chatfrommodeltostaff.countDocuments({
      senderId: fromModelId,
      receiverId: toStaffId,
      read: false,
    });

    return res.json({ success: true, count });
  } catch (error) {
    return res.status(500).json({ success: false, message: error.message });
  }
};

// ================= NOTIFICATION CONTROLLERS ===================
export const getRecentNotificationFromStaff = async (req, res) => {
  try {
    const { staffId, modelId } = req.params;
    const recent = await Notificationfromstafftomodel.findOne({
      sender: staffId,
      receiver: modelId,
    })
      .sort({ createdAt: -1 })
      .limit(1);

    return res.json({ success: true, recent });
  } catch (error) {
    return res.status(500).json({ success: false, message: error.message });
  }
};

export const countUnseenNotifications = async (req, res) => {
  try {
    const { fromStaffId, toModelId } = req.params;
    const count = await Notificationfromstafftomodel.countDocuments({
      sender: fromStaffId,
      receiver: toModelId,
      read: false,
    });

    return res.json({ success: true, count });
  } catch (error) {
    return res.status(500).json({ success: false, message: error.message });
  }
};
// Mark all messages as read (from model to staff)
export const markMessagesAsRead = async (req, res) => {
  const { fromModelId, toStaffId } = req.params;
  try {
    await Chatfrommodeltostaff.updateMany(
      { senderId: fromModelId, receiverId: toStaffId, read: false },
      { $set: { read: true } },
    );
    res.json({ success: true, message: "Messages marked as read" });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
};

// Mark all notifications as read
export const markNotificationsAsRead = async (req, res) => {
  const { fromStaffId, toModelId } = req.params;
  try {
    await Notificationfromstafftomodel.updateMany(
      { sender: fromStaffId, receiver: toModelId, read: false },
      { $set: { read: true } },
    );
    res.json({ success: true, message: "Notifications marked as read" });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
};
