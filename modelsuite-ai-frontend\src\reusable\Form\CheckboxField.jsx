import * as React from "react";
import { cn } from "@/lib/utils";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";

const CheckboxField = ({
  control,
  name,
  label,
  description,
  disabled = false,
  required = false,
  options = [], // For multiple checkboxes
  variant = "single", // "single", "multiple", "group"
  layout = "vertical", // "vertical", "horizontal", "grid"
  gridCols = 2, // For grid layout
  showSelectAll = false, // For multiple checkboxes
  className,
  ...props
}) => {
  // Single checkbox
  const renderSingleCheckbox = (field) => (
    <div className="flex items-center space-x-2">
      <Checkbox
        id={name}
        checked={field.value}
        onCheckedChange={field.onChange}
        disabled={disabled}
        className="border-gray-600 data-[state=checked]:bg-gray-600 data-[state=checked]:border-gray-500"
      />
      {label && (
        <FormLabel
          htmlFor={name}
          className="text-sm font-medium text-gray-300 cursor-pointer"
        >
          {label}
          {required && <span className="text-red-400 ml-1">*</span>}
        </FormLabel>
      )}
    </div>
  );

  // Multiple checkboxes
  const renderMultipleCheckboxes = (field) => {
    const selectedValues = field.value || [];

    const handleSelectAll = () => {
      if (selectedValues.length === options.length) {
        field.onChange([]);
      } else {
        field.onChange(options.map((option) => option.value));
      }
    };

    const handleOptionChange = (optionValue, checked) => {
      const currentValues = field.value || [];
      if (checked) {
        field.onChange([...currentValues, optionValue]);
      } else {
        field.onChange(currentValues.filter((value) => value !== optionValue));
      }
    };

    const getLayoutClass = () => {
      switch (layout) {
        case "horizontal":
          return "flex flex-wrap gap-4";
        case "grid":
          return `grid grid-cols-${gridCols} gap-4`;
        default:
          return "space-y-3";
      }
    };

    return (
      <div className="space-y-3">
        {/* Header with Select All */}
        <div className="flex items-center justify-between">
          {label && (
            <FormLabel className="text-sm font-medium text-gray-300">
              {label}
              {required && <span className="text-red-400 ml-1">*</span>}
            </FormLabel>
          )}

          {showSelectAll && options.length > 0 && (
            <div className="flex items-center gap-2">
              <Badge
                variant="secondary"
                className="bg-gray-700 text-gray-200 text-xs"
              >
                {selectedValues.length}/{options.length}
              </Badge>
              <button
                type="button"
                onClick={handleSelectAll}
                className="text-xs text-gray-400 hover:text-gray-300 underline"
              >
                {selectedValues.length === options.length
                  ? "Deselect All"
                  : "Select All"}
              </button>
            </div>
          )}
        </div>

        {/* Options */}
        <div className={getLayoutClass()}>
          {options.map((option) => {
            const isChecked = selectedValues.includes(option.value);
            const optionId = `${name}-${option.value}`;

            return (
              <div
                key={option.value}
                className={cn(
                  "flex items-center space-x-2 p-2 rounded border border-transparent",
                  "hover:border-gray-600 hover:bg-gray-800/30 transition-colors",
                  isChecked && "border-gray-600/50 bg-gray-600/10"
                )}
              >
                <Checkbox
                  id={optionId}
                  checked={isChecked}
                  onCheckedChange={(checked) =>
                    handleOptionChange(option.value, checked)
                  }
                  disabled={disabled || option.disabled}
                  className="border-gray-600 data-[state=checked]:bg-gray-600 data-[state=checked]:border-gray-500"
                />
                <div className="flex-1 min-w-0">
                  <FormLabel
                    htmlFor={optionId}
                    className={cn(
                      "text-sm font-medium cursor-pointer",
                      option.disabled ? "text-gray-500" : "text-gray-300"
                    )}
                  >
                    <div className="flex items-center gap-2">
                      {option.icon && <span>{option.icon}</span>}
                      <span className="truncate">{option.label}</span>
                      {option.badge && (
                        <Badge variant="outline" className="text-xs">
                          {option.badge}
                        </Badge>
                      )}
                    </div>
                    {option.description && (
                      <div className="text-xs text-gray-400 font-normal mt-1">
                        {option.description}
                      </div>
                    )}
                  </FormLabel>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  // Group checkboxes (for boolean fields)
  const renderGroupCheckboxes = (field) => {
    const handleGroupChange = (optionKey, checked) => {
      const currentValue = field.value || {};
      field.onChange({
        ...currentValue,
        [optionKey]: checked,
      });
    };

    const getLayoutClass = () => {
      switch (layout) {
        case "horizontal":
          return "flex flex-wrap gap-4";
        case "grid":
          return `grid grid-cols-${gridCols} gap-4`;
        default:
          return "space-y-3";
      }
    };

    return (
      <div className="space-y-3">
        {label && (
          <FormLabel className="text-sm font-medium text-gray-300">
            {label}
            {required && <span className="text-red-400 ml-1">*</span>}
          </FormLabel>
        )}

        <div className={getLayoutClass()}>
          {options.map((option) => {
            const isChecked = field.value?.[option.key] || false;
            const optionId = `${name}-${option.key}`;

            return (
              <div
                key={option.key}
                className={cn(
                  "flex items-center space-x-2 p-2 rounded border border-transparent",
                  "hover:border-gray-600 hover:bg-gray-800/30 transition-colors",
                  isChecked && "border-gray-600/50 bg-gray-600/10"
                )}
              >
                <Checkbox
                  id={optionId}
                  checked={isChecked}
                  onCheckedChange={(checked) =>
                    handleGroupChange(option.key, checked)
                  }
                  disabled={disabled || option.disabled}
                  className="border-gray-600 data-[state=checked]:bg-gray-600 data-[state=checked]:border-gray-500"
                />
                <FormLabel
                  htmlFor={optionId}
                  className={cn(
                    "text-sm font-medium cursor-pointer flex-1",
                    option.disabled ? "text-gray-500" : "text-gray-300"
                  )}
                >
                  <div className="flex items-center gap-2">
                    {option.icon && <span>{option.icon}</span>}
                    <span>{option.label}</span>
                    {option.badge && (
                      <Badge variant="outline" className="text-xs">
                        {option.badge}
                      </Badge>
                    )}
                  </div>
                  {option.description && (
                    <div className="text-xs text-gray-400 font-normal mt-1">
                      {option.description}
                    </div>
                  )}
                </FormLabel>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className={cn("space-y-3", className)}>
          <FormControl>
            <div>
              {variant === "single" && renderSingleCheckbox(field)}
              {variant === "multiple" && renderMultipleCheckboxes(field)}
              {variant === "group" && renderGroupCheckboxes(field)}
            </div>
          </FormControl>

          {description && (
            <FormDescription className="text-xs text-gray-400">
              {description}
            </FormDescription>
          )}

          <FormMessage className="text-xs text-red-400" />
        </FormItem>
      )}
      {...props}
    />
  );
};

export default CheckboxField;
