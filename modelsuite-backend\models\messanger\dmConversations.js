import mongoose from "mongoose";

const dmConversationSchema = new mongoose.Schema({
  type: { type: String, default: "dm" },
  members: [
    {
      userId: {
        type: mongoose.Schema.Types.ObjectId,
        required: true,
        ref: "User",
      },
      joinedAt: { type: Date, default: Date.now },
      pinned: { type: Boolean, default: false },
      language: { type: String, defalut: null },
      autoTranslate: { type: Boolean, default: false },
    },
  ],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: "User",
  },
  createdAt: { type: Date, default: Date.now },
});

export default mongoose.model("DmConversation", dmConversationSchema);
