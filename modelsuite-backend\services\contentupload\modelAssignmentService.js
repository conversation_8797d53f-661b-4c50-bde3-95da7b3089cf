import ModelCategoryAssignment from "../../models/contentupload/ModelCategoryAssignment.js";
import ContentCategory from "../../models/contentupload/ContentCategory.js";
import ModelUser from "../../models/model.js";
import Agency from "../../models/agency.js";
import { ApiError } from "../../utils/ApiError.js";

/**
 * Model Assignment Service - Handles model-category assignments,
 * validation, bulk operations, and assignment history tracking
 */
class ModelAssignmentService {
  /**
   * Assign a model to a category
   * @param {Object} assignmentData - Assignment data
   * @param {string} assignmentData.agencyId - Agency ID
   * @param {string} assignmentData.modelId - Model ID
   * @param {string} assignmentData.categoryId - Category ID
   * @param {string} assignmentData.assignedBy - User who assigned
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Created assignment
   */
  static async assignModelToCategory(assignmentData, options = {}) {
    try {
      // Validate input data
      await this.validateAssignmentData(assignmentData);

      // Check if assignment already exists
      const existingAssignment = await ModelCategoryAssignment.findOne({
        agencyId: assignmentData.agencyId,
        modelId: assignmentData.modelId,
        categoryId: assignmentData.categoryId,
      });

      if (existingAssignment) {
        if (existingAssignment.isActive) {
          throw new ApiError(400, "Model is already assigned to this category");
        } else {
          // Reactivate existing assignment
          existingAssignment.isActive = true;
          existingAssignment.assignedBy = assignmentData.assignedBy;
          existingAssignment.assignedAt = new Date();
          existingAssignment.deactivatedAt = null;
          existingAssignment.deactivatedBy = null;
          existingAssignment.deactivationReason = null;

          if (options.priority) existingAssignment.priority = options.priority;
          if (options.customReminderFrequency)
            existingAssignment.customReminderFrequency =
              options.customReminderFrequency;
          if (options.customReminderType)
            existingAssignment.customReminderType = options.customReminderType;
          if (options.notes) existingAssignment.notes = options.notes;

          await existingAssignment.save();
          return existingAssignment;
        }
      }

      // Create new assignment
      const assignment = new ModelCategoryAssignment({
        ...assignmentData,
        ...options,
      });

      await assignment.save();

      // Populate references for response
      await assignment.populate([
        { path: "modelId", select: "firstName lastName email" },
        {
          path: "categoryId",
          select: "label platform reminderFrequency reminderType",
        },
        { path: "assignedBy", select: "name email" },
      ]);

      return assignment;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      throw new ApiError(
        500,
        `Failed to assign model to category: ${error.message}`,
      );
    }
  }

  /**
   * Bulk assign models to categories
   * @param {Array} assignments - Array of assignment data
   * @param {string} assignedBy - User who assigned
   * @returns {Promise<Object>} Bulk assignment results
   */
  static async bulkAssignModels(assignments, assignedBy) {
    const results = {
      successful: [],
      failed: [],
      reactivated: [],
    };

    for (const assignmentData of assignments) {
      try {
        const assignment = await this.assignModelToCategory({
          ...assignmentData,
          assignedBy,
        });

        if (
          assignment.assignedAt.getTime() !== assignment.createdAt.getTime()
        ) {
          results.reactivated.push(assignment);
        } else {
          results.successful.push(assignment);
        }
      } catch (error) {
        results.failed.push({
          assignmentData,
          error: error.message,
        });
      }
    }

    return results;
  }

  /**
   * Update an existing assignment
   * @param {string} assignmentId - Assignment ID
   * @param {Object} updateData - Data to update
   * @param {string} updatedBy - User who updated
   * @returns {Promise<Object>} Updated assignment
   */
  static async updateAssignment(assignmentId, updateData, updatedBy) {
    try {
      const assignment = await ModelCategoryAssignment.findById(assignmentId);
      if (!assignment) {
        throw new ApiError(404, "Assignment not found");
      }

      if (!assignment.isActive) {
        throw new ApiError(400, "Cannot update inactive assignment");
      }

      // Update allowed fields
      const allowedFields = [
        "priority",
        "customReminderFrequency",
        "customReminderType",
        "notes",
      ];

      allowedFields.forEach((field) => {
        if (updateData[field] !== undefined) {
          assignment[field] = updateData[field];
        }
      });

      assignment.lastModifiedBy = updatedBy;
      await assignment.save();

      await assignment.populate([
        { path: "modelId", select: "firstName lastName email" },
        {
          path: "categoryId",
          select: "label platform reminderFrequency reminderType",
        },
      ]);

      return assignment;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      throw new ApiError(500, `Failed to update assignment: ${error.message}`);
    }
  }

  /**
   * Remove (deactivate) an assignment
   * @param {string} assignmentId - Assignment ID
   * @param {string} deactivatedBy - User who deactivated
   * @param {string} reason - Reason for deactivation
   * @returns {Promise<Object>} Deactivated assignment
   */
  static async removeAssignment(assignmentId, deactivatedBy, reason = null) {
    try {
      const assignment = await ModelCategoryAssignment.findById(assignmentId);
      if (!assignment) {
        throw new ApiError(404, "Assignment not found");
      }

      if (!assignment.isActive) {
        throw new ApiError(400, "Assignment is already inactive");
      }

      await assignment.deactivate(deactivatedBy, reason);
      return assignment;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      throw new ApiError(500, `Failed to remove assignment: ${error.message}`);
    }
  }

  /**
   * Get assignments for a model
   * @param {string} modelId - Model ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Model assignments
   */
  static async getModelAssignments(modelId, options = {}) {
    try {
      const { includeInactive = false, agencyId = null } = options;

      const query = { modelId };
      if (!includeInactive) {
        query.isActive = true;
      }
      if (agencyId) {
        query.agencyId = agencyId;
      }

      const assignments = await ModelCategoryAssignment.find(query)
        .populate(
          "categoryId",
          "label platform reminderFrequency reminderType instructionTooltip",
        )
        .populate("agencyId", "name email")
        .sort({ priority: -1, assignedAt: -1 });

      return assignments;
    } catch (error) {
      throw new ApiError(
        500,
        `Failed to get model assignments: ${error.message}`,
      );
    }
  }

  /**
   * Get assignments for an agency
   * @param {string} agencyId - Agency ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Agency assignments
   */
  static async getAgencyAssignments(agencyId, options = {}) {
    try {
      const {
        includeInactive = false,
        modelId = null,
        categoryId = null,
        priority = null,
        overdue = null,
        page = 1,
        limit = 50,
      } = options;

      const query = { agencyId };
      if (!includeInactive) {
        query.isActive = true;
      }
      if (modelId) {
        query.modelId = modelId;
      }
      if (categoryId) {
        query.categoryId = categoryId;
      }
      if (priority) {
        query.priority = priority;
      }
      if (overdue === true) {
        query.nextDueDate = { $lt: new Date() };
      } else if (overdue === false) {
        query.nextDueDate = { $gte: new Date() };
      }

      const skip = (page - 1) * limit;

      const [assignments, total] = await Promise.all([
        ModelCategoryAssignment.find(query)
          .populate("modelId", "firstName lastName email")
          .populate(
            "categoryId",
            "label platform reminderFrequency reminderType",
          )
          .sort({ nextDueDate: 1, priority: -1 })
          .skip(skip)
          .limit(limit),
        ModelCategoryAssignment.countDocuments(query),
      ]);

      return {
        assignments,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      throw new ApiError(
        500,
        `Failed to get agency assignments: ${error.message}`,
      );
    }
  }

  /**
   * Get assignment statistics for an agency
   * @param {string} agencyId - Agency ID
   * @returns {Promise<Object>} Assignment statistics
   */
  static async getAssignmentStats(agencyId) {
    try {
      const [totalActive, overdue, dueSoon, byPriority, byCategory] =
        await Promise.all([
          // Total active assignments
          ModelCategoryAssignment.countDocuments({ agencyId, isActive: true }),

          // Overdue assignments
          ModelCategoryAssignment.countDocuments({
            agencyId,
            isActive: true,
            nextDueDate: { $lt: new Date() },
          }),

          // Due soon (next 3 days)
          ModelCategoryAssignment.countDocuments({
            agencyId,
            isActive: true,
            nextDueDate: {
              $gte: new Date(),
              $lte: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
            },
          }),

          // By priority
          ModelCategoryAssignment.aggregate([
            { $match: { agencyId: agencyId, isActive: true } },
            { $group: { _id: "$priority", count: { $sum: 1 } } },
          ]),

          // By category
          ModelCategoryAssignment.aggregate([
            { $match: { agencyId: agencyId, isActive: true } },
            {
              $lookup: {
                from: "contentcategories",
                localField: "categoryId",
                foreignField: "_id",
                as: "category",
              },
            },
            { $unwind: "$category" },
            {
              $group: {
                _id: {
                  categoryId: "$categoryId",
                  label: "$category.label",
                  platform: "$category.platform",
                },
                count: { $sum: 1 },
                overdue: {
                  $sum: {
                    $cond: [{ $lt: ["$nextDueDate", new Date()] }, 1, 0],
                  },
                },
              },
            },
          ]),
        ]);

      return {
        totalActive,
        overdue,
        dueSoon,
        byPriority: byPriority.reduce((acc, item) => {
          acc[item._id] = item.count;
          return acc;
        }, {}),
        byCategory: byCategory.map((item) => ({
          categoryId: item._id.categoryId,
          label: item._id.label,
          platform: item._id.platform,
          totalAssignments: item.count,
          overdueAssignments: item.overdue,
        })),
      };
    } catch (error) {
      throw new ApiError(
        500,
        `Failed to get assignment statistics: ${error.message}`,
      );
    }
  }

  /**
   * Validate assignment data
   * @param {Object} assignmentData - Assignment data to validate
   * @returns {Promise<void>}
   */
  static async validateAssignmentData(assignmentData) {
    const { agencyId, modelId, categoryId, assignedBy } = assignmentData;

    if (!agencyId || !modelId || !categoryId || !assignedBy) {
      throw new ApiError(
        400,
        "Missing required fields: agencyId, modelId, categoryId, assignedBy",
      );
    }

    // Validate agency exists
    const agency = await Agency.findById(agencyId);
    if (!agency) {
      throw new ApiError(404, "Agency not found");
    }

    // Validate model exists and belongs to agency
    const model = await ModelUser.findById(modelId);
    if (!model) {
      throw new ApiError(404, "Model not found");
    }
    if (model.agencyId.toString() !== agencyId) {
      throw new ApiError(400, "Model does not belong to the specified agency");
    }

    // Validate category exists and belongs to agency
    const category = await ContentCategory.findById(categoryId);
    if (!category) {
      throw new ApiError(404, "Category not found");
    }
    if (category.agencyId.toString() !== agencyId) {
      throw new ApiError(
        400,
        "Category does not belong to the specified agency",
      );
    }
    if (!category.isActive) {
      throw new ApiError(400, "Cannot assign to inactive category");
    }

    // Validate assignedBy user exists
    const assignedByUser = await Agency.findById(assignedBy);
    if (!assignedByUser) {
      throw new ApiError(404, "Assigned by user not found");
    }
  }

  /**
   * Get assignment history for a model
   * @param {string} modelId - Model ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Assignment history
   */
  static async getAssignmentHistory(modelId, options = {}) {
    try {
      const { limit = 50, page = 1 } = options;
      const skip = (page - 1) * limit;

      const history = await ModelCategoryAssignment.find({ modelId })
        .populate("categoryId", "label platform")
        .populate("assignedBy", "name email")
        .populate("deactivatedBy", "name email")
        .sort({ assignedAt: -1 })
        .skip(skip)
        .limit(limit);

      return history;
    } catch (error) {
      throw new ApiError(
        500,
        `Failed to get assignment history: ${error.message}`,
      );
    }
  }
}

export default ModelAssignmentService;
