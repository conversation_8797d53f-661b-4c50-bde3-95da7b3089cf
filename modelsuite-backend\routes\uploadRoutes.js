import express from "express";
import { verifyToken } from "../middlewares/authMiddleware.js";
import checkPermission from "../middlewares/permissionCheck.js";
import upload from "../middlewares/multer.js";
import {
  uploadAttachment,
  getAttachments,
  deleteAttachment,
} from "../controllers/attachmentController.js";

const router = express.Router();

router.post(
  "/profile-photo",
  verifyToken,
  checkPermission("uploads.create"),
  upload.single("image"),
  (req, res) => {
    const imageUrl = req.file.path; // secure Cloudinary URL
    res.status(200).json({ imageUrl });
  },
);

// Note attachments
router.post(
  "/notes/:noteId/attachments",
  upload.single("file"),
  uploadAttachment,
);
router.get(
  "/notes/:noteId/attachments",
  verifyToken,
  checkPermission("uploads.view"),
  getAttachments,
);
router.delete(
  "/notes/:noteId/attachments/:attachmentId",
  verifyToken,
  checkPermission("uploads.delete"),
  deleteAttachment,
);

export default router;
