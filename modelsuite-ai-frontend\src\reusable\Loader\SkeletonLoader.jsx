import * as React from "react";
import { cn } from "@/lib/utils";

const SkeletonLoader = ({
  variant = "text", // "text", "card", "avatar", "button", "image", "table", "custom"
  lines = 3,
  className,
  children,
  ...props
}) => {
  const renderTextSkeleton = () => (
    <div className="space-y-2">
      {Array.from({ length: lines }).map((_, index) => (
        <div
          key={index}
          className={cn(
            "h-4 bg-[#1a1a1a] rounded animate-pulse",
            index === lines - 1 ? "w-3/4" : "w-full"
          )}
        />
      ))}
    </div>
  );

  const renderCardSkeleton = () => (
    <div className="bg-[#0f0f0f] border border-gray-800 rounded-lg p-4 space-y-4">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <div className="h-10 w-10 bg-[#1a1a1a] rounded-full animate-pulse" />
        <div className="flex-1 space-y-2">
          <div className="h-4 bg-[#1a1a1a] rounded animate-pulse w-1/4" />
          <div className="h-3 bg-[#1a1a1a] rounded animate-pulse w-1/2" />
        </div>
      </div>

      {/* Content */}
      <div className="space-y-2">
        {Array.from({ length: 3 }).map((_, index) => (
          <div
            key={index}
            className={cn(
              "h-3 bg-[#1a1a1a] rounded animate-pulse",
              index === 2 ? "w-3/4" : "w-full"
            )}
          />
        ))}
      </div>

      {/* Footer */}
      <div className="flex space-x-2 pt-2">
        <div className="h-8 w-16 bg-[#1a1a1a] rounded animate-pulse" />
        <div className="h-8 w-16 bg-[#1a1a1a] rounded animate-pulse" />
      </div>
    </div>
  );

  const renderAvatarSkeleton = () => (
    <div className="flex items-center space-x-3">
      <div className="h-10 w-10 bg-[#1a1a1a] rounded-full animate-pulse" />
      <div className="flex-1 space-y-2">
        <div className="h-4 bg-[#1a1a1a] rounded animate-pulse w-1/4" />
        <div className="h-3 bg-[#1a1a1a] rounded animate-pulse w-1/2" />
      </div>
    </div>
  );

  const renderButtonSkeleton = () => (
    <div className="h-9 w-20 bg-[#1a1a1a] rounded animate-pulse" />
  );

  const renderImageSkeleton = () => (
    <div className="bg-[#1a1a1a] rounded animate-pulse aspect-video w-full" />
  );

  const renderTableSkeleton = () => (
    <div className="bg-[#0f0f0f] border border-gray-800 rounded-lg overflow-hidden">
      {/* Table Header */}
      <div className="border-b border-gray-800 p-4">
        <div className="grid grid-cols-4 gap-4">
          {Array.from({ length: 4 }).map((_, index) => (
            <div
              key={index}
              className="h-4 bg-[#1a1a1a] rounded animate-pulse"
            />
          ))}
        </div>
      </div>

      {/* Table Rows */}
      {Array.from({ length: 5 }).map((_, rowIndex) => (
        <div
          key={rowIndex}
          className="border-b border-gray-800 p-4 last:border-b-0"
        >
          <div className="grid grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, colIndex) => (
              <div
                key={colIndex}
                className={cn(
                  "h-3 bg-[#1a1a1a] rounded animate-pulse",
                  colIndex === 3 ? "w-1/2" : "w-full"
                )}
              />
            ))}
          </div>
        </div>
      ))}
    </div>
  );

  const renderCustomSkeleton = () => (
    <div className={cn("bg-[#1a1a1a] rounded animate-pulse", className)}>
      {children}
    </div>
  );

  const renderSkeleton = () => {
    switch (variant) {
      case "card":
        return renderCardSkeleton();
      case "avatar":
        return renderAvatarSkeleton();
      case "button":
        return renderButtonSkeleton();
      case "image":
        return renderImageSkeleton();
      case "table":
        return renderTableSkeleton();
      case "custom":
        return renderCustomSkeleton();
      default:
        return renderTextSkeleton();
    }
  };

  return (
    <div
      className={cn("animate-pulse", variant !== "custom" && className)}
      {...props}
    >
      {renderSkeleton()}
    </div>
  );
};

export default SkeletonLoader;
