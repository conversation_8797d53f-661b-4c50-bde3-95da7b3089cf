import express from "express";
import {
  generateCaptions,
  getQuotaStatus,
  getCaptionHistory,
  getPersonaOptions,
  rateCaptionEffectiveness,
  getPersonaAnalytics,
} from "../../controllers/caption/captionController.js";
import { verifyToken } from "../../middlewares/authMiddleware.js";
import { checkModel, checkAgency } from "../../middlewares/roleCheck.js";
import captionRateLimit from "../../middlewares/captionRateLimit.js";
import captionUpload from "../../middlewares/captionUpload.js";

const router = express.Router();

// Helper middleware to check if user is model or agency
const checkModelOrAgency = (req, res, next) => {
  if (req.user && (req.user.role === "model" || req.user.role === "agency")) {
    next();
  } else {
    return res
      .status(403)
      .json({ message: "Access denied: Model or Agency role required" });
  }
};

// All routes require authentication
router.use(verifyToken);
router.use(checkModelOrAgency);

// Generate captions with rate limiting
router.post("/generate", captionRateLimit, generateCaptions);

// Get quota status
router.get("/quota", getQuotaStatus);

// Get caption history
router.get("/history", getCaptionHistory);

// File upload endpoint for direct uploads
router.post("/upload", captionUpload.single("media"), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: "No file uploaded",
      });
    }

    // Determine media type based on Cloudinary resource type
    const mediaType = req.file.resource_type === "video" ? "video" : "image";

    res.status(200).json({
      success: true,
      data: {
        mediaUrl: req.file.path,
        mediaType,
        fileSize: req.file.bytes,
        publicId: req.file.public_id,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: "Upload failed",
    });
  }
});

// Phase 2: Persona integration routes

// Get available personas for caption generation
router.get("/personas", getPersonaOptions);

// Rate caption effectiveness (agencies only)
router.post("/rate-effectiveness", checkAgency, rateCaptionEffectiveness);

// Get persona analytics for captions
router.get("/persona-analytics", getPersonaAnalytics);

export default router;
