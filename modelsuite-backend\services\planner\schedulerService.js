import ContentTask from "../../models/planner/ContentTask.js";
import ContentPlan from "../../models/planner/ContentPlan.js";
import AutoScheduleLog from "../../models/planner/AutoScheduleLog.js";
import ModelUser from "../../models/model.js";
import { insertEventToGoogleCalendar } from "../../utils/googleCalendar.js";
import { ApiError } from "../../utils/ApiError.js";

/**
 * Scheduler Service
 * Handles auto-scheduling, calendar integration, and time slot suggestions
 */
class SchedulerService {
  /**
   * Suggest optimal posting slots based on model's history and preferences
   */
  async suggestPostingSlots(modelId, dateRange, preferences = {}) {
    try {
      const model = await ModelUser.findById(modelId);
      if (!model) {
        throw new ApiError(404, "Model not found");
      }

      const { startDate, endDate } = dateRange;
      const startTime = Date.now();

      // Create auto-schedule log
      const scheduleLog = new AutoScheduleLog({
        modelId,
        agencyId: model.agencyId,
        dateRange: {
          startDate: new Date(startDate),
          endDate: new Date(endDate),
        },
        status: "processing",
        algorithmParams: {
          timeZone: preferences.timezone || "UTC",
          preferredTimes: preferences.preferredTimes || [],
          minGapBetweenPosts: preferences.minGapBetweenPosts || 4,
          maxPostsPerDay: preferences.maxPostsPerDay || 3,
          platformSpecificTiming: preferences.platformSpecificTiming !== false,
          audienceAnalysis: preferences.audienceAnalysis !== false,
        },
        createdBy: model._id,
        createdByType: "ModelUser",
      });

      await scheduleLog.save();

      try {
        // Get historical performance data
        const historicalData = await this.getHistoricalPerformance(modelId);

        // Generate time slots
        const slotSuggestions = await this.generateOptimalSlots(
          dateRange,
          preferences,
          historicalData,
          model,
        );

        // Check for conflicts
        const conflicts = await this.checkSchedulingConflicts(
          modelId,
          slotSuggestions,
        );

        // Update log with results
        scheduleLog.slotSuggestions = slotSuggestions;
        scheduleLog.conflicts = conflicts;
        scheduleLog.status = "completed";
        scheduleLog.performance.processingTime = Date.now() - startTime;

        await scheduleLog.save();

        return {
          logId: scheduleLog._id,
          suggestions: slotSuggestions,
          conflicts,
          metadata: {
            processingTime: scheduleLog.performance.processingTime,
            totalSuggestions: slotSuggestions.length,
            conflictCount: conflicts.length,
          },
        };
      } catch (error) {
        // Update log with error
        scheduleLog.status = "failed";
        scheduleLog.errors.push({
          stage: "suggestion",
          message: error.message,
          timestamp: new Date(),
        });
        await scheduleLog.save();
        throw error;
      }
    } catch (error) {
      if (error instanceof ApiError) throw error;
      throw new ApiError(
        500,
        `Failed to suggest posting slots: ${error.message}`,
      );
    }
  }

  /**
   * Check calendar availability for a model
   */
  async checkCalendarAvailability(modelId, dateRange) {
    try {
      const model = await ModelUser.findById(modelId);
      if (!model) {
        throw new ApiError(404, "Model not found");
      }

      // Check if model has Google Calendar connected
      if (!model.googleAccessToken) {
        return {
          connected: false,
          message: "Google Calendar not connected",
          availability: [],
        };
      }

      // Get existing calendar events (this would integrate with Google Calendar API)
      const existingEvents = await this.getCalendarEvents(model, dateRange);

      // Get existing scheduled content tasks
      const scheduledTasks = await ContentTask.find({
        modelId,
        scheduledTime: {
          $gte: new Date(dateRange.startDate),
          $lte: new Date(dateRange.endDate),
        },
        status: { $in: ["scheduled", "published"] },
        isArchived: false,
      });

      // Combine calendar events and scheduled tasks
      const busySlots = [
        ...existingEvents.map((event) => ({
          start: event.start,
          end: event.end,
          type: "calendar_event",
          title: event.summary,
        })),
        ...scheduledTasks.map((task) => ({
          start: task.scheduledTime,
          end: new Date(task.scheduledTime.getTime() + 60 * 60 * 1000), // 1 hour default
          type: "content_task",
          title: task.title,
        })),
      ];

      return {
        connected: true,
        busySlots,
        availability: this.generateAvailabilitySlots(dateRange, busySlots),
      };
    } catch (error) {
      throw new ApiError(
        500,
        `Failed to check calendar availability: ${error.message}`,
      );
    }
  }

  /**
   * Auto-schedule selected slots to calendar
   */
  async autoScheduleToCalendar(modelId, selectedSlots, logId) {
    try {
      const model = await ModelUser.findById(modelId);
      if (!model) {
        throw new ApiError(404, "Model not found");
      }

      const scheduleLog = await AutoScheduleLog.findById(logId);
      if (!scheduleLog) {
        throw new ApiError(404, "Schedule log not found");
      }

      scheduleLog.calendarSyncStatus = "syncing";
      scheduleLog.selectedSlots = selectedSlots.map((slot) => ({
        ...slot,
        approved: false,
      }));

      await scheduleLog.save();

      const syncResults = {
        totalSlots: selectedSlots.length,
        successfulSyncs: 0,
        failedSyncs: 0,
        calendarEventsCreated: [],
        errors: [],
      };

      // Process each selected slot
      for (const slot of selectedSlots) {
        try {
          // Create calendar event if Google Calendar is connected
          if (model.googleAccessToken && slot.datetime) {
            const eventData = {
              title: `Content Post: ${slot.title || "Scheduled Post"}`,
              description: `Auto-scheduled content posting\nPlatform: ${slot.platform}\nContent Type: ${slot.contentType}`,
              start: new Date(slot.datetime),
              end: new Date(new Date(slot.datetime).getTime() + 30 * 60 * 1000), // 30 minutes
              timezone: model.timezone || "UTC",
              allDay: false,
            };

            const calendarEvent = await insertEventToGoogleCalendar(
              model,
              eventData,
            );

            // Update task with calendar event ID
            if (slot.taskId) {
              const task = await ContentTask.findById(slot.taskId);
              if (task) {
                task.calendarEventId = calendarEvent.id;
                task.calendarSynced = true;
                task.scheduledTime = new Date(slot.datetime);
                task.status = "scheduled";
                await task.save();
              }
            }

            syncResults.calendarEventsCreated.push({
              eventId: calendarEvent.id,
              taskId: slot.taskId,
              datetime: new Date(slot.datetime),
              title: eventData.title,
              status: "created",
            });

            syncResults.successfulSyncs++;
          } else {
            // Just update task scheduling without calendar
            if (slot.taskId) {
              const task = await ContentTask.findById(slot.taskId);
              if (task) {
                task.scheduledTime = new Date(slot.datetime);
                task.status = "scheduled";
                await task.save();
              }
            }
            syncResults.successfulSyncs++;
          }
        } catch (slotError) {
          syncResults.failedSyncs++;
          syncResults.errors.push({
            slot: new Date(slot.datetime),
            error: slotError.message,
            errorCode: slotError.code || "UNKNOWN",
            timestamp: new Date(),
          });
        }
      }

      // Update schedule log
      scheduleLog.syncResults = syncResults;
      scheduleLog.calendarSyncStatus =
        syncResults.failedSyncs === 0 ? "synced" : "partial";

      if (syncResults.failedSyncs > 0) {
        scheduleLog.errors.push({
          stage: "calendar_sync",
          message: `${syncResults.failedSyncs} slots failed to sync`,
          timestamp: new Date(),
        });
      }

      await scheduleLog.save();

      return {
        success: true,
        syncResults,
        message: `${syncResults.successfulSyncs}/${syncResults.totalSlots} slots scheduled successfully`,
      };
    } catch (error) {
      // Update log with error
      if (scheduleLog) {
        scheduleLog.status = "failed";
        scheduleLog.calendarSyncStatus = "failed";
        scheduleLog.errors.push({
          stage: "calendar_sync",
          message: error.message,
          timestamp: new Date(),
        });
        await scheduleLog.save();
      }

      throw new ApiError(
        500,
        `Failed to auto-schedule to calendar: ${error.message}`,
      );
    }
  }

  /**
   * Get scheduling logs for a model
   */
  async getSchedulingLogs(modelId, agencyId, filters = {}) {
    try {
      const query = { modelId, agencyId, isArchived: false };

      // Apply filters
      if (filters.status) {
        query.status = filters.status;
      }

      if (filters.dateFrom || filters.dateTo) {
        query.createdAt = {};
        if (filters.dateFrom) {
          query.createdAt.$gte = new Date(filters.dateFrom);
        }
        if (filters.dateTo) {
          query.createdAt.$lte = new Date(filters.dateTo);
        }
      }

      const logs = await AutoScheduleLog.find(query)
        .populate("planId", "name weekStart")
        .sort({ createdAt: -1 })
        .limit(filters.limit || 20)
        .skip((filters.page - 1) * (filters.limit || 20) || 0);

      const total = await AutoScheduleLog.countDocuments(query);

      return {
        logs,
        pagination: {
          page: filters.page || 1,
          limit: filters.limit || 20,
          total,
          pages: Math.ceil(total / (filters.limit || 20)),
        },
      };
    } catch (error) {
      throw new ApiError(
        500,
        `Failed to get scheduling logs: ${error.message}`,
      );
    }
  }

  /**
   * Get scheduling analytics
   */
  async getSchedulingAnalytics(agencyId, timeframe = 30) {
    try {
      const analytics = await AutoScheduleLog.getSchedulingAnalytics(
        agencyId,
        timeframe,
      );
      const conflictAnalytics = await AutoScheduleLog.findConflictsByType(
        agencyId,
        timeframe,
      );

      return {
        overview: analytics[0] || {
          totalSchedulingRuns: 0,
          successfulRuns: 0,
          totalSlotsProcessed: 0,
          totalSlotsSelected: 0,
          totalCalendarEvents: 0,
          avgProcessingTime: 0,
          avgAcceptanceRate: 0,
          totalConflicts: 0,
          resolvedConflicts: 0,
        },
        conflicts: conflictAnalytics,
        performance: {
          successRate: analytics[0]
            ? Math.round(
                (analytics[0].successfulRuns /
                  analytics[0].totalSchedulingRuns) *
                  100,
              )
            : 0,
          acceptanceRate: analytics[0]
            ? Math.round(
                (analytics[0].totalSlotsSelected /
                  analytics[0].totalSlotsProcessed) *
                  100,
              )
            : 0,
          conflictResolutionRate: analytics[0]
            ? Math.round(
                (analytics[0].resolvedConflicts / analytics[0].totalConflicts) *
                  100,
              )
            : 0,
        },
      };
    } catch (error) {
      throw new ApiError(
        500,
        `Failed to get scheduling analytics: ${error.message}`,
      );
    }
  }

  // Helper methods

  /**
   * Get historical performance data for optimal timing
   */
  async getHistoricalPerformance(modelId) {
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const historicalTasks = await ContentTask.find({
        modelId,
        status: "published",
        createdAt: { $gte: thirtyDaysAgo },
        isArchived: false,
      })
        .select("scheduledTime platforms performance")
        .lean();

      // Analyze performance by time slots
      const timeSlotPerformance = {};

      historicalTasks.forEach((task) => {
        if (task.scheduledTime) {
          const hour = new Date(task.scheduledTime).getHours();
          const dayOfWeek = new Date(task.scheduledTime).getDay();
          const key = `${dayOfWeek}-${hour}`;

          if (!timeSlotPerformance[key]) {
            timeSlotPerformance[key] = {
              count: 0,
              totalEngagement: 0,
              avgEngagementRate: 0,
            };
          }

          timeSlotPerformance[key].count++;
          timeSlotPerformance[key].totalEngagement +=
            task.performance?.totalEngagement || 0;
        }
      });

      // Calculate averages
      Object.keys(timeSlotPerformance).forEach((key) => {
        const slot = timeSlotPerformance[key];
        slot.avgEngagementRate =
          slot.count > 0 ? slot.totalEngagement / slot.count : 0;
      });

      return timeSlotPerformance;
    } catch (error) {
      console.error("Error getting historical performance:", error);
      return {};
    }
  }

  /**
   * Generate optimal time slots based on preferences and historical data
   */
  async generateOptimalSlots(dateRange, preferences, historicalData, model) {
    const slots = [];
    const { startDate, endDate } = dateRange;
    const start = new Date(startDate);
    const end = new Date(endDate);

    const timezone = preferences.timezone || model.timezone || "UTC";
    const minGap = preferences.minGapBetweenPosts || 4; // hours
    const maxPostsPerDay = preferences.maxPostsPerDay || 3;

    // Platform-specific optimal times (industry averages)
    const platformOptimalTimes = {
      instagram: [9, 11, 13, 15, 17, 19], // 9am, 11am, 1pm, 3pm, 5pm, 7pm
      tiktok: [6, 10, 19, 20, 21], // 6am, 10am, 7pm, 8pm, 9pm
      facebook: [9, 13, 15], // 9am, 1pm, 3pm
      twitter: [8, 12, 17, 19], // 8am, 12pm, 5pm, 7pm
      linkedin: [8, 12, 17], // 8am, 12pm, 5pm (business hours)
    };

    // Generate slots for each day
    for (
      let date = new Date(start);
      date <= end;
      date.setDate(date.getDate() + 1)
    ) {
      const daySlots = [];
      const dayOfWeek = date.getDay();

      // Generate potential slots for this day
      for (let hour = 6; hour <= 22; hour++) {
        // 6am to 10pm
        const slotTime = new Date(date);
        slotTime.setHours(hour, 0, 0, 0);

        // Skip if slot is in the past
        if (slotTime <= new Date()) continue;

        // Calculate confidence based on historical data and platform preferences
        let confidence = 50; // base confidence

        // Historical performance boost
        const historicalKey = `${dayOfWeek}-${hour}`;
        if (historicalData[historicalKey]) {
          const performance = historicalData[historicalKey];
          confidence += Math.min(performance.avgEngagementRate * 2, 30);
        }

        // Platform-specific timing boost
        const platforms = preferences.platforms || ["instagram"];
        platforms.forEach((platform) => {
          if (
            platformOptimalTimes[platform] &&
            platformOptimalTimes[platform].includes(hour)
          ) {
            confidence += 15;
          }
        });

        // Preferred times boost
        if (preferences.preferredTimes) {
          const isPreferredTime = preferences.preferredTimes.some(
            (pt) =>
              pt.day ===
                [
                  "sunday",
                  "monday",
                  "tuesday",
                  "wednesday",
                  "thursday",
                  "friday",
                  "saturday",
                ][dayOfWeek] && pt.times.some((t) => t.hour === hour),
          );
          if (isPreferredTime) {
            confidence += 20;
          }
        }

        // Weekend vs weekday adjustment
        if (dayOfWeek === 0 || dayOfWeek === 6) {
          // Weekend
          if (hour >= 10 && hour <= 14) confidence += 10; // Late morning/early afternoon
        } else {
          // Weekday
          if (hour >= 17 && hour <= 20) confidence += 10; // Evening hours
        }

        confidence = Math.min(confidence, 100);

        daySlots.push({
          datetime: slotTime,
          confidence,
          reasoning: this.generateSlotReasoning(
            hour,
            dayOfWeek,
            confidence,
            historicalData[historicalKey],
          ),
          platform: platforms[0], // Default to first platform
          estimatedReach: Math.round(confidence * 10 + Math.random() * 100),
          estimatedEngagement: Math.round(
            confidence * 0.5 + Math.random() * 10,
          ),
        });
      }

      // Select best slots for this day (respecting maxPostsPerDay)
      daySlots.sort((a, b) => b.confidence - a.confidence);
      const selectedDaySlots = daySlots.slice(0, maxPostsPerDay);

      // Apply minimum gap constraint
      const filteredSlots = [];
      let lastSlotTime = null;

      for (const slot of selectedDaySlots) {
        if (
          !lastSlotTime ||
          slot.datetime.getTime() - lastSlotTime.getTime() >=
            minGap * 60 * 60 * 1000
        ) {
          filteredSlots.push(slot);
          lastSlotTime = slot.datetime;
        }
      }

      slots.push(...filteredSlots);
    }

    return slots.sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * Generate reasoning text for slot suggestions
   */
  generateSlotReasoning(hour, dayOfWeek, confidence, historicalData) {
    const dayNames = [
      "Sunday",
      "Monday",
      "Tuesday",
      "Wednesday",
      "Thursday",
      "Friday",
      "Saturday",
    ];
    const dayName = dayNames[dayOfWeek];

    let reasoning = `${dayName} at ${hour}:00 - `;

    if (confidence >= 80) {
      reasoning += "Excellent time slot based on ";
    } else if (confidence >= 60) {
      reasoning += "Good time slot with ";
    } else {
      reasoning += "Moderate performance expected with ";
    }

    if (historicalData && historicalData.count > 0) {
      reasoning += `historical performance (${
        historicalData.count
      } posts, avg engagement: ${historicalData.avgEngagementRate.toFixed(1)})`;
    } else {
      reasoning += "industry best practices for this time";
    }

    return reasoning;
  }

  /**
   * Check for scheduling conflicts
   */
  async checkSchedulingConflicts(modelId, slotSuggestions) {
    const conflicts = [];

    try {
      // Get existing scheduled tasks
      const existingTasks = await ContentTask.find({
        modelId,
        scheduledTime: {
          $gte: new Date(Math.min(...slotSuggestions.map((s) => s.datetime))),
          $lte: new Date(Math.max(...slotSuggestions.map((s) => s.datetime))),
        },
        status: { $in: ["scheduled", "published"] },
        isArchived: false,
      });

      // Check for time conflicts
      slotSuggestions.forEach((slot) => {
        const slotTime = new Date(slot.datetime);

        // Check against existing tasks
        existingTasks.forEach((task) => {
          const taskTime = new Date(task.scheduledTime);
          const timeDiff = Math.abs(slotTime.getTime() - taskTime.getTime());

          // Conflict if within 2 hours
          if (timeDiff < 2 * 60 * 60 * 1000) {
            conflicts.push({
              type: "content_overlap",
              description: `Suggested slot conflicts with existing task "${task.title}"`,
              severity: "medium",
              affectedSlots: [slotTime],
              resolved: false,
            });
          }
        });

        // Check for too frequent posting
        const sameHourSlots = slotSuggestions.filter(
          (s) =>
            new Date(s.datetime).getHours() === slotTime.getHours() &&
            new Date(s.datetime).toDateString() === slotTime.toDateString(),
        );

        if (sameHourSlots.length > 1) {
          conflicts.push({
            type: "too_frequent",
            description: `Multiple posts suggested for the same hour`,
            severity: "low",
            affectedSlots: sameHourSlots.map((s) => new Date(s.datetime)),
            resolved: false,
          });
        }
      });
    } catch (error) {
      conflicts.push({
        type: "calendar_conflict",
        description: `Unable to check calendar conflicts: ${error.message}`,
        severity: "high",
        affectedSlots: [],
        resolved: false,
      });
    }

    return conflicts;
  }

  /**
   * Get calendar events from Google Calendar
   */
  async getCalendarEvents(model, dateRange) {
    // This would integrate with Google Calendar API
    // For now, return empty array
    // TODO: Implement actual Google Calendar integration
    try {
      // Placeholder for Google Calendar API integration
      return [];
    } catch (error) {
      console.error("Error fetching calendar events:", error);
      return [];
    }
  }

  /**
   * Generate availability slots
   */
  generateAvailabilitySlots(dateRange, busySlots) {
    const availableSlots = [];
    const { startDate, endDate } = dateRange;

    // Generate hourly slots and check against busy times
    for (
      let date = new Date(startDate);
      date <= new Date(endDate);
      date.setDate(date.getDate() + 1)
    ) {
      for (let hour = 6; hour <= 22; hour++) {
        const slotStart = new Date(date);
        slotStart.setHours(hour, 0, 0, 0);

        const slotEnd = new Date(slotStart);
        slotEnd.setHours(hour + 1, 0, 0, 0);

        // Check if slot conflicts with busy times
        const isConflict = busySlots.some((busy) => {
          const busyStart = new Date(busy.start);
          const busyEnd = new Date(busy.end);

          return slotStart < busyEnd && slotEnd > busyStart;
        });

        if (!isConflict && slotStart > new Date()) {
          availableSlots.push({
            start: slotStart,
            end: slotEnd,
            available: true,
          });
        }
      }
    }

    return availableSlots;
  }
}

export default new SchedulerService();
