import React from "react";

const ModelsIcon = ({ className }) => {
  return (
    <svg
      width="100%"
      height="100%"
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <defs>
        {/* Gradient for the main icon shape */}
        <linearGradient
          id="app-bg-gradient"
          x1="0%"
          y1="0%"
          x2="100%"
          y2="100%"
        >
          <stop offset="0%" stopColor="#0E3CEE" />
          <stop offset="100%" stopColor="#A724F0" />
        </linearGradient>

        {/* Gradient for the line art stroke */}
        <linearGradient
          id="line-art-gradient"
          x1="50%"
          y1="0%"
          x2="50%"
          y2="100%"
        >
          <stop offset="0%" stopColor="#EE5FF5" />
          <stop offset="100%" stopColor="#A85BEF" />
        </linearGradient>

        {/* Filter to create a subtle raised/embossed effect on the line art */}
        <filter id="emboss-effect" x="-15%" y="-15%" width="130%" height="130%">
          <feDropShadow
            dx="8"
            dy="8"
            stdDeviation="6"
            floodColor="#000000"
            floodOpacity="0.4"
          />
        </filter>
      </defs>

      {/* Dark background */}
      <rect width="1024" height="1024" fill="#0F0F16" />

      {/* The rounded square icon base */}
      <rect
        x="40"
        y="40"
        width="944"
        height="944"
        rx="215"
        ry="215"
        fill="url(#app-bg-gradient)"
      />

      {/* The central line art icon */}
      <path
        fill="none"
        stroke="url(#line-art-gradient)"
        strokeWidth="35"
        strokeLinecap="round"
        strokeLinejoin="round"
        filter="url(#emboss-effect)"
        d="M 808 620 C 791 662, 757 707, 712 734 C 667 761, 614 770, 566 760 C 510 748, 461 713, 428 664 L 428 518 C 428 474, 439 430, 460 391 C 481 352, 511 319, 547 298 C 583 277, 624 268, 665 273 C 716 280, 760 307, 789 349 C 812 383, 824 423, 824 464 C 824 507, 816 548, 800 584 M 552 391.5 A 60.5 60.5 0 1 1 552 392.5 M 552 462 C 502 487, 477 547, 502 597 M 494 321 A 47 47 0 1 1 494 322 M 494 391 C 454 416, 434 466, 454 506 M 436 250.5 A 34.5 34.5 0 1 1 436 251.5 M 436 308 C 406 328, 391 368, 406 398"
      />
    </svg>
  );
};

export default ModelsIcon;
