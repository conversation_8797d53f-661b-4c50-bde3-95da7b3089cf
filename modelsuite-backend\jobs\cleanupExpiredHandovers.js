import cron from "node-cron";
import ShiftHandover from "../models/shifts/ShiftHandover.js";

// Run every hour to cleanup expired handovers
const cleanupExpiredHandovers = cron.schedule(
  "0 * * * *",
  async () => {
    try {
      const now = new Date();

      const result = await ShiftHandover.updateMany(
        {
          status: "pending",
          expiresAt: { $lte: now },
        },
        {
          status: "cancelled",
        },
      );

      if (result.modifiedCount > 0) {
        console.log(
          `Cleaned up ${result.modifiedCount} expired handover requests`,
        );
      }
    } catch (error) {
      console.error("Error cleaning up expired handovers:", error);
    }
  },
  {
    scheduled: false, // Don't start automatically, will be started manually
  },
);

// Function to start the cleanup job
export const startCleanupJob = () => {
  cleanupExpiredHandovers.start();
  console.log("Expired handover cleanup job started");
};

// Function to stop the cleanup job
export const stopCleanupJob = () => {
  cleanupExpiredHandovers.stop();
  console.log("Expired handover cleanup job stopped");
};

export default cleanupExpiredHandovers;
