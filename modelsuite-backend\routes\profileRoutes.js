import express from "express";
import {
  createUserProfile,
  getMyProfile,
  updateMyProfile,
  uploadAvatar,
  getUserProfileById,
  updateUserProfileById,
  uploadCoverPhoto,
  uploadPortfolioImage,
} from "../controllers/userProfileController.js";
import { verifyToken } from "../middlewares/authMiddleware.js";
import checkPermission, {
  checkLegacyPermission,
} from "../middlewares/permissionCheck.js";
import upload from "../middlewares/multer.js";
import { requireAdminOrAgency } from "../middlewares/roleCheck.js";

const router = express.Router();

// Create user profile (usually after registration, optional)
router.post(
  "/",
  verifyToken,
  checkLegacyPermission("profile", "create"),
  createUserProfile,
);

// Get current user's profile
router.get(
  "/",
  verifyToken,
  checkLegacyPermission("profile", "view"),
  getMyProfile,
);

// Update current user's profile
router.put(
  "/",
  verifyToken,
  checkLegacyPermission("profile", "edit"),
  updateMyProfile,
);

// Upload avatar
router.post(
  "/avatar",
  verifyToken,
  checkLegacyPermission("profile", "edit"),
  upload.single("avatar"),
  uploadAvatar,
);

// Upload cover photo
router.post(
  "/cover",
  verifyToken,
  checkLegacyPermission("profile", "edit"),
  upload.single("cover"),
  uploadCoverPhoto,
);

// Upload portfolio image
router.post(
  "/portfolio-image",
  verifyToken,
  checkLegacyPermission("profile", "edit"),
  uploadPortfolioImage,
);

// Get any user's profile by userId
router.get(
  "/:userId",
  verifyToken,
  checkLegacyPermission("profile", "view"),
  getUserProfileById,
);

// Update any user's profile by userId (admin/agency only)
router.put(
  "/:userId",
  verifyToken,
  requireAdminOrAgency,
  updateUserProfileById,
);

export default router;
