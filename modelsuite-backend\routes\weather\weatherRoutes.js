import express from "express";
import {
  getWeatherData,
  getLocationSuggestions,
} from "../../controllers/weather/weatherController.js";
import { verifyToken } from "../../middlewares/authMiddleware.js";

const router = express.Router();

// All weather routes require authentication
router.use(verifyToken);

// Get current weather or forecast
router.get("/current", getWeatherData);
router.get("/forecast", (req, res) => {
  req.query.type = "forecast";
  getWeatherData(req, res);
});

// Search for locations
router.get("/locations/search", getLocationSuggestions);

export default router;
