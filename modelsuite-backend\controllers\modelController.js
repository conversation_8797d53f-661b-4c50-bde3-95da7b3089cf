import ModelUser from "../models/model.js";
import UserProfile from "../models/UserProfile.js";
import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";
import Otp from "../models/otp.js";
import { randomInt } from "crypto";
import { sendOtpToEmail } from "../utils/sendOtpToEmail.js";
import { ApiResponse } from "../utils/ApiResponse.js";
import { asyncHandler } from "../utils/asyncHandler.js";
import cloudinary from "cloudinary";
import { uploadOnCloudinary } from "../config/cloudinary.js";
import { RewardService } from "../utils/RewardService.js";

async function generateOtp() {
  return randomInt(100000, 999999).toString();
}

const options = {
  httpOnly: true,
  secure: true,
  sameSite: "strict", // or 'lax' for usability
  maxAge: 1000 * 60 * 60, // 1 hour
};

export const generateAccessAndRefreshTokens = async (userId) => {
  try {
    const user = await ModelUser.findById(userId);

    const accessToken = user.generateAccessToken();
    const refreshToken = user.generateRefreshToken();

    user.loginRefreshToken = refreshToken;

    await user.save({ validateBeforeSave: false });

    return { accessToken, refreshToken };
  } catch (error) {
    console.error("Error generating tokens:", error);
    throw new Error(
      "Something went wrong while generating Access and Refresh Tokens.",
    );
  }
};

export const sendOtpForRegistration = async (req, res) => {
  try {
    const { fullName, email, username, password, confirmPassword } = req.body;

    if (!fullName || !username || !password || !confirmPassword || !email) {
      return res.status(400).json({ error: "All fields are required" });
    }

    if (password !== confirmPassword) {
      return res.status(400).json({ error: "Passwords do not match" });
    }

    if (
      password.length < 8 ||
      !/\d/.test(password) ||
      !/[a-zA-Z]/.test(password)
    ) {
      return res
        .status(400)
        .json({ error: "Password must be 8+ chars with letters & numbers" });
    }

    const orConditions = [];

    if (email) orConditions.push({ email });
    if (username) orConditions.push({ username });

    const userExists = await ModelUser.findOne({ $or: orConditions });

    if (userExists) {
      return res
        .status(409)
        .json({ error: "Email or Username already exists." });
    }
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 min expiry for otps
    const userData = { fullName, email, username, password };

    console.log(userData);

    if (email) {
      await Otp.deleteMany({ email });
      const emailOtp = await generateOtp();

      await Otp.create({
        email,
        otp: emailOtp,
        expiresAt,
        userData,
        type: "email",
      });
      try {
        await sendOtpToEmail(email, emailOtp);
      } catch (error) {
        return res.status(500).json({ error: "Email Otp could not be sent." });
      }
    }

    res.status(200).json({
      message:
        "OTP has been sent successfully. Please verify your credentials for a successfull registration.",
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Server error" });
  }
};

export const verifyOtpAndRegister = async (req, res) => {
  try {
    const { email: requestEmail, otp } = req.body;

    if (!otp || !requestEmail) {
      return res.status(400).json({ error: "Required fields are missing." });
    }

    const otpRecord = await Otp.findOne({ email: requestEmail, otp });

    if (!otpRecord || otpRecord.expiresAt < new Date()) {
      return res.status(400).json({ error: "Invalid or expired OTP." });
    }

    const { fullName, username, password, email } = otpRecord.userData;

    await Otp.deleteOne({ _id: otpRecord._id });
    const hashedPassword = await bcrypt.hash(password, 10);
    const isEmailVerified = !!email; // if email OTP was used

    console.log("🔍 Debug extracted data:");
    console.log("- fullName:", fullName);
    console.log("- email:", email);
    console.log("- username:", username);

    const newModel = new ModelUser({
      fullName,
      email,
      username,
      role: "model",
      password: hashedPassword,
      phoneVerified: false,
      emailVerified: isEmailVerified,
    });

    await newModel.save();

    const { accessToken, refreshToken } = await generateAccessAndRefreshTokens(
      newModel._id,
    );
    // console.log("Access:", accessToken)
    // console.log("Refresh:", refreshToken)
    const user = {
      _id: newModel._id,
      fullName: newModel.fullName,
      email: newModel.email,
      username: newModel.username,
      phoneVerified: newModel.phoneVerified,
      emailVerified: newModel.emailVerified,
    };

    res
      .cookie("accessToken", accessToken, options)
      .cookie("refreshToken", refreshToken, options);

    return res
      .status(200)
      .json({ message: "Registered successfully", user, token: accessToken });
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: "Server error" });
  }
};

export const login = async (req, res) => {
  try {
    // Accept either email or phone or username
    const { identifier, password } = req.body;

    if (!identifier || !password) {
      return res.status(400).json({ error: "Email and password are required" });
    }

    // Search for user by email OR username OR phone
    const user = await ModelUser.findOne({
      $or: [{ email: identifier }, { username: identifier }],
    });

    if (!user) {
      return res.status(401).json({ error: "Invalid credentials" });
    }

    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) return res.status(401).json({ error: "Invalid credentials" });

    // MFA logic
    if (user.isMfaActive) {
      const otp = await generateOtp();
      const expiresAt = new Date(Date.now() + 10 * 60 * 1000);

      // Remove old OTPs for this user
      await Otp.deleteMany({
        $or: [
          user.email ? { email: user.email } : null,
          // user.phone ? { phone: user.phone } : null
        ].filter(Boolean),
      });

      if (user.email) {
        await Otp.create({
          email: user.email,
          email: user.email,
          otp,
          expiresAt,
          userData: { username: user.username, email: user.email },
          type: "email",
        });
        await sendOtpToEmail(user.email, otp);
        return res.status(200).json({ message: "OTP sent to email" });
      }
    } else {
      const { accessToken, refreshToken } =
        await generateAccessAndRefreshTokens(user._id);
      // Use accessToken and refreshToken as needed

      const safeUser = {
        _id: user._id,
        fullName: user.fullName,
        email: user.email,
        username: user.username,
        role: user.role,
        profilePhoto: user.profilePhoto,
        agencyId: user.agencyId,
      };

      res
        .cookie("accessToken", accessToken, options)
        .cookie("refreshToken", refreshToken, options);

      try {
        const streakResult = await RewardService.recordActivity(
          user._id,
          "model",
          "login_streak",
        );

        console.log("Streak Result: ", streakResult);
        return res.status(200).json({
          message: "Login successful",
          user: safeUser,
          token: accessToken,
          rewards: {
            loginStreak: {
              currentStreak: streakResult.currentValue,
              pointsEarned: streakResult.pointsEarned,
              newBadges: streakResult.newBadges || [],
              totalPoints: streakResult.totalPoints,
              level: streakResult.level,
              isFirstLogin: streakResult.isFirstLogin, // Good for welcome messages
              streakBroken: streakResult.streakBroken, // Good for UI feedback
            },
          },
        });
      } catch (streakError) {
        console.error("Streak recording failed:", streakError);

        //original login is returned even if strea fails
        return res.status(200).json({
          message: "Login successful",
          user: safeUser,
          token: accessToken,
        });
      }
    }
  } catch (err) {
    console.error(err);
    return res.status(500).json({ error: "Server error" });
  }
};

export const verifyOtpAndLogin = async (req, res) => {
  try {
    const { identifier, otp } = req.body;

    if (!identifier || !otp) {
      return res.status(400).json({ error: "Please provide required fields" });
    }

    const query = {
      otp,
      $or: [{ email: identifier }, { "userData.username": identifier }],
    };
    const otpRecord = await Otp.findOne(query);
    if (!identifier || !otp) {
      return res.status(400).json({ error: "Please provide required fields" });
    }
    if (!otpRecord || otpRecord.expiresAt < new Date()) {
      return res.status(400).json({ error: "Invalid or expired OTP." });
    }
    // Get user by email or username
    const user = await ModelUser.findOne({
      $or: [{ email: identifier }, { username: identifier }],
    });

    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    await Otp.deleteOne({ _id: otpRecord._id });

    const { accessToken, refreshToken } = await generateAccessAndRefreshTokens(
      user._id,
    );

    const safeUser = {
      fullName: user.fullName,
      _id: user._id,
      email: user.email,
      username: user.username,
      role: user.role,
      profilePhoto: user.profilePhoto,
      agencyId: user.agencyId,
    };

    res
      .cookie("accessToken", accessToken, options)
      .cookie("refreshToken", refreshToken, options);

    try {
      const streakResult = await RewardService.recordActivity(
        user._id,
        "model",
        "login_streak",
      );

      return res.status(200).json({
        message: "Login successful",
        user: safeUser,
        token: accessToken,
        rewards: {
          loginStreak: {
            currentStreak: streakResult.currentValue,
            pointsEarned: streakResult.pointsEarned,
            newBadges: streakResult.newBadges || [],
            totalPoints: streakResult.totalPoints,
            level: streakResult.level,
            isFirstLogin: streakResult.isFirstLogin, // Good for welcome messages
            streakBroken: streakResult.streakBroken, // Good for UI feedback
          },
        },
      });
    } catch (streakError) {
      console.log("Streak record failed: ", streakError);
      return res.status(200).json({
        message: "Login successful",
        user: safeUser,
        token: accessToken,
      });
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: "Server error" });
  }
};

export const logoutUser = async (req, res) => {
  await ModelUser.findByIdAndUpdate(
    req.user._id,
    {
      $set: {
        refreshToken: undefined,
      },
    },
    {
      new: true,
    },
  );

  return res
    .status(200)
    .clearCookie("accessToken", options)
    .clearCookie("refreshToken", options)
    .json({ message: "Logged out successfully!" });
};

export const toggleMfa = async (req, res) => {
  try {
    const userId = req.user.id;

    const user = await ModelUser.findById(userId);
    if (!user) return res.status(404).json({ error: "User not found" });

    user.isMfaActive = !user.isMfaActive;
    await user.save();

    res.status(200).json({
      message: `MFA is now ${user.isMfaActive ? "enabled" : "disabled"}`,
      isMfaActive: user.isMfaActive,
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Server error" });
  }
};

export const getModelById = async (req, res) => {
  try {
    const model = await ModelUser.findById(req.params.id).select(
      "-password -confirmPassword",
    );
    if (!model) {
      return res.status(404).json({ error: "Model not found" });
    }
    res.status(200).json(model);
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Server error" });
  }
};

export const getModelStatus = async (req, res) => {};

export const getAllModels = async (req, res) => {
  try {
    const models = await ModelUser.find().select("-password -confirmPassword");
    res.status(200).json(models);
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Failed to fetch models" });
  }
};

export const updateModel = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      email,
      platforms,
      tags,
      additionalTags,
      status,
      contractStatus,
    } = req.body;

    // Validate status if provided
    if (
      status &&
      !["active", "inactive", "pending", "suspended"].includes(status)
    ) {
      return res.status(400).json({ error: "Invalid status value" });
    }

    // Check if email is already taken by another model
    if (email) {
      const existingModel = await ModelUser.findOne({
        email,
        _id: { $ne: id },
      });
      if (existingModel) {
        return res.status(400).json({ error: "Email already exists" });
      }
    }

    // Prepare update data
    const updateData = {};
    if (name) updateData.fullName = name;
    if (email) updateData.email = email;
    if (platforms) updateData.category = platforms;
    if (tags) updateData.tags = tags;
    if (additionalTags) updateData.additionalTags = additionalTags;
    if (status) updateData.status = status;
    if (contractStatus) updateData.contractStatus = contractStatus;

    const updatedModel = await ModelUser.findByIdAndUpdate(id, updateData, {
      new: true,
      select: "-password -loginRefreshToken",
    });

    if (!updatedModel) {
      return res.status(404).json({ error: "Model not found" });
    }

    res.status(200).json({
      message: "Model updated successfully",
      model: updatedModel,
    });
  } catch (err) {
    console.error("Update model error:", err);
    res.status(500).json({ error: "Server error" });
  }
};

export const deleteModel = async (req, res) => {
  // just a utility func for now to re-register with same credentials
  const { email, phone } = req.body;

  try {
    // Find the user by email or phone
    const user = await ModelUser.findOne({
      email,
    });

    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    // Delete the user
    await ModelUser.deleteOne({ _id: user._id });

    res.status(200).json({ message: "User deleted successfully" });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Server error" });
  }
};

export const forgotPasswordSendOtp = async (req, res) => {
  const { identifier, newPassword, confirmPassword } = req.body;

  if (!identifier || !newPassword || !confirmPassword) {
    return res.status(400).json({ error: "Required fields missing" });
  }

  if (newPassword !== confirmPassword) {
    return res.status(400).json({ error: "Passwords do not match" });
  }

  if (
    newPassword.length < 8 ||
    !/\d/.test(newPassword) ||
    !/[a-zA-Z]/.test(newPassword)
  ) {
    return res.status(400).json({
      error:
        "Password must be at least 8 characters long and contain letters and numbers",
    });
  }

  const model = await ModelUser.findOne({
    $or: [{ email: identifier }, { username: identifier }],
  });

  if (!model) {
    return res
      .status(400)
      .json({ error: "No account found with these credentials." });
  }

  let platform = null;
  let contact = null;

  const inputIsEmail = identifier.includes("@");

  if (inputIsEmail) {
    if (model.email !== identifier) {
      return res
        .status(400)
        .json({ error: "Email does not match our records." });
    }
    if (!model.emailVerified) {
      return res.status(400).json({ error: "Email is not verified." });
    }
    platform = "email";
    contact = model.email;
  } else {
    // Treat as username
    if (model.username !== identifier) {
      return res.status(400).json({ error: "Username does not match." });
    }
    if (!model.emailVerified) {
      return res
        .status(400)
        .json({ error: "No verified email found for this user." });
    }
    platform = "email";
    contact = model.email;
  }

  const otp = await generateOtp();
  const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 mins
  const userData = { userId: model._id, newPassword };

  if (platform === "email") {
    await Otp.deleteMany({ email: contact });
  }

  await Otp.create({
    email: platform === "email" ? contact : undefined,
    otp,
    expiresAt,
    userData,
    type: platform,
  });

  try {
    if (platform === "email") {
      await sendOtpToEmail(contact, otp);
    }

    return res.status(200).json({
      message: `OTP sent to your ${platform}. Please verify to reset your password.`,
    });
  } catch (error) {
    return res
      .status(500)
      .json({ error: `Failed to send OTP via ${platform}` });
  }
};

export const forgotPasswordVerifyOtp = async (req, res) => {
  const { identifier, otp } = req.body;

  if (!identifier || !otp) {
    return res.status(400).json({ error: "Identifier and OTP are required" });
  }

  const otpEntry = await Otp.findOne({
    $or: [{ email: identifier }, { "userData.username": identifier }],
    otp,
  });

  if (!otpEntry) {
    return res.status(400).json({ error: "Invalid OTP." });
  }

  if (otpEntry.expiresAt < new Date()) {
    await Otp.deleteOne({ _id: otpEntry._id });
    return res.status(400).json({ error: "OTP has expired" });
  }

  const { userId, newPassword } = otpEntry.userData;

  if (!userId || !newPassword) {
    return res.status(500).json({ error: "OTP data is incomplete" });
  }

  const hashedPassword = await bcrypt.hash(newPassword, 10);

  const model = await ModelUser.findByIdAndUpdate(
    userId,
    { password: hashedPassword },
    { new: true },
  );

  if (!model) {
    return res.status(404).json({ error: "User not found" });
  }

  await Otp.deleteOne({ _id: otpEntry._id });

  return res.status(200).json({ message: "Password reset successfully" });
};

export const isAcceptingEmail = async (req, res) => {
  const modelId = req.user.id;
  const { status } = req.body;

  try {
    if (!modelId) {
      return res.status(400).json({ message: "Model Id is required!" });
    }

    if (typeof status !== "boolean") {
      return res
        .status(400)
        .json({ message: "Status must be a boolean value!" });
    }

    const model = await ModelUser.findById(modelId);
    if (!model) {
      return res.status(404).json({ message: "Model not found!" });
    }

    model.isAcceptingEmail = status;
    await model.save();

    return res.status(200).json({
      message: `Model is now ${
        status ? "accepting" : "not accepting"
      } email messages`,
      isAcceptingEmail: status,
    });
  } catch (err) {
    console.error(err);
    return res.status(500).json({ message: "Internal Server Error!" });
  }
};

export const uploadModelAvatar = asyncHandler(async (req, res) => {
  try {
    // Check if file was uploaded
    if (!req.file) {
      return res
        .status(400)
        .json(new ApiResponse(400, null, "No file uploaded"));
    }

    // Upload to Cloudinary
    const result = await cloudinary.v2.uploader.upload(req.file.path, {
      folder: "model_avatars",
      public_id: req.user._id,
      overwrite: true,
    });

    // Update model's profilePhoto field
    const model = await ModelUser.findByIdAndUpdate(
      req.user._id,
      { profilePhoto: result.secure_url },
      { new: true },
    );

    if (!model) {
      return res
        .status(404)
        .json(new ApiResponse(404, null, "Model not found"));
    }

    // Also update UserProfile.avatar_url to keep both systems in sync
    await UserProfile.findOneAndUpdate(
      { user: req.user._id },
      { avatar_url: result.secure_url },
      { upsert: true },
    );

    return res
      .status(200)
      .json(
        new ApiResponse(
          200,
          { profilePhoto: result.secure_url, model },
          "Avatar uploaded successfully",
        ),
      );
  } catch (error) {
    console.error("Error uploading avatar:", error);
    return res
      .status(500)
      .json(
        new ApiResponse(500, null, error.message || "Error uploading avatar"),
      );
  }
});

export const changeModelEmail = async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) return res.status(401).json({ message: "Email is required." });

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res
        .status(400)
        .json({ message: "Please provide a valid email address." });
    }
    const emailExists = await ModelUser.findOne({ email });

    if (emailExists)
      return res.status(401).json({ message: "This email already exists." });

    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); //10minutes

    await Otp.deleteMany({ email: email });

    const emailOtp = await generateOtp();
    const userData = {
      email,
    };

    await Otp.create({
      email,
      otp: emailOtp,
      expiresAt,
      userData,
      type: "email",
    });
    try {
      await sendOtpToEmail(email, emailOtp);
    } catch (error) {
      return res.status(500).json({ error: "Email OTP could not be sent." });
    }
    return res.status(200).json({
      message: "OTP has been sent successfully. Please verify your new email.",
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: "Server error" });
  }
};

export const verifyNewModelEmail = async (req, res) => {
  try {
    const userId = req.user._id;

    const { email, otp } = req.body;

    const otpRecord = await Otp.findOne({
      email,
      otp,
    });

    if (!otp || otpRecord.expiresAt < new Date())
      return res.status(400).json({ error: "Invalid or expired OTP" });

    await Otp.deleteOne({ _id: otpRecord._id });
    const model = await ModelUser.findByIdAndUpdate(
      userId,
      { email: email },
      {
        new: true,
        select: "-password -loginRefreshToken",
      },
    );

    if (!model)
      return res.status(404).json({ message: "Model could not be found." });

    return res.status(200).json({
      message: "Your Email has been changed successfully.",
      model: {
        _id: model._id,
        fullName: model.fullName,
        email: model.email,
        username: model.username,
      },
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({ error: "Server error." });
  }
};

export const changePasswordSendOtp = async (req, res) => {
  try {
    const userId = req.user._id;
    const { newPassword, confirmPassword } = req.body;

    if (!newPassword || !confirmPassword)
      return res.status(400).json({ error: "Both Passwords are required." });

    if (newPassword !== confirmPassword)
      return res.status(400).json({ error: "Passwords do not match" });

    if (
      newPassword.length < 8 ||
      !/\d/.test(newPassword) ||
      !/[a-zA-Z]/.test(newPassword)
    ) {
      return res
        .status(400)
        .json({ error: "Password must be 8+ charswith letters & numbers" });
    }

    const model = await ModelUser.findById(userId);
    if (!model)
      return res
        .status(500)
        .json({ message: "Something went wrong in the server." });

    const otp = await generateOtp();
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 mins
    const userData = { userId, newPassword };

    await Otp.deleteMany({ email: model.email });

    await Otp.create({
      email: model.email,
      otp,
      expiresAt,
      userData,
      type: "email",
    });

    try {
      await sendOtpToEmail(model.email, otp);
      return res.status(200).json({
        message: `OTP sent to your Email(${model.email}). Please verify to reset your password.`,
      });
    } catch (error) {
      return res.status(500).json({ error: `Failed to send OTP via email.` });
    }
  } catch (error) {
    console.log("Error:", error);
    return res.status(500).json({ message: "Server error." });
  }
};

export const verifyOtpAndChangePassword = async (req, res) => {
  try {
    const userId = req.user._id;
    const { otp } = req.body;

    const model = await ModelUser.findById(userId);

    if (!model)
      return res.status(401).json({ message: "unauthorized access." });

    const email = model.email;

    const otpRecord = await Otp.findOne({
      email,
      otp,
    });

    if (!otpRecord || otpRecord.expiresAt < new Date())
      return res.status(401).json({ message: "Invalid or expired otp." });

    const { newPassword } = otpRecord.userData;

    if (!newPassword)
      return res
        .status(401)
        .json({ message: "OTP data corrupted or incomplete." });

    const hashedPassword = await bcrypt.hash(newPassword, 10);

    try {
      await ModelUser.findByIdAndUpdate(
        userId,
        { password: hashedPassword },
        { new: true },
      );
    } catch (error) {
      return res.status(500).json({ message: "Password could not be changed" });
    }

    await Otp.deleteOne({ _id: otpRecord._id });

    return res
      .status(200)
      .json({ message: "Password has been reset successfully" });
  } catch (error) {
    console.log(error);
    return res.status(500).json({ message: "Server error" });
  }
};

// Bulk Operations
export const bulkUpdateStatus = asyncHandler(async (req, res) => {
  const { modelIds, status } = req.body;

  if (!modelIds || !Array.isArray(modelIds) || modelIds.length === 0) {
    return res.status(400).json({ message: "Model IDs array is required" });
  }

  if (
    !status ||
    !["active", "inactive", "pending", "suspended"].includes(status)
  ) {
    return res.status(400).json({ message: "Valid status is required" });
  }

  try {
    const result = await ModelUser.updateMany(
      { _id: { $in: modelIds } },
      { $set: { status: status } },
    );

    return res.status(200).json({
      message: `Successfully updated status for ${result.modifiedCount} models`,
      modifiedCount: result.modifiedCount,
    });
  } catch (error) {
    console.error("Bulk status update error:", error);
    return res
      .status(500)
      .json({ message: "Server error during bulk status update" });
  }
});

export const bulkUpdateTags = asyncHandler(async (req, res) => {
  const { modelIds, tags, action } = req.body; // action: 'add', 'remove', 'replace'

  if (!modelIds || !Array.isArray(modelIds) || modelIds.length === 0) {
    return res.status(400).json({ message: "Model IDs array is required" });
  }

  if (!tags || !Array.isArray(tags)) {
    return res.status(400).json({ message: "Tags array is required" });
  }

  if (!action || !["add", "remove", "replace"].includes(action)) {
    return res
      .status(400)
      .json({ message: "Valid action (add, remove, replace) is required" });
  }

  try {
    let updateOperation;

    switch (action) {
      case "add":
        updateOperation = { $addToSet: { tags: { $each: tags } } };
        break;
      case "remove":
        updateOperation = { $pullAll: { tags: tags } };
        break;
      case "replace":
        updateOperation = { $set: { tags: tags } };
        break;
    }

    const result = await ModelUser.updateMany(
      { _id: { $in: modelIds } },
      updateOperation,
    );

    return res.status(200).json({
      message: `Successfully ${action}ed tags for ${result.modifiedCount} models`,
      modifiedCount: result.modifiedCount,
    });
  } catch (error) {
    console.error("Bulk tags update error:", error);
    return res
      .status(500)
      .json({ message: "Server error during bulk tags update" });
  }
});

export const bulkRemoveFromAgency = asyncHandler(async (req, res) => {
  const { modelIds } = req.body;
  const agencyId =
    req.user.role === "employee" ? req.user.agencyId : req.user._id;

  if (!modelIds || !Array.isArray(modelIds) || modelIds.length === 0) {
    return res.status(400).json({ message: "Model IDs array is required" });
  }

  try {
    // Only remove models that belong to this agency
    const result = await ModelUser.updateMany(
      {
        _id: { $in: modelIds },
        agencyId: agencyId,
      },
      { $set: { agencyId: null } },
    );

    return res.status(200).json({
      message: `Successfully removed ${result.modifiedCount} models from agency`,
      modifiedCount: result.modifiedCount,
    });
  } catch (error) {
    console.error("Bulk remove from agency error:", error);
    return res
      .status(500)
      .json({ message: "Server error during bulk remove from agency" });
  }
});

// Keep the original bulkDeleteModels for permanent deletion if needed
export const bulkDeleteModels = asyncHandler(async (req, res) => {
  const { modelIds } = req.body;

  if (!modelIds || !Array.isArray(modelIds) || modelIds.length === 0) {
    return res.status(400).json({ message: "Model IDs array is required" });
  }

  try {
    const result = await ModelUser.deleteMany({ _id: { $in: modelIds } });

    return res.status(200).json({
      message: `Successfully deleted ${result.deletedCount} models`,
      deletedCount: result.deletedCount,
    });
  } catch (error) {
    console.error("Bulk delete error:", error);
    return res.status(500).json({ message: "Server error during bulk delete" });
  }
});

export const exportModelsCSV = asyncHandler(async (req, res) => {
  const { modelIds } = req.query;

  try {
    let query = {};
    if (modelIds) {
      const ids = modelIds.split(",");
      query = { _id: { $in: ids } };
    }

    const models = await ModelUser.find(query)
      .select(
        "phone username country city category tags additionalTags status contractStatus createdAt lastOnline",
      )
      .lean();

    if (models.length === 0) {
      return res.status(404).json({ message: "No models found" });
    }

    // Check if columns have any data to determine which to include
    const hasPhone = models.some((model) => model.phone && model.phone.trim());
    const hasCountry = models.some(
      (model) => model.country && model.country.trim(),
    );
    const hasCity = models.some((model) => model.city && model.city.trim());

    // Build dynamic headers based on available data
    const csvHeaders = ["Username"];
    if (hasPhone) csvHeaders.push("Phone");
    if (hasCountry) csvHeaders.push("Country");
    if (hasCity) csvHeaders.push("City");
    csvHeaders.push(
      "Category",
      "Tags",
      "Additional Tags",
      "Status",
      "Contract Status",
      "Created At",
      "Last Online",
    );

    const csvRows = models.map((model) => {
      const row = [model.username || ""];

      if (hasPhone) row.push(model.phone || "");
      if (hasCountry) row.push(model.country || "");
      if (hasCity) row.push(model.city || "");

      // Normalize tags - convert arrays to comma-separated lists
      const categoryList = Array.isArray(model.category)
        ? model.category.join(", ")
        : model.category || "";
      const tagsList = Array.isArray(model.tags)
        ? model.tags.join(", ")
        : model.tags || "";
      const additionalTagsList = Array.isArray(model.additionalTags)
        ? model.additionalTags.join(", ")
        : model.additionalTags || "";

      row.push(
        categoryList,
        tagsList,
        additionalTagsList,
        // Ensure consistent status formatting (lowercase, trimmed)
        model.status ? model.status.toString().toLowerCase().trim() : "",
        model.contractStatus
          ? model.contractStatus.toString().toLowerCase().trim()
          : "",
        // Parse dates properly - convert to readable format
        model.createdAt
          ? new Date(model.createdAt).toLocaleString("en-US", {
              year: "numeric",
              month: "2-digit",
              day: "2-digit",
              hour: "2-digit",
              minute: "2-digit",
              second: "2-digit",
              hour12: false,
            })
          : "",
        // Handle lastOnline - could be string or date
        model.lastOnline
          ? model.lastOnline instanceof Date
            ? model.lastOnline.toLocaleString("en-US", {
                year: "numeric",
                month: "2-digit",
                day: "2-digit",
                hour: "2-digit",
                minute: "2-digit",
                second: "2-digit",
                hour12: false,
              })
            : new Date(model.lastOnline).toString() !== "Invalid Date"
              ? new Date(model.lastOnline).toLocaleString("en-US", {
                  year: "numeric",
                  month: "2-digit",
                  day: "2-digit",
                  hour: "2-digit",
                  minute: "2-digit",
                  second: "2-digit",
                  hour12: false,
                })
              : model.lastOnline
          : "",
      );

      return row;
    });

    const csvContent = [csvHeaders, ...csvRows]
      .map((row) =>
        row.map((field) => `"${String(field).replace(/"/g, '""')}"`).join(","),
      )
      .join("\n");

    res.setHeader("Content-Type", "text/csv");
    res.setHeader(
      "Content-Disposition",
      'attachment; filename="models_export.csv"',
    );

    return res.status(200).send(csvContent);
  } catch (error) {
    console.error("CSV export error:", error);
    return res.status(500).json({ message: "Server error during CSV export" });
  }
});
