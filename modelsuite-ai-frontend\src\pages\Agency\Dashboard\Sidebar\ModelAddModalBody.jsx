import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "sonner";
import { SearchBar } from '@/reusable';
import { Button } from '@/components/ui/button';
import { searchAllModels } from '@/redux/features/models/searchAllModelsSlice';
import { inviteModel } from '@/redux/features/models/inviteModelSlice';

const ModelAddModalBody = () => {
  const dispatch = useDispatch();
  const [search, setSearch] = useState("");
  const [selectedModel, setSelectedModel] = useState(null);
  const { searchResults, loading: searchLoading, error: searchError } = useSelector(state => state.searchAllModelsReducer || {});
  const { loading: addLoading, success: addSuccess, error: addError } = useSelector(state => state.inviteModelReducer || {});

  // Debounced search
  useEffect(() => {
    if (search.trim() !== "") {
      const handler = setTimeout(() => {
        dispatch(searchAllModels(search));
      }, 400);
      return () => clearTimeout(handler);
    }
  }, [search, dispatch]);

  // Reset selection on new search
  useEffect(() => { setSelectedModel(null); }, [search]);

  // Toast on success/error
  useEffect(() => {
    if (addSuccess) {
      toast.success("Invite sent successfully!");
    }
    if (addError) {
      toast.error(addError);
    }
  }, [addSuccess, addError]);

  // Handle add model
  const handleAddModel = () => {
    if (selectedModel) {
      dispatch(inviteModel({ modelToAdd: selectedModel }));
    }
  };

  return (
    <div>
      <div className="mb-4">
        <SearchBar
          placeholder="Search models..."
          value={search}
          onChange={val => setSearch(val)}
          loading={searchLoading}
        />
      </div>
      {searchError && <div className="text-red-500 text-xs mb-2">{searchError.message || searchError}</div>}
      <div className="max-h-56 overflow-y-auto mb-4">
        {searchLoading && <div className="text-gray-400 text-xs">Searching...</div>}
        {searchResults && Array.isArray(searchResults) && searchResults.length > 0 ? (
          <ul className="divide-y divide-gray-700">
            {searchResults.map(model => (
              <li
                key={model._id}
                className={`flex items-center px-3 py-2 cursor-pointer hover:bg-[#18181b] ${selectedModel && selectedModel._id === model._id ? 'bg-[#232324]' : ''}`}
                onClick={() => setSelectedModel(model)}
              >
                <div className="w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center text-white text-xs font-bold mr-3">
                  {model.avatar ? <img src={model.avatar} alt={model.fullName} className="w-full h-full rounded-full object-cover" /> : (model.fullName ? model.fullName.split(' ').map(n => n[0]).join('').toUpperCase() : 'M')}
                </div>
                <span className="text-sm text-white">{model.fullName || model.name}</span>
                {selectedModel && selectedModel._id === model._id && <span className="ml-auto text-xs text-green-400">Selected</span>}
              </li>
            ))}
          </ul>
        ) : search && !searchLoading ? (
          <div className="text-gray-400 text-xs">No models found.</div>
        ) : null}
      </div>
      <Button
        className="w-full"
        disabled={!selectedModel || addLoading}
        onClick={handleAddModel}
      >
        {addLoading ? 'Sending...' : 'Send Invite'}
      </Button>

    </div>
  );
};

export default ModelAddModalBody;
