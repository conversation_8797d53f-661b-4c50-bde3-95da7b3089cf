import * as React from "react";
import { cn } from "@/lib/utils";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardDescription,
  CardTitle,
} from "@/components/ui/card";

const FormWrapper = ({
  title,
  description,
  children,
  className,
  variant = "default", // "default", "compact", "fullscreen"
  showBorder = true,
  ...props
}) => {
  const getVariantStyles = () => {
    switch (variant) {
      case "compact":
        return "p-4";
      case "fullscreen":
        return "min-h-screen p-8";
      default:
        return "p-6";
    }
  };

  if (!showBorder) {
    return (
      <div
        className={cn("space-y-6", getVariantStyles(), className)}
        {...props}
      >
        {(title || description) && (
          <div className="space-y-2">
            {title && (
              <h2 className="text-2xl font-semibold tracking-tight text-gray-300">
                {title}
              </h2>
            )}
            {description && (
              <p className="text-sm text-gray-500">{description}</p>
            )}
          </div>
        )}
        <div className="space-y-4">{children}</div>
      </div>
    );
  }

  return (
    <Card className={cn("bg-[#0f0f0f] border-gray-800", className)} {...props}>
      {(title || description) && (
        <CardHeader className="space-y-2">
          {title && (
            <CardTitle className="text-2xl font-semibold tracking-tight text-gray-300">
              {title}
            </CardTitle>
          )}
          {description && (
            <CardDescription className="text-gray-500">
              {description}
            </CardDescription>
          )}
        </CardHeader>
      )}
      <CardContent className={cn("space-y-4", getVariantStyles())}>
        {children}
      </CardContent>
    </Card>
  );
};

export default FormWrapper;
