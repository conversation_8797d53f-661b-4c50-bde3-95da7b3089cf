import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"

const InputFormField = ({ mandatory = true, name, disabled = false, control, placeholder, label, type, className, ...props }) => {
    return (
        <FormField control={control} name={name} render={({ field }) => (
            <FormItem className={className}>
                <FormLabel className="w-[50%]">{label} {mandatory !== false ? <span className="text-white">*</span> : ""}</FormLabel>
                <FormControl>
                    <Input
                        className="border border-[#2a2a2a] bg-[#1a1a1a] text-white"
                        disabled={disabled}
                        type={type}
                        placeholder={placeholder}
                        {...field}
                        {...props}
                    />
                </FormControl>
                <FormMessage />
            </FormItem>
        )}>
        </FormField>
    )
}

export default InputFormField