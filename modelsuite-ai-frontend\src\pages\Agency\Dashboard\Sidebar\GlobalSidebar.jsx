import React, { useState } from "react";

import { But<PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from "@/components/ui/accordion";
import { globalSidebarNavigation } from "../../../../utils/data";
import { MdDashboard } from "react-icons/md";
import {
  FaUsers,
  FaLaptop,
  FaBullseye,
  FaCalendarAlt,
  FaFileSignature,
  FaFileInvoiceDollar,
  FaRegCommentDots,
  FaMicrophone,
  FaMagic,
  FaHeadset,
  FaChartBar,
  FaHandSparkles,
  FaClipboard,
} from "react-icons/fa";
import { PanelLeft } from "lucide-react";

const iconMap = {
  MdDashboard,
  FaUsers,
  FaLaptop,
  FaBullseye,
  FaCalendarAlt,
  FaFileSignature,
  FaFileInvoiceDollar,
  FaRegCommentDots,
  FaMicrophone,
  FaMagic,
  FaHeadset,
  FaChartBar,
  FaClipboard,
  FaHandSparkles,
};

const GlobalSidebar = () => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [activeNav, setActiveNav] = useState("Dashboard");

  const onToggleCollapse = () => {
    setIsCollapsed((prev) => !prev);
  };

  // Use globalSidebarNavigation instead of navigationItems
  const navigationItems = globalSidebarNavigation.map((item) => ({
    ...item,
    icon: item.icon && iconMap[item.icon],
    children: item.children
      ? item.children.map((child) => ({ ...child }))
      : undefined,
  }));

  return (
    <div
      className={`bg-[#0a0a0a] border-r border-[#1a1a1a] flex flex-col transition-all duration-300 ${
        isCollapsed ? "w-20" : "w-64"
      }`}
    >
      {/* Header */}
      <div className="p-6 border-b border-[#1a1a1a] flex items-center justify-between">
        {!isCollapsed ? (
          <>
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-xs">MS</span>
              </div>
              <div>
                <h2 className="font-semibold text-white">ModelSuite.ai</h2>
                {/* <p className="text-gray-500 text-sm">Agency Manager</p> */}
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleCollapse}
              className="text-gray-400 hover:text-white"
            >
              <PanelLeft className="w-5 h-5" />
            </Button>
          </>
        ) : (
          <div className="w-full flex justify-center py-2">
            <Button
              variant="ghost"
              size="lg"
              onClick={onToggleCollapse}
              className="text-gray-400 hover:text-white hover:bg-[#1a1a1a] w-10 h-10 rounded-xl"
            >
              <PanelLeft className="w-5 h-5" />
            </Button>
          </div>
        )}
      </div>

      {/* Navigation */}
      <div className="flex-1 px-4">
        <nav className="space-y-1">
          <Accordion type="multiple" className="space-y-1">
            {navigationItems.map((item) => {
              const isActive = activeNav === item.name;
              const Icon = item.icon;

              if (item.children && item.children.length > 0) {
                return (
                  <AccordionItem
                    key={item.name}
                    value={item.name}
                    className="border-0"
                  >
                    <AccordionTrigger
                      className={`w-full flex items-center justify-between px-3 py-2.5 rounded-lg transition-all duration-200 text-sm font-normal group relative
    ${
      isActive ||
      (item.children && item.children.some((child) => activeNav === child.name))
        ? "bg-[#1a1a1a] text-gray-300"
        : "text-gray-300 hover:bg-[#18181b] hover:text-white"
    }
  `}
                      hideArrow={isCollapsed}
                    >
                      <div className="flex items-center gap-3">
                        {Icon && <Icon className="w-5 h-5" />}
                        {!isCollapsed && <span>{item.name}</span>}
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="pl-8 space-y-2 pt-2 pb-2">
                      {item.children.map((child) => (
                        <Link
                          to={child.href}
                          key={child.name}
                          onClick={() => setActiveNav(child.name)}
                          className={`w-full flex items-center px-3 py-2 rounded-lg text-sm font-medium text-left transition-colors duration-200 ${
                            activeNav === child.name
                              ? "bg-[#1a1a1a] text-gray-300"
                              : "text-gray-400 hover:bg-[#1a1a1a] hover:text-gray-300"
                          }`}
                        >
                          {child.name}
                        </Link>
                      ))}
                    </AccordionContent>
                  </AccordionItem>
                );
              }

              return (
                <Link
                  to={item.href}
                  key={item.name}
                  onClick={() => setActiveNav(item.name)}
                  className={`w-full flex items-center ${
                    isCollapsed ? "justify-center mb-4" : "space-x-3"
                  } px-3 ${
                    isCollapsed ? "py-3" : "py-2.5"
                  } rounded-lg transition-all duration-200 text-sm font-medium group relative ${
                    isActive
                      ? "bg-[#18181b] text-white"
                      : "text-gray-400 hover:bg-[#18181b] hover:text-white"
                  }`}
                >
                  {Icon && (
                    <Icon
                      className={`${isCollapsed ? "w-4 h-4" : "w-4 h-4"}`}
                    />
                  )}
                  {!isCollapsed && <span>{item.name}</span>}
                  {isCollapsed && (
                    <div className="absolute left-full ml-4 top-1/2 -translate-y-1/2 bg-gray-900 text-white text-sm px-3 py-2 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50 shadow-lg">
                      {item.name}
                    </div>
                  )}
                </Link>
              );
            })}
          </Accordion>
        </nav>
      </div>
    </div>
  );
};

export default GlobalSidebar;
