import React, { useEffect, useCallback, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  closeSidebar,
  selectIsWidgetSidebarOpen,
} from "@/redux/features/widgetSidebar/widgetSidebarSlice";
import {
  launchpadFeature,
  getSelectedFeatures,
  getFeaturesByIds,
} from "./featureRegistry";
import FeatureIcon from "./FeatureIcon";
import LaunchpadModal from "./LaunchpadModal";

const WidgetSidebar = () => {
  const dispatch = useDispatch();
  const isOpen = useSelector(selectIsWidgetSidebarOpen);
  const [selectedFeatures, setSelectedFeatures] = useState([]);
  const [isLaunchpadOpen, setIsLaunchpadOpen] = useState(false);

  // Load selected features on component mount
  useEffect(() => {
    setSelectedFeatures(getFeaturesByIds(getSelectedFeatures()));
  }, []);

  const handleClose = useCallback(() => {
    dispatch(closeSidebar());
  }, [dispatch]);

  const handleLaunchpadClick = () => {
    setIsLaunchpadOpen(true);
  };

  const handleLaunchpadSave = (selectedIds) => {
    setSelectedFeatures(getFeaturesByIds(selectedIds));
  };

  // Keyboard navigation and click outside
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === "Escape") {
        if (isLaunchpadOpen) {
          setIsLaunchpadOpen(false);
        } else if (isOpen) {
          handleClose();
        }
      }
    };

    const handleClickOutside = (event) => {
      if (isOpen && !isLaunchpadOpen) {
        const sidebar = document.querySelector("[data-widget-sidebar]");
        if (sidebar && !sidebar.contains(event.target)) {
          handleClose();
        }
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, isLaunchpadOpen, handleClose]);

  return (
    <>
      {/* Widget Sidebar - macOS Dock Style */}
      <div
        className={`
          fixed left-1/2 bottom-8 transform -translate-x-1/2 z-50
          flex flex-row items-center justify-center
          bg-black/30 backdrop-blur-xl border border-white/10
          rounded-full shadow-2xl
          px-4 py-1 gap-6
          max-w-[80vw] min-h-[44px] h-auto
          transition-all duration-300 ease-in-out
          ${
            isOpen
              ? "translate-y-0 opacity-100 scale-100"
              : "translate-y-8 opacity-0 scale-95 pointer-events-none"
          }
        `}
        data-widget-sidebar
        tabIndex={-1}
        role="dialog"
        aria-label="Widget sidebar"
        aria-modal="true"
      >
        {/* Always show Launchpad icon first */}
        <FeatureIcon
          feature={launchpadFeature}
          onClick={handleLaunchpadClick}
        />

        {/* Show selected feature icons (up to 5) */}
        {selectedFeatures.map((feature) => (
          <FeatureIcon key={feature.id} feature={feature} />
        ))}
      </div>

      {/* Launchpad Modal */}
      <LaunchpadModal
        isOpen={isLaunchpadOpen}
        onClose={() => setIsLaunchpadOpen(false)}
        onSave={handleLaunchpadSave}
      />
    </>
  );
};

export default WidgetSidebar;
