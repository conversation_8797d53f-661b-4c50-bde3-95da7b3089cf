import * as React from "react";
import { cn } from "@/lib/utils";
import {
  Circle,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  Pause,
  Play,
  User,
  Wifi,
  WifiOff,
} from "lucide-react";

const StatusBadge = ({
  status = "default",
  variant = "default", // "default", "dot", "outline", "ghost"
  size = "default", // "sm", "default", "lg"
  showIcon = true,
  showDot = false,
  pulse = false,
  customIcon: CustomIcon,
  customColor,
  children,
  className,
  ...props
}) => {
  // Status configurations
  const statusConfig = {
    online: {
      label: "Online",
      icon: Wifi,
      colors: {
        bg: "bg-green-900",
        text: "text-green-400",
        border: "border-green-700",
        dot: "bg-green-400",
      },
    },
    offline: {
      label: "Offline",
      icon: WifiOff,
      colors: {
        bg: "bg-gray-900",
        text: "text-gray-500",
        border: "border-gray-700",
        dot: "bg-gray-600",
      },
    },
    active: {
      label: "Active",
      icon: CheckCircle,
      colors: {
        bg: "bg-green-900",
        text: "text-green-400",
        border: "border-green-700",
        dot: "bg-green-400",
      },
    },
    inactive: {
      label: "Inactive",
      icon: XCircle,
      colors: {
        bg: "bg-gray-900",
        text: "text-gray-500",
        border: "border-gray-700",
        dot: "bg-gray-600",
      },
    },
    pending: {
      label: "Pending",
      icon: Clock,
      colors: {
        bg: "bg-yellow-900",
        text: "text-yellow-400",
        border: "border-yellow-700",
        dot: "bg-yellow-400",
      },
    },
    processing: {
      label: "Processing",
      icon: Play,
      colors: {
        bg: "bg-blue-900",
        text: "text-blue-400",
        border: "border-blue-700",
        dot: "bg-blue-400",
      },
    },
    paused: {
      label: "Paused",
      icon: Pause,
      colors: {
        bg: "bg-gray-800",
        text: "text-gray-400",
        border: "border-gray-600",
        dot: "bg-gray-500",
      },
    },
    warning: {
      label: "Warning",
      icon: AlertCircle,
      colors: {
        bg: "bg-yellow-900",
        text: "text-yellow-400",
        border: "border-yellow-700",
        dot: "bg-yellow-400",
      },
    },
    error: {
      label: "Error",
      icon: XCircle,
      colors: {
        bg: "bg-red-900",
        text: "text-red-400",
        border: "border-red-700",
        dot: "bg-red-400",
      },
    },
    success: {
      label: "Success",
      icon: CheckCircle,
      colors: {
        bg: "bg-green-900",
        text: "text-green-400",
        border: "border-green-700",
        dot: "bg-green-400",
      },
    },
    new: {
      label: "New",
      icon: Circle,
      colors: {
        bg: "bg-gray-800",
        text: "text-gray-300",
        border: "border-gray-600",
        dot: "bg-gray-400",
      },
    },
    verified: {
      label: "Verified",
      icon: CheckCircle,
      colors: {
        bg: "bg-gray-800",
        text: "text-gray-300",
        border: "border-gray-600",
        dot: "bg-gray-400",
      },
    },
    unverified: {
      label: "Unverified",
      icon: AlertCircle,
      colors: {
        bg: "bg-gray-900",
        text: "text-gray-500",
        border: "border-gray-700",
        dot: "bg-gray-600",
      },
    },
    default: {
      label: "Default",
      icon: Circle,
      colors: {
        bg: "bg-gray-800",
        text: "text-gray-400",
        border: "border-gray-600",
        dot: "bg-gray-500",
      },
    },
  };

  const config = statusConfig[status] || statusConfig.default;
  const IconComponent = CustomIcon || config.icon;

  // Size configurations
  const sizeConfig = {
    sm: {
      container: "h-5 px-2 text-xs",
      icon: "h-3 w-3",
      dot: "h-1.5 w-1.5",
      gap: "gap-1",
    },
    default: {
      container: "h-6 px-2.5 text-sm",
      icon: "h-3.5 w-3.5",
      dot: "h-2 w-2",
      gap: "gap-1.5",
    },
    lg: {
      container: "h-7 px-3 text-sm",
      icon: "h-4 w-4",
      dot: "h-2.5 w-2.5",
      gap: "gap-2",
    },
  };

  const sizes = sizeConfig[size] || sizeConfig.default;

  // Variant styles
  const getVariantClasses = () => {
    const colors = customColor || config.colors;

    switch (variant) {
      case "dot":
        return cn(
          "bg-transparent border-0 text-gray-400",
          sizes.container,
          sizes.gap
        );
      case "outline":
        return cn(
          "bg-transparent border",
          colors.border,
          colors.text,
          sizes.container,
          sizes.gap
        );
      case "ghost":
        return cn(
          "bg-transparent border-0",
          colors.text,
          sizes.container,
          sizes.gap
        );
      default:
        return cn(
          colors.bg,
          colors.text,
          "border border-transparent",
          sizes.container,
          sizes.gap
        );
    }
  };

  const displayText = children || config.label;

  return (
    <span
      className={cn(
        "inline-flex items-center rounded-full font-medium transition-colors",
        "focus:outline-none focus:ring-2 focus:ring-gray-600 focus:ring-offset-1 focus:ring-offset-[#0f0f0f]",
        getVariantClasses(),
        className
      )}
      {...props}
    >
      {/* Status Dot */}
      {showDot && (
        <span
          className={cn(
            "rounded-full flex-shrink-0",
            sizes.dot,
            customColor?.dot || config.colors.dot,
            pulse && "animate-pulse"
          )}
        />
      )}

      {/* Status Icon */}
      {showIcon && !showDot && IconComponent && (
        <IconComponent
          className={cn("flex-shrink-0", sizes.icon, pulse && "animate-pulse")}
        />
      )}

      {/* Status Text */}
      {displayText && <span className="truncate">{displayText}</span>}
    </span>
  );
};

export default StatusBadge;
