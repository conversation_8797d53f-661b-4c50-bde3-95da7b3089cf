import * as React from "react";
import { cn } from "@/lib/utils";
import { Loader2 } from "lucide-react";

const Spinner = ({
  size = "default", // "sm", "default", "lg", "xl"
  variant = "default", // "default", "primary", "secondary", "accent"
  className,
  children,
  ...props
}) => {
  const getSizeClass = () => {
    switch (size) {
      case "sm":
        return "h-4 w-4";
      case "lg":
        return "h-8 w-8";
      case "xl":
        return "h-12 w-12";
      default:
        return "h-6 w-6";
    }
  };

  const getVariantClass = () => {
    switch (variant) {
      case "primary":
        return "text-gray-300";
      case "secondary":
        return "text-gray-500";
      case "accent":
        return "text-gray-400";
      default:
        return "text-gray-500";
    }
  };

  return (
    <div
      className={cn("flex items-center justify-center", className)}
      {...props}
    >
      <Loader2
        className={cn("animate-spin", getSizeClass(), getVariantClass())}
      />
      {children && (
        <span className={cn("ml-2 text-sm", getVariantClass())}>
          {children}
        </span>
      )}
    </div>
  );
};

export default Spinner;
