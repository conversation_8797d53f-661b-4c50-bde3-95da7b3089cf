import mongoose from "mongoose";

const timeSessionSchema = new mongoose.Schema(
  {
    employeeId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Employee",
      required: true,
    },

    agencyId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      required: true,
    },

    startTime: {
      type: Date,
      required: true,
    },

    endTime: {
      type: Date,
    },

    duration: {
      type: Number, // in minutes
    },

    taskDescription: {
      type: String,
      trim: true,
      maxlength: 500,
    },

    status: {
      type: String,
      enum: ["active", "completed", "paused"],
      default: "active",
    },

    breakTime: {
      type: Number, // in minutes
      default: 0,
    },

    date: {
      type: String, // Format: YYYY-MM-DD for easy querying
      required: true,
    },
  },
  { timestamps: true },
);

// Index for efficient queries
timeSessionSchema.index({ employeeId: 1, date: 1 });
timeSessionSchema.index({ agencyId: 1, date: 1 });

export default mongoose.model("TimeSession", timeSessionSchema);
