import mongoose from "mongoose";

const weeklyScheduleSchema = new mongoose.Schema(
  {
    agencyId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      required: true,
      index: true,
    },
    weekStartDate: {
      type: Date,
      required: true,
      index: true,
    },
    weekEndDate: {
      type: Date,
      required: true,
    },
    weekNumber: {
      type: Number,
      required: true,
    },
    year: {
      type: Number,
      required: true,
    },
    description: {
      type: String,
      trim: true,
      maxlength: 500,
      default: "",
    },
    status: {
      type: String,
      enum: ["draft", "updated", "published", "completed"],
      default: "draft",
    },
    shifts: [
      {
        day: {
          type: String,
          enum: [
            "Monday",
            "Tuesday",
            "Wednesday",
            "Thursday",
            "Friday",
            "Saturday",
            "Sunday",
          ],
          required: true,
        },
        date: {
          type: Date,
          required: true,
        },
        startTime: {
          type: String,
          required: true,
          validate: {
            validator: function (v) {
              return /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(v);
            },
            message: "Start time must be in HH:MM format",
          },
        },
        endTime: {
          type: String,
          required: true,
          validate: {
            validator: function (v) {
              return /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(v);
            },
            message: "End time must be in HH:MM format",
          },
        },
        employeeId: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Employee",
          default: null, // null means unassigned
        },
        position: {
          type: String,
          default: "Employee",
        },
        note: {
          type: String,
          trim: true,
          maxlength: 500,
          default: "",
        },
        isClosingShift: {
          type: Boolean,
          default: false,
        },
        breakMinutes: {
          type: Number,
          default: 0,
          min: 0,
        },
        comments: [
          {
            author: {
              type: mongoose.Schema.Types.ObjectId,
              required: true,
              refPath: "shifts.comments.authorModel",
            },
            authorModel: {
              type: String,
              required: true,
              enum: ["Agency", "Employee"],
            },
            content: {
              type: String,
              required: true,
              trim: true,
              maxlength: 1000,
            },
            createdAt: {
              type: Date,
              default: Date.now,
            },
          },
        ],
      },
    ],
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      refPath: "createdByModel",
    },
    createdByModel: {
      type: String,
      required: true,
      enum: ["Agency", "Employee"],
    },
  },
  {
    timestamps: true,
  },
);

// Indexes for efficient queries
weeklyScheduleSchema.index({ agencyId: 1, weekStartDate: 1 });
weeklyScheduleSchema.index({ agencyId: 1, year: 1, weekNumber: 1 });
weeklyScheduleSchema.index({ "shifts.employeeId": 1, weekStartDate: 1 });

// Virtual for week identifier
weeklyScheduleSchema.virtual("weekIdentifier").get(function () {
  return `${this.year}-W${this.weekNumber.toString().padStart(2, "0")}`;
});

// Method to get total hours for the week
weeklyScheduleSchema.methods.getTotalHours = function () {
  return this.shifts.reduce((total, shift) => {
    const [startHour, startMin] = shift.startTime.split(":").map(Number);
    const [endHour, endMin] = shift.endTime.split(":").map(Number);
    const startMinutes = startHour * 60 + startMin;
    const endMinutes = endHour * 60 + endMin;
    const shiftMinutes = endMinutes - startMinutes - (shift.breakMinutes || 0);
    return total + shiftMinutes / 60;
  }, 0);
};

// Method to get employee hours for the week
weeklyScheduleSchema.methods.getEmployeeHours = function (employeeId) {
  return this.shifts
    .filter(
      (shift) =>
        shift.employeeId &&
        shift.employeeId.toString() === employeeId.toString(),
    )
    .reduce((total, shift) => {
      const [startHour, startMin] = shift.startTime.split(":").map(Number);
      const [endHour, endMin] = shift.endTime.split(":").map(Number);
      const startMinutes = startHour * 60 + startMin;
      const endMinutes = endHour * 60 + endMin;
      const shiftMinutes =
        endMinutes - startMinutes - (shift.breakMinutes || 0);
      return total + shiftMinutes / 60;
    }, 0);
};

// Static method to find schedule by week
weeklyScheduleSchema.statics.findByWeek = function (
  agencyId,
  year,
  weekNumber,
) {
  return this.findOne({ agencyId, year, weekNumber })
    .populate("shifts.employeeId", "name email role")
    .populate("createdBy", "firstName lastName email");
};

// Static method to get schedules for date range
weeklyScheduleSchema.statics.findByDateRange = function (
  agencyId,
  startDate,
  endDate,
) {
  return this.find({
    agencyId,
    weekStartDate: { $gte: startDate },
    weekEndDate: { $lte: endDate },
  })
    .populate("shifts.employeeId", "name email role")
    .sort({ weekStartDate: 1 });
};

const WeeklySchedule = mongoose.model("WeeklySchedule", weeklyScheduleSchema);

export default WeeklySchedule;
