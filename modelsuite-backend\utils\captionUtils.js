// Utility functions for the Smart Caption Generator
import { CAPTION_CONFIG } from "../config/captionConfig.js";

/**
 * Get today's date in YYYY-MM-DD format
 * @returns {string} Today's date
 */
export const getToday = () => {
  return new Date().toISOString().split("T")[0];
};

/**
 * Validate if a URL is properly formatted and secure
 * @param {string} url - URL to validate
 * @returns {boolean} True if valid
 */
export const isValidUrl = (url) => {
  try {
    const parsedUrl = new URL(url);
    return parsedUrl.protocol === "https:" || parsedUrl.protocol === "http:";
  } catch {
    return false;
  }
};

/**
 * Sleep for specified milliseconds
 * @param {number} ms - Milliseconds to sleep
 * @returns {Promise<void>}
 */
export const sleep = (ms) => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

/**
 * Retry a function with exponential backoff
 * @param {Function} fn - Function to retry
 * @param {number} maxRetries - Maximum number of retries
 * @param {number} baseDelay - Base delay in milliseconds
 * @returns {Promise<any>} Result of the function
 */
export const retryWithBackoff = async (
  fn,
  maxRetries = CAPTION_CONFIG.MAX_RETRIES,
  baseDelay = CAPTION_CONFIG.RETRY_DELAY,
) => {
  let lastError;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;

      if (attempt === maxRetries) {
        throw lastError;
      }

      // Exponential backoff: 1s, 2s, 4s, etc.
      const delay = baseDelay * Math.pow(2, attempt);
      await sleep(delay);

      console.warn(
        `Attempt ${attempt + 1} failed, retrying in ${delay}ms:`,
        error.message,
      );
    }
  }

  throw lastError;
};

/**
 * Create a fetch request with timeout
 * @param {string} url - URL to fetch
 * @param {object} options - Fetch options
 * @param {number} timeout - Timeout in milliseconds
 * @returns {Promise<Response>} Fetch response
 */
export const fetchWithTimeout = async (
  url,
  options = {},
  timeout = CAPTION_CONFIG.API_TIMEOUT,
) => {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
    });

    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);

    if (error.name === "AbortError") {
      throw new Error(`Request timeout after ${timeout}ms`);
    }

    throw error;
  }
};

/**
 * Sanitize and validate media type
 * @param {string} mediaType - Media type to validate
 * @returns {string} Validated media type
 * @throws {Error} If invalid media type
 */
export const validateMediaType = (mediaType) => {
  if (!mediaType || typeof mediaType !== "string") {
    throw new Error("Media type is required");
  }

  const sanitized = mediaType.toLowerCase().trim();

  if (!["image", "video"].includes(sanitized)) {
    throw new Error('Media type must be either "image" or "video"');
  }

  return sanitized;
};

/**
 * Create minimal metadata object (avoiding large responses)
 * @param {object} fullResponse - Full AI response
 * @param {string} aiService - AI service name
 * @returns {object} Minimal metadata
 */
export const createMinimalMetadata = (fullResponse, aiService) => {
  const metadata = {
    aiService,
    timestamp: new Date().toISOString(),
  };

  if (aiService === "gpt-4" && fullResponse) {
    metadata.modelVersion = fullResponse.model || "gpt-4-vision-preview";
    metadata.tokenUsage = fullResponse.usage?.total_tokens || 0;
    metadata.responseId = fullResponse.id;
  } else if (aiService === "gemini-vision" && fullResponse) {
    metadata.modelVersion = "gemini-pro-vision";
    metadata.candidateCount = fullResponse.candidates?.length || 0;
    metadata.responseId = fullResponse.candidates?.[0]?.finishReason;
  }

  return metadata;
};
