import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import notesApi from "@/services/notesApi";

// Thunks
export const fetchNotes = createAsyncThunk(
  "notes/fetchNotes",
  async ({ modelId, params }, { rejectWithValue }) => {
    try {
      const res = await notesApi.fetchNotesByModel(modelId, params);
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const fetchAgencyNotes = createAsyncThunk(
  "notes/fetchAgencyNotes",
  async (
    { agencyId = localStorage.getItem("agencyId"), params = {} } = {},
    { rejectWithValue }
  ) => {
    try {
      const res = await notesApi.fetchNotesByAgency(agencyId, params);
      // backend returns { notes, pagination }
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const createNote = createAsyncThunk(
  "notes/createNote",
  async ({ payload }, { rejectWithValue }) => {
    try {
      const res = await notesApi.createNote(payload);
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const updateNote = createAsyncThunk(
  "notes/updateNote",
  async ({ noteId, data }, { rejectWithValue }) => {
    try {
      const res = await notesApi.updateNote(noteId, data);
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const deleteNote = createAsyncThunk(
  "notes/deleteNote",
  async ({ noteId }, { rejectWithValue }) => {
    try {
      const res = await notesApi.deleteNote(noteId);
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const togglePin = createAsyncThunk(
  "notes/togglePin",
  async ({ noteId }, { rejectWithValue }) => {
    try {
      const res = await notesApi.togglePinNote(noteId);
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const bulkDelete = createAsyncThunk(
  "notes/bulkDelete",
  async ({ noteIds }, { rejectWithValue }) => {
    try {
      const res = await notesApi.bulkDeleteNotes(noteIds);
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const bulkPin = createAsyncThunk(
  "notes/bulkPin",
  async ({ noteIds, isPinned }, { rejectWithValue }) => {
    try {
      const res = await notesApi.bulkPinNotes(noteIds, isPinned);
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

const initialState = {
  byId: {},
  allIds: [],
  pagination: null,
  loading: false,
  error: null,
};

const notesSlice = createSlice({
  name: "notes",
  initialState,
  reducers: {
    clearNotesError(state) {
      state.error = null;
    },
    clearNotes(state) {
      state.byId = {};
      state.allIds = [];
      state.pagination = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchNotes.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchNotes.fulfilled, (state, action) => {
        state.loading = false;
        const notes = action.payload.data?.notes || action.payload.notes || [];
        const pagination =
          action.payload.data?.pagination || action.payload.pagination || null;
        state.byId = {};
        state.allIds = [];
        notes.forEach((n) => {
          state.byId[n._id] = n;
          state.allIds.push(n._id);
        });
        state.pagination = pagination;
      })

      .addCase(fetchAgencyNotes.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAgencyNotes.fulfilled, (state, action) => {
        state.loading = false;
        const notes = action.payload.data?.notes || action.payload.notes || [];
        const pagination =
          action.payload.data?.pagination || action.payload.pagination || null;
        state.byId = {};
        state.allIds = [];
        notes.forEach((n) => {
          state.byId[n._id] = n;
          state.allIds.push(n._id);
        });
        state.pagination = pagination;
      })
      .addCase(fetchAgencyNotes.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || action.error.message;
      })
      .addCase(fetchNotes.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || action.error.message;
      })

      .addCase(createNote.fulfilled, (state, action) => {
        const note = action.payload.data || action.payload;
        if (note) {
          state.byId[note._id] = note;
          state.allIds.unshift(note._id);
        }
      })

      .addCase(updateNote.fulfilled, (state, action) => {
        const note = action.payload.data || action.payload;
        if (note) {
          state.byId[note._id] = note;
        }
      })

      .addCase(deleteNote.fulfilled, (state, action) => {
        const meta = action.meta.arg;
        const id = meta.noteId;
        if (id) {
          delete state.byId[id];
          state.allIds = state.allIds.filter((i) => i !== id);
        }
      })

      .addCase(togglePin.fulfilled, (state, action) => {
        const note = action.payload.data || action.payload;
        if (note) {
          state.byId[note._id] = note;
        }
      })

      .addCase(bulkDelete.fulfilled, (state, action) => {
        const noteIds = action.meta.arg.noteIds || [];
        noteIds.forEach((id) => {
          delete state.byId[id];
        });
        state.allIds = state.allIds.filter((id) => !noteIds.includes(id));
      })

      .addCase(bulkPin.fulfilled, (s) => s);
  },
});

export const { clearNotesError, clearNotes } = notesSlice.actions;

export default notesSlice.reducer;
