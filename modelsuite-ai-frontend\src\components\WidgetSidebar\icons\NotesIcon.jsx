import React from "react";

const NotesIcon = ({ className }) => {
  return (
    <svg
      width="100%"
      height="100%"
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <defs>
        {/* Background gradient - dark purple to blue */}
        <radialGradient id="notesBgGrad" cx="50%" cy="30%" r="80%">
          <stop offset="0%" stopColor="#2D1B69" />
          <stop offset="50%" stopColor="#1A1B4A" />
          <stop offset="100%" stopColor="#0F1B3C" />
        </radialGradient>

        {/* Main stroke gradient - magenta to blue */}
        <linearGradient id="notesMainGrad" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#E91E63" />
          <stop offset="30%" stopColor="#9C27B0" />
          <stop offset="70%" stopColor="#673AB7" />
          <stop offset="100%" stopColor="#2196F3" />
        </linearGradient>

        {/* Spiral binding gradient */}
        <linearGradient id="notesSpiralGrad" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" stopColor="#E91E63" />
          <stop offset="50%" stopColor="#9C27B0" />
          <stop offset="100%" stopColor="#673AB7" />
        </linearGradient>

        {/* Pencil gradient */}
        <linearGradient
          id="notesPencilGrad"
          x1="0%"
          y1="0%"
          x2="100%"
          y2="100%"
        >
          <stop offset="0%" stopColor="#673AB7" />
          <stop offset="50%" stopColor="#3F51B5" />
          <stop offset="100%" stopColor="#2196F3" />
        </linearGradient>

        {/* Text lines gradient */}
        <linearGradient id="notesTextGrad" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stopColor="#9C27B0" />
          <stop offset="100%" stopColor="#3F51B5" />
        </linearGradient>

        {/* Neon glow filter */}
        <filter id="notesGlow" x="-50%" y="-50%" width="200%" height="200%">
          <feGaussianBlur stdDeviation="8" result="blur" />
          <feMerge>
            <feMergeNode in="blur" />
            <feMergeNode in="SourceGraphic" />
          </feMerge>
        </filter>

        {/* Subtle inner glow */}
        <filter
          id="notesInnerGlow"
          x="-30%"
          y="-30%"
          width="160%"
          height="160%"
        >
          <feGaussianBlur stdDeviation="4" result="blur" />
          <feMerge>
            <feMergeNode in="blur" />
            <feMergeNode in="SourceGraphic" />
          </feMerge>
        </filter>
      </defs>

      {/* Rounded square background */}
      <rect
        x="32"
        y="32"
        width="960"
        height="960"
        rx="180"
        ry="180"
        fill="url(#notesBgGrad)"
      />

      {/* Notepad background */}
      <rect
        x="200"
        y="180"
        width="480"
        height="580"
        rx="24"
        ry="24"
        fill="none"
        stroke="url(#notesMainGrad)"
        strokeWidth="16"
        filter="url(#notesGlow)"
      />

      {/* Spiral binding - left side */}
      <g filter="url(#notesInnerGlow)">
        {/* Binding strip */}
        <rect
          x="180"
          y="200"
          width="60"
          height="540"
          rx="8"
          ry="8"
          fill="none"
          stroke="url(#notesSpiralGrad)"
          strokeWidth="12"
        />

        {/* Spiral rings */}
        <circle
          cx="210"
          cy="280"
          r="24"
          fill="none"
          stroke="url(#notesSpiralGrad)"
          strokeWidth="16"
          filter="url(#notesGlow)"
        />
        <circle
          cx="210"
          cy="440"
          r="24"
          fill="none"
          stroke="url(#notesSpiralGrad)"
          strokeWidth="16"
          filter="url(#notesGlow)"
        />
        <circle
          cx="210"
          cy="600"
          r="24"
          fill="none"
          stroke="url(#notesSpiralGrad)"
          strokeWidth="16"
          filter="url(#notesGlow)"
        />
      </g>

      {/* Text lines */}
      <g filter="url(#notesInnerGlow)">
        <line
          x1="280"
          y1="300"
          x2="580"
          y2="300"
          stroke="url(#notesTextGrad)"
          strokeWidth="12"
          strokeLinecap="round"
        />
        <line
          x1="280"
          y1="360"
          x2="540"
          y2="360"
          stroke="url(#notesTextGrad)"
          strokeWidth="12"
          strokeLinecap="round"
        />
        <line
          x1="280"
          y1="420"
          x2="560"
          y2="420"
          stroke="url(#notesTextGrad)"
          strokeWidth="12"
          strokeLinecap="round"
        />
        <line
          x1="280"
          y1="480"
          x2="520"
          y2="480"
          stroke="url(#notesTextGrad)"
          strokeWidth="12"
          strokeLinecap="round"
        />
      </g>

      {/* Pencil */}
      <g transform="translate(500, 520) rotate(35)" filter="url(#notesGlow)">
        {/* Pencil body */}
        <rect
          x="0"
          y="-12"
          width="200"
          height="24"
          rx="12"
          ry="12"
          fill="none"
          stroke="url(#notesPencilGrad)"
          strokeWidth="16"
          strokeLinecap="round"
        />

        {/* Pencil tip */}
        <polygon
          points="200,-12 240,0 200,12"
          fill="none"
          stroke="url(#notesPencilGrad)"
          strokeWidth="16"
          strokeLinejoin="round"
          strokeLinecap="round"
        />

        {/* Pencil end/eraser */}
        <rect
          x="-20"
          y="-8"
          width="20"
          height="16"
          rx="8"
          ry="8"
          fill="none"
          stroke="url(#notesPencilGrad)"
          strokeWidth="12"
        />

        {/* Metal ferrule */}
        <rect
          x="-8"
          y="-6"
          width="16"
          height="12"
          fill="none"
          stroke="url(#notesPencilGrad)"
          strokeWidth="8"
        />
      </g>
    </svg>
  );
};

export default NotesIcon;
