import { ApiError } from "../../utils/ApiError.js";
import { ApiResponse } from "../../utils/ApiResponse.js";
import { asyncHandler } from "../../utils/asyncHandler.js";
import ContentCategory from "../../models/contentupload/ContentCategory.js";
import ContentUpload from "../../models/contentupload/ContentUpload.js";
import ModelUser from "../../models/model.js";
import Agency from "../../models/agency.js";
import AgencyConfiguration from "../../models/contentupload/AgencyConfiguration.js";
import r2Client from "../../utils/cloudflarer2.js";
import StatusTrackingService from "../../services/contentupload/statusTrackingService.js";
import ModelCategoryAssignment from "../../models/contentupload/ModelCategoryAssignment.js";
import ModelAssignmentService from "../../services/contentupload/modelAssignmentService.js";
import ReminderCalculationService from "../../services/contentupload/reminderCalculationService.js";
import NotificationService from "../../services/contentupload/notificationService.js";

/**
 * Content Upload Controller - Handles content upload management endpoints
 * Provides CRUD operations for categories and uploads with approval workflows
 */
class ContentUploadController {
  /**
   * Helper function to get agencyId based on user role
   * @param {Object} user - The user object from req.user
   * @returns {String} - The agencyId
   */
  static getAgencyId(user) {
    // For agency users: return their own _id (they are the agency)
    // For model users: return their agencyId (reference to the agency)
    // For employee users: return their agencyId (reference to the agency they work for)
    if (user.role === "agency") {
      return user._id;
    }
    return user.agencyId;
  }

  /**
   * Get effective agency ID - simplified to always use direct agency relationship
   * @param {Object} user - The user object from req.user
   * @returns {String} - The direct agencyId
   */
  static getEffectiveAgencyId(user) {
    // Always use direct agency relationship for simplicity
    return ContentUploadController.getAgencyId(user);
  }

  /**
   * Check if user has permission to perform upload actions (approve/reject)
   * @param {Object} user - The authenticated user object
   * @param {string} action - The action to check ('approve' or 'reject')
   * @returns {Promise<boolean>} - True if user has permission, false otherwise
   */
  static checkUploadPermission = async (user, action) => {
    try {
      // Agency users have full access
      if (user.role === "agency") {
        return true;
      }

      // Employees can have specific permissions
      if (user.role === "employee" && user.agencyId) {
        // Check if employee has content moderation permissions
        const effectivePermissions = user.effectivePermissions || [];

        // Check for specific upload permissions
        const hasApprovePermission =
          effectivePermissions.includes("uploads.approve");
        const hasRejectPermission =
          effectivePermissions.includes("uploads.reject");
        const hasModeratePermission =
          effectivePermissions.includes("uploads.moderate");

        if (action === "approve") {
          return hasApprovePermission || hasModeratePermission;
        }

        if (action === "reject") {
          return hasRejectPermission || hasModeratePermission;
        }

        // Default: allow if employee has any content management role
        const allowedRoles = ["manager", "admin", "content_moderator"];
        return allowedRoles.includes(user.subRole);
      }

      // Models that are part of an agency cannot approve/reject
      if (user.role === "model" && user.agencyId) {
        return false;
      }

      return false;
    } catch (error) {
      console.error("Error checking upload permission:", error);
      return false;
    }
  };

  /**
   * Check if user has permission to perform category management actions
   * @param {Object} user - The authenticated user object
   * @param {string} action - The action to check ('create', 'update', or 'delete')
   * @returns {Promise<boolean>} - True if user has permission, false otherwise
   */
  static checkCategoryPermission = async (user, action) => {
    try {
      // Agency users have full access
      if (user.role === "agency") {
        return true;
      }

      // Employees can have specific permissions
      if (user.role === "employee" && user.agencyId) {
        // Check if employee has category management permissions
        const effectivePermissions = user.effectivePermissions || [];

        // Check for specific category permissions
        const hasCreatePermission =
          effectivePermissions.includes("categories.create");
        const hasUpdatePermission =
          effectivePermissions.includes("categories.update");
        const hasDeletePermission =
          effectivePermissions.includes("categories.delete");
        const hasManagePermission =
          effectivePermissions.includes("categories.manage");

        if (action === "create") {
          return hasCreatePermission || hasManagePermission;
        }

        if (action === "update") {
          return hasUpdatePermission || hasManagePermission;
        }

        if (action === "delete") {
          return hasDeletePermission || hasManagePermission;
        }

        // Default: allow if employee has any management role
        const allowedRoles = ["manager", "admin", "content_moderator"];
        return allowedRoles.includes(user.subRole);
      }

      // Models cannot manage categories
      if (user.role === "model" && user.agencyId) {
        return false;
      }

      return false;
    } catch (error) {
      console.error("Error checking category permission:", error);
      return false;
    }
  };
  /**
   * Create a new content category
   * POST /api/v1/content/categories
   */
  static createCategory = asyncHandler(async (req, res) => {
    const {
      label,
      platform,
      reminderFrequency,
      reminderType,
      instructionTooltip,
      hardRules,
    } = req.body;
    const { _id: userId, role } = req.user;
    const agencyId = ContentUploadController.getAgencyId(req.user);

    // Check if user has permission to create categories
    const hasPermission = await ContentUploadController.checkCategoryPermission(
      req.user,
      "create",
    );
    if (!hasPermission) {
      throw new ApiError(403, "Access denied: Agency access required");
    }

    // Validate required fields
    if (!label || !platform || !reminderFrequency) {
      throw new ApiError(
        400,
        "Label, platform, and reminder frequency are required",
      );
    }

    // Check if category with same label exists for this agency
    const existingCategory = await ContentCategory.findOne({
      agencyId,
      label: label.trim(),
      isActive: true,
    });

    if (existingCategory) {
      throw new ApiError(409, "Category with this label already exists");
    }

    try {
      const categoryData = {
        agencyId,
        label: label.trim(),
        platform,
        reminderFrequency,
        reminderType: reminderType || "soft",
        instructionTooltip: instructionTooltip?.trim(),
        hardRules: {
          maxFileSize: hardRules?.maxFileSize
            ? Number(hardRules.maxFileSize)
            : 2147483648, // 2GB default
          allowedFormats: hardRules?.allowedFormats || [
            "jpg",
            "jpeg",
            "png",
            "mp4",
            "mov",
          ],
          requiresApproval:
            hardRules?.requiresApproval === true ||
            hardRules?.requiresApproval === "true",
          requiresWatermark:
            hardRules?.requiresWatermark === true ||
            hardRules?.requiresWatermark === "true",
          requiresConsent:
            hardRules?.requiresConsent !== false &&
            hardRules?.requiresConsent !== "false", // default true
        },
        createdBy: userId,
      };

      // Debug logging
      console.log(
        "Creating category with data:",
        JSON.stringify(categoryData, null, 2),
      );
      console.log("Original hardRules from request:", hardRules);

      const category = new ContentCategory(categoryData);
      await category.save();

      res
        .status(201)
        .json(new ApiResponse(201, category, "Category created successfully"));
    } catch (error) {
      console.error("Category creation failed:", error);

      // Handle mongoose validation errors
      if (error.name === "ValidationError") {
        const validationErrors = Object.values(error.errors).map(
          (err) => err.message,
        );
        throw new ApiError(
          400,
          `Validation failed: ${validationErrors.join(", ")}`,
        );
      }

      // Handle duplicate key errors
      if (error.code === 11000) {
        throw new ApiError(409, "Category with this label already exists");
      }

      // Log the full error for debugging
      console.error("Full error details:", {
        name: error.name,
        message: error.message,
        stack: error.stack,
        code: error.code,
      });

      throw new ApiError(500, `Failed to create category: ${error.message}`);
    }
  });

  /**
   * Get all categories for an agency
   * GET /api/v1/content/categories
   */
  static getAgencyCategories = asyncHandler(async (req, res) => {
    const { includeInactive = false } = req.query;
    const agencyId = ContentUploadController.getEffectiveAgencyId(req.user);

    try {
      const query = { agencyId };
      if (!includeInactive) {
        query.isActive = true;
      }

      const categories = await ContentCategory.find(query)
        .populate("uploadCount")
        .sort({ createdAt: -1 });

      res.status(200).json(
        new ApiResponse(
          200,
          {
            categories,
            totalActive: categories.filter((cat) => cat.isActive).length,
          },
          "Categories retrieved successfully",
        ),
      );
    } catch (error) {
      console.error("Failed to get agency categories:", error);
      throw new ApiError(500, "Failed to retrieve categories");
    }
  });

  /**
   * Update a category
   * PUT /api/v1/content/categories/:id
   */
  static updateCategory = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { _id: userId, role } = req.user;
    const updateData = req.body;
    const agencyId = ContentUploadController.getAgencyId(req.user);

    // Check if user has permission to update categories
    const hasPermission = await ContentUploadController.checkCategoryPermission(
      req.user,
      "update",
    );
    if (!hasPermission) {
      throw new ApiError(403, "Access denied: Agency access required");
    }

    try {
      const category = await ContentCategory.findOne({ _id: id, agencyId });

      if (!category) {
        throw new ApiError(404, "Category not found");
      }

      // Update fields
      Object.keys(updateData).forEach((key) => {
        if (key !== "_id" && key !== "agencyId" && key !== "createdBy") {
          category[key] = updateData[key];
        }
      });

      category.lastModifiedBy = userId;
      await category.save();

      res
        .status(200)
        .json(new ApiResponse(200, category, "Category updated successfully"));
    } catch (error) {
      console.error("Category update failed:", error);
      throw new ApiError(500, "Failed to update category");
    }
  });

  /**
   * Delete (deactivate) a category
   * DELETE /api/v1/content/categories/:id
   */
  static deleteCategory = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { _id: userId, role } = req.user;
    const agencyId = ContentUploadController.getEffectiveAgencyId(req.user);

    // Check if user has permission to delete categories
    const hasPermission = await ContentUploadController.checkCategoryPermission(
      req.user,
      "delete",
    );
    if (!hasPermission) {
      throw new ApiError(403, "Access denied: Agency access required");
    }

    try {
      const category = await ContentCategory.findOne({ _id: id, agencyId });

      if (!category) {
        throw new ApiError(404, "Category not found");
      }

      // Check if category has active uploads
      const activeUploads = await ContentUpload.countDocuments({
        categoryId: id,
        status: { $nin: ["archived"] },
        isDeleted: false,
      });

      if (activeUploads > 0) {
        throw new ApiError(400, "Cannot delete category with active uploads");
      }

      category.isActive = false;
      category.lastModifiedBy = userId;
      await category.save();

      res
        .status(200)
        .json(new ApiResponse(200, null, "Category deleted successfully"));
    } catch (error) {
      console.error("Category deletion failed:", error);
      throw new ApiError(500, "Failed to delete category");
    }
  });

  /**
   * Upload content file with enhanced validation and metadata processing
   * POST /api/v1/content/upload
   */
  static uploadContent = asyncHandler(async (req, res) => {
    const { categoryId, metadata, hasConsent, hasWatermark, releaseFormUrl } =
      req.body;
    const { _id: modelId, role } = req.user;

    // Use direct agency relationship for uploads
    const agencyId = ContentUploadController.getAgencyId(req.user);
    const file = req.file;

    // Enhanced validation for required fields
    if (!file) {
      throw new ApiError(400, "File is required");
    }

    if (!categoryId) {
      throw new ApiError(400, "Category ID is required");
    }

    if (!hasConsent || hasConsent === "false") {
      throw new ApiError(400, "Consent is required for content upload");
    }

    // Validate file integrity
    if (!file.originalname || file.originalname.trim() === "") {
      throw new ApiError(400, "File must have a valid name");
    }

    if (file.size === 0) {
      throw new ApiError(400, "File cannot be empty");
    }

    // Validate metadata if provided
    if (metadata) {
      if (metadata.title && metadata.title.length > 200) {
        throw new ApiError(400, "Title cannot exceed 200 characters");
      }
      if (metadata.description && metadata.description.length > 1000) {
        throw new ApiError(400, "Description cannot exceed 1000 characters");
      }
      if (
        metadata.tags &&
        Array.isArray(metadata.tags) &&
        metadata.tags.length > 20
      ) {
        throw new ApiError(400, "Cannot have more than 20 tags");
      }
    }

    try {
      // Verify category exists and belongs to agency
      const category = await ContentCategory.findOne({
        _id: categoryId,
        agencyId,
        isActive: true,
      });

      if (!category) {
        throw new ApiError(404, "Category not found or inactive");
      }

      // Enhanced file validation against category rules
      const fileExtension = file.originalname.split(".").pop()?.toLowerCase();

      if (!fileExtension) {
        throw new ApiError(400, "File must have a valid extension");
      }

      // Validate MIME type matches extension
      const expectedMimeTypes = {
        jpg: ["image/jpeg"],
        jpeg: ["image/jpeg"],
        png: ["image/png"],
        gif: ["image/gif"],
        webp: ["image/webp"],
        mp4: ["video/mp4"],
        mov: ["video/quicktime"],
        avi: ["video/x-msvideo"],
        pdf: ["application/pdf"],
        doc: ["application/msword"],
        docx: [
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        ],
      };

      if (
        expectedMimeTypes[fileExtension] &&
        !expectedMimeTypes[fileExtension].includes(file.mimetype)
      ) {
        throw new ApiError(
          400,
          `File MIME type ${file.mimetype} does not match extension .${fileExtension}`,
        );
      }

      if (!category.isFileFormatAllowed(fileExtension)) {
        throw new ApiError(
          400,
          `File format .${fileExtension} is not allowed for this category. Allowed formats: ${category.hardRules.allowedFormats.join(
            ", ",
          )}`,
        );
      }

      if (!category.isFileSizeAllowed(file.size)) {
        const maxSizeMB = Math.round(
          category.hardRules.maxFileSize / (1024 * 1024),
        );
        throw new ApiError(
          400,
          `File size ${Math.round(
            file.size / (1024 * 1024),
          )}MB exceeds maximum allowed size of ${maxSizeMB}MB`,
        );
      }

      // Additional security checks
      const dangerousExtensions = [
        "exe",
        "bat",
        "cmd",
        "scr",
        "pif",
        "vbs",
        "js",
        "jar",
      ];
      if (dangerousExtensions.includes(fileExtension)) {
        throw new ApiError(400, "File type not allowed for security reasons");
      }

      // Upload file to Cloudflare R2
      const uploadResult = await r2Client.uploadContentFile(
        file,
        agencyId,
        modelId,
        categoryId,
        {
          uploadedBy: modelId,
          category: category.label,
          platform: category.platform,
        },
      );

      if (!uploadResult.success) {
        throw new ApiError(500, "Failed to upload file to storage");
      }

      // Process and sanitize metadata
      const processedMetadata = {
        title: metadata?.title?.trim() || "",
        description: metadata?.description?.trim() || "",
        tags: Array.isArray(metadata?.tags)
          ? metadata.tags
              .filter((tag) => tag && typeof tag === "string")
              .map((tag) => tag.trim())
              .slice(0, 20)
          : [],
        platformTags: Array.isArray(metadata?.platformTags)
          ? metadata.platformTags
              .filter((tag) => tag && typeof tag === "string")
              .map((tag) => tag.trim())
          : [],
        customFields:
          metadata?.customFields && typeof metadata.customFields === "object"
            ? metadata.customFields
            : {},
      };

      // Set due date based on category reminder settings
      let dueDate = null;
      if (category.reminderFrequency && category.reminderFrequency > 0) {
        dueDate = new Date();
        dueDate.setDate(dueDate.getDate() + category.reminderFrequency);
      }

      // Create upload record with enhanced data
      const uploadData = {
        modelId,
        agencyId,
        categoryId,
        filename: file.filename || file.originalname,
        originalName: file.originalname,
        mimeType: file.mimetype,
        fileSize: file.size,
        fileExtension,
        r2Key: uploadResult.key,
        r2Url: uploadResult.url,
        r2Bucket: uploadResult.bucket,
        status: category.hardRules.requiresApproval ? "pending" : "uploaded",
        hasConsent: Boolean(hasConsent),
        hasWatermark: Boolean(hasWatermark),
        releaseFormUrl: releaseFormUrl?.trim() || null,
        metadata: processedMetadata,
        dueDate,
      };

      const upload = new ContentUpload(uploadData);
      await upload.save();

      // Populate references for response
      await upload.populate([
        { path: "categoryId", select: "label platform" },
        { path: "modelId", select: "username profilePhoto" },
      ]);

      res
        .status(201)
        .json(new ApiResponse(201, upload, "Content uploaded successfully"));
    } catch (error) {
      console.error("Content upload failed:", error);
      throw error;
    }
  });

  /**
   * Get uploads for a model
   * GET /api/v1/content/uploads/model/:modelId
   */
  static getModelUploads = asyncHandler(async (req, res) => {
    const { modelId } = req.params;
    const { _id: userId, role } = req.user;
    const agencyId = ContentUploadController.getEffectiveAgencyId(req.user);
    const baseAgencyId = ContentUploadController.getAgencyId(req.user);
    const {
      categoryId,
      status,
      page = 1,
      limit = 20,
      sortBy = "createdAt",
      sortOrder = "desc",
    } = req.query;

    // Authorization check
    if (role === "model" && modelId !== userId.toString()) {
      throw new ApiError(403, "Access denied: Can only view own uploads");
    }

    try {
      const query = {
        modelId,
        agencyId: { $in: [agencyId, baseAgencyId] },
        isDeleted: false,
      };

      if (categoryId) query.categoryId = categoryId;
      if (status) query.status = status;

      const skip = (parseInt(page) - 1) * parseInt(limit);
      const sortOptions = { [sortBy]: sortOrder === "desc" ? -1 : 1 };

      const [uploads, total] = await Promise.all([
        ContentUpload.find(query)
          .populate("categoryId", "label platform reminderType")
          .populate("modelId", "username profilePhoto")
          .sort(sortOptions)
          .skip(skip)
          .limit(parseInt(limit)),
        ContentUpload.countDocuments(query),
      ]);

      const hasMore = skip + uploads.length < total;

      res.status(200).json(
        new ApiResponse(
          200,
          {
            uploads,
            pagination: {
              page: parseInt(page),
              limit: parseInt(limit),
              total,
              hasMore,
              totalPages: Math.ceil(total / parseInt(limit)),
            },
          },
          "Uploads retrieved successfully",
        ),
      );
    } catch (error) {
      console.error("Failed to get model uploads:", error);
      throw new ApiError(500, "Failed to retrieve uploads");
    }
  });

  // Agency uploads function removed - not needed for current requirements

  /**
   * Approve an upload
   * PUT /api/v1/content/uploads/:id/approve
   */
  static approveUpload = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { comment } = req.body;
    const { _id: userId, role } = req.user;
    const agencyId = ContentUploadController.getEffectiveAgencyId(req.user);
    const baseAgencyId = ContentUploadController.getAgencyId(req.user);

    // Check if user has permission to approve uploads
    const hasPermission = await ContentUploadController.checkUploadPermission(
      req.user,
      "approve",
    );
    if (!hasPermission) {
      console.error(
        `Access denied for user ${userId}: insufficient permissions for approve action`,
      );
      throw new ApiError(403, "Access denied: Agency access required");
    }

    try {
      // Build a comprehensive list of possible agency IDs to check
      const possibleAgencyIds = [agencyId, baseAgencyId];

      // For agency users, also include their direct _id
      if (role === "agency") {
        possibleAgencyIds.push(userId.toString());
      }

      // Remove duplicates
      const uniqueAgencyIds = [...new Set(possibleAgencyIds.filter(Boolean))];

      const upload = await ContentUpload.findOne({
        _id: id,
        agencyId: { $in: uniqueAgencyIds },
        isDeleted: false,
      });

      if (!upload) {
        throw new ApiError(404, "Upload not found or access denied");
      }

      if (upload.status === "approved") {
        throw new ApiError(400, "Upload is already approved");
      }

      await upload.approve(userId, comment);

      // Populate for response
      await upload.populate([
        { path: "categoryId", select: "label platform" },
        { path: "modelId", select: "username profilePhoto" },
        { path: "approvedBy", select: "username" },
      ]);

      res
        .status(200)
        .json(new ApiResponse(200, upload, "Upload approved successfully"));
    } catch (error) {
      console.error("Upload approval failed:", error);
      throw error;
    }
  });

  /**
   * Reject an upload
   * PUT /api/v1/content/uploads/:id/reject
   */
  static rejectUpload = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { reason, comment } = req.body;
    const { _id: userId, role } = req.user;
    const agencyId = ContentUploadController.getEffectiveAgencyId(req.user);
    const baseAgencyId = ContentUploadController.getAgencyId(req.user);

    // Check if user has permission to reject uploads
    const hasPermission = await ContentUploadController.checkUploadPermission(
      req.user,
      "reject",
    );
    if (!hasPermission) {
      console.error(
        `Access denied for user ${userId}: insufficient permissions for reject action`,
      );
      throw new ApiError(403, "Access denied: Agency access required");
    }

    if (!reason) {
      throw new ApiError(400, "Rejection reason is required");
    }

    try {
      // Build a comprehensive list of possible agency IDs to check
      const possibleAgencyIds = [agencyId, baseAgencyId];

      // For agency users, also include their direct _id
      if (role === "agency") {
        possibleAgencyIds.push(userId.toString());
      }

      // Remove duplicates
      const uniqueAgencyIds = [...new Set(possibleAgencyIds.filter(Boolean))];

      const upload = await ContentUpload.findOne({
        _id: id,
        agencyId: { $in: uniqueAgencyIds },
        isDeleted: false,
      });

      if (!upload) {
        throw new ApiError(404, "Upload not found");
      }

      if (upload.status === "rejected") {
        throw new ApiError(400, "Upload is already rejected");
      }

      await upload.reject(userId, reason, comment);

      // Populate for response
      await upload.populate([
        { path: "categoryId", select: "label platform" },
        { path: "modelId", select: "username profilePhoto" },
        { path: "rejectedBy", select: "username" },
      ]);

      res
        .status(200)
        .json(new ApiResponse(200, upload, "Upload rejected successfully"));
    } catch (error) {
      console.error("Upload rejection failed:", error);
      throw error;
    }
  });

  /**
   * Add comment to upload
   * POST /api/v1/content/uploads/:id/comments
   */
  static addComment = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { comment } = req.body;
    const { _id: userId, role } = req.user;

    // For agency users, use their _id directly; for models, use their agencyId
    const agencyId =
      role === "agency"
        ? userId
        : ContentUploadController.getEffectiveAgencyId(req.user);
    const baseAgencyId =
      role === "agency"
        ? userId
        : ContentUploadController.getAgencyId(req.user);

    if (!comment || !comment.trim()) {
      throw new ApiError(400, "Comment is required");
    }

    try {
      const upload = await ContentUpload.findOne({
        _id: id,
        agencyId: { $in: [agencyId, baseAgencyId] },
        isDeleted: false,
      });

      if (!upload) {
        throw new ApiError(404, "Upload not found");
      }

      // Determine comment type based on role
      const commentByType = role === "model" ? "ModelUser" : "Agency";

      await upload.addComment(userId, commentByType, comment.trim());

      res
        .status(200)
        .json(
          new ApiResponse(
            200,
            upload.comments[upload.comments.length - 1],
            "Comment added successfully",
          ),
        );
    } catch (error) {
      console.error("Add comment failed:", error);
      throw error;
    }
  });

  /**
   * Update upload status with workflow validation
   * PUT /api/v1/content/uploads/:id/status
   */
  static updateStatus = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { status, reason, comment } = req.body;
    const { _id: userId, role } = req.user;

    // For agency users, use their _id directly; for models, use their agencyId
    const agencyId =
      role === "agency"
        ? userId
        : ContentUploadController.getEffectiveAgencyId(req.user);
    const baseAgencyId =
      role === "agency"
        ? userId
        : ContentUploadController.getAgencyId(req.user);

    if (!status) {
      throw new ApiError(400, "Status is required");
    }

    try {
      // Verify upload exists and belongs to agency
      const upload = await ContentUpload.findOne({
        _id: id,
        agencyId: { $in: [agencyId, baseAgencyId] },
        isDeleted: false,
      });

      if (!upload) {
        throw new ApiError(404, "Upload not found");
      }

      // Use status tracking service for validation and update
      const updatedUpload = await StatusTrackingService.updateStatus(
        id,
        status,
        userId,
        role,
        { reason, comment },
      );

      // Populate for response
      await updatedUpload.populate([
        { path: "categoryId", select: "label platform" },
        { path: "modelId", select: "username profilePhoto" },
        { path: "approvedBy", select: "username" },
        { path: "rejectedBy", select: "username" },
      ]);

      res
        .status(200)
        .json(
          new ApiResponse(
            200,
            updatedUpload,
            `Upload status updated to ${status} successfully`,
          ),
        );
    } catch (error) {
      console.error("Status update failed:", error);
      throw error;
    }
  });

  // Workflow summary function removed - not needed for current requirements

  // Get uploads by status function removed - not needed for current requirements

  /**
   * Get comments for a specific upload
   * GET /api/v1/content/uploads/:id/comments
   */
  static getUploadComments = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { _id: userId, role } = req.user;

    // For agency users, use their _id directly; for models, use their agencyId
    const agencyId =
      role === "agency"
        ? userId
        : ContentUploadController.getEffectiveAgencyId(req.user);
    const baseAgencyId =
      role === "agency"
        ? userId
        : ContentUploadController.getAgencyId(req.user);
    const { page = 1, limit = 20 } = req.query;

    try {
      const upload = await ContentUpload.findOne({
        _id: id,
        agencyId: { $in: [agencyId, baseAgencyId] },
        isDeleted: false,
      });

      if (!upload) {
        throw new ApiError(404, "Upload not found");
      }

      // Authorization check for models
      if (role === "model" && upload.modelId.toString() !== userId.toString()) {
        throw new ApiError(
          403,
          "Access denied: Can only view comments on own uploads",
        );
      }

      // Paginate comments (newest first)
      const skip = (parseInt(page) - 1) * parseInt(limit);
      const totalComments = upload.comments.length;
      const paginatedComments = upload.comments
        .sort((a, b) => new Date(b.commentedAt) - new Date(a.commentedAt))
        .slice(skip, skip + parseInt(limit));

      // Populate comment authors
      await ContentUpload.populate(paginatedComments, [
        {
          path: "commentBy",
          select: "username profilePhoto",
          model: function (doc) {
            return doc.commentByType;
          },
        },
      ]);

      res.status(200).json(
        new ApiResponse(
          200,
          {
            comments: paginatedComments,
            pagination: {
              page: parseInt(page),
              limit: parseInt(limit),
              total: totalComments,
              hasMore: skip + paginatedComments.length < totalComments,
              totalPages: Math.ceil(totalComments / parseInt(limit)),
            },
          },
          "Comments retrieved successfully",
        ),
      );
    } catch (error) {
      console.error("Failed to get upload comments:", error);
      throw error;
    }
  });

  // Update comment function removed - not needed for current requirements

  // Delete comment function removed - not needed for current requirements

  // Detailed analytics function removed - not needed for current requirements

  // Analytics function removed - not needed for current requirements

  // ===== PHASE 3: BUSINESS LOGIC METHODS =====

  /**
   * Assign a model to a category
   * POST /api/v1/content/assignments
   */
  static assignModelToCategory = asyncHandler(async (req, res) => {
    const {
      modelId,
      categoryId,
      priority,
      customReminderFrequency,
      customReminderType,
      notes,
    } = req.body;
    const { _id: userId, role } = req.user;
    const agencyId = ContentUploadController.getAgencyId(req.user);

    // Check if user has permission to create assignments
    const hasPermission =
      await ContentUploadController.checkAssignmentPermission(
        req.user,
        "create",
      );
    if (!hasPermission) {
      console.error(
        `Access denied for user ${userId}: insufficient permissions for create assignment action`,
      );
      throw new ApiError(403, "Access denied: Agency access required");
    }

    if (!modelId || !categoryId) {
      throw new ApiError(400, "Model ID and Category ID are required");
    }

    try {
      const assignmentData = {
        agencyId,
        modelId,
        categoryId,
        assignedBy: userId,
        priority,
        customReminderFrequency,
        customReminderType,
        notes,
      };

      const assignment =
        await ModelAssignmentService.assignModelToCategory(assignmentData);

      res
        .status(201)
        .json(
          new ApiResponse(
            201,
            assignment,
            "Model assigned to category successfully",
          ),
        );
    } catch (error) {
      console.error("Assignment creation failed:", error);
      throw error;
    }
  });

  /**
   * Get model assignments
   * GET /api/v1/content/assignments/model/:modelId
   */
  static getModelAssignments = asyncHandler(async (req, res) => {
    const { modelId } = req.params;
    const { _id: userId, role } = req.user;
    const agencyId = ContentUploadController.getAgencyId(req.user);
    const { includeInactive } = req.query;

    // Authorization check
    if (role === "model" && modelId !== userId.toString()) {
      throw new ApiError(403, "Access denied: Can only view own assignments");
    }

    try {
      const assignments = await ModelAssignmentService.getModelAssignments(
        modelId,
        {
          includeInactive: includeInactive === "true",
          agencyId,
        },
      );

      res
        .status(200)
        .json(
          new ApiResponse(
            200,
            assignments,
            "Model assignments retrieved successfully",
          ),
        );
    } catch (error) {
      console.error("Failed to get model assignments:", error);
      throw error;
    }
  });

  /**
   * Get agency assignments with filtering
   * GET /api/v1/content/assignments
   */
  static getAgencyAssignments = asyncHandler(async (req, res) => {
    const { role } = req.user;
    const agencyId = ContentUploadController.getAgencyId(req.user);
    const {
      includeInactive,
      modelId,
      categoryId,
      priority,
      overdue,
      page,
      limit,
    } = req.query;

    // Check if user has permission to view all assignments
    const hasPermission =
      await ContentUploadController.checkAssignmentPermission(req.user, "read");
    if (!hasPermission) {
      console.error(
        `Access denied for user ${req.user._id}: insufficient permissions for view assignments action`,
      );
      throw new ApiError(403, "Access denied: Agency access required");
    }

    try {
      const options = {
        includeInactive: includeInactive === "true",
        modelId,
        categoryId,
        priority,
        overdue: overdue === "true" ? true : overdue === "false" ? false : null,
        page: parseInt(page) || 1,
        limit: parseInt(limit) || 50,
      };

      const result = await ModelAssignmentService.getAgencyAssignments(
        agencyId,
        options,
      );

      res
        .status(200)
        .json(
          new ApiResponse(
            200,
            result,
            "Agency assignments retrieved successfully",
          ),
        );
    } catch (error) {
      console.error("Failed to get agency assignments:", error);
      throw error;
    }
  });

  /**
   * Update an assignment
   * PUT /api/v1/content/assignments/:id
   */
  static updateAssignment = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { _id: userId, role } = req.user;
    const agencyId = ContentUploadController.getAgencyId(req.user);
    const updateData = req.body;

    // Check if user has permission to update assignments
    const hasPermission =
      await ContentUploadController.checkAssignmentPermission(
        req.user,
        "update",
      );
    if (!hasPermission) {
      console.error(
        `Access denied for user ${userId}: insufficient permissions for update assignment action`,
      );
      throw new ApiError(403, "Access denied: Agency access required");
    }

    try {
      const assignment = await ModelAssignmentService.updateAssignment(
        id,
        updateData,
        userId,
      );

      res
        .status(200)
        .json(
          new ApiResponse(200, assignment, "Assignment updated successfully"),
        );
    } catch (error) {
      console.error("Assignment update failed:", error);
      throw error;
    }
  });

  /**
   * Remove (deactivate) an assignment
   * DELETE /api/v1/content/assignments/:id
   */
  static removeAssignment = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { _id: userId, role } = req.user;
    const agencyId = ContentUploadController.getAgencyId(req.user);
    const { reason } = req.body;

    // Check if user has permission to remove assignments
    const hasPermission =
      await ContentUploadController.checkAssignmentPermission(
        req.user,
        "delete",
      );
    if (!hasPermission) {
      console.error(
        `Access denied for user ${userId}: insufficient permissions for remove assignment action`,
      );
      throw new ApiError(403, "Access denied: Agency access required");
    }

    try {
      const assignment = await ModelAssignmentService.removeAssignment(
        id,
        userId,
        reason,
      );

      res
        .status(200)
        .json(
          new ApiResponse(200, assignment, "Assignment removed successfully"),
        );
    } catch (error) {
      console.error("Assignment removal failed:", error);
      throw error;
    }
  });

  /**
   * Get overdue content for agency
   * GET /api/v1/content/overdue
   */
  static getOverdueContent = asyncHandler(async (req, res) => {
    const { role } = req.user;
    const agencyId = ContentUploadController.getAgencyId(req.user);
    const { modelId, categoryId, priority, daysPastDue, page, limit } =
      req.query;

    // Check if user has permission to view overdue content
    const hasPermission =
      await ContentUploadController.checkAssignmentPermission(
        req.user,
        "view-overdue",
      );
    if (!hasPermission) {
      console.error(
        `Access denied for user ${req.user._id}: insufficient permissions for view overdue content action`,
      );
      throw new ApiError(403, "Access denied: Agency access required");
    }

    try {
      const options = {
        modelId,
        categoryId,
        priority,
        daysPastDue: daysPastDue ? parseInt(daysPastDue) : null,
        page: parseInt(page) || 1,
        limit: parseInt(limit) || 50,
      };

      const result = await ReminderCalculationService.getOverdueAssignments(
        agencyId,
        options,
      );

      res
        .status(200)
        .json(
          new ApiResponse(
            200,
            result,
            "Overdue content retrieved successfully",
          ),
        );
    } catch (error) {
      console.error("Failed to get overdue content:", error);
      throw error;
    }
  });

  /**
   * Send reminders for assignments
   * POST /api/v1/content/reminders/send
   */
  static sendReminders = asyncHandler(async (req, res) => {
    const { _id: userId, role } = req.user;
    const agencyId = ContentUploadController.getAgencyId(req.user);
    const {
      assignmentIds,
      notificationType = "email",
      templateType,
    } = req.body;

    // Check if user has permission to send reminders
    const hasPermission =
      await ContentUploadController.checkAssignmentPermission(
        req.user,
        "send-reminders",
      );
    if (!hasPermission) {
      console.error(
        `Access denied for user ${userId}: insufficient permissions for send reminders action`,
      );
      throw new ApiError(403, "Access denied: Agency access required");
    }

    if (
      !assignmentIds ||
      !Array.isArray(assignmentIds) ||
      assignmentIds.length === 0
    ) {
      throw new ApiError(400, "Assignment IDs array is required");
    }

    try {
      const options = {
        templateType,
        uploadUrl: `${process.env.FRONTEND_URL}/upload`,
      };

      const results = await NotificationService.sendBulkReminders(
        assignmentIds,
        notificationType,
        options,
      );

      // Update reminder sent status for successful notifications
      for (const result of results.successful) {
        if (result.results.email?.success || result.results.sms?.success) {
          await ReminderCalculationService.markReminderSent(
            result.assignmentId,
            notificationType,
          );
        }
      }

      res
        .status(200)
        .json(new ApiResponse(200, results, "Reminders sent successfully"));
    } catch (error) {
      console.error("Failed to send reminders:", error);
      throw error;
    }
  });

  // Agency configuration function removed - not needed for current requirements

  // Update agency configuration function removed - not needed for current requirements

  // Assignment statistics function removed - not needed for current requirements

  /**
   * Get assignments due soon
   * GET /api/v1/content/due-soon
   */
  static getAssignmentsDueSoon = asyncHandler(async (req, res) => {
    const { role } = req.user;
    const agencyId = ContentUploadController.getAgencyId(req.user);
    const { daysAhead = 7 } = req.query;

    // Check if user has permission to view due soon assignments
    const hasPermission =
      await ContentUploadController.checkAssignmentPermission(
        req.user,
        "view-due-soon",
      );
    if (!hasPermission) {
      console.error(
        `Access denied for user ${req.user._id}: insufficient permissions for view due soon action`,
      );
      throw new ApiError(403, "Access denied: Agency access required");
    }

    try {
      const assignments =
        await ReminderCalculationService.getAssignmentsDueSoon(
          agencyId,
          parseInt(daysAhead),
        );

      res
        .status(200)
        .json(
          new ApiResponse(
            200,
            assignments,
            "Assignments due soon retrieved successfully",
          ),
        );
    } catch (error) {
      console.error("Failed to get assignments due soon:", error);
      throw error;
    }
  });

  /**
   * Check if user has permission for assignment management actions
   * @param {Object} user - User object from auth
   * @param {string} action - Action to check permission for
   * @returns {Promise<boolean>} - Whether user has permission
   */
  static checkAssignmentPermission = async (user, action) => {
    try {
      const { role, permissions, effectivePermissions } = user;

      // Agency users have full access
      if (role === "agency") {
        return true;
      }

      // Models cannot manage assignments regardless of agencyId
      if (role === "model") {
        return false;
      }

      // Employees need specific permissions
      if (role === "employee") {
        // Check if user has management roles
        if (
          effectivePermissions?.roles?.includes("manager") ||
          effectivePermissions?.roles?.includes("admin") ||
          effectivePermissions?.roles?.includes("content_moderator")
        ) {
          return true;
        }

        // Check specific assignment permissions
        const permissionsToCheck = {
          create: "assignments.create",
          read: "assignments.read",
          update: "assignments.update",
          delete: "assignments.delete",
          manage: "assignments.manage",
          "send-reminders": "assignments.send_reminders",
          "view-overdue": "assignments.view_overdue",
          "view-due-soon": "assignments.view_due_soon",
          "test-notifications": "assignments.test_notifications",
        };

        const requiredPermission = permissionsToCheck[action];
        if (
          requiredPermission &&
          effectivePermissions?.permissions?.includes(requiredPermission)
        ) {
          return true;
        }
      }

      return false;
    } catch (error) {
      console.error("Error checking assignment permission:", error);
      return false;
    }
  };

  /**
   * Test notification configuration
   * POST /api/v1/content/notifications/test
   */
  static testNotificationConfiguration = asyncHandler(async (req, res) => {
    const { role } = req.user;
    const agencyId = ContentUploadController.getAgencyId(req.user);
    const { testEmail } = req.body;

    // Check if user has permission to test notifications
    const hasPermission =
      await ContentUploadController.checkAssignmentPermission(
        req.user,
        "test-notifications",
      );
    if (!hasPermission) {
      console.error(
        `Access denied for user ${req.user._id}: insufficient permissions for test notifications action`,
      );
      throw new ApiError(403, "Access denied: Agency access required");
    }

    if (!testEmail) {
      throw new ApiError(400, "Test email address is required");
    }

    try {
      const result = await NotificationService.testNotificationConfiguration(
        agencyId,
        testEmail,
      );

      res
        .status(200)
        .json(new ApiResponse(200, result, "Notification test completed"));
    } catch (error) {
      console.error("Failed to test notification configuration:", error);
      throw error;
    }
  });
}

export default ContentUploadController;
