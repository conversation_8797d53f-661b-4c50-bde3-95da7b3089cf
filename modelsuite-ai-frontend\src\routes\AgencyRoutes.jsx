import { Route } from "react-router-dom";
import ProtectedRoute from "../reusable/ProtectedRoute";
import AgencyDashboardLayout from "../layouts/AgencyDashboardLayout";
import DashboardContent from "../pages/Agency/Dashboard/DashboardContent";
import AgencySettings from "../components/Agency/Settings/AgencySettings";
import ModelsPage from "../pages/Agency/Dashboard/ModelsPage";
import TeamPage from "../pages/Agency/Dashboard/TeamPage";
import SkillMatrixPage from "../components/skillmatrix/SkillMatrixPage";
import ModelMenuLayout from "../layouts/ModelMenuLayout";
import Messenger from "../pages/Agency/ModelMenuSections/Messenger";
import Calendar from "../pages/Agency/ModelMenuSections/Calendar";
import Tasks from "../pages/Agency/ModelMenuSections/Tasks";
import NotesPage from "../pages/Agency/ModelNotes/NotesPage";
import NotesDashboard from "../pages/Agency/Notes/NotesDashboard";

// Questionnaire imports
import QuestionnaireTemplatesPage from "../pages/Agency/Questionnaire/QuestionnaireTemplatesPage";
import CreateTemplatePage from "../pages/Agency/Questionnaire/CreateTemplatePage";
import EditTemplatePage from "../pages/Agency/Questionnaire/EditTemplatePage";
import AssignmentsPage from "../pages/Agency/Questionnaire/AssignmentsPage";
import AnalyticsPage from "../pages/Agency/Questionnaire/AnalyticsPage";

export default function AgencyRoutes() {
  return (
    <>
      <Route element={<ProtectedRoute />}>
        <Route path="/agency" element={<AgencyDashboardLayout />}>
          <Route path="dashboard" element={<DashboardContent />} />
          <Route path="skill-matrix" element={<SkillMatrixPage />} />
          <Route path="models" element={<ModelsPage />} />
          <Route path="model-menu/:id" element={<ModelMenuLayout />}>
            <Route index element={<Messenger />} />
            <Route path="messenger" element={<Messenger />} />
            <Route path="calendar" element={<Calendar />} />
            <Route path="tasks" element={<Tasks />} />
            <Route path="notes" element={<NotesPage />} />
          </Route>
          <Route path="team" element={<TeamPage />} />
          <Route path="notes" element={<NotesDashboard />} />
          <Route path="profile/settings" element={<AgencySettings />} />

          {/* Questionnaire routes */}
          <Route
            path="questionnaires"
            element={<QuestionnaireTemplatesPage />}
          />
          <Route
            path="questionnaires/create"
            element={<CreateTemplatePage />}
          />
          <Route
            path="questionnaires/edit/:templateId"
            element={<EditTemplatePage />}
          />
          <Route
            path="questionnaires/assignments"
            element={<AssignmentsPage />}
          />
          <Route
            path="questionnaires/analytics/:templateId"
            element={<AnalyticsPage />}
          />
        </Route>
      </Route>
    </>
  );
}
