import React from "react";

const OtpForm = ({ otp, setOtp, loading, onSubmit }) => (
  <form onSubmit={onSubmit} className="space-y-4 mt-2">
    <div className="flex flex-col">
      <label
        htmlFor="otp"
        className="text-sm font-medium text-gray-300 mb-1"
      >
        Enter OTP
      </label>
      <input
        type="text"
        id="otp"
        value={otp}
        onChange={e => setOtp(e.target.value.trim())}
        placeholder="Enter OTP"
        className="px-4 py-2 rounded-lg bg-gray-800 border border-gray-700 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
        required
      />
    </div>
    <button
      type="submit"
      className="w-full mt-2 bg-blue-600 hover:bg-blue-700 transition-colors duration-200 text-white py-2 rounded-lg font-semibold flex items-center justify-center"
      disabled={loading}
    >
      {loading ? (
        <>
          <div className="animate-spin w-5 h-5 border-2 border-white border-t-transparent rounded-full mr-2"></div>
          Verifying...
        </>
      ) : (
        "Verify OTP & Register"
      )}
    </button>
  </form>
);

export default OtpForm;
