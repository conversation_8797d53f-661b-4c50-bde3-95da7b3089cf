import { Route, Routes } from "react-router-dom";
import "./App.css";
import ResuableExample from "./DTF/ResuableExample";
import Home from "./pages/Home/Home";
import Register from "./pages/Register/Register";
import AgencyRoutes from "./routes/AgencyRoutes";
import ModelRoutes from "./routes/ModelRoutes";

function App() {

  return (
    <>
      <Routes>
        <Route path = "/" element = {<Home/>} ></Route>
        <Route path = "/register" element = {<Register/>} ></Route>
        {AgencyRoutes()}
        {ModelRoutes()}
         <Route path="/resuable-example" element={<ResuableExample />}></Route>
      </Routes>
    </>
  );
}

export default App;
