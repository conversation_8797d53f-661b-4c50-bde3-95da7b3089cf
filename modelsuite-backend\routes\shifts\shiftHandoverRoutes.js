import express from "express";
import {
  createHandoverRequest,
  getAvailableEmployees,
  acceptHandover,
  rejectHandover,
  cancelHandover,
  getAgencyHandovers,
  getEmployeeHandovers,
  cleanupExpiredHandovers,
} from "../../controllers/shifts/shiftHandoverController.js";
import { verifyToken } from "../../middlewares/authMiddleware.js";
import checkPermission from "../../middlewares/permissionCheck.js";

const router = express.Router();

// Apply authentication middleware to all routes
router.use(verifyToken);

// Employee routes
router.post("/request", createHandoverRequest);
router.get("/available-employees/:handoverId", getAvailableEmployees);
router.post("/accept/:handoverId", acceptHandover);
router.post("/reject/:handoverId", rejectHandover);
router.post("/cancel/:handoverId", cancelHandover);
router.get("/employee", getEmployeeHandovers);

// Agency/Manager routes
router.get("/agency", checkPermission("shifts.view"), getAgencyHandovers);
router.post(
  "/cleanup-expired",
  checkPermission("shifts.edit"),
  cleanupExpiredHandovers,
);

export default router;
