import mongoose from "mongoose";

const questionTemplateSchema = new mongoose.Schema(
  {
    text: {
      type: String,
      required: [true, "Question text is required"],
      trim: true,
      maxlength: [1000, "Question text cannot exceed 1000 characters"],
    },
    sectionId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "QuestionSection",
      required: [true, "Section ID is required"],
      index: true,
    },
    tags: {
      type: [
        {
          type: String,
          trim: true,
          enum: [
            "good_morning",
            "good_night",
            "flirty",
            "sensual",
            "affectionate",
            "non_sexual",
            "conversation_starter",
            "mixed_tone",
            "romantic",
            "playful",
            "intimate",
            "casual",
            "sweet",
            "naughty",
            "teasing",
            "caring",
            "loving",
            "sleepy",
            "energetic",
            "soft_spoken",
          ],
        },
      ],
      validate: [
        {
          validator: function (tags) {
            return tags.length <= 10;
          },
          message: "Cannot have more than 10 tags",
        },
        {
          validator: function (tags) {
            // Ensure no duplicate tags
            return new Set(tags).size === tags.length;
          },
          message: "Duplicate tags are not allowed",
        },
      ],
      default: [],
      index: true,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      default: null, // null for default questions
    },
    isDeleted: {
      type: Boolean,
      default: false,
      index: true,
    },
  },
  {
    timestamps: true,
  },
);

// Indexes for performance
questionTemplateSchema.index({ sectionId: 1, isDeleted: 1 });
questionTemplateSchema.index({ createdBy: 1, isDeleted: 1 });

// Don't return deleted questions by default
questionTemplateSchema.pre(/^find/, function (next) {
  if (!this.getQuery().isDeleted) {
    this.where({ isDeleted: { $ne: true } });
  }
  next();
});

const QuestionTemplate = mongoose.model(
  "QuestionTemplate",
  questionTemplateSchema,
);

export default QuestionTemplate;
