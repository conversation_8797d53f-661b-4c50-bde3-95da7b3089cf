import mongoose from "mongoose";

/**
 * PersonaFeedback Schema - Stores agency feedback on generated personas
 * Used for improving persona quality and AI prompt optimization
 */
const personaFeedbackSchema = new mongoose.Schema(
  {
    // Reference to the persona being rated
    personaId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "PersonaProfile",
      required: true,
    },

    // Reference to the model the persona belongs to
    modelId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ModelUser",
      required: true,
    },

    // Agency providing feedback
    agencyId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      required: true,
    },

    // User who provided feedback
    providedBy: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      refPath: "providedByModel",
    },

    providedByModel: {
      type: String,
      required: true,
      enum: ["Agency", "Employee"],
    },

    // Rating system (1-5 stars)
    rating: {
      type: Number,
      required: true,
      min: 1,
      max: 5,
    },

    // Optional text feedback
    textFeedback: {
      type: String,
      maxlength: 1000,
      trim: true,
    },

    // Specific feedback categories
    feedbackCategories: {
      accuracy: {
        type: Number,
        min: 1,
        max: 5,
      },
      usefulness: {
        type: Number,
        min: 1,
        max: 5,
      },
      completeness: {
        type: Number,
        min: 1,
        max: 5,
      },
      brandAlignment: {
        type: Number,
        min: 1,
        max: 5,
      },
    },

    // Improvement suggestions
    improvementAreas: {
      type: [String],
      enum: [
        "More detailed demographics",
        "Better psychographics",
        "Clearer communication style",
        "More accurate interests",
        "Better behavioral patterns",
        "Tone refinement",
        "Goal clarity",
        "Pain point accuracy",
      ],
      default: [],
    },

    // Context when feedback was provided
    feedbackContext: {
      usageContext: {
        type: String,
        enum: [
          "Campaign planning",
          "Content creation",
          "Audience analysis",
          "General review",
        ],
      },
      campaignId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Campaign", // If feedback was provided during campaign usage
      },
    },

    // Response from system (if any)
    systemResponse: {
      acknowledged: {
        type: Boolean,
        default: false,
      },
      actionTaken: {
        type: String,
        enum: [
          "Persona regenerated",
          "Prompt updated",
          "Training data adjusted",
          "No action",
        ],
      },
      responseDate: {
        type: Date,
      },
    },

    // Status tracking
    status: {
      type: String,
      enum: ["pending", "reviewed", "implemented", "dismissed"],
      default: "pending",
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  },
);

// Indexes for performance
personaFeedbackSchema.index({ personaId: 1, agencyId: 1 });
personaFeedbackSchema.index({ agencyId: 1, createdAt: -1 });
personaFeedbackSchema.index({ rating: 1 });
personaFeedbackSchema.index({ status: 1 });
personaFeedbackSchema.index({ modelId: 1, rating: -1 });

// Virtual for average rating calculation
personaFeedbackSchema.virtual("isPositive").get(function () {
  return this.rating >= 4;
});

// Static method to get feedback summary for persona
personaFeedbackSchema.statics.getFeedbackSummary = function (personaId) {
  return this.aggregate([
    { $match: { personaId: new mongoose.Types.ObjectId(personaId) } },
    {
      $group: {
        _id: null,
        averageRating: { $avg: "$rating" },
        totalFeedback: { $sum: 1 },
        positiveCount: {
          $sum: { $cond: [{ $gte: ["$rating", 4] }, 1, 0] },
        },
        negativeCount: {
          $sum: { $cond: [{ $lt: ["$rating", 3] }, 1, 0] },
        },
        avgAccuracy: { $avg: "$feedbackCategories.accuracy" },
        avgUsefulness: { $avg: "$feedbackCategories.usefulness" },
        avgCompleteness: { $avg: "$feedbackCategories.completeness" },
        avgBrandAlignment: { $avg: "$feedbackCategories.brandAlignment" },
      },
    },
  ]);
};

// Static method to get improvement suggestions aggregated
personaFeedbackSchema.statics.getImprovementSuggestions = function (personaId) {
  return this.aggregate([
    { $match: { personaId: new mongoose.Types.ObjectId(personaId) } },
    { $unwind: "$improvementAreas" },
    {
      $group: {
        _id: "$improvementAreas",
        count: { $sum: 1 },
        averageRating: { $avg: "$rating" },
      },
    },
    { $sort: { count: -1 } },
  ]);
};

// Static method to get agency feedback history
personaFeedbackSchema.statics.getAgencyFeedbackHistory = function (
  agencyId,
  options = {},
) {
  const query = this.find({ agencyId });

  if (options.modelId) {
    query.where({ modelId: options.modelId });
  }

  if (options.rating) {
    query.where({ rating: options.rating });
  }

  return query
    .populate("personaId", "personaText version")
    .populate("modelId", "fullName username")
    .sort({ createdAt: -1 })
    .limit(options.limit || 50);
};

// Pre-save middleware to update persona rating
personaFeedbackSchema.pre("save", async function (next) {
  if (this.isNew) {
    try {
      const PersonaProfile = mongoose.model("PersonaProfile");
      const persona = await PersonaProfile.findById(this.personaId);

      if (persona) {
        persona.scoring.agencyRating = this.rating;
        persona.scoring.lastRatedAt = new Date();
        await persona.save();
      }
    } catch (error) {
      console.error("Error updating persona rating:", error);
    }
  }
  next();
});

const PersonaFeedback = mongoose.model(
  "PersonaFeedback",
  personaFeedbackSchema,
);

export default PersonaFeedback;
