import mongoose from "mongoose";

async function fixCategoryDuplicateKey() {
  try {
    // Use the actual MongoDB URI from .env (MongoDB Atlas)
    const mongoUri =
      "mongodb+srv://devanand:<EMAIL>/modelsuite?retryWrites=true&w=majority&appName=Cluster0";
    await mongoose.connect(mongoUri);
    console.log("🔍 Connected to MongoDB Atlas database");

    const db = mongoose.connection.db;
    const collection = db.collection("contentcategories");

    console.log("\n🗂️ BEFORE - Current indexes:");
    const beforeIndexes = await collection.indexes();
    beforeIndexes.forEach((index, i) => {
      console.log(
        `${i + 1}. ${index.name}: ${JSON.stringify(index.key)} ${index.unique ? "(UNIQUE)" : ""}`,
      );
    });

    // Check current documents
    console.log("\n📋 Current documents in collection:");
    const allDocs = await collection.find({}).toArray();
    console.log(`Total documents: ${allDocs.length}`);
    allDocs.forEach((doc, i) => {
      console.log(`${i + 1}.`, {
        _id: doc._id,
        agencyId: doc.agencyId,
        label: doc.label,
        name: doc.name,
        platform: doc.platform,
        isActive: doc.isActive,
      });
    });

    // Drop the problematic unique index agencyId_1_name_1
    console.log("\n🗑️ Dropping problematic index: agencyId_1_name_1");
    try {
      await collection.dropIndex("agencyId_1_name_1");
      console.log("✅ Successfully dropped agencyId_1_name_1 index");
    } catch (error) {
      console.log("❌ Failed to drop agencyId_1_name_1 index:", error.message);
    }

    // Remove the 'name' field from all documents (cleanup)
    console.log("\n🧹 Cleaning up 'name' field from all documents...");
    try {
      const updateResult = await collection.updateMany(
        {},
        { $unset: { name: "" } },
      );
      console.log(
        `✅ Cleaned up 'name' field from ${updateResult.modifiedCount} documents`,
      );
    } catch (error) {
      console.log("❌ Failed to clean up 'name' field:", error.message);
    }

    // Create a proper unique index based on current schema (agencyId + label)
    console.log("\n🔨 Creating proper unique index for agencyId + label...");
    try {
      await collection.createIndex(
        { agencyId: 1, label: 1 },
        {
          unique: true,
          partialFilterExpression: { isActive: true },
          name: "unique_agency_label_active",
        },
      );
      console.log("✅ Created unique_agency_label_active index");
    } catch (error) {
      console.log("❌ Failed to create unique index:", error.message);
    }

    console.log("\n🗂️ AFTER - Final indexes:");
    const afterIndexes = await collection.indexes();
    afterIndexes.forEach((index, i) => {
      console.log(
        `${i + 1}. ${index.name}: ${JSON.stringify(index.key)} ${index.unique ? "(UNIQUE)" : ""}`,
      );
      if (index.partialFilterExpression) {
        console.log(
          `   Partial Filter: ${JSON.stringify(index.partialFilterExpression)}`,
        );
      }
    });

    // Test category creation with the original payload
    console.log("\n🧪 Testing category creation with original payload...");
    try {
      const testCategory = {
        agencyId: new mongoose.Types.ObjectId("68723da81257ef228b214073"),
        label: "India",
        platform: "instagram",
        reminderFrequency: 7,
        reminderType: "soft",
        instructionTooltip: "Upload high-quality images with engaging captions",
        hardRules: {
          maxFileSize: 52428800,
          allowedFormats: ["jpg", "jpeg", "png", "mp4"],
          requiresApproval: true,
          requiresWatermark: false,
          requiresConsent: true,
        },
        createdBy: new mongoose.Types.ObjectId("68723da81257ef228b214073"),
        isActive: true,
        createdAt: new Date(),
        lastModifiedAt: new Date(),
      };

      const testResult = await collection.insertOne(testCategory);
      console.log(
        "✅ Test category 'India' created successfully:",
        testResult.insertedId,
      );

      // Clean up test category
      await collection.deleteOne({ _id: testResult.insertedId });
      console.log("🧹 Test category cleaned up");
    } catch (error) {
      console.log("❌ Test category creation failed:", error.message);
    }

    console.log("\n✅ Category duplicate key fix completed successfully!");
    console.log("\n📝 Summary of changes:");
    console.log("   - Dropped problematic 'agencyId_1_name_1' unique index");
    console.log("   - Cleaned up 'name' field from existing documents");
    console.log("   - Created proper 'unique_agency_label_active' index");
    console.log("   - Verified category creation works with original payload");

    await mongoose.disconnect();
  } catch (error) {
    console.error("❌ Error:", error);
  }
}

fixCategoryDuplicateKey();
