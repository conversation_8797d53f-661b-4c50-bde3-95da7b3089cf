import mongoose from "mongoose";
import jwt from "jsonwebtoken";
import bcrypt from "bcryptjs";

const CTASchema = new mongoose.Schema({
  label: { type: String, required: true },
  link: { type: String, required: true },
});

const SocialLinksSchema = new mongoose.Schema({
  instagram: String,
  tiktok: String,
  youtube: String,
  facebook: String,
  twitter: String,
  linkedin: String,
});

const TeamMemberSchema = new mongoose.Schema({
  name: String,
  role: String,
  avatar: String,
  profileLink: String,
});

const agencySchema = new mongoose.Schema(
  {
    role: {
      type: String,
      default: "agency",
      immutable: true,
    },
    firstName: {
      type: String,
      required: [false, "First Name is required!"],
    },
    lastName: {
      type: String,
      required: [false, "Last Name is required!"],
    },
    agencyName: {
      type: String,
      required: [true, "Agency Name is required!"],
    },
    username: {
      type: String,
      required: [true, "Username is required!"],
      unique: true,
    },
    agencyEmail: {
      type: String,
      unique: true,
    },
    agencyPhone: {
      type: String,
      unique: true,
    },
    password: {
      type: String,
      required: [true, "Password is required!"],
    },

    // Profile Information
    logo: String,
    profilePhoto: String, // Avatar upload will use this field
    bannerImage: String,
    description: String,
    website: String,
    country: String,
    city: String,
    category: String,
    companySize: String,
    agencyType: String,
    certificate: String,
    socialLinks: SocialLinksSchema,
    trustBadge: String,
    ctaButtons: [CTASchema],
    specialties: [String],
    whyUs: [String],
    team: [TeamMemberSchema],

    // Verification & Authentication
    isPhoneVerified: {
      type: Boolean,
      default: false,
    },
    isEmailVerified: {
      type: Boolean,
      default: false,
    },
    isMfaActive: {
      type: Boolean,
      default: false,
    },
    loginRefreshToken: String,
    magicLinkToken: String,
    magicLinkExpiresAt: Date,
    magicLinkUsed: {
      type: Boolean,
      default: false,
    },

    // Session Tracking
    lastOnline: {
      type: String,
      default: null,
    },
    totalActiveMinutes: {
      type: Number,
      default: 0,
    },
    sessionStartTime: {
      type: Date,
      default: null,
    },

    // Model Management
    modelGroups: [
      {
        id: {
          type: String,
          required: true,
        },
        name: {
          type: String,
          required: true,
        },
        isCollapsed: {
          type: Boolean,
          default: false,
        },
        order: {
          type: Number,
          default: 0,
        },
      },
    ],
    modelGroupAssignments: [
      {
        modelId: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "ModelUser",
          required: true,
        },
        groupId: {
          type: String,
          required: true,
        },
        orderInGroup: {
          type: Number,
          default: 0,
        },
      },
    ],
    modelOrder: {
      type: [String],
      default: [],
    },
    customInserts: [
      {
        id: String,
        type: {
          type: String,
          enum: ["spacer", "divider", "note"],
          required: true,
        },
        content: String,
        position: Number,
        createdAt: {
          type: Date,
          default: Date.now,
        },
      },
    ],

    // Relationships
    modelUsers: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "ModelUser",
      },
    ],
    groups: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Group",
      },
    ],
    requestedTasks: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Task",
      },
    ],
    handlingTasks: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Task",
      },
    ],
    comments: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "TaskComment",
      },
    ],
    topics: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Topic",
      },
    ],
    AgencyToModelMssg: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "AgencyToModelMessage",
      },
    ],
    groupTopicMessageSend: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "GroupTopicMessage",
      },
    ],
    ModelContracts: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "ModelContract",
      },
    ],
    portfolios: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "ModelPortfolio",
      },
    ],
  },
  { timestamps: true },
);

// JWT Token Generation Methods
agencySchema.methods.generateAccessToken = function () {
  return jwt.sign(
    {
      _id: this._id,
      role: "agency",
    },
    process.env.LOGIN_ACCESS_TOKEN_SECRET,
    {
      expiresIn: process.env.LOGIN_ACCESS_TOKEN_EXPIRY,
    },
  );
};
agencySchema.methods.generateRefreshToken = function () {
  return jwt.sign(
    {
      _id: this._id,
      role: "agency",
    },
    process.env.LOGIN_REFRESH_TOKEN_SECRET,
    {
      expiresIn: process.env.LOGIN_REFRESH_TOKEN_EXPIRY,
    },
  );
};
// Hash password before save
// agencySchema.pre("save", async function (next) {
//   if (!this.isModified("password")) return next();
//   try {
//     const salt = await bcrypt.genSalt(10);
//     this.password = await bcrypt.hash(this.password, salt);
//     next();
//   } catch (err) {
//     next(err);
//   }
// });

const Agency = mongoose.model("Agency", agencySchema);
export default Agency;
