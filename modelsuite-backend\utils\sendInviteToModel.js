import nodemailer from "nodemailer";

const transporter = nodemailer.createTransport({
  // Your email configuration
  service: "gmail", // or your email service
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
});

export const sendInviteToModel = async (email, inviteData) => {
  const { modelName, agencyName, inviteLink } = inviteData;

  const htmlTemplate = `
    <!DOCTYPE html>
    <html>
    <head>
        <style>
            .container { max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
            .content { padding: 30px; background: #f9f9f9; }
            .button { background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 20px 0; }
            .footer { background: #333; color: white; padding: 20px; text-align: center; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🎉 Agency Invitation</h1>
                <p>You've been invited to join an agency!</p>
            </div>
            <div class="content">
                <h2>Hello ${modelName}!</h2>
                <p><strong>${agencyName}</strong> has invited you to join their agency on ModelSuite.ai</p>
                
                <p>To accept this invitation:</p>
                <ol>
                    <li>Click the button below</li>
                    <li>Enter your current password to verify your identity</li>
                    <li>You'll be automatically added to the agency</li>
                </ol>
                
                <div style="text-align: center;">
                    <a href="${inviteLink}" class="button">Accept Invitation</a>
                </div>
                
                <p><strong>Important:</strong></p>
                <ul>
                    <li>This invitation expires in 7 days</li>
                    <li>You'll need your current password to accept</li>
                    <li>You can only be part of one agency at a time</li>
                </ul>
                
                <p>If you didn't expect this invitation or have any questions, please contact support.</p>
            </div>
            <div class="footer">
                <p>© 2025 ModelSuite.ai. All rights reserved.</p>
                <p>This is an automated email. Please do not reply.</p>
            </div>
        </div>
    </body>
    </html>
  `;

  const mailOptions = {
    from: process.env.EMAIL_USER,
    to: email,
    subject: `Invitation to join ${agencyName} - ModelSuite.ai`,
    html: htmlTemplate,
  };

  return transporter.sendMail(mailOptions);
};
