export const LEVEL_THRESHOLDS = [
  { level: 1, minPoints: 0 },
  { level: 2, minPoints: 100 },
  { level: 3, minPoints: 250 },
  { level: 4, minPoints: 500 },
  { level: 5, minPoints: 850 },
  { level: 6, minPoints: 1300 },
  { level: 7, minPoints: 1850 },
  { level: 8, minPoints: 2500 },
  { level: 9, minPoints: 3250 },
  { level: 10, minPoints: 4100 },
  // Extended levels for power users
  { level: 15, minPoints: 7500 },
  { level: 20, minPoints: 12500 },
  { level: 25, minPoints: 20000 },
  { level: 50, minPoints: 100000 },
];

//add new reward structures here

// LOGIN STREAK REWARDS (for models)
export const LOGIN_STREAK_BADGES = {
  WARM_UP: {
    id: "login_warm_up",
    name: "Warm Up",
    description: "You've started to build consistency.",
    milestone: 3,
    points: 50,
    category: "streak",
    rarity: "common",
  },
  DAILY_GRINDER: {
    id: "login_daily_grinder",
    name: "Daily Grinder",
    description: "One week of consistent presence.",
    milestone: 7,
    points: 150,
    category: "streak",
    rarity: "common",
  },
  CONSISTENCY_HERO: {
    id: "login_consistency_hero",
    name: "Consistency Hero",
    description: "Two weeks of daily presence.",
    milestone: 14,
    points: 300,
    category: "streak",
    rarity: "rare",
  },
  ULTRA_FOCUSED: {
    id: "login_ultra_focused",
    name: "Ultra Focused",
    description: "A full month of commitment.",
    milestone: 30,
    points: 750,
    category: "streak",
    rarity: "epic",
  },
  DEDICATION_MASTER: {
    id: "login_dedication_master",
    name: "Dedication Master",
    description: "Two months of unwavering focus.",
    milestone: 60,
    points: 1500,
    category: "streak",
    rarity: "epic",
  },
  CONSISTENCY_LEGEND: {
    id: "login_consistency_legend",
    name: "Consistency Legend",
    description: "Three months of daily commitment.",
    milestone: 90,
    points: 3000,
    category: "streak",
    rarity: "legendary",
  },
};

//post counting for agency
export const POST_COUNT_BADGES = {
  CONTENT_CREATOR: {
    id: "post_content_creator",
    name: "Content Creator",
    description: "Posted first content for your models!",
    milestone: 1,
    points: 25,
    category: "milestone",
    rarity: "common",
  },
  CONTENT_MANAGER: {
    id: "post_content_manager",
    name: "Content Manager",
    description: "Posted 10 pieces of content for your models.",
    milestone: 10,
    points: 100,
    category: "milestone",
    rarity: "common",
  },
  PROLIFIC_AGENCY: {
    id: "post_prolific_agency",
    name: "Prolific Agency",
    description: "Posted 50 pieces of content for your models.",
    milestone: 50,
    points: 500,
    category: "milestone",
    rarity: "rare",
  },
  CONTENT_POWERHOUSE: {
    id: "post_content_powerhouse",
    name: "Content Powerhouse",
    description: "Posted 100 pieces of content for your models.",
    milestone: 100,
    points: 1200,
    category: "milestone",
    rarity: "epic",
  },
  CONTENT_EMPIRE: {
    id: "post_content_empire",
    name: "Content Empire",
    description: "Posted 500+ pieces of content for your models.",
    milestone: 500,
    points: 5000,
    category: "milestone",
    rarity: "legendary",
  },
};

// MODEL SIGNUP REWARDS (for agencies)
export const MODEL_SIGNUP_BADGES = {
  TALENT_SCOUT: {
    id: "signup_talent_scout",
    name: "Talent Scout",
    description: "Signed your first model!",
    milestone: 1,
    points: 100,
    category: "milestone",
    rarity: "common",
  },
  RECRUITER: {
    id: "signup_recruiter",
    name: "Recruiter",
    description: "Signed 5 models to your agency.",
    milestone: 5,
    points: 300,
    category: "milestone",
    rarity: "common",
  },
  TALENT_MAGNET: {
    id: "signup_talent_magnet",
    name: "Talent Magnet",
    description: "Signed 25 models to your agency.",
    milestone: 25,
    points: 1000,
    category: "milestone",
    rarity: "rare",
  },
  AGENCY_BUILDER: {
    id: "signup_agency_builder",
    name: "Agency Builder",
    description: "Signed 50 models to your agency.",
    milestone: 50,
    points: 2500,
    category: "milestone",
    rarity: "epic",
  },
  EMPIRE_CREATOR: {
    id: "signup_empire_creator",
    name: "Empire Creator",
    description: "Signed 100+ models to your agency.",
    milestone: 100,
    points: 6000,
    category: "milestone",
    rarity: "legendary",
  },
};

// Reward type configurations
//more types can be added
export const REWARD_CONFIGS = {
  login_streak: {
    badges: LOGIN_STREAK_BADGES,
    userTypes: ["model"],
    dailyPoints: 20,
    streakBonus: true, // Extra points for consecutive actions
    description: "Daily login consistency rewards",
  },
  post_count: {
    badges: POST_COUNT_BADGES,
    userTypes: ["agency"],
    dailyPoints: 10,
    streakBonus: false,
    description: "Content creation milestone rewards for agencies",
  },
  model_signup: {
    badges: MODEL_SIGNUP_BADGES,
    userTypes: ["agency"],
    dailyPoints: 0,
    streakBonus: false,
    description: "Model recruitment milestone rewards",
  },
};

//normal loop to return the level of any user
export const calculateLevel = (points) => {
  for (let i = LEVEL_THRESHOLDS.length - 1; i >= 0; i--) {
    if (points >= LEVEL_THRESHOLDS[i].minPoints) {
      return LEVEL_THRESHOLDS[i].level;
    }
  }
  return 1;
};
