import { asyncHand<PERSON> } from "../../utils/asyncHandler.js";
import { ApiError } from "../../utils/ApiError.js";
import CaptionLog from "../../models/caption/CaptionLog.js";
import CaptionQuota from "../../models/caption/CaptionQuota.js";
import gptService from "../../services/captionServices/gptService.js";
import geminiService from "../../services/captionServices/geminiService.js";
import PersonaIntegrationService from "../../services/personaIntegrationService.js";
import { getToday, isValidUrl } from "../../utils/captionUtils.js";
import { CAPTION_CONFIG } from "../../config/captionConfig.js";

const generateCaptions = asyncHandler(async (req, res) => {
  const {
    mediaUrl,
    captionCount = 3,
    style = "professional",
    personaEnhanced = false,
    personaId = null,
  } = req.body;
  const modelId = req.user._id;

  // Enhanced input validation
  if (!mediaUrl) {
    throw new ApiError(400, "Media URL is required");
  }

  if (!isValidUrl(mediaUrl)) {
    throw new ApiError(400, "Invalid media URL format");
  }

  const safeCount = Math.min(
    Math.max(parseInt(captionCount) || 3, 1),
    CAPTION_CONFIG.MAX_CAPTIONS_PER_REQUEST,
  );

  const validStyles = ["professional", "casual", "creative", "trendy"];
  const safeStyle = validStyles.includes(style) ? style : "professional";

  // Check quota
  const today = getToday();
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);

  const currentQuota = await CaptionQuota.findOne({
    modelId,
    date: today,
  });

  if (currentQuota && currentQuota.usageCount >= CAPTION_CONFIG.DAILY_QUOTA) {
    throw new ApiError(
      429,
      `Daily quota of ${
        CAPTION_CONFIG.DAILY_QUOTA
      } captions exceeded. Resets at ${tomorrow.toISOString()}`,
    );
  }

  try {
    const startTime = Date.now();
    let personaContext = null;
    let personaInfluence = null;

    // Get persona context if persona-enhanced mode is enabled
    if (personaEnhanced) {
      try {
        personaContext =
          await PersonaIntegrationService.getPersonaContextForCaptions(modelId);

        if (personaContext) {
          // Validate persona for caption generation
          const validation =
            PersonaIntegrationService.validatePersonaForCaption(personaContext);
          if (!validation.isValid) {
            console.warn("Persona validation failed:", validation.reason);
            personaContext = null; // Fall back to standard generation
          } else {
            // Extract persona influence for tracking
            personaInfluence =
              PersonaIntegrationService.extractPersonaInfluence(personaContext);
          }
        }
      } catch (personaError) {
        console.warn(
          "Persona integration failed, continuing with standard generation:",
          personaError.message,
        );
        personaContext = null;
      }
    }

    // Primary service (GPT-4)
    let captions, aiService, isNsfw;
    try {
      const gptResult = await gptService.generateCaptions(
        mediaUrl,
        "image", // mediaType
        0, // nsfwScore
        safeCount,
        safeStyle,
        personaContext,
      );
      captions = gptResult.captions;
      isNsfw = gptResult.isNsfw || false;
      aiService = "gpt-4";
    } catch (gptError) {
      console.warn(
        "GPT service failed, falling back to Gemini:",
        gptError.message,
      );

      // Fallback service (Gemini)
      try {
        const geminiResult = await geminiService.generateCaptions(
          mediaUrl,
          "image", // mediaType
          0, // nsfwScore
          safeCount,
          safeStyle,
          personaContext,
        );
        captions = geminiResult.captions;
        isNsfw = geminiResult.isNsfw || false;
        aiService = "gemini-vision";
      } catch (geminiError) {
        console.error("Both AI services failed:", {
          gpt: gptError.message,
          gemini: geminiError.message,
        });
        throw new ApiError(
          503,
          "AI caption services are temporarily unavailable. Please try again later.",
        );
      }
    }

    const processingTime = Date.now() - startTime;

    // Create a basic prompt for logging
    let prompt = `Generate ${safeCount} ${safeStyle} captions for this ${
      mediaUrl.includes("video") ? "video" : "image"
    }`;

    if (personaContext) {
      prompt += " (Persona-Enhanced)";
    }

    // Update quota atomically
    await CaptionQuota.findOneAndUpdate(
      { modelId, date: today },
      { $inc: { usageCount: 1 } },
      { upsert: true, new: true },
    );

    // Prepare caption log data
    const captionLogData = {
      modelId,
      mediaUrl,
      mediaType: mediaUrl.includes("video") ? "video" : "image",
      prompt,
      captions,
      aiService,
      processingTime,
      isNsfw,
      generationMode: personaContext ? "persona_enhanced" : "standard",
    };

    // Add persona data if available
    if (personaContext) {
      captionLogData.linkedPersona = {
        personaId: personaContext.personaId,
        version: personaContext.version,
        influenceScore: Math.round(
          (personaInfluence.toneInfluence +
            personaInfluence.interestsInfluence +
            personaInfluence.goalsInfluence +
            personaInfluence.demographicsInfluence +
            personaInfluence.behavioralInfluence) /
            5,
        ),
      };
      captionLogData.personaInfluence = personaInfluence;

      // Track persona usage
      await PersonaIntegrationService.trackPersonaUsage(
        personaContext.personaId,
        "caption_generation",
      );
    }

    // Log the request with minimal data
    const captionLog = new CaptionLog(captionLogData);

    try {
      await captionLog.save();
    } catch (saveError) {
      console.error("Failed to save caption log:", saveError);
      // Continue execution even if logging fails
    }

    // Response with persona information if applicable
    const response = {
      success: true,
      data: {
        captions,
        metadata: {
          aiService,
          processingTime,
          isNsfw,
          style: safeStyle,
          quotaUsed: (currentQuota?.usageCount || 0) + 1,
          quotaRemaining:
            CAPTION_CONFIG.DAILY_QUOTA - ((currentQuota?.usageCount || 0) + 1),
          generationMode: personaContext ? "persona_enhanced" : "standard",
        },
      },
    };

    if (personaContext) {
      response.data.personaInfo = {
        personaId: personaContext.personaId,
        version: personaContext.version,
        tone: personaContext.tone,
        topInterests: personaContext.topInterests,
        influenceScore: captionLogData.linkedPersona.influenceScore,
      };
    }

    res.status(200).json(response);
  } catch (error) {
    console.error("Caption generation error:", error);

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      500,
      "Failed to generate captions. Please try again later.",
    );
  }
});

const getQuotaStatus = asyncHandler(async (req, res) => {
  const modelId = req.user._id;
  const today = getToday();

  const quota = await CaptionQuota.findOne({ modelId, date: today });
  const usageCount = quota ? quota.usageCount : 0;

  // Calculate reset time (midnight UTC next day)
  const tomorrow = new Date();
  tomorrow.setUTCDate(tomorrow.getUTCDate() + 1);
  tomorrow.setUTCHours(0, 0, 0, 0);

  res.status(200).json({
    success: true,
    data: {
      usageCount,
      quotaRemaining: CAPTION_CONFIG.DAILY_QUOTA - usageCount,
      resetTime: tomorrow.toISOString(),
      dailyLimit: CAPTION_CONFIG.DAILY_QUOTA,
    },
  });
});

const getCaptionHistory = asyncHandler(async (req, res) => {
  const { page = 1, limit = CAPTION_CONFIG.DEFAULT_HISTORY_LIMIT } = req.query;
  const modelId = req.user._id;

  // Enforce maximum limit
  const safeLimit = Math.min(parseInt(limit), CAPTION_CONFIG.MAX_HISTORY_LIMIT);

  const history = await CaptionLog.find({ modelId })
    .select(
      "mediaUrl mediaType captions aiService createdAt processingTime isNsfw",
    )
    .sort({ createdAt: -1 })
    .limit(safeLimit)
    .skip((page - 1) * safeLimit);

  const total = await CaptionLog.countDocuments({ modelId });

  res.status(200).json({
    success: true,
    data: {
      history,
      pagination: {
        current: parseInt(page),
        total: Math.ceil(total / safeLimit),
        hasNext: page * safeLimit < total,
        count: history.length,
        totalRecords: total,
        limit: safeLimit,
      },
    },
  });
});

// Phase 2: New persona-related endpoints

/**
 * Get available personas for caption generation
 */
const getPersonaOptions = asyncHandler(async (req, res) => {
  console.log("=== getPersonaOptions Debug ===");
  console.log("req.user:", {
    _id: req.user._id,
    role: req.user.role,
    agencyId: req.user.agencyId,
    fullName: req.user.fullName,
  });
  console.log("req.query:", req.query);
  console.log("req.body:", req.body);

  const modelId = req.user._id;
  const agencyId = req.user.agencyId || req.body.agencyId || req.query.agencyId;

  console.log("Derived values:", { modelId, agencyId });

  if (!agencyId) {
    console.log("❌ No agencyId found!");
    throw new ApiError(400, "Agency ID is required");
  }

  console.log(
    "🔍 Calling PersonaIntegrationService.getPersonaSelectionOptions...",
  );
  const personaOptions =
    await PersonaIntegrationService.getPersonaSelectionOptions(
      modelId,
      agencyId,
    );

  console.log(
    "✅ PersonaIntegrationService returned:",
    personaOptions.length,
    "personas",
  );

  res.status(200).json({
    success: true,
    data: {
      personas: personaOptions,
      count: personaOptions.length,
    },
  });
});

/**
 * Rate caption effectiveness for analytics
 */
const rateCaptionEffectiveness = asyncHandler(async (req, res) => {
  const { captionLogId, rating, campaignId } = req.body;
  const ratedBy = req.user._id;
  const ratedByModel = req.user.role === "agency" ? "Agency" : "Employee";

  if (!captionLogId || !rating) {
    throw new ApiError(400, "Caption log ID and rating are required");
  }

  if (!["good", "neutral", "bad"].includes(rating)) {
    throw new ApiError(400, "Rating must be 'good', 'neutral', or 'bad'");
  }

  const captionLog = await CaptionLog.findById(captionLogId);

  if (!captionLog) {
    throw new ApiError(404, "Caption log not found");
  }

  // Update effectiveness rating
  captionLog.effectiveness = {
    agencyRating: rating,
    ratedAt: new Date(),
    ratedBy,
    ratedByModel,
  };

  await captionLog.save();

  res.status(200).json({
    success: true,
    message: "Caption effectiveness rated successfully",
    data: {
      captionLogId,
      rating,
      ratedAt: captionLog.effectiveness.ratedAt,
    },
  });
});

/**
 * Get persona influence analytics for captions
 */
const getPersonaAnalytics = asyncHandler(async (req, res) => {
  const modelId = req.user._id;
  const { personaId, dateRange = 30 } = req.query;

  const startDate = new Date();
  startDate.setDate(startDate.getDate() - parseInt(dateRange));

  const matchConditions = {
    modelId,
    createdAt: { $gte: startDate },
    generationMode: "persona_enhanced",
  };

  if (personaId) {
    matchConditions["linkedPersona.personaId"] = personaId;
  }

  const analytics = await CaptionLog.aggregate([
    { $match: matchConditions },
    {
      $group: {
        _id: null,
        totalPersonaEnhanced: { $sum: 1 },
        avgInfluenceScore: { $avg: "$linkedPersona.influenceScore" },
        avgToneInfluence: { $avg: "$personaInfluence.toneInfluence" },
        avgInterestsInfluence: { $avg: "$personaInfluence.interestsInfluence" },
        avgGoalsInfluence: { $avg: "$personaInfluence.goalsInfluence" },
        avgDemographicsInfluence: {
          $avg: "$personaInfluence.demographicsInfluence",
        },
        avgBehavioralInfluence: {
          $avg: "$personaInfluence.behavioralInfluence",
        },
        effectivenessRatings: {
          $push: "$effectiveness.agencyRating",
        },
      },
    },
  ]);

  // Get effectiveness breakdown
  const effectivenessBreakdown = await CaptionLog.aggregate([
    {
      $match: {
        ...matchConditions,
        "effectiveness.agencyRating": { $exists: true },
      },
    },
    {
      $group: {
        _id: "$effectiveness.agencyRating",
        count: { $sum: 1 },
      },
    },
  ]);

  res.status(200).json({
    success: true,
    data: {
      analytics: analytics[0] || {},
      effectivenessBreakdown,
      dateRange: parseInt(dateRange),
      personaId: personaId || "all",
    },
  });
});

export {
  generateCaptions,
  getQuotaStatus,
  getCaptionHistory,
  getPersonaOptions,
  rateCaptionEffectiveness,
  getPersonaAnalytics,
};
