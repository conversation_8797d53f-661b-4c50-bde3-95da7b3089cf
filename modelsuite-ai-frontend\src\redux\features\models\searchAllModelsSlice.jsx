import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axiosInstance from "@/config/axiosInstance";

// Thunk to search all models for agency
export const searchAllModels = createAsyncThunk(
  "searchAllModels",
  async (searchTerm = "", { rejectWithValue }) => {
    try {
      const response = await axiosInstance.get("/agency/search-all-models", {
        params: { search: searchTerm }
      });
      return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

const searchAllModelsSlice = createSlice({
  name: "searchAllModels",
  initialState: {
    searchResults: [],
    loading: false,
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(searchAllModels.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(searchAllModels.fulfilled, (state, action) => {
        state.loading = false;
        state.searchResults = action.payload;
      })
      .addCase(searchAllModels.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default searchAllModelsSlice.reducer;
