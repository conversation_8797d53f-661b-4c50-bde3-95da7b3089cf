import mongoose from "mongoose";

const availabilitySchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ModelUser", // IMPORTANT: matches your model name
      required: true,
    },
    startDate: {
      type: Date,
      required: true,
    },
    endDate: {
      type: Date,
      required: true,
    },
    note: {
      type: String,
      maxlength: 200,
    },
  },
  { timestamps: true },
);

availabilitySchema.index({ userId: 1, startDate: 1, endDate: 1 });

const Availability = mongoose.model("Availability", availabilitySchema);
export default Availability;
