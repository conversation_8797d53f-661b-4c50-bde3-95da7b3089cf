import OpenAI from "openai";
import { ApiError } from "../../utils/ApiError.js";
import { CAPTION_CONFIG } from "../../config/captionConfig.js";
import {
  retryWithBackoff,
  createMinimalMetadata,
} from "../../utils/captionUtils.js";

class GPTService {
  constructor() {
    this.apiKey = process.env.OPENAI_API_KEY;
    if (!this.apiKey) {
      console.warn("OPENAI_API_KEY not found in environment variables");
    }
    this.openai = new OpenAI({
      apiKey: this.apiKey,
    });
  }

  async generateCaptions(
    mediaUrl,
    mediaType,
    nsfwScore = 0,
    captionCount = 3,
    style = "professional",
    personaContext = null,
  ) {
    const startTime = Date.now();

    try {
      const result = await retryWithBackoff(async () => {
        return await this._makeApiCall(
          mediaUrl,
          mediaType,
          nsfwScore,
          captionCount,
          style,
          personaContext,
        );
      });

      return {
        ...result,
        processingTime: Date.now() - startTime,
      };
    } catch (error) {
      throw new ApiError(500, `GPT-4 generation failed: ${error.message}`);
    }
  }

  async _makeApiCall(
    mediaUrl,
    mediaType,
    nsfwScore,
    captionCount,
    style,
    personaContext = null,
  ) {
    const prompt = this.buildPrompt(
      mediaType,
      nsfwScore,
      captionCount,
      style,
      personaContext,
    );

    const response = await this.openai.chat.completions.create({
      model: "gpt-4o-mini", // Updated to current model
      messages: [
        {
          role: "user",
          content: [
            { type: "text", text: prompt },
            {
              type: "image_url",
              image_url: {
                url: mediaUrl,
                detail: "low", // Reduce cost
              },
            },
          ],
        },
      ],
      max_tokens: CAPTION_CONFIG.GPT_MAX_TOKENS,
      temperature: CAPTION_CONFIG.GPT_TEMPERATURE,
    });

    const content = response.choices[0]?.message?.content;

    if (!content) {
      throw new Error("No content received from GPT-4");
    }

    const captions = this.parseCaptions(content);

    return {
      captions,
      rawResponse: createMinimalMetadata(response, "gpt-4o-mini"),
      personaContext, // Include persona context in response for tracking
    };
  }

  buildPrompt(
    mediaType,
    nsfwScore,
    captionCount = 3,
    style = "professional",
    personaContext = null,
  ) {
    const stylePrompts = {
      professional: "professional and polished",
      casual: "casual and relatable",
      creative: "creative and artistic",
      trendy: "trendy and viral-worthy",
    };

    const styleDescription = stylePrompts[style] || stylePrompts.professional;

    let basePrompt = `Generate exactly ${captionCount} engaging ${styleDescription} social media caption${
      captionCount > 1 ? "s" : ""
    } for this ${mediaType}. 
Make them suitable for a model's Instagram/TikTok post with a ${style} tone.
Include relevant emojis and make each caption unique.
Return only the captions, one per line, no numbering or bullets.`;

    // Add persona context if available
    if (personaContext) {
      basePrompt += "\n\nPERSONA CONTEXT:";

      if (personaContext.personaSummary) {
        basePrompt += `\nTarget Audience: ${personaContext.personaSummary}`;
      }

      if (personaContext.tone) {
        basePrompt += `\nBrand Tone: ${personaContext.tone}`;
      }

      if (personaContext.topInterests.length > 0) {
        basePrompt += `\nKey Interests: ${personaContext.topInterests.join(
          ", ",
        )}`;
      }

      if (personaContext.emojiUsage) {
        basePrompt += `\nEmoji Style: ${personaContext.emojiUsage}`;
      }

      if (personaContext.hashtagStrategy) {
        basePrompt += `\nHashtag Approach: ${personaContext.hashtagStrategy}`;
      }

      if (personaContext.brandValues.length > 0) {
        basePrompt += `\nBrand Values: ${personaContext.brandValues.join(
          ", ",
        )}`;
      }

      if (personaContext.goals.length > 0) {
        basePrompt += `\nContent Goals: ${personaContext.goals.join(", ")}`;
      }

      basePrompt += "\n\nINSTRUCTIONS:";
      basePrompt +=
        "\n- Align captions with the target audience persona described above";
      basePrompt += "\n- Match the specified brand tone and emoji usage style";
      basePrompt +=
        "\n- Incorporate relevant interests and brand values naturally";
      basePrompt +=
        "\n- Ensure hashtag strategy aligns with the persona's approach";
      basePrompt += "\n- Keep content goals in mind when crafting messaging";
    }

    if (nsfwScore >= CAPTION_CONFIG.NSFW_THRESHOLD) {
      basePrompt +=
        "\n\nNote: Keep captions tasteful and platform-appropriate.";
    }

    return basePrompt;
  }

  parseCaptions(content) {
    return content
      .split("\n")
      .map((line) => line.trim())
      .filter((line) => line && !line.match(/^\d+\./) && !line.match(/^-/)) // Remove numbering
      .slice(0, CAPTION_CONFIG.MAX_CAPTIONS_PER_REQUEST); // Use the correct config property
  }

  /**
   * Generate persona profile using GPT-4
   */
  async generatePersona(prompt) {
    const startTime = Date.now();

    try {
      const result = await retryWithBackoff(async () => {
        return await this._makePersonaApiCall(prompt);
      });

      return {
        content: result,
        processingTime: Date.now() - startTime,
      };
    } catch (error) {
      throw new ApiError(
        500,
        `GPT-4 persona generation failed: ${error.message}`,
      );
    }
  }

  /**
   * Make API call for persona generation
   */
  async _makePersonaApiCall(prompt) {
    const response = await this.openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content:
            "You are an expert marketing strategist specializing in audience persona development for social media influencers and models. Generate detailed, actionable personas based on the provided information.",
        },
        {
          role: "user",
          content: prompt,
        },
      ],
      max_tokens: 1000,
      temperature: 0.7,
      response_format: { type: "json_object" },
    });

    const content = response.choices[0]?.message?.content;

    if (!content) {
      throw new Error("No content received from GPT-4 for persona generation");
    }

    return content;
  }
}

export default new GPTService();
