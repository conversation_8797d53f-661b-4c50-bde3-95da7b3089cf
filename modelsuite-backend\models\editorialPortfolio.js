import mongoose from "mongoose";

// Define the canvas element schema
const canvasElementSchema = new mongoose.Schema(
  {
    id: Number,
    type: { type: String, enum: ["image", "text"], required: true },
    x: Number,
    y: Number,
    width: Number,
    height: Number,
    rotation: Number,
    zIndex: Number,

    // For image
    imageIndex: Number,
    imageUrl: String,
    imageFilename: String,
    originalImageName: String,

    // For text
    content: String,
    fontSize: Number,
    fontFamily: String,
    color: String,
    bold: Boolean,
    italic: Boolean,
    underline: Boolean,
  },
  { _id: false },
);

// Main schema
const editorialPortfolioSchema = new mongoose.Schema(
  {
    slug: { type: String, required: true, unique: true },
    modelName: String,
    password: String,
    template: String,

    visibility: {
      type: String,
      enum: ["public", "private", "protected"],
      default: "public",
    },

    measurements: {
      height: String,
      weight: String,
      bust: String,
      waist: String,
      hips: String,
      dressSize: String,
      shoeSize: String,
      eyeColor: String,
      hairColor: String,
    },

    contact: {
      email: String,
      phone: String,
      instagram: String,
      website: String,
    },

    canvas: {
      backgroundColor: String,
      elements: [canvasElementSchema],
    },

    coverImage: {
      url: String,
      filename: String,
      originalName: String,
    },

    agency: { type: mongoose.Schema.Types.ObjectId, ref: "Agency" },

    model: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ModelUser",
      unique: true,
    },
    resetPasswordOTP: {
      code: {
        type: String,
      },
      expiresAt: {
        type: Date,
      },
    },
    resetPasswordOTPVerified: {
      type: Boolean,
      default: false,
    },
    description: {
      type: String,
      maxlength: 500,
      trim: true,
    },
  },
  { timestamps: true }, // Adds createdAt and updatedAt
);

const EditorialPortfolio = mongoose.model(
  "EditorialPortfolio",
  editorialPortfolioSchema,
);

export default EditorialPortfolio;
