import express from "express";
import { verifyToken } from "../../middlewares/authMiddleware.js";
import checkPermission from "../../middlewares/permissionCheck.js";
import {
  disconnectTikTok,
  getTikTokProfile,
  getTikTokStats,
  handleTikTokCallback,
  isTikTokConnected,
} from "../../controllers/socialMedia/tiktokController.js";

const router = express.Router();

router.get("/callback", handleTikTokCallback);
router.get(
  "/profile",
  verifyToken,
  checkPermission("social_media.view"),
  getTikTokProfile,
);
router.get(
  "/stats",
  verifyToken,
  checkPermission("social_media.view"),
  getTikTokStats,
);
router.delete(
  "/disconnect",
  verifyToken,
  checkPermission("social_media.manage"),
  disconnectTikTok,
);
router.get(
  "/is-connected",
  verifyToken,
  checkPermission("social_media.view"),
  isTikTokConnected,
);

export default router;
