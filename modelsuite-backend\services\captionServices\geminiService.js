import { GoogleGenerativeAI } from "@google/generative-ai";
import { ApiError } from "../../utils/ApiError.js";
import { CAPTION_CONFIG } from "../../config/captionConfig.js";
import {
  retryWithBackoff,
  createMinimalMetadata,
} from "../../utils/captionUtils.js";

class GeminiService {
  constructor() {
    this.apiKey = process.env.GEMINI_API_KEY;
    if (!this.apiKey) {
      console.warn("GEMINI_API_KEY not found in environment variables");
    }
    this.genAI = new GoogleGenerativeAI(this.apiKey);
    this.model = this.genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
  }

  async generateCaptions(
    mediaUrl,
    mediaType,
    nsfwScore = 0,
    captionCount = 3,
    style = "professional",
    personaContext = null,
  ) {
    const startTime = Date.now();

    try {
      const result = await retryWithBackoff(async () => {
        return await this._makeApiCall(
          mediaUrl,
          mediaType,
          captionCount,
          style,
          personaContext,
        );
      });

      return {
        ...result,
        processingTime: Date.now() - startTime,
      };
    } catch (error) {
      throw new ApiError(500, `Gemini generation failed: ${error.message}`);
    }
  }

  async _makeApiCall(
    mediaUrl,
    mediaType,
    captionCount,
    style,
    personaContext = null,
  ) {
    const prompt = this.buildPrompt(
      mediaType,
      captionCount,
      style,
      personaContext,
    );
    const imagePart = await this.fileToGenerativePart(mediaUrl);

    const result = await this.model.generateContent([prompt, imagePart]);
    const response = await result.response;
    const content = response.text();

    if (!content) {
      throw new Error("No content received from Gemini");
    }

    const captions = this.parseCaptions(content);
    const nsfwScore = 0; // Gemini 1.5 handles safety differently

    return {
      captions,
      nsfwScore,
      rawResponse: createMinimalMetadata(response, "gemini-1.5-flash"),
      personaContext, // Include persona context in response for tracking
    };
  }

  async fileToGenerativePart(url) {
    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch media: ${response.statusText}`);
      }

      const arrayBuffer = await response.arrayBuffer();
      const mimeType = response.headers.get("content-type") || "image/jpeg";

      return {
        inlineData: {
          data: Buffer.from(arrayBuffer).toString("base64"),
          mimeType: mimeType,
        },
      };
    } catch (error) {
      throw new Error(`Failed to process media file: ${error.message}`);
    }
  }

  buildPrompt(
    mediaType,
    captionCount = 3,
    style = "professional",
    personaContext = null,
  ) {
    const stylePrompts = {
      professional: "professional and polished",
      casual: "casual and relatable",
      creative: "creative and artistic",
      trendy: "trendy and viral-worthy",
    };

    const styleDescription = stylePrompts[style] || stylePrompts.professional;

    let basePrompt = `Generate exactly ${captionCount} engaging ${styleDescription} social media caption${
      captionCount > 1 ? "s" : ""
    } for this ${mediaType}. 
Make them suitable for a model's social media post with a ${style} tone.
Include relevant emojis and make each caption unique.
Return only captions, one per line, no numbering or bullets.`;

    // Add persona context if available
    if (personaContext) {
      basePrompt += "\n\nPERSONA CONTEXT:";

      if (personaContext.personaSummary) {
        basePrompt += `\nTarget Audience: ${personaContext.personaSummary}`;
      }

      if (personaContext.tone) {
        basePrompt += `\nBrand Tone: ${personaContext.tone}`;
      }

      if (personaContext.topInterests.length > 0) {
        basePrompt += `\nKey Interests: ${personaContext.topInterests.join(
          ", ",
        )}`;
      }

      if (personaContext.emojiUsage) {
        basePrompt += `\nEmoji Style: ${personaContext.emojiUsage}`;
      }

      if (personaContext.hashtagStrategy) {
        basePrompt += `\nHashtag Approach: ${personaContext.hashtagStrategy}`;
      }

      if (personaContext.brandValues.length > 0) {
        basePrompt += `\nBrand Values: ${personaContext.brandValues.join(
          ", ",
        )}`;
      }

      if (personaContext.goals.length > 0) {
        basePrompt += `\nContent Goals: ${personaContext.goals.join(", ")}`;
      }

      basePrompt += "\n\nINSTRUCTIONS:";
      basePrompt +=
        "\n- Align captions with the target audience persona described above";
      basePrompt += "\n- Match the specified brand tone and emoji usage style";
      basePrompt +=
        "\n- Incorporate relevant interests and brand values naturally";
      basePrompt +=
        "\n- Ensure hashtag strategy aligns with the persona's approach";
      basePrompt += "\n- Keep content goals in mind when crafting messaging";
    }

    return basePrompt;
  }

  parseCaptions(content) {
    return content
      .split("\n")
      .map((line) => line.trim())
      .filter((line) => line && !line.match(/^\d+\./) && !line.match(/^-/)) // Remove numbering
      .slice(0, CAPTION_CONFIG.MAX_CAPTIONS_PER_REQUEST); // Use the correct config property
  }

  /**
   * Generate persona profile using Gemini as fallback
   */
  async generatePersona(prompt) {
    const startTime = Date.now();

    try {
      const result = await retryWithBackoff(async () => {
        return await this._makePersonaApiCall(prompt);
      });

      return {
        content: result,
        processingTime: Date.now() - startTime,
      };
    } catch (error) {
      throw new ApiError(
        500,
        `Gemini persona generation failed: ${error.message}`,
      );
    }
  }

  /**
   * Make API call for persona generation
   */
  async _makePersonaApiCall(prompt) {
    const enhancedPrompt = `${prompt}\n\nIMPORTANT: Return your response as a valid JSON object only, no additional text or formatting.`;

    const result = await this.model.generateContent(enhancedPrompt);
    const response = await result.response;
    const content = response.text();

    if (!content) {
      throw new Error("No content received from Gemini for persona generation");
    }

    return content;
  }
}

export default new GeminiService();
