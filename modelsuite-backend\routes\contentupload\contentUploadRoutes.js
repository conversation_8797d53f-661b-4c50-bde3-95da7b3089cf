import express from "express";
import ContentUploadController from "../../controllers/contentupload/contentuploadcontroller.js";
import { verifyToken } from "../../middlewares/authMiddleware.js";
import multer from "multer";

/**
 * Content Upload Routes - Handles all content upload related endpoints
 * Includes category management, file uploads, approval workflows, and analytics
 */
const router = express.Router();

// Configure multer for file uploads (memory storage for Cloudflare R2)
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 2147483648, // 2GB max file size
  },
  fileFilter: (req, file, cb) => {
    // Basic file type validation (will be enhanced by category rules)
    const allowedMimes = [
      "image/jpeg",
      "image/jpg",
      "image/png",
      "image/gif",
      "image/webp",
      "video/mp4",
      "video/mov",
      "video/avi",
      "video/quicktime",
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ];

    if (allowedMimes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`File type ${file.mimetype} is not supported`), false);
    }
  },
});

// Apply JWT verification to all routes
router.use(verifyToken);

// ============================================================================
// CATEGORY MANAGEMENT ROUTES
// ============================================================================

/**
 * Create a new content category
 * POST /api/v1/content/categories
 * Access: Agency users only
 */
router.post("/categories", ContentUploadController.createCategory);

/**
 * Get all categories for the authenticated user's agency
 * GET /api/v1/content/categories
 * Query params: includeInactive (boolean)
 * Access: All authenticated users
 */
router.get("/categories", ContentUploadController.getAgencyCategories);

/**
 * Update a specific category
 * PUT /api/v1/content/categories/:id
 * Access: Agency users only
 */
router.put("/categories/:id", ContentUploadController.updateCategory);

/**
 * Delete (deactivate) a specific category
 * DELETE /api/v1/content/categories/:id
 * Access: Agency users only
 */
router.delete("/categories/:id", ContentUploadController.deleteCategory);
// ============================================================================
// CONTENT UPLOAD ROUTES
// ============================================================================

// Helper middleware to check if user is a model
const checkModelRole = (req, res, next) => {
  // Debug logging to identify the issue
  console.log("=== DEBUG: checkModelRole middleware ===");
  console.log("req.user exists:", !!req.user);
  console.log("req.user:", req.user);
  console.log("req.user.role:", req.user?.role);
  console.log("typeof req.user.role:", typeof req.user?.role);
  console.log("role comparison result:", req.user?.role === "model");
  console.log("========================================");

  if (req.user && req.user.role === "model") {
    next();
  } else {
    return res.status(403).json({
      message: "Access denied: Only models can upload content",
      debug: {
        userExists: !!req.user,
        userRole: req.user?.role,
        roleType: typeof req.user?.role,
      },
    });
  }
};

/**
 * Upload content file with metadata
 * POST /api/v1/content/upload
 * Content-Type: multipart/form-data
 * Fields: file, categoryId, metadata, hasConsent, hasWatermark, releaseFormUrl
 * Access: Model users only
 */
router.post(
  "/upload",
  checkModelRole,
  upload.single("file"),
  ContentUploadController.uploadContent,
);

/**
 * Get uploads for a specific model
 * GET /api/v1/content/uploads/model/:modelId
 * Query params: categoryId, status, page, limit, sortBy, sortOrder
 * Access: Models can only view their own uploads, Agency can view any model's uploads
 */
router.get("/uploads/model/:modelId", ContentUploadController.getModelUploads);

// Agency uploads endpoint removed - not needed for current requirements

// ============================================================================
// APPROVAL WORKFLOW ROUTES
// ============================================================================

/**
 * Approve an upload
 * PUT /api/v1/content/uploads/:id/approve
 * Body: { comment?: string }
 * Access: Agency users only
 */
router.put("/uploads/:id/approve", ContentUploadController.approveUpload);

/**
 * Reject an upload
 * PUT /api/v1/content/uploads/:id/reject
 * Body: { reason: string, comment?: string }
 * Access: Agency users only
 */
router.put("/uploads/:id/reject", ContentUploadController.rejectUpload);

/**
 * Add comment to an upload
 * POST /api/v1/content/uploads/:id/comments
 * Body: { comment: string }
 * Access: All authenticated users (models can comment on their uploads, agency on any)
 */
router.post("/uploads/:id/comments", ContentUploadController.addComment);

/**
 * Update upload status with workflow validation
 * PUT /api/v1/content/uploads/:id/status
 * Body: { status: string, reason?: string, comment?: string }
 * Access: Agency users (full access), Models (limited transitions)
 */
router.put("/uploads/:id/status", ContentUploadController.updateStatus);

/**
 * Get comments for a specific upload
 * GET /api/v1/content/uploads/:id/comments
 * Access: Models can view comments on their uploads, Agency can view any upload's comments
 */
router.get("/uploads/:id/comments", ContentUploadController.getUploadComments);

// Comment update and delete endpoints removed - not needed for current requirements

// Analytics endpoints removed - not needed for current requirements

// ============================================================================
// PHASE 3: MODEL ASSIGNMENT & REMINDER ROUTES
// ============================================================================

/**
 * Assign a model to a category
 * POST /api/v1/content/assignments
 * Body: { modelId, categoryId, reminderFrequency?, customSettings? }
 * Access: Agency users only
 */
router.post("/assignments", ContentUploadController.assignModelToCategory);

/**
 * Get assignments for a specific model
 * GET /api/v1/content/assignments/model/:modelId
 * Access: Models can view their own assignments, Agency can view any model's assignments
 */
router.get(
  "/assignments/model/:modelId",
  ContentUploadController.getModelAssignments,
);

/**
 * Get all assignments for the agency
 * GET /api/v1/content/assignments/agency
 * Query params: modelId, categoryId, isActive, page, limit
 * Access: Agency users only
 */
router.get("/assignments/agency", ContentUploadController.getAgencyAssignments);

/**
 * Update an existing assignment
 * PUT /api/v1/content/assignments/:id
 * Body: { reminderFrequency?, customSettings?, isActive?, priority? }
 * Access: Agency users only
 */
router.put("/assignments/:id", ContentUploadController.updateAssignment);

/**
 * Remove (deactivate) an assignment
 * DELETE /api/v1/content/assignments/:id
 * Access: Agency users only
 */
router.delete("/assignments/:id", ContentUploadController.removeAssignment);

/**
 * Get overdue content for the agency
 * GET /api/v1/content/overdue
 * Query params: modelId, categoryId, daysOverdue
 * Access: Agency users only
 */
router.get("/overdue", ContentUploadController.getOverdueContent);

/**
 * Send reminders for specific assignments
 * POST /api/v1/content/reminders/send
 * Body: { assignmentIds: string[], reminderType?: string }
 * Access: Agency users only
 */
router.post("/reminders/send", ContentUploadController.sendReminders);

/**
 * Get assignments due soon
 * GET /api/v1/content/assignments/due-soon
 * Query params: days (default: 7), modelId, categoryId
 * Access: Agency users only
 */
router.get(
  "/assignments/due-soon",
  ContentUploadController.getAssignmentsDueSoon,
);

// Assignment statistics endpoint removed - not needed for current requirements

// Agency configuration endpoints removed - not needed for current requirements

/**
 * Test notification configuration
 * POST /api/v1/content/configuration/test-notifications
 * Body: { notificationType: 'email'|'sms', testRecipient: string }
 * Access: Agency users only
 */
router.post(
  "/configuration/test-notifications",
  ContentUploadController.testNotificationConfiguration,
);

// ============================================================================
// UTILITY ROUTES (Future Implementation)
// ============================================================================

/**
 * Get upload by ID with full details
 * GET /api/v1/content/uploads/:id
 * Access: Models can view their own uploads, Agency can view any upload
 */
// router.get("/uploads/:id", ContentUploadController.getUploadById);

/**
 * Update upload metadata
 * PUT /api/v1/content/uploads/:id/metadata
 * Access: Models can update their own uploads (if not approved), Agency can update any
 */
// router.put("/uploads/:id/metadata", ContentUploadController.updateUploadMetadata);

/**
 * Delete (soft delete) an upload
 * DELETE /api/v1/content/uploads/:id
 * Access: Models can delete their own uploads (if not approved), Agency can delete any
 */
// router.delete("/uploads/:id", ContentUploadController.deleteUpload);

/**
 * Download/access upload file
 * GET /api/v1/content/uploads/:id/download
 * Access: Authenticated users with proper permissions
 */
// router.get("/uploads/:id/download", ContentUploadController.downloadUpload);

/**
 * Get overdue uploads for reminders
 * GET /api/v1/content/overdue
 * Access: Agency users only
 */
// router.get("/overdue", ContentUploadController.getOverdueUploads);

/**
 * Bulk operations on uploads
 * POST /api/v1/content/uploads/bulk
 * Body: { action: 'approve'|'reject'|'delete', uploadIds: string[], reason?: string }
 * Access: Agency users only
 */
// router.post("/uploads/bulk", ContentUploadController.bulkOperations);

export default router;
