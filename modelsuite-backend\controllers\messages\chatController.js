import MsgTemplate from "../../models/messanger/msgTemplate.js";
import { GoogleGenAI } from "@google/genai";
import dotenv from "dotenv";
dotenv.config();
/**
 * GET /messanger/chat/templates
 * Fetch all templates for the authenticated user (or global ones).
 */
export const getTemplates = async (req, res) => {
  try {
    const userId = req.user?._id;
    const templates = await MsgTemplate.find({
      $or: [
        { userId }, // user-specific
        { userId: null }, // global
      ],
    }).sort({ createdAt: -1 });

    res.json(templates);
  } catch (err) {
    console.error("Error fetching templates:", err);
    res.status(500).json({ message: "Failed to load templates" });
  }
};

/**
 * POST /messanger/chat/templates/create
 * Create a new message template.
 */
export const createTemplate = async (req, res) => {
  try {
    const { title, content, category } = req.body;
    const userId = req.user?._id;

    const newTemplate = new MsgTemplate({
      title,
      content,
      category,
      userId,
    });

    await newTemplate.save();
    res.status(201).json(newTemplate);
  } catch (err) {
    console.error("Error creating template:", err);
    res.status(500).json({ message: "Failed to create template" });
  }
};

/**
 * PUT /messanger/chat/templates/update
 * Update an existing template.
 */
export const updateTemplate = async (req, res) => {
  try {
    const { id, title, content, category } = req.body;
    const userId = req.user?._id;

    const existingTemplate = await MsgTemplate.findById(id);

    if (!existingTemplate) {
      return res.status(404).json({ message: "Template not found" });
    }

    // Make sure the template belongs to the current user
    if (existingTemplate.userId?.toString() !== userId.toString()) {
      return res.status(403).json({ message: "Unauthorized" });
    }

    existingTemplate.title = title;
    existingTemplate.content = content;
    existingTemplate.category = category;

    await existingTemplate.save();
    res.json(existingTemplate);
  } catch (err) {
    console.error("Error updating template:", err);
    res.status(500).json({ message: "Failed to update template" });
  }
};

/**
 * DELETE /messanger/chat/templates/delete
 * Delete a template by ID (passed in body).
 */
export const deleteTemplate = async (req, res) => {
  try {
    const { id } = req.body;
    const userId = req.user?._id;

    const template = await MsgTemplate.findById(id);

    if (!template) {
      return res.status(404).json({ message: "Template not found" });
    }

    if (template.userId?.toString() !== userId.toString()) {
      return res.status(403).json({ message: "Unauthorized" });
    }

    await MsgTemplate.deleteOne({ _id: id });
    res.json({ message: "Template deleted" });
  } catch (err) {
    console.error("Error deleting template:", err);
    res.status(500).json({ message: "Failed to delete template" });
  }
};

// translate enpoint

const GEMINI_API_KEY = process.env.GEMINI_PRO_API_KEY;
const ai = new GoogleGenAI({ apiKey: GEMINI_API_KEY });

const MAX_INPUT_CHARS = 1000; // Limit input size
const SUPPORTED_LANGUAGES = [
  "english",
  "german",
  "french",
  "spanish",
  "russian",
];

export const translateMsg = async (req, res) => {
  try {
    const { text, targetLang } = req.body;

    if (!text || typeof text !== "string") {
      return res.status(400).json({ error: "Invalid text input" });
    }

    if (text.length > MAX_INPUT_CHARS) {
      return res
        .status(413)
        .json({ error: "Input too long. Limit is 1000 characters." });
    }

    if (!SUPPORTED_LANGUAGES.includes(targetLang.toLowerCase())) {
      return res.status(400).json({ error: "Unsupported target language" });
    }

    const prompt = `
Translate the following text to ${targetLang} using simple, clear, and commonly spoken language. Preserve any markdown like - **dmo**, *demo*, backtick[demo]backtick ~~demo~~ # demo, links, /n as they are useful for formatting.

Text to translate:
${text}
`;

    const result = await ai.models.generateContent({
      model: "gemini-1.5-flash",
      contents: [{ role: "user", parts: [{ text: prompt }] }],
    });

    const translatedText = result?.candidates?.[0]?.content?.parts?.[0]?.text;
    return res.json({ translated: translatedText });
  } catch (err) {
    console.error(err);
    return res.status(500).json({ error: "Translation failed" });
  }
};

// bulk message translation
// utils/geminiBulkTranslate.js

export const bulkTranslateWithGemini = async (
  messagesToTranslate,
  targetLang,
) => {
  if (!Array.isArray(messagesToTranslate) || messagesToTranslate.length === 0) {
    throw new Error("No messages to translate");
  }

  if (!SUPPORTED_LANGUAGES.includes(targetLang.toLowerCase())) {
    throw new Error(`Unsupported target language: ${targetLang}`);
  }

  // Combine into one prompt
  const prompt = `
You are a translation assistant. 
Translate each of the provided messages into ${targetLang} using simple, clear, and natural language.
Preserve any markdown inside text property (**bold**, *italic*, backticks, ~~strikethrough~~, # headers, links) and newlines exactly.
Respond with **ONLY** a valid JSON array — no prose, no code fences, no \`\`\` markers outside json.
Return the result ONLY as a JSON array of objects in the format as a text not code block:
[{ "messageId": "<original_messageId>", "text": "<translated_text>" }]

Messages to translate:
${JSON.stringify(messagesToTranslate)}
`;

  const result = await ai.models.generateContent({
    model: "gemini-1.5-flash",
    contents: [{ role: "user", parts: [{ text: prompt }] }],
  });

  let parsed;
  try {
    const rawResponse = result?.candidates?.[0]?.content?.parts?.[0]?.text;
    const start = rawResponse.indexOf("[");
    const end = rawResponse.lastIndexOf("]");

    if (start === -1 || end === -1 || end <= start) {
      throw new Error("No valid JSON array found");
    }

    const jsonString = rawResponse.slice(start, end + 1);
    parsed = JSON.parse(jsonString);
  } catch (err) {
    console.error(
      "Failed to parse Gemini output:",
      err,
      result?.candidates?.[0]?.content?.parts?.[0]?.text,
    );
    throw new Error("Gemini returned invalid JSON");
  }

  return parsed; // array of { messageId, text }
};

export const translateBulkMessages = async (req, res) => {
  try {
    const { messagesToTranslate, lang } = req.body;

    if (!Array.isArray(messagesToTranslate) || !lang) {
      return res.status(400).json({ error: "Invalid request data" });
    }

    const translatedMessages = await bulkTranslateWithGemini(
      messagesToTranslate,
      lang,
    );

    return res.json({ translatedMessages });
  } catch (err) {
    console.error("translateBulkMessages error:", err);
    res.status(500).json({ error: err.message || "Translation failed" });
  }
};
