import mongoose from "mongoose";

const ToolEntrySchema = new mongoose.Schema(
  {
    agency: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      required: true,
    },
    tool_name: { type: String, required: true },
    purpose: { type: String, required: true },
    tags: [{ type: String }],
    users: [{ type: mongoose.Schema.Types.ObjectId, ref: "UserProfile" }],
    access_type: {
      type: String,
      enum: ["Admin", "Team Login", "Individual"],
      required: true,
    },
    monthly_cost: { type: Number, required: true },
    currency: { type: String, default: "EUR" },
    billing_cycle: {
      type: String,
      enum: ["Monthly", "Annual", "One-Time"],
      required: true,
    },
    subscription_date: { type: Date, required: true },
    responsible_person: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "UserProfile",
      required: true,
    },
    login_details: { type: String },
    notes: { type: String },
    reminder_enabled: { type: Boolean, default: false },
    reminder_days_before: { type: Number, default: 7 },
  },
  {
    timestamps: true,
  },
);

export default mongoose.model("ToolEntry", ToolEntrySchema);
