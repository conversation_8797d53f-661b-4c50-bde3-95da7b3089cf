import VoiceScript from "../../models/voice/VoiceScript.js";
import VoiceAssignment from "../../models/voice/VoiceAssignment.js";
import ModelUser from "../../models/model.js";
import Agency from "../../models/agency.js";
import { asyncHandler } from "../../utils/asyncHandler.js";
import { ApiError } from "../../utils/ApiError.js";
import { ApiResponse } from "../../utils/ApiResponse.js";
import mongoose from "mongoose";

/**
 * Create a new voice script
 * @route POST /api/v1/voice/scripts
 * @access Agency only
 */
export const createScript = asyncHandler(async (req, res) => {
  const {
    title,
    description,
    scriptText,
    scriptType,
    singleLinePrompt,
    tags,
    tone,
    category,
    priority,
    status,
    deadline,
    referenceAudioUrl,
    recordingGuidelines,
    expectedDuration,
    isTemplate,
  } = req.body;

  // Validate required fields based on script type
  const type = scriptType || "detailed";

  if (!title || !description || !category) {
    throw new ApiError(400, "Title, description, and category are required");
  }

  // Validate script type specific requirements
  if (type === "detailed" && !scriptText) {
    throw new ApiError(400, "Script text is required for detailed scripts");
  }

  if (type === "single_line" && !singleLinePrompt) {
    throw new ApiError(
      400,
      "Single line prompt is required for single-line scripts",
    );
  }

  // Validate agency access
  if (req.user.role !== "agency") {
    throw new ApiError(403, "Only agencies can create voice scripts");
  }

  // Validate tags against allowed enum values
  const allowedTags = [
    "NSFW",
    "Welcome",
    "Whisper",
    "Moaning",
    "Promo",
    "Sensual",
    "Funny",
    "Romantic",
    "Aggressive",
    "Soft",
    "Intro",
    "Outro",
    "Custom",
  ];

  const validatedTags = [];
  if (tags && Array.isArray(tags)) {
    for (const tag of tags) {
      if (allowedTags.includes(tag)) {
        validatedTags.push(tag);
      } else {
        console.warn(
          `Invalid tag "${tag}" ignored. Allowed tags:`,
          allowedTags,
        );
      }
    }
  }

  // Validate category
  const allowedCategories = [
    "moaning_pack",
    "welcome_message",
    "promo_content",
    "custom_script",
  ];
  if (!allowedCategories.includes(category)) {
    throw new ApiError(
      400,
      `Invalid category. Allowed values: ${allowedCategories.join(", ")}`,
    );
  }

  // Create the script
  const scriptData = {
    title,
    description,
    scriptType: type,
    agencyId: req.user._id,
    createdBy: req.user._id,
    tags: validatedTags,
    tone: tone || "neutral",
    category,
    priority: priority || "medium",
    status: status || "draft",
    deadline: deadline ? new Date(deadline) : null,
    referenceAudioUrl,
    recordingGuidelines,
    expectedDuration,
    isTemplate: isTemplate || false,
  };

  // Add script type specific fields
  if (type === "detailed") {
    scriptData.scriptText = scriptText;

    scriptData.recordingMode = "full"; // Detailed scripts record the full text
  } else if (type === "single_line") {
    scriptData.singleLinePrompt = singleLinePrompt;
    scriptData.recordingMode = "full"; // Single-line scripts record the full prompt
  }

  const script = await VoiceScript.create(scriptData);

  res
    .status(201)
    .json(new ApiResponse(201, script, "Voice script created successfully"));
});

/**
 * Get all scripts for an agency
 * @route GET /api/v1/voice/scripts
 * @access Agency only
 */
export const getAgencyScripts = asyncHandler(async (req, res) => {
  const { status, category, tags, page = 1, limit = 10, search } = req.query;

  if (req.user.role !== "agency") {
    throw new ApiError(403, "Only agencies can view their scripts");
  }

  // Build query
  const query = {
    agencyId: req.user._id,
    isDeleted: false,
  };

  if (status) query.status = status;
  if (category) query.category = category;
  if (tags) {
    const tagArray = Array.isArray(tags) ? tags : tags.split(",");
    query.tags = { $in: tagArray };
  }
  if (search) {
    query.$or = [
      { title: { $regex: search, $options: "i" } },
      { description: { $regex: search, $options: "i" } },
      { scriptText: { $regex: search, $options: "i" } },
    ];
  }

  // Calculate pagination
  const skip = (parseInt(page) - 1) * parseInt(limit);

  // Get scripts with pagination
  const scripts = await VoiceScript.find(query)
    .sort({ priority: -1, createdAt: -1 })
    .skip(skip)
    .limit(parseInt(limit))
    .lean();

  // Get total count for pagination
  const totalScripts = await VoiceScript.countDocuments(query);

  // Add completion statistics to each script
  const scriptsWithStats = await Promise.all(
    scripts.map(async (script) => {
      const assignments = await VoiceAssignment.aggregate([
        { $match: { scriptId: script._id, isDeleted: false } },
        {
          $group: {
            _id: "$status",
            count: { $sum: 1 },
          },
        },
      ]);

      const stats = assignments.reduce((acc, item) => {
        acc[item._id] = item.count;
        return acc;
      }, {});

      return {
        ...script,
        assignmentStats: {
          total: Object.values(stats).reduce((sum, count) => sum + count, 0),
          assigned: stats.assigned || 0,
          in_progress: stats.in_progress || 0,
          submitted: stats.submitted || 0,
          approved: stats.approved || 0,
          rejected: stats.rejected || 0,
        },
      };
    }),
  );

  res.status(200).json(
    new ApiResponse(
      200,
      {
        scripts: scriptsWithStats,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalScripts / parseInt(limit)),
          totalScripts,
          hasNext: skip + scripts.length < totalScripts,
          hasPrev: parseInt(page) > 1,
        },
      },
      "Scripts retrieved successfully",
    ),
  );
});

/**
 * Get a single script by ID
 * @route GET /api/v1/voice/scripts/:id
 * @access Agency and assigned models
 */
export const getScriptById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  if (!mongoose.Types.ObjectId.isValid(id)) {
    throw new ApiError(400, "Invalid script ID");
  }

  const script = await VoiceScript.findById(id)
    .populate("agencyId", "agencyName")
    .populate("createdBy", "agencyName");

  if (!script || script.isDeleted) {
    throw new ApiError(404, "Script not found");
  }

  // Check access permissions
  if (req.user.role === "agency") {
    if (script.agencyId._id.toString() !== req.user._id.toString()) {
      throw new ApiError(403, "Access denied");
    }
  } else if (req.user.role === "model") {
    // Check if model has assignment for this script
    const assignment = await VoiceAssignment.findOne({
      scriptId: id,
      modelId: req.user._id,
      isDeleted: false,
    });

    if (!assignment) {
      throw new ApiError(403, "Access denied - script not assigned to you");
    }
  } else {
    throw new ApiError(403, "Access denied");
  }

  // Get assignment statistics if agency
  let assignmentStats = null;
  if (req.user.role === "agency") {
    const assignments = await VoiceAssignment.aggregate([
      { $match: { scriptId: script._id, isDeleted: false } },
      {
        $group: {
          _id: "$status",
          count: { $sum: 1 },
        },
      },
    ]);

    assignmentStats = assignments.reduce((acc, item) => {
      acc[item._id] = item.count;
      return acc;
    }, {});
  }

  res.status(200).json(
    new ApiResponse(
      200,
      {
        script,
        assignmentStats,
      },
      "Script retrieved successfully",
    ),
  );
});

/**
 * Update a voice script
 * @route PUT /api/v1/voice/scripts/:id
 * @access Agency only
 */
export const updateScript = asyncHandler(async (req, res) => {
  const { id } = req.params;

  if (!mongoose.Types.ObjectId.isValid(id)) {
    throw new ApiError(400, "Invalid script ID");
  }

  if (req.user.role !== "agency") {
    throw new ApiError(403, "Only agencies can update scripts");
  }

  const script = await VoiceScript.findById(id);

  if (!script || script.isDeleted) {
    throw new ApiError(404, "Script not found");
  }

  if (script.agencyId.toString() !== req.user._id.toString()) {
    throw new ApiError(403, "Access denied");
  }

  // Check if script has active assignments
  const activeAssignments = await VoiceAssignment.countDocuments({
    scriptId: id,
    status: { $in: ["assigned", "in_progress", "submitted"] },
    isDeleted: false,
  });

  // Restrict certain updates if there are active assignments
  const restrictedFields = ["scriptText", "category"];
  if (activeAssignments > 0) {
    const hasRestrictedUpdates = restrictedFields.some((field) =>
      req.body.hasOwnProperty(field),
    );

    if (hasRestrictedUpdates) {
      throw new ApiError(
        400,
        "Cannot modify script text or category while there are active assignments",
      );
    }
  }

  // Update allowed fields
  const allowedUpdates = [
    "title",
    "description",
    "scriptText",
    "tags",
    "tone",
    "category",
    "priority",
    "deadline",
    "referenceAudioUrl",
    "recordingGuidelines",
    "expectedDuration",
    "status",
  ];

  const updates = {};
  allowedUpdates.forEach((field) => {
    if (req.body.hasOwnProperty(field)) {
      updates[field] = req.body[field];
    }
  });

  if (updates.deadline) {
    updates.deadline = new Date(updates.deadline);
  }

  // Handle script type specific updates
  if (updates.scriptText && script.scriptType === "detailed") {
    // For detailed scripts, just update the script text
    // No sentence parsing - keep it simple
  }

  const updatedScript = await VoiceScript.findByIdAndUpdate(id, updates, {
    new: true,
    runValidators: true,
  });

  res
    .status(200)
    .json(new ApiResponse(200, updatedScript, "Script updated successfully"));
});

/**
 * Delete a voice script (soft delete)
 * @route DELETE /api/v1/voice/scripts/:id
 * @access Agency only
 */
export const deleteScript = asyncHandler(async (req, res) => {
  const { id } = req.params;

  if (!mongoose.Types.ObjectId.isValid(id)) {
    throw new ApiError(400, "Invalid script ID");
  }

  if (req.user.role !== "agency") {
    throw new ApiError(403, "Only agencies can delete scripts");
  }

  const script = await VoiceScript.findById(id);

  if (!script || script.isDeleted) {
    throw new ApiError(404, "Script not found");
  }

  if (script.agencyId.toString() !== req.user._id.toString()) {
    throw new ApiError(403, "Access denied");
  }

  // Check for active assignments
  const activeAssignments = await VoiceAssignment.countDocuments({
    scriptId: id,
    status: { $in: ["assigned", "in_progress", "submitted"] },
    isDeleted: false,
  });

  if (activeAssignments > 0) {
    throw new ApiError(
      400,
      "Cannot delete script with active assignments. Cancel assignments first.",
    );
  }

  // Soft delete the script
  script.isDeleted = true;
  script.deletedAt = new Date();
  script.status = "archived";
  await script.save();

  // Also soft delete all related assignments
  await VoiceAssignment.updateMany(
    { scriptId: id },
    {
      isDeleted: true,
      deletedAt: new Date(),
      status: "cancelled",
    },
  );

  res
    .status(200)
    .json(new ApiResponse(200, null, "Script deleted successfully"));
});

/**
 * Assign script to models
 * @route POST /api/v1/voice/scripts/:id/assign
 * @access Agency only
 */
export const assignScriptByParams = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { modelIds, deadline, priority, allowRevisions, maxRevisions } =
    req.body;

  if (!mongoose.Types.ObjectId.isValid(id)) {
    throw new ApiError(400, "Invalid script ID");
  }

  if (req.user.role !== "agency") {
    throw new ApiError(403, "Only agencies can assign scripts");
  }

  if (!modelIds || !Array.isArray(modelIds) || modelIds.length === 0) {
    throw new ApiError(400, "Model IDs array is required");
  }

  const script = await VoiceScript.findById(id);

  if (!script || script.isDeleted) {
    throw new ApiError(404, "Script not found");
  }

  if (script.agencyId.toString() !== req.user._id.toString()) {
    throw new ApiError(403, "Access denied");
  }

  if (!script.canBeAssigned()) {
    throw new ApiError(400, "Script cannot be assigned in its current status");
  }

  // Validate all model IDs exist and are active models
  const models = await ModelUser.find({
    _id: { $in: modelIds },
    role: "model",
    isDeleted: { $ne: true },
  });

  if (models.length !== modelIds.length) {
    throw new ApiError(400, "Some models not found or are not active");
  }

  // Check for existing assignments
  const existingAssignments = await VoiceAssignment.find({
    scriptId: id,
    modelId: { $in: modelIds },
    isDeleted: false,
  });

  if (existingAssignments.length > 0) {
    const existingModelIds = existingAssignments.map((a) =>
      a.modelId.toString(),
    );
    throw new ApiError(
      400,
      `Script already assigned to some models: ${existingModelIds.join(", ")}`,
    );
  }

  // Create assignments
  const assignments = modelIds.map((modelId) => ({
    scriptId: id,
    modelId,
    agencyId: req.user._id,
    assignedBy: req.user._id,
    deadline: deadline ? new Date(deadline) : script.deadline,
    priority: priority || script.priority,
    allowRevisions: allowRevisions !== undefined ? allowRevisions : true,
    maxRevisions: maxRevisions || 3,
    status: "assigned",
  }));

  const createdAssignments = await VoiceAssignment.insertMany(assignments);

  // Update script statistics
  await script.updateCompletionStats();

  // Activate script if it was in draft
  if (script.status === "draft") {
    script.status = "active";
    await script.save();
  }

  res.status(201).json(
    new ApiResponse(
      201,
      {
        assignmentsCreated: createdAssignments.length,
        assignments: createdAssignments,
      },
      "Script assigned successfully",
    ),
  );
});

/**
 * Bulk assign script to models based on filters
 * @route POST /api/v1/voice/scripts/:id/bulk-assign
 * @access Agency only
 */
export const bulkAssignScript = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { filters, deadline, priority, allowRevisions, maxRevisions } =
    req.body;

  if (!mongoose.Types.ObjectId.isValid(id)) {
    throw new ApiError(400, "Invalid script ID");
  }

  if (req.user.role !== "agency") {
    throw new ApiError(403, "Only agencies can assign scripts");
  }

  const script = await VoiceScript.findById(id);

  if (!script || script.isDeleted) {
    throw new ApiError(404, "Script not found");
  }

  if (script.agencyId.toString() !== req.user._id.toString()) {
    throw new ApiError(403, "Access denied");
  }

  // Build model query based on filters
  const modelQuery = {
    agencyId: req.user._id,
  };

  if (filters) {
    if (filters.category && filters.category.length > 0) {
      modelQuery.category = { $in: filters.category };
    }
    if (filters.country) {
      modelQuery.country = filters.country;
    }
    if (filters.lastOnline) {
      // Filter by last online date
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - filters.lastOnline);
      modelQuery.lastOnline = { $gte: cutoffDate };
    }
  }

  // Get eligible models
  const models = await ModelUser.find(modelQuery).select(
    "_id fullName username",
  );

  if (models.length === 0) {
    throw new ApiError(400, "No models found matching the criteria");
  }

  const modelIds = models.map((model) => model._id);

  // Check for existing assignments
  const existingAssignments = await VoiceAssignment.find({
    scriptId: id,
    modelId: { $in: modelIds },
    isDeleted: false,
  });

  // Filter out models that already have assignments
  const existingModelIds = existingAssignments.map((a) => a.modelId.toString());
  const newModelIds = modelIds.filter(
    (id) => !existingModelIds.includes(id.toString()),
  );

  if (newModelIds.length === 0) {
    throw new ApiError(
      400,
      "All matching models already have assignments for this script",
    );
  }

  // Create assignments for new models
  const assignments = newModelIds.map((modelId) => ({
    scriptId: id,
    modelId,
    agencyId: req.user._id,
    assignedBy: req.user._id,
    deadline: deadline ? new Date(deadline) : script.deadline,
    priority: priority || script.priority,
    allowRevisions: allowRevisions !== undefined ? allowRevisions : true,
    maxRevisions: maxRevisions || 3,
    status: "assigned",
  }));

  const createdAssignments = await VoiceAssignment.insertMany(assignments);

  // Update script statistics
  await script.updateCompletionStats();

  // Activate script if it was in draft
  if (script.status === "draft") {
    script.status = "active";
    await script.save();
  }

  res.status(201).json(
    new ApiResponse(
      201,
      {
        totalModelsFound: models.length,
        existingAssignments: existingAssignments.length,
        newAssignmentsCreated: createdAssignments.length,
        assignments: createdAssignments,
      },
      "Bulk assignment completed successfully",
    ),
  );
});

/**
 * Bulk assign multiple scripts to models based on criteria
 * @route POST /api/v1/voice/scripts/bulk-assign
 * @access Agency only
 */
export const bulkAssignMultipleScripts = asyncHandler(async (req, res) => {
  const { scriptIds, assignmentCriteria, deadline, priority } = req.body;

  if (!scriptIds || !Array.isArray(scriptIds) || scriptIds.length === 0) {
    throw new ApiError(400, "Script IDs array is required");
  }

  if (req.user.role !== "agency") {
    throw new ApiError(403, "Only agencies can assign scripts");
  }

  // Validate all script IDs
  const invalidIds = scriptIds.filter(
    (id) => !mongoose.Types.ObjectId.isValid(id),
  );
  if (invalidIds.length > 0) {
    throw new ApiError(400, `Invalid script IDs: ${invalidIds.join(", ")}`);
  }

  // Find and validate scripts
  const scripts = await VoiceScript.find({
    _id: { $in: scriptIds },
    agencyId: req.user._id,
    isDeleted: false,
  });

  if (scripts.length !== scriptIds.length) {
    const foundIds = scripts.map((s) => s._id.toString());
    const missingIds = scriptIds.filter((id) => !foundIds.includes(id));
    throw new ApiError(
      400,
      `Scripts not found or access denied: ${missingIds.join(", ")}`,
    );
  }

  // Check if scripts can be assigned
  const unassignableScripts = scripts.filter(
    (script) => !script.canBeAssigned(),
  );
  if (unassignableScripts.length > 0) {
    const unassignableTitles = unassignableScripts.map((s) => s.title);
    throw new ApiError(
      400,
      `Some scripts cannot be assigned: ${unassignableTitles.join(", ")}`,
    );
  }

  // Build model query based on assignment criteria
  const modelQuery = {
    agencyId: req.user._id,
    isDeleted: false,
  };

  if (assignmentCriteria) {
    // Note: Removed modelTags and minRating filters as these fields don't exist in ModelUser schema
    if (
      assignmentCriteria.excludeModelIds &&
      assignmentCriteria.excludeModelIds.length > 0
    ) {
      modelQuery._id = { $nin: assignmentCriteria.excludeModelIds };
    }
  }

  // Get eligible models
  const models = await ModelUser.find(modelQuery).select(
    "_id fullName username",
  );

  if (models.length === 0) {
    throw new ApiError(400, "No models found matching the criteria");
  }

  // Apply maxAssignments limit if specified
  let selectedModels = models;
  if (
    assignmentCriteria?.maxAssignments &&
    assignmentCriteria.maxAssignments < models.length
  ) {
    selectedModels = models.slice(0, assignmentCriteria.maxAssignments);
  }

  const modelIds = selectedModels.map((model) => model._id);

  // Check for existing assignments for all script-model combinations
  const existingAssignments = await VoiceAssignment.find({
    scriptId: { $in: scriptIds },
    modelId: { $in: modelIds },
    isDeleted: false,
  });

  // Create assignments for each script-model combination that doesn't exist
  const assignmentsToCreate = [];
  const skippedAssignments = [];

  for (const script of scripts) {
    for (const modelId of modelIds) {
      const existingAssignment = existingAssignments.find(
        (a) =>
          a.scriptId.toString() === script._id.toString() &&
          a.modelId.toString() === modelId.toString(),
      );

      if (existingAssignment) {
        skippedAssignments.push({
          scriptId: script._id,
          scriptTitle: script.title,
          modelId: modelId,
          reason: "Already assigned",
        });
      } else {
        assignmentsToCreate.push({
          scriptId: script._id,
          modelId,
          agencyId: req.user._id,
          assignedBy: req.user._id,
          deadline: deadline ? new Date(deadline) : script.deadline,
          priority: priority || script.priority,
          allowRevisions: true,
          maxRevisions: 3,
          status: "assigned",
        });
      }
    }
  }

  if (assignmentsToCreate.length === 0) {
    throw new ApiError(
      400,
      "All script-model combinations already have assignments",
    );
  }

  // Create assignments
  const createdAssignments =
    await VoiceAssignment.insertMany(assignmentsToCreate);

  // Update script statistics and activate draft scripts
  for (const script of scripts) {
    await script.updateCompletionStats();

    if (script.status === "draft") {
      script.status = "active";
      await script.save();
    }
  }

  res.status(201).json(
    new ApiResponse(
      201,
      {
        totalScripts: scripts.length,
        totalModelsFound: models.length,
        selectedModels: selectedModels.length,
        assignmentsCreated: createdAssignments.length,
        skippedAssignments: skippedAssignments.length,
        assignments: createdAssignments,
        skipped: skippedAssignments,
      },
      "Bulk assignment completed successfully",
    ),
  );
});

/**
 * Get script templates
 * @route GET /api/v1/voice/scripts/templates
 * @access Agency only
 */
export const getScriptTemplates = asyncHandler(async (req, res) => {
  if (req.user.role !== "agency") {
    throw new ApiError(403, "Only agencies can view script templates");
  }

  const templates = await VoiceScript.find({
    agencyId: req.user._id,
    isTemplate: true,
    isDeleted: false,
  })
    .select("title description category tags tone expectedDuration")
    .sort({ createdAt: -1 });

  res
    .status(200)
    .json(
      new ApiResponse(
        200,
        templates,
        "Script templates retrieved successfully",
      ),
    );
});

/**
 * Create script from template
 * @route POST /api/v1/voice/scripts/from-template/:templateId
 * @access Agency only
 */
export const createFromTemplate = asyncHandler(async (req, res) => {
  const { templateId } = req.params;
  const { title, deadline, priority } = req.body;

  if (!mongoose.Types.ObjectId.isValid(templateId)) {
    throw new ApiError(400, "Invalid template ID");
  }

  if (req.user.role !== "agency") {
    throw new ApiError(403, "Only agencies can create scripts from templates");
  }

  const template = await VoiceScript.findById(templateId);

  if (!template || template.isDeleted || !template.isTemplate) {
    throw new ApiError(404, "Template not found");
  }

  if (template.agencyId.toString() !== req.user._id.toString()) {
    throw new ApiError(403, "Access denied");
  }

  // Create new script from template
  const scriptData = {
    title: title || `${template.title} - ${new Date().toLocaleDateString()}`,
    description: template.description,
    scriptText: template.scriptText,
    agencyId: req.user._id,
    createdBy: req.user._id,
    tags: template.tags,
    tone: template.tone,
    category: template.category,
    priority: priority || template.priority,
    deadline: deadline ? new Date(deadline) : null,
    referenceAudioUrl: template.referenceAudioUrl,
    recordingGuidelines: template.recordingGuidelines,
    expectedDuration: template.expectedDuration,
    isTemplate: false,
    status: "draft",
  };

  const script = await VoiceScript.create(scriptData);

  res
    .status(201)
    .json(
      new ApiResponse(201, script, "Script created from template successfully"),
    );
});

/**
 * Get script analytics
 * @route GET /api/v1/voice/scripts/analytics
 * @access Agency only
 */
export const getScriptAnalytics = asyncHandler(async (req, res) => {
  if (req.user.role !== "agency") {
    throw new ApiError(403, "Only agencies can view analytics");
  }

  const { startDate, endDate } = req.query;

  const matchStage = {
    agencyId: req.user._id,
    isDeleted: false,
  };

  if (startDate && endDate) {
    matchStage.createdAt = {
      $gte: new Date(startDate),
      $lte: new Date(endDate),
    };
  }

  // Get script statistics
  const scriptStats = await VoiceScript.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: "$status",
        count: { $sum: 1 },
        avgCompletionTime: { $avg: "$averageCompletionTime" },
      },
    },
  ]);

  // Get category breakdown
  const categoryStats = await VoiceScript.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: "$category",
        count: { $sum: 1 },
        avgRating: { $avg: "$averageRating" },
      },
    },
  ]);

  // Get assignment statistics
  const assignmentStats = await VoiceAssignment.aggregate([
    {
      $match: {
        agencyId: req.user._id,
        isDeleted: false,
      },
    },
    {
      $group: {
        _id: "$status",
        count: { $sum: 1 },
      },
    },
  ]);

  res.status(200).json(
    new ApiResponse(
      200,
      {
        scriptStats,
        categoryStats,
        assignmentStats,
      },
      "Analytics retrieved successfully",
    ),
  );
});

/**
 * Add sentence to script
 * @route POST /api/v1/voice/scripts/:id/sentences
 * @access Agency only
 */
export const addSentence = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { text, agencyComments } = req.body;

  if (!mongoose.Types.ObjectId.isValid(id)) {
    throw new ApiError(400, "Invalid script ID");
  }

  if (req.user.role !== "agency") {
    throw new ApiError(403, "Only agencies can add sentences");
  }

  if (!text || text.trim().length === 0) {
    throw new ApiError(400, "Sentence text is required");
  }

  const script = await VoiceScript.findById(id);

  if (!script || script.isDeleted) {
    throw new ApiError(404, "Script not found");
  }

  if (script.agencyId.toString() !== req.user._id.toString()) {
    throw new ApiError(403, "Access denied");
  }

  // Add sentence using the model method
  const newSentence = script.addSentence(text, agencyComments || "");

  // Set recording mode to sentence if not already set
  if (script.recordingMode !== "sentence") {
    script.recordingMode = "sentence";
  }

  await script.save();

  res
    .status(201)
    .json(new ApiResponse(201, newSentence, "Sentence added successfully"));
});

/**
 * Update sentence in script
 * @route PUT /api/v1/voice/scripts/:id/sentences/:sentenceId
 * @access Agency only
 */
export const updateSentence = asyncHandler(async (req, res) => {
  const { id, sentenceId } = req.params;
  const { text, agencyComments } = req.body;

  if (!mongoose.Types.ObjectId.isValid(id)) {
    throw new ApiError(400, "Invalid script ID");
  }

  if (req.user.role !== "agency") {
    throw new ApiError(403, "Only agencies can update sentences");
  }

  const script = await VoiceScript.findById(id);

  if (!script || script.isDeleted) {
    throw new ApiError(404, "Script not found");
  }

  if (script.agencyId.toString() !== req.user._id.toString()) {
    throw new ApiError(403, "Access denied");
  }

  const sentence = script.sentences.find((s) => s.id === sentenceId);
  if (!sentence) {
    throw new ApiError(404, "Sentence not found");
  }

  // Update sentence fields
  if (text !== undefined) sentence.text = text.trim();
  if (agencyComments !== undefined)
    sentence.agencyComments = agencyComments.trim();

  await script.save();

  res
    .status(200)
    .json(new ApiResponse(200, sentence, "Sentence updated successfully"));
});

/**
 * Remove sentence from script
 * @route DELETE /api/v1/voice/scripts/:id/sentences/:sentenceId
 * @access Agency only
 */
export const removeSentence = asyncHandler(async (req, res) => {
  const { id, sentenceId } = req.params;

  if (!mongoose.Types.ObjectId.isValid(id)) {
    throw new ApiError(400, "Invalid script ID");
  }

  if (req.user.role !== "agency") {
    throw new ApiError(403, "Only agencies can remove sentences");
  }

  const script = await VoiceScript.findById(id);

  if (!script || script.isDeleted) {
    throw new ApiError(404, "Script not found");
  }

  if (script.agencyId.toString() !== req.user._id.toString()) {
    throw new ApiError(403, "Access denied");
  }

  try {
    script.removeSentence(sentenceId);
    await script.save();

    res
      .status(200)
      .json(new ApiResponse(200, null, "Sentence removed successfully"));
  } catch (error) {
    throw new ApiError(404, error.message);
  }
});

/**
 * Reorder sentences in script
 * @route PUT /api/v1/voice/scripts/:id/sentences/reorder
 * @access Agency only
 */
export const reorderSentences = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { sentenceOrders } = req.body;

  if (!mongoose.Types.ObjectId.isValid(id)) {
    throw new ApiError(400, "Invalid script ID");
  }

  if (req.user.role !== "agency") {
    throw new ApiError(403, "Only agencies can reorder sentences");
  }

  if (!Array.isArray(sentenceOrders)) {
    throw new ApiError(400, "sentenceOrders must be an array");
  }

  const script = await VoiceScript.findById(id);

  if (!script || script.isDeleted) {
    throw new ApiError(404, "Script not found");
  }

  if (script.agencyId.toString() !== req.user._id.toString()) {
    throw new ApiError(403, "Access denied");
  }

  script.reorderSentences(sentenceOrders);
  await script.save();

  res
    .status(200)
    .json(
      new ApiResponse(
        200,
        script.sentences,
        "Sentences reordered successfully",
      ),
    );
});

/**
 * Break script text into sentences automatically
 * @route POST /api/v1/voice/scripts/:id/break-into-sentences
 * @access Agency only
 */
export const breakIntoSentences = asyncHandler(async (req, res) => {
  const { id } = req.params;

  if (!mongoose.Types.ObjectId.isValid(id)) {
    throw new ApiError(400, "Invalid script ID");
  }

  if (req.user.role !== "agency") {
    throw new ApiError(403, "Only agencies can break scripts into sentences");
  }

  const script = await VoiceScript.findById(id);

  if (!script || script.isDeleted) {
    throw new ApiError(404, "Script not found");
  }

  if (script.agencyId.toString() !== req.user._id.toString()) {
    throw new ApiError(403, "Access denied");
  }

  // Simple sentence splitting logic (can be enhanced with NLP libraries)
  const sentences = script.scriptText
    .split(/[.!?]+/)
    .map((s) => s.trim())
    .filter((s) => s.length > 0);

  // Clear existing sentences and add new ones
  script.sentences = [];
  sentences.forEach((sentenceText, index) => {
    script.addSentence(sentenceText);
  });

  script.recordingMode = "sentence";
  await script.save();

  res.status(200).json(
    new ApiResponse(
      200,
      {
        sentences: script.sentences,
        count: script.sentences.length,
      },
      "Script broken into sentences successfully",
    ),
  );
});
