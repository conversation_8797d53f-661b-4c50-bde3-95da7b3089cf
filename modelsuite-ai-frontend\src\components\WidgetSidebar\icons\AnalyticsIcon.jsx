import React from "react";

const AnalyticsIcon = ({ className }) => {
  return (
    <svg
      width="100%"
      height="100%"
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <defs>
        {/* Background gradient - dark purple to blue */}
        <radialGradient id="analyticsBgGrad" cx="50%" cy="30%" r="80%">
          <stop offset="0%" stopColor="#2D1B69" />
          <stop offset="50%" stopColor="#1A1B4A" />
          <stop offset="100%" stopColor="#0F1B3C" />
        </radialGradient>

        {/* Left bar gradient - magenta */}
        <linearGradient id="analyticsLeftBar" x1="0%" y1="100%" x2="0%" y2="0%">
          <stop offset="0%" stopColor="#E91E63" />
          <stop offset="100%" stopColor="#FF1744" />
        </linearGradient>

        {/* Middle bar gradient - purple */}
        <linearGradient
          id="analyticsMiddleBar"
          x1="0%"
          y1="100%"
          x2="0%"
          y2="0%"
        >
          <stop offset="0%" stopColor="#9C27B0" />
          <stop offset="100%" stopColor="#E91E63" />
        </linearGradient>

        {/* Right bar gradient - blue */}
        <linearGradient
          id="analyticsRightBar"
          x1="0%"
          y1="100%"
          x2="0%"
          y2="0%"
        >
          <stop offset="0%" stopColor="#2196F3" />
          <stop offset="100%" stopColor="#9C27B0" />
        </linearGradient>

        {/* Neon glow filter */}
        <filter id="analyticsGlow" x="-50%" y="-50%" width="200%" height="200%">
          <feGaussianBlur stdDeviation="12" result="blur" />
          <feMerge>
            <feMergeNode in="blur" />
            <feMergeNode in="SourceGraphic" />
          </feMerge>
        </filter>
      </defs>

      {/* Rounded square background */}
      <rect
        x="32"
        y="32"
        width="960"
        height="960"
        rx="180"
        ry="180"
        fill="url(#analyticsBgGrad)"
      />

      {/* Simple bar charts - clean and minimal */}
      <g filter="url(#analyticsGlow)">
        {/* Left bar (shortest) */}
        <rect
          x="280"
          y="520"
          width="80"
          height="180"
          fill="none"
          stroke="url(#analyticsLeftBar)"
          strokeWidth="24"
          strokeLinecap="round"
        />

        {/* Middle bar (medium) */}
        <rect
          x="420"
          y="420"
          width="80"
          height="280"
          fill="none"
          stroke="url(#analyticsMiddleBar)"
          strokeWidth="24"
          strokeLinecap="round"
        />

        {/* Right bar (tallest) */}
        <rect
          x="560"
          y="320"
          width="80"
          height="380"
          fill="none"
          stroke="url(#analyticsRightBar)"
          strokeWidth="24"
          strokeLinecap="round"
        />
      </g>
    </svg>
  );
};

export default AnalyticsIcon;
