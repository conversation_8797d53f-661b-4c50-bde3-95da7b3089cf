import Note from "../models/Note.js";
import { ApiError } from "../utils/ApiError.js";
import { ApiResponse } from "../utils/ApiResponse.js";
import { asyncHandler } from "../utils/asyncHandler.js";

// Helper function to determine user access level
const getUserAccessLevel = (user, modelId, agencyId) => {
  if (user.role === "agency" && user._id.toString() === agencyId.toString()) {
    return "agency_owner";
  }
  if (user.role === "model" && user._id.toString() === modelId.toString()) {
    return "model_owner";
  }
  if (
    user.role === "agency" &&
    user.modelUsers &&
    user.modelUsers.includes(modelId)
  ) {
    return "agency_manager";
  }
  return "no_access";
};

// Helper function to check if user can access note based on visibility
const canAccessNote = (note, user, accessLevel) => {
  if (accessLevel === "no_access") return false;

  // Agency owners and managers can see all notes
  if (accessLevel === "agency_owner" || accessLevel === "agency_manager") {
    return true;
  }

  // Model owners can see notes based on visibility
  if (accessLevel === "model_owner") {
    return (
      note.visibility === "shared_with_model" || note.visibility === "neutral"
    );
  }

  return false;
};

// Create a new note
const createNote = asyncHandler(async (req, res) => {
  const {
    title,
    content,
    contentType = "plain",
    modelId,
    agencyId,
    visibility = "internal",
    category = "general",
    customCategory,
    priority = "medium",
    tags = [],
    attachments = [],
    reminders = [],
    linkedActions = [],
  } = req.body;

  // Validate required fields
  if (!title || !content || !modelId || !agencyId) {
    throw new ApiError(
      400,
      "Title, content, modelId, and agencyId are required"
    );
  }

  // Check user access
  const accessLevel = getUserAccessLevel(req.user, modelId, agencyId);
  if (accessLevel === "no_access") {
    throw new ApiError(
      403,
      "You don't have permission to create notes for this model"
    );
  }

  // Only agency users can create notes
  if (req.user.role !== "agency") {
    throw new ApiError(403, "Only agency users can create notes");
  }

  const noteData = {
    title,
    content,
    contentType,
    modelId,
    agencyId,
    visibility,
    category,
    priority,
    tags,
    attachments,
    reminders,
    linkedActions,
    createdBy: {
      userId: req.user._id,
      userType: req.user.role === "agency" ? "Agency" : "ModelUser",
    },
  };

  if (customCategory) {
    noteData.customCategory = customCategory;
  }

  let note;
  try {
    note = await Note.create(noteData);
  } catch (e) {
    // Mongoose validation or other errors
    throw new ApiError(400, e.message);
  }
  await note.populate([
    { path: "modelId", select: "fullName username email" },
    { path: "agencyId", select: "agencyName agencyEmail" },
    {
      path: "createdBy.userId",
      select: "fullName agencyName email agencyEmail",
    },
  ]);

  // Emit socket event for realtime updates
  try {
    const io = req.app.get("io");
    if (io)
      io.emit("notes:created", {
        modelId: note.modelId._id || note.modelId,
        note,
      });
  } catch (e) {
    console.warn("Failed to emit socket event for note creation:", e.message);
  }

  res.status(201).json(new ApiResponse(201, note, "Note created successfully"));
});

// Get notes for a specific model
const getNotesByModel = asyncHandler(async (req, res) => {
  const { modelId } = req.params;
  const {
    page = 1,
    limit = 10,
    category,
    priority,
    tags,
    search,
    sortBy = "createdAt",
    sortOrder = "desc",
    status = "active",
    visibility,
  } = req.query;

  // Check user access
  const accessLevel = getUserAccessLevel(
    req.user,
    modelId,
    req.user.agencyId || req.user._id
  );
  if (accessLevel === "no_access") {
    throw new ApiError(
      403,
      "You don't have permission to view notes for this model"
    );
  }

  // Build filter query
  const filter = {
    modelId,
    status,
    deletedAt: { $exists: false },
  };

  // Add agency filter for proper access control
  if (req.user.role === "agency") {
    filter.agencyId =
      req.user.role === "employee" ? req.user.agencyId : req.user._id;
  } else if (req.user.role === "model") {
    filter.agencyId = req.user.agencyId;
    // Models can only see shared or neutral notes
    filter.visibility = { $in: ["shared_with_model", "neutral"] };
  }

  // Apply additional filters
  if (category) filter.category = category;
  if (priority) filter.priority = priority;
  if (tags) {
    const tagArray = Array.isArray(tags) ? tags : tags.split(",");
    if (tagArray.length > 0) {
      filter.tags = { $in: tagArray };
    }
  }
  if (visibility && req.user.role === "agency") {
    filter.visibility = visibility;
  }

  // Search functionality
  if (search) {
    filter.$or = [
      { title: { $regex: search, $options: "i" } },
      { content: { $regex: search, $options: "i" } },
      { tags: { $regex: search, $options: "i" } },
    ];
  }

  // Pagination
  const skip = (parseInt(page) - 1) * parseInt(limit);
  const sortOptions = {};
  sortOptions[sortBy] = sortOrder === "desc" ? -1 : 1;

  // Add secondary sort by isPinned for better UX
  if (sortBy !== "isPinned") {
    sortOptions.isPinned = -1;
  }

  const notes = await Note.find(filter)
    .populate([
      { path: "modelId", select: "fullName username email" },
      { path: "agencyId", select: "agencyName agencyEmail" },
      {
        path: "createdBy.userId",
        select: "fullName agencyName email agencyEmail",
      },
      {
        path: "lastEditedBy.userId",
        select: "fullName agencyName email agencyEmail",
      },
    ])
    .sort(sortOptions)
    .skip(skip)
    .limit(parseInt(limit));

  const totalNotes = await Note.countDocuments(filter);
  const totalPages = Math.ceil(totalNotes / parseInt(limit));

  res.status(200).json(
    new ApiResponse(
      200,
      {
        notes,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalNotes,
          hasNextPage: parseInt(page) < totalPages,
          hasPrevPage: parseInt(page) > 1,
        },
      },
      "Notes retrieved successfully"
    )
  );
});

// Get a specific note by ID
const getNoteById = asyncHandler(async (req, res) => {
  const { noteId } = req.params;

  const note = await Note.findById(noteId).populate([
    { path: "modelId", select: "fullName username email" },
    { path: "agencyId", select: "agencyName agencyEmail" },
    {
      path: "createdBy.userId",
      select: "fullName agencyName email agencyEmail",
    },
    {
      path: "lastEditedBy.userId",
      select: "fullName agencyName email agencyEmail",
    },
  ]);

  if (!note || note.status === "deleted" || note.deletedAt) {
    throw new ApiError(404, "Note not found");
  }

  // Check user access
  const accessLevel = getUserAccessLevel(
    req.user,
    note.modelId._id,
    note.agencyId._id
  );
  if (!canAccessNote(note, req.user, accessLevel)) {
    throw new ApiError(403, "You don't have permission to view this note");
  }

  res
    .status(200)
    .json(new ApiResponse(200, note, "Note retrieved successfully"));
});

// Update a note
const updateNote = asyncHandler(async (req, res) => {
  const { noteId } = req.params;
  // Only allow specific fields to be updated
  const allowedFields = ["title", "content", "priority", "tags"];
  const updateData = {};
  allowedFields.forEach((field) => {
    if (req.body[field] !== undefined) updateData[field] = req.body[field];
  });

  const note = await Note.findById(noteId);
  if (!note || note.status === "deleted" || note.deletedAt) {
    throw new ApiError(404, "Note not found");
  }

  // Check user access - only agency users can update notes
  const accessLevel = getUserAccessLevel(req.user, note.modelId, note.agencyId);
  if (
    req.user.role !== "agency" ||
    (accessLevel !== "agency_owner" && accessLevel !== "agency_manager")
  ) {
    throw new ApiError(403, "You don't have permission to update this note");
  }

  // Disallowed fields are not added since only whitelisted fields are picked

  // Set last edited info
  updateData.lastEditedBy = {
    userId: req.user._id,
    userType: "Agency",
  };
  updateData.lastEditedAt = new Date();

  const updatedNote = await Note.findByIdAndUpdate(noteId, updateData, {
    new: true,
    runValidators: true,
  }).populate([
    { path: "modelId", select: "fullName username email" },
    { path: "agencyId", select: "agencyName agencyEmail" },
    {
      path: "createdBy.userId",
      select: "fullName agencyName email agencyEmail",
    },
    {
      path: "lastEditedBy.userId",
      select: "fullName agencyName email agencyEmail",
    },
  ]);

  res
    .status(200)
    .json(new ApiResponse(200, updatedNote, "Note updated successfully"));

  // Emit update event
  try {
    const io = req.app.get("io");
    if (io)
      io.emit("notes:updated", {
        modelId: updatedNote.modelId._id || updatedNote.modelId,
        note: updatedNote,
      });
  } catch (e) {
    console.warn("Failed to emit socket event for note update:", e.message);
  }
});

// Soft delete a note
const deleteNote = asyncHandler(async (req, res) => {
  const { noteId } = req.params;

  const note = await Note.findById(noteId);
  if (!note || note.status === "deleted" || note.deletedAt) {
    throw new ApiError(404, "Note not found");
  }

  // Check user access - only agency users can delete notes
  const accessLevel = getUserAccessLevel(req.user, note.modelId, note.agencyId);
  if (
    req.user.role !== "agency" ||
    (accessLevel !== "agency_owner" && accessLevel !== "agency_manager")
  ) {
    throw new ApiError(403, "You don't have permission to delete this note");
  }

  const deletedBy = {
    userId: req.user._id,
    userType: "Agency",
  };

  await note.softDelete(deletedBy);

  // Emit delete event
  try {
    const io = req.app.get("io");
    if (io)
      io.emit("notes:deleted", {
        modelId: note.modelId._id || note.modelId,
        noteId,
      });
  } catch (e) {
    console.warn("Failed to emit socket event for note delete:", e.message);
  }

  res.status(200).json(new ApiResponse(200, null, "Note deleted successfully"));
});

// Toggle pin status of a note
const togglePinNote = asyncHandler(async (req, res) => {
  const { noteId } = req.params;

  const note = await Note.findById(noteId);
  if (!note || note.status === "deleted" || note.deletedAt) {
    throw new ApiError(404, "Note not found");
  }

  // Check user access - only agency users can pin/unpin notes
  const accessLevel = getUserAccessLevel(req.user, note.modelId, note.agencyId);
  if (
    req.user.role !== "agency" ||
    (accessLevel !== "agency_owner" && accessLevel !== "agency_manager")
  ) {
    throw new ApiError(403, "You don't have permission to pin/unpin this note");
  }

  const pinnedBy = {
    userId: req.user._id,
    userType: "Agency",
  };

  await note.togglePin(pinnedBy);
  await note.populate([
    { path: "modelId", select: "fullName username email" },
    { path: "agencyId", select: "agencyName agencyEmail" },
    {
      path: "createdBy.userId",
      select: "fullName agencyName email agencyEmail",
    },
  ]);

  res
    .status(200)
    .json(
      new ApiResponse(
        200,
        note,
        `Note ${note.isPinned ? "pinned" : "unpinned"} successfully`
      )
    );

  // Emit pin/unpin event
  try {
    const io = req.app.get("io");
    if (io)
      io.emit("notes:pinned", {
        modelId: note.modelId._id || note.modelId,
        note,
      });
  } catch (e) {
    console.warn("Failed to emit socket event for note pin/unpin:", e.message);
  }
});

// Get note statistics for a model
const getNoteStats = asyncHandler(async (req, res) => {
  const { modelId } = req.params;

  // Check user access
  const accessLevel = getUserAccessLevel(
    req.user,
    modelId,
    req.user.agencyId || req.user._id
  );
  if (accessLevel === "no_access") {
    throw new ApiError(
      403,
      "You don't have permission to view note statistics for this model"
    );
  }

  const baseFilter = {
    modelId,
    status: "active",
    deletedAt: { $exists: false },
  };

  // Add agency filter for proper access control
  if (req.user.role === "agency") {
    baseFilter.agencyId =
      req.user.role === "employee" ? req.user.agencyId : req.user._id;
  } else if (req.user.role === "model") {
    baseFilter.agencyId = req.user.agencyId;
    baseFilter.visibility = { $in: ["shared_with_model", "neutral"] };
  }

  const [totalNotes, pinnedNotes, categoryStats, priorityStats] =
    await Promise.all([
      Note.countDocuments(baseFilter),
      Note.countDocuments({ ...baseFilter, isPinned: true }),
      Note.aggregate([
        { $match: baseFilter },
        { $group: { _id: "$category", count: { $sum: 1 } } },
        { $sort: { count: -1 } },
      ]),
      Note.aggregate([
        { $match: baseFilter },
        { $group: { _id: "$priority", count: { $sum: 1 } } },
        { $sort: { count: -1 } },
      ]),
    ]);

  const stats = {
    totalNotes,
    pinnedNotes,
    categoryBreakdown: categoryStats.reduce((acc, item) => {
      acc[item._id] = item.count;
      return acc;
    }, {}),
    priorityBreakdown: priorityStats.reduce((acc, item) => {
      acc[item._id] = item.count;
      return acc;
    }, {}),
  };

  res
    .status(200)
    .json(
      new ApiResponse(200, stats, "Note statistics retrieved successfully")
    );
});

// Get notes across all models for an agency (paginated, filtered)
const getNotesByAgency = asyncHandler(async (req, res) => {
  const { agencyId } = req.params;
  const {
    page = 1,
    limit = 10,
    tags,
    search,
    isPinned,
    sortBy = "createdAt",
    sortOrder = "desc",
  } = req.query;

  // Only agency users or employees with agency scope should call this
  if (req.user.role !== "agency" && req.user.role !== "employee") {
    throw new ApiError(403, "You don't have permission to view agency notes");
  }

  // If employee, ensure agencyId matches
  if (
    req.user.role === "employee" &&
    String(req.user.agencyId) !== String(agencyId)
  ) {
    throw new ApiError(403, "Employee does not belong to this agency");
  }

  // Build base filter
  const filter = {
    agencyId,
    status: "active",
    deletedAt: { $exists: false },
  };

  if (typeof isPinned !== "undefined") {
    filter.isPinned = isPinned === "true" || isPinned === true;
  }

  if (tags) {
    const tagArray = Array.isArray(tags) ? tags : tags.split(",");
    if (tagArray.length) filter.tags = { $in: tagArray };
  }

  if (search) {
    filter.$or = [
      { title: { $regex: search, $options: "i" } },
      { content: { $regex: search, $options: "i" } },
      { tags: { $regex: search, $options: "i" } },
    ];
  }

  const skip = (parseInt(page) - 1) * parseInt(limit);
  const sortOptions = {};
  sortOptions[sortBy] = sortOrder === "desc" ? -1 : 1;
  if (sortBy !== "isPinned") sortOptions.isPinned = -1;

  // Query notes directly by agencyId (assumes notes store agencyId)
  const notes = await Note.find(filter)
    .populate([
      { path: "modelId", select: "fullName username email" },
      { path: "agencyId", select: "agencyName agencyEmail" },
      {
        path: "createdBy.userId",
        select: "fullName agencyName email agencyEmail",
      },
    ])
    .sort(sortOptions)
    .skip(skip)
    .limit(parseInt(limit));

  const totalNotes = await Note.countDocuments(filter);
  const totalPages = Math.ceil(totalNotes / parseInt(limit));

  res.status(200).json(
    new ApiResponse(
      200,
      {
        notes,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalNotes,
          hasNextPage: parseInt(page) < totalPages,
          hasPrevPage: parseInt(page) > 1,
        },
      },
      "Agency notes retrieved successfully"
    )
  );
});

// Bulk delete notes by IDs (soft delete)
const bulkDeleteNotes = asyncHandler(async (req, res) => {
  const { noteIds } = req.body;
  if (!Array.isArray(noteIds) || noteIds.length === 0) {
    throw new ApiError(400, "noteIds array is required");
  }
  const deletedBy = { userId: req.user._id, userType: "Agency" };
  // Permission checks
  for (const id of noteIds) {
    const note = await Note.findById(id);
    if (!note) continue;
    const accessLevel = getUserAccessLevel(
      req.user,
      note.modelId,
      note.agencyId
    );
    if (
      req.user.role !== "agency" ||
      (accessLevel !== "agency_owner" && accessLevel !== "agency_manager")
    ) {
      throw new ApiError(403, `No permission to delete note ${id}`);
    }
  }
  await Promise.all(
    noteIds.map(async (id) => {
      const note = await Note.findById(id);
      if (note) {
        note.status = "deleted";
        note.deletedAt = new Date();
        note.deletedBy = deletedBy;
        await note.save();
      }
    })
  );
  res
    .status(200)
    .json(new ApiResponse(200, null, `${noteIds.length} notes deleted`));

  // Emit bulk delete event
  try {
    const io = req.app.get("io");
    if (io) io.emit("notes:bulk-deleted", { modelId: null, noteIds });
  } catch (e) {
    console.warn(
      "Failed to emit socket event for bulk note delete:",
      e.message
    );
  }
});

// Bulk pin/unpin notes
const bulkPinNotes = asyncHandler(async (req, res) => {
  const { noteIds, isPinned = true } = req.body;
  if (!Array.isArray(noteIds) || noteIds.length === 0) {
    throw new ApiError(400, "noteIds array is required");
  }
  const pinnedBy = { userId: req.user._id, userType: "Agency" };
  // Permission checks
  for (const id of noteIds) {
    const note = await Note.findById(id);
    if (!note) continue;
    const accessLevel = getUserAccessLevel(
      req.user,
      note.modelId,
      note.agencyId
    );
    if (
      req.user.role !== "agency" ||
      (accessLevel !== "agency_owner" && accessLevel !== "agency_manager")
    ) {
      throw new ApiError(403, `No permission to pin/unpin note ${id}`);
    }
  }
  await Promise.all(
    noteIds.map(async (id) => {
      const note = await Note.findById(id);
      if (note) {
        note.isPinned = isPinned;
        note.pinnedBy = isPinned ? pinnedBy : undefined;
        note.pinnedAt = isPinned ? new Date() : undefined;
        await note.save();
      }
    })
  );
  res
    .status(200)
    .json(
      new ApiResponse(
        200,
        null,
        `${noteIds.length} notes ${isPinned ? "pinned" : "unpinned"}`
      )
    );

  // Emit bulk pin event
  try {
    const io = req.app.get("io");
    if (io) io.emit("notes:bulk-pinned", { modelId: null, noteIds, isPinned });
  } catch (e) {
    console.warn("Failed to emit socket event for bulk pin:", e.message);
  }
});

// Bulk archive/unarchive notes
const bulkArchiveNotes = asyncHandler(async (req, res) => {
  const { noteIds, isArchived = true } = req.body;
  if (!Array.isArray(noteIds) || noteIds.length === 0) {
    throw new ApiError(400, "noteIds array is required");
  }
  // Permission checks
  for (const id of noteIds) {
    const note = await Note.findById(id);
    if (!note) continue;
    const accessLevel = getUserAccessLevel(
      req.user,
      note.modelId,
      note.agencyId
    );
    if (
      req.user.role !== "agency" ||
      (accessLevel !== "agency_owner" && accessLevel !== "agency_manager")
    ) {
      throw new ApiError(403, `No permission to archive/unarchive note ${id}`);
    }
  }
  await Promise.all(
    noteIds.map(async (id) => {
      const note = await Note.findById(id);
      if (note) {
        note.status = isArchived ? "archived" : "active";
        note.lastEditedBy = { userId: req.user._id, userType: "Agency" };
        note.lastEditedAt = new Date();
        await note.save();
      }
    })
  );
  res
    .status(200)
    .json(
      new ApiResponse(
        200,
        null,
        `${noteIds.length} notes ${isArchived ? "archived" : "unarchived"}`
      )
    );

  // Emit bulk archive event
  try {
    const io = req.app.get("io");
    if (io)
      io.emit("notes:bulk-archived", { modelId: null, noteIds, isArchived });
  } catch (e) {
    console.warn("Failed to emit socket event for bulk archive:", e.message);
  }
});

export {
  createNote,
  getNotesByModel,
  getNotesByAgency,
  getNoteById,
  updateNote,
  deleteNote,
  togglePinNote,
  getNoteStats,
  bulkDeleteNotes,
  bulkPinNotes,
  bulkArchiveNotes,
};
