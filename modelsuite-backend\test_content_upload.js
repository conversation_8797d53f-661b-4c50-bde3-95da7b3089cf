import fetch from "node-fetch";
import FormData from "form-data";
import fs from "fs";
import path from "path";

// Your token from the previous login
const TOKEN =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2ODc3ZDFkMWQ0NjYwMDMxZTkwN2E4ZWIiLCJyb2xlIjoibW9kZWwiLCJpYXQiOjE3NTQ1NjYyNjEsImV4cCI6MTc1NDY1MjY2MX0.zyTswpqbWqeOjS9Rw9quw11sMuh04-EES230EmdYOno";

async function testContentUpload() {
  try {
    console.log("=== Testing Content Upload with Token ===");
    console.log("Token:", TOKEN.substring(0, 50) + "...");

    // First, let's test token validation by getting categories
    console.log("\n1. Testing token validation with categories endpoint...");
    const categoriesResponse = await fetch(
      "http://localhost:5000/api/v1/content/categories",
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${TOKEN}`,
          "Content-Type": "application/json",
        },
      },
    );

    console.log("Categories Status:", categoriesResponse.status);
    const categoriesData = await categoriesResponse.json();
    console.log(
      "Categories Response:",
      JSON.stringify(categoriesData, null, 2),
    );

    if (categoriesResponse.status !== 200) {
      console.log("❌ Token validation failed at categories endpoint");
      return;
    }

    // Create a category first (if none exist)
    let categoryId;
    if (categoriesData.data.categories.length === 0) {
      console.log("\n1.5. Creating a test category...");
      const createCategoryResponse = await fetch(
        "http://localhost:5000/api/v1/content/categories",
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${TOKEN}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            label: "Test Category",
            platform: "instagram",
            reminderFrequency: 1, // 1 day
            reminderType: "soft",
            instructionTooltip: "Upload your content here",
            hardRules: {
              maxFileSize: 10485760, // 10MB
              allowedFormats: ["jpg", "jpeg", "png", "gif"],
              requiresApproval: false,
              requiresWatermark: false,
              requiresConsent: true,
            },
          }),
        },
      );

      console.log("Create Category Status:", createCategoryResponse.status);
      const createCategoryData = await createCategoryResponse.json();
      console.log(
        "Create Category Response:",
        JSON.stringify(createCategoryData, null, 2),
      );

      if (
        createCategoryResponse.status === 201 &&
        createCategoryData.data?._id
      ) {
        categoryId = createCategoryData.data._id;
        console.log("✅ Category created successfully:", categoryId);
      } else {
        console.log("❌ Failed to create category, using dummy ID");
        categoryId = "507f1f77bcf86cd799439011"; // dummy ObjectId
      }
    } else {
      categoryId = categoriesData.data.categories[0]._id;
    }

    console.log("Using category ID:", categoryId);

    // Create a simple test JPEG file (1x1 pixel)
    const testFilePath = path.join(process.cwd(), "test-image.jpg");
    // Create a minimal JPEG file (base64 decoded)
    const jpegData = Buffer.from(
      "/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8A",
      "base64",
    );
    fs.writeFileSync(testFilePath, jpegData);

    console.log("\n2. Testing content upload endpoint...");

    // Create form data
    const form = new FormData();
    form.append("file", fs.createReadStream(testFilePath), {
      filename: "test-image.jpg",
      contentType: "image/jpeg",
    });

    // Add the actual category ID
    form.append("categoryId", categoryId);
    form.append(
      "metadata",
      JSON.stringify({
        title: "Test Upload",
        description: "Testing content upload",
        tags: ["test"],
      }),
    );
    form.append("hasConsent", "true");
    form.append("hasWatermark", "false");

    const uploadResponse = await fetch(
      "http://localhost:5000/api/v1/content/upload",
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${TOKEN}`,
          ...form.getHeaders(),
        },
        body: form,
      },
    );

    console.log("Upload Status:", uploadResponse.status);
    const uploadData = await uploadResponse.json();
    console.log("Upload Response:", JSON.stringify(uploadData, null, 2));

    // Clean up test file
    fs.unlinkSync(testFilePath);
  } catch (error) {
    console.error("Error:", error.message);
    console.error("Stack:", error.stack);
  }
}

testContentUpload();
