import React, { useState, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { addWidget } from "@/redux/features/dashboard/dashboardSlice";
import {
  endDrag,
  selectIsDragging,
} from "@/redux/features/widgetSidebar/widgetSidebarSlice";

const DropZone = ({ children, className = "", onDrop }) => {
  const dispatch = useDispatch();
  const isDragging = useSelector(selectIsDragging);
  const [isDragOver, setIsDragOver] = useState(false);
  const [dropPosition, setDropPosition] = useState({ x: 0, y: 0 });

  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "copy";

    // Update drop position for visual feedback
    const rect = e.currentTarget.getBoundingClientRect();
    setDropPosition({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    });
  }, []);

  const handleDragEnter = useCallback((e) => {
    e.preventDefault();
    if (e.dataTransfer.types.includes("application/json")) {
      setIsDragOver(true);
    }
  }, []);

  const handleDragLeave = useCallback((e) => {
    e.preventDefault();
    // Only set dragOver to false if we're leaving the drop zone completely
    if (!e.currentTarget.contains(e.relatedTarget)) {
      setIsDragOver(false);
    }
  }, []);

  const handleDrop = useCallback(
    (e) => {
      e.preventDefault();
      setIsDragOver(false);

      try {
        // Get the widget data from the drag event
        const widgetDataStr = e.dataTransfer.getData("application/json");
        if (!widgetDataStr) return;

        const widget = JSON.parse(widgetDataStr);

        // Calculate position relative to the drop zone
        const rect = e.currentTarget.getBoundingClientRect();
        const position = {
          x: e.clientX - rect.left,
          y: e.clientY - rect.top,
        };

        // Ensure the widget doesn't go outside the bounds
        const adjustedPosition = {
          x: Math.max(20, Math.min(position.x - 150, rect.width - 320)), // Account for widget width
          y: Math.max(20, Math.min(position.y - 100, rect.height - 220)), // Account for widget height
        };

        // Dispatch the add widget action
        dispatch(
          addWidget({
            widgetType: widget.type,
            position: adjustedPosition,
          })
        );

        // Call custom onDrop handler if provided
        if (onDrop) {
          onDrop(widget, adjustedPosition);
        }

        // End the drag state
        dispatch(endDrag());
      } catch (error) {
        console.error("Error handling widget drop:", error);
        dispatch(endDrag());
      }
    },
    [dispatch, onDrop]
  );

  return (
    <div
      className={`
        relative
        ${className}
        ${isDragging ? "transition-all duration-200" : ""}
        ${isDragOver && isDragging ? "bg-blue-50/30" : ""}
      `}
      onDragOver={handleDragOver}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      data-drop-zone
    >
      {/* Drop indicator */}
      {isDragging && isDragOver && (
        <>
          {/* Drop zone overlay */}
          <div className="absolute inset-0 border-2 border-dashed border-blue-400 rounded-lg bg-blue-100/10 z-10">
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium shadow-lg">
                Drop widget here
              </div>
            </div>
          </div>

          {/* Drop position indicator */}
          <div
            className="absolute w-4 h-4 bg-blue-500 rounded-full border-2 border-white shadow-lg z-20 transform -translate-x-1/2 -translate-y-1/2"
            style={{
              left: dropPosition.x,
              top: dropPosition.y,
            }}
          />
        </>
      )}

      {/* Regular content */}
      {children}
    </div>
  );
};

export default DropZone;
