import React from "react";

const CustomCalendarIcon = ({ size = 24, className = "", ...props }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    className={className}
    {...props}
  >
    <defs>
      {/* Main calendar gradient - darker purple to blue like in image */}
      <linearGradient
        id="calendarMainGradient"
        x1="0%"
        y1="0%"
        x2="100%"
        y2="100%"
      >
        <stop offset="0%" stopColor="#4A1D8C" />
        <stop offset="30%" stopColor="#3B1A6B" />
        <stop offset="70%" stopColor="#1E2B7A" />
        <stop offset="100%" stopColor="#0F4C8C" />
      </linearGradient>

      {/* Binding holes gradient - bright magenta to blue */}
      <linearGradient id="bindingGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FF00FF" />
        <stop offset="50%" stopColor="#9D4EDD" />
        <stop offset="100%" stopColor="#0EA5E9" />
      </linearGradient>

      {/* Inner content gradient */}
      <linearGradient id="contentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#8B5CF6" />
        <stop offset="100%" stopColor="#3B82F6" />
      </linearGradient>
    </defs>

    {/* Outer rounded container */}
    <rect
      x="1"
      y="2"
      width="22"
      height="20"
      rx="6"
      ry="6"
      fill="url(#calendarMainGradient)"
      stroke="url(#bindingGradient)"
      strokeWidth="0.3"
    />

    {/* Left binding hole - thicker and more prominent */}
    <rect
      x="6.5"
      y="0.5"
      width="3"
      height="6"
      rx="1.5"
      ry="1.5"
      fill="url(#bindingGradient)"
    />

    {/* Right binding hole - thicker and more prominent */}
    <rect
      x="14.5"
      y="0.5"
      width="3"
      height="6"
      rx="1.5"
      ry="1.5"
      fill="url(#bindingGradient)"
    />

    {/* Calendar header area */}
    <rect
      x="3"
      y="4.5"
      width="18"
      height="4"
      fill="url(#contentGradient)"
      opacity="0.3"
    />

    {/* Top horizontal separator line */}
    <line
      x1="3"
      y1="8.5"
      x2="21"
      y2="8.5"
      stroke="url(#contentGradient)"
      strokeWidth="1"
      opacity="0.8"
    />

    {/* Calendar grid - first row */}
    <rect
      x="5"
      y="10.5"
      width="3"
      height="2.5"
      rx="0.5"
      fill="url(#contentGradient)"
      opacity="0.9"
    />
    <rect
      x="10.5"
      y="10.5"
      width="3"
      height="2.5"
      rx="0.5"
      fill="url(#contentGradient)"
      opacity="0.9"
    />
    <rect
      x="16"
      y="10.5"
      width="3"
      height="2.5"
      rx="0.5"
      fill="url(#contentGradient)"
      opacity="0.9"
    />

    {/* Calendar grid - second row */}
    <rect
      x="5"
      y="14"
      width="3"
      height="2.5"
      rx="0.5"
      fill="url(#contentGradient)"
      opacity="0.9"
    />
    <rect
      x="10.5"
      y="14"
      width="3"
      height="2.5"
      rx="0.5"
      fill="url(#contentGradient)"
      opacity="0.9"
    />
    <rect
      x="16"
      y="14"
      width="3"
      height="2.5"
      rx="0.5"
      fill="url(#contentGradient)"
      opacity="0.9"
    />

    {/* Calendar grid - third row (partial) */}
    <rect
      x="5"
      y="17.5"
      width="3"
      height="2.5"
      rx="0.5"
      fill="url(#contentGradient)"
      opacity="0.9"
    />
    <rect
      x="10.5"
      y="17.5"
      width="3"
      height="2.5"
      rx="0.5"
      fill="url(#contentGradient)"
      opacity="0.9"
    />
  </svg>
);

export default CustomCalendarIcon;
