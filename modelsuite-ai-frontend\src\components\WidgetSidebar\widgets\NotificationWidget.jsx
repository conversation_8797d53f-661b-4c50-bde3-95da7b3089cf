import React from "react";

const NotificationWidget = () => (
  <div className="space-y-2 text-white p-4 h-full">
    <div className="text-lg font-semibold text-blue-400 mb-3">
      Notifications
    </div>
    <div className="p-3 bg-blue-900/50 border border-blue-700 rounded-lg text-sm">
      <div className="font-medium text-blue-300">New message from <PERSON></div>
      <div className="text-xs text-gray-400 mt-1">2 minutes ago</div>
    </div>
    <div className="p-3 bg-yellow-900/50 border border-yellow-700 rounded-lg text-sm">
      <div className="font-medium text-yellow-300">Booking reminder in 1h</div>
      <div className="text-xs text-gray-400 mt-1">5 minutes ago</div>
    </div>
    <div className="p-3 bg-green-900/50 border border-green-700 rounded-lg text-sm">
      <div className="font-medium text-green-300">Payment received</div>
      <div className="text-xs text-gray-400 mt-1">15 minutes ago</div>
    </div>
  </div>
);

export default NotificationWidget;
