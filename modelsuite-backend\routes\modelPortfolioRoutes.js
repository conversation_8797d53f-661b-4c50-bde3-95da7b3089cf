import express from "express";
import { verifyToken } from "../middlewares/authMiddleware.js";
import {
  createCompCard,
  getPortfolioBySlug,
  getPublicPortfolioBySlug,
  getAllModelsForAnAgency,
  createEditorialCompCard,
  verifyPassword,
  generateResetPasswordOTP,
  verifyOTP,
  resetPassword,
} from "../controllers/modelPortfolioController.js";
import upload, { uploadPortfolio } from "../middlewares/multer.js";

const router = express.Router();

router.post("/new", verifyToken, uploadPortfolio.any(), createCompCard);

router.post(
  "/editorial/new",
  verifyToken,
  uploadPortfolio.any(),
  createEditorialCompCard,
);

router.get("/allModels", verifyToken, getAllModelsForAnAgency);

router.get("/:slug", verifyToken, getPortfolioBySlug);

router.get("/public/:slug", getPublicPortfolioBySlug);

router.post("/protected/verify-password", verifyToken, verifyPassword);

router.post("/:slug/reset-password-otp", verifyToken, generateResetPasswordOTP);

router.post("/verify-otp/:slug", verifyToken, verifyOTP);

router.post("/reset-password/:slug", verifyToken, resetPassword);

export default router;
