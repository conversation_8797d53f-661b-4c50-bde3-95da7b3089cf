import gptService from "../captionServices/gptService.js";
import geminiService from "../captionServices/geminiService.js";
import PersonaProfile from "../../models/persona/PersonaProfile.js";
import ModelUser from "../../models/model.js";
import Agency from "../../models/agency.js";
import ContentTask from "../../models/planner/ContentTask.js";
import ContentPlan from "../../models/planner/ContentPlan.js";
import { ApiError } from "../../utils/ApiError.js";

/**
 * AI Content Service
 * Provides AI-powered content planning, strategy generation, and optimization
 */
class AIContentService {
  constructor() {
    this.defaultProvider = "gpt"; // Default to GPT, can be switched to gemini
  }

  /**
   * Generate AI content strategy for a model
   */
  async generateContentStrategy(modelId, agencyId, preferences = {}) {
    try {
      // Get model and agency context
      const model = await ModelUser.findById(modelId);
      const agency = await Agency.findById(agencyId);

      if (!model || !agency) {
        throw new ApiError(404, "Model or Agency not found");
      }

      // Get persona context if available
      const persona = await PersonaProfile.findOne({
        modelId,
        isActive: true,
      }).sort({ createdAt: -1 });

      // Get historical performance data
      const historicalData = await this.getHistoricalPerformance(modelId);

      // Build strategy prompt
      const strategyPrompt = this.buildStrategyPrompt(
        model,
        agency,
        persona,
        historicalData,
        preferences,
      );

      // Generate strategy using AI
      const provider = preferences.provider || this.defaultProvider;
      const strategy = await this.generateAIStrategy(strategyPrompt, provider);

      return {
        strategy,
        modelId,
        agencyId,
        persona: persona
          ? {
              id: persona._id,
              summary: persona.personaSummary,
              tone: persona.tone,
            }
          : null,
        generatedAt: new Date(),
        provider,
      };
    } catch (error) {
      throw new ApiError(
        500,
        `Failed to generate content strategy: ${error.message}`,
      );
    }
  }

  /**
   * Generate AI content suggestions for specific platforms
   */
  async generateContentSuggestions(planId, platform, count = 5) {
    try {
      const plan = await ContentPlan.findById(planId)
        .populate("modelId")
        .populate("agencyId");

      if (!plan) {
        throw new ApiError(404, "Content plan not found");
      }

      // Get persona context
      const persona = await PersonaProfile.findOne({
        modelId: plan.modelId._id,
        isActive: true,
      });

      // Get existing tasks for context
      const existingTasks = await ContentTask.find({ planId });

      // Build content suggestion prompt
      const suggestionPrompt = this.buildContentSuggestionPrompt(
        plan,
        persona,
        existingTasks,
        platform,
        count,
      );

      // Generate suggestions using AI
      const suggestions = await this.generateAISuggestions(suggestionPrompt);

      return {
        planId,
        platform,
        suggestions,
        generatedAt: new Date(),
        count: suggestions.length,
      };
    } catch (error) {
      throw new ApiError(
        500,
        `Failed to generate content suggestions: ${error.message}`,
      );
    }
  }

  /**
   * Analyze optimal posting times using AI
   */
  async analyzeOptimalTiming(modelId, platform, weekData) {
    try {
      const model = await ModelUser.findById(modelId);
      if (!model) {
        throw new ApiError(404, "Model not found");
      }

      // Get audience persona data
      const persona = await PersonaProfile.findOne({
        modelId,
        isActive: true,
      });

      // Get historical posting performance
      const performanceData = await this.getHistoricalPerformance(
        modelId,
        platform,
      );

      // Build timing analysis prompt
      const timingPrompt = this.buildTimingAnalysisPrompt(
        model,
        persona,
        performanceData,
        platform,
        weekData,
      );

      // Generate timing analysis using AI
      const analysis = await this.generateTimingAnalysis(timingPrompt);

      return {
        modelId,
        platform,
        analysis,
        generatedAt: new Date(),
        weekData,
      };
    } catch (error) {
      throw new ApiError(
        500,
        `Failed to analyze optimal timing: ${error.message}`,
      );
    }
  }

  /**
   * Generate theme suggestions for content plans
   */
  async generateThemeSuggestions(modelId, agencyId, preferences = {}) {
    try {
      const model = await ModelUser.findById(modelId);
      const agency = await Agency.findById(agencyId);

      if (!model || !agency) {
        throw new ApiError(404, "Model or Agency not found");
      }

      // Get persona and trends data
      const persona = await PersonaProfile.findOne({
        modelId,
        isActive: true,
      });

      // Build theme prompt
      const themePrompt = this.buildThemePrompt(
        model,
        agency,
        persona,
        preferences,
      );

      // Generate themes using AI
      const themes = await this.generateAIThemes(themePrompt);

      return {
        modelId,
        agencyId,
        themes,
        generatedAt: new Date(),
        preferences,
      };
    } catch (error) {
      throw new ApiError(
        500,
        `Failed to generate theme suggestions: ${error.message}`,
      );
    }
  }

  // ===== PRIVATE HELPER METHODS =====

  /**
   * Build strategy generation prompt
   */
  buildStrategyPrompt(model, agency, persona, historicalData, preferences) {
    let prompt = `Generate a comprehensive content strategy for a social media model/influencer.

MODEL CONTEXT:
- Name: ${model.fullName}
- Platform Focus: ${preferences.platforms || "Instagram, TikTok"}
- Content Goals: ${preferences.goals || "Engagement, Growth, Brand Building"}

AGENCY CONTEXT:
- Agency: ${agency.name}
- Management Style: ${agency.description || "Professional management"}

HISTORICAL PERFORMANCE:
${
  historicalData
    ? `
- Average Engagement: ${historicalData.avgEngagement || "N/A"}
- Best Performing Content: ${historicalData.topContent || "N/A"}
- Peak Activity Times: ${historicalData.peakTimes || "N/A"}
`
    : "- Limited historical data available"
}`;

    if (persona) {
      prompt += `

PERSONA CONTEXT:
- Target Audience: ${persona.personaSummary}
- Brand Tone: ${persona.tone}
- Key Interests: ${persona.topInterests?.join(", ") || "N/A"}
- Brand Values: ${persona.brandValues?.join(", ") || "N/A"}
- Content Goals: ${persona.goals?.join(", ") || "N/A"}`;
    }

    prompt += `

REQUIREMENTS:
Generate a detailed content strategy in JSON format with the following structure:
{
  "overallStrategy": "High-level strategy description",
  "contentPillars": ["pillar1", "pillar2", "pillar3", "pillar4"],
  "weeklyThemes": ["theme1", "theme2", "theme3", "theme4"],
  "platformStrategy": {
    "instagram": {
      "focus": "primary focus area",
      "postTypes": ["type1", "type2"],
      "frequency": "posts per week",
      "optimalTimes": ["time1", "time2"]
    },
    "tiktok": {
      "focus": "primary focus area", 
      "postTypes": ["type1", "type2"],
      "frequency": "posts per week",
      "optimalTimes": ["time1", "time2"]
    }
  },
  "contentBalance": {
    "personal": "percentage",
    "professional": "percentage", 
    "behind_scenes": "percentage",
    "promotional": "percentage"
  },
  "engagementTactics": ["tactic1", "tactic2", "tactic3"],
  "hashtagStrategy": "hashtag approach",
  "collaborationOpportunities": ["opportunity1", "opportunity2"],
  "kpis": ["metric1", "metric2", "metric3"]
}

Ensure the strategy is personalized, actionable, and aligned with the model's brand and audience.`;

    return prompt;
  }

  /**
   * Build content suggestion prompt
   */
  buildContentSuggestionPrompt(plan, persona, existingTasks, platform, count) {
    let prompt = `Generate ${count} specific content ideas for a ${platform} post.

CONTENT PLAN CONTEXT:
- Week Theme: ${plan.weeklyTheme}
- Plan Goals: ${plan.goals?.join(", ") || "General engagement"}
- Target Platforms: ${plan.targetPlatforms?.join(", ") || platform}

EXISTING CONTENT THIS WEEK:
${
  existingTasks
    .map((task) => `- ${task.title}: ${task.description}`)
    .join("\n") || "- No existing content planned"
}`;

    if (persona) {
      prompt += `

BRAND PERSONA:
- Target Audience: ${persona.personaSummary}
- Brand Tone: ${persona.tone}
- Key Interests: ${persona.topInterests?.join(", ") || "N/A"}
- Brand Values: ${persona.brandValues?.join(", ") || "N/A"}`;
    }

    prompt += `

REQUIREMENTS:
Generate ${count} creative, unique content ideas in JSON format:
{
  "suggestions": [
    {
      "title": "Content Title",
      "description": "Detailed description of the content",
      "contentType": "photo/video/carousel/story", 
      "platform": "${platform}",
      "engagementHooks": ["hook1", "hook2"],
      "visualConcepts": ["concept1", "concept2"],
      "captionDirection": "Caption style and approach",
      "hashtagSuggestions": ["#tag1", "#tag2", "#tag3"],
      "estimatedEngagement": "predicted engagement level",
      "bestPostingTime": "optimal time to post",
      "difficulty": "easy/medium/hard",
      "props_needed": ["prop1", "prop2"] 
    }
  ]
}

Ensure variety in content types, high engagement potential, and alignment with brand persona.`;

    return prompt;
  }

  /**
   * Build timing analysis prompt
   */
  buildTimingAnalysisPrompt(
    model,
    persona,
    performanceData,
    platform,
    weekData,
  ) {
    let prompt = `Analyze optimal posting times for a ${platform} content creator.

MODEL CONTEXT:
- Platform: ${platform}
- Time Zone: ${model.timezone || "UTC"}
- Current Week Data: ${JSON.stringify(weekData)}

AUDIENCE PERSONA:
${
  persona
    ? `
- Target Demographics: ${persona.personaSummary}
- Primary Interests: ${persona.topInterests?.join(", ") || "N/A"}
- Engagement Patterns: ${persona.engagementPatterns || "N/A"}
`
    : "- Limited persona data available"
}

HISTORICAL PERFORMANCE:
${
  performanceData
    ? `
- Previous Peak Times: ${performanceData.peakTimes || "N/A"}
- Best Performing Days: ${performanceData.bestDays || "N/A"}
- Average Engagement by Hour: ${performanceData.hourlyEngagement || "N/A"}
`
    : "- Limited historical data available"
}

REQUIREMENTS:
Provide timing analysis in JSON format:
{
  "optimalTimes": [
    {
      "day": "monday",
      "timeSlots": [
        {
          "time": "09:00",
          "score": 95,
          "reasoning": "why this time works well"
        }
      ]
    }
  ],
  "peakEngagementWindows": ["window1", "window2"],
  "worstTimes": ["time1", "time2"],
  "weeklyStrategy": {
    "highPriority": ["day1", "day2"],
    "mediumPriority": ["day3", "day4"], 
    "lowPriority": ["day5", "day6", "day7"]
  },
  "audienceInsights": "audience behavior patterns",
  "recommendations": ["rec1", "rec2", "rec3"]
}`;

    return prompt;
  }

  /**
   * Build theme suggestion prompt
   */
  buildThemePrompt(model, agency, persona, preferences) {
    let prompt = `Generate weekly content themes for a social media model/influencer.

CONTEXT:
- Model: ${model.fullName}
- Agency: ${agency.name}
- Brand Focus: ${preferences.brandFocus || "Personal brand building"}
- Season/Month: ${preferences.timeframe || "Current month"}

BRAND PERSONA:
${
  persona
    ? `
- Target Audience: ${persona.personaSummary}
- Brand Values: ${persona.brandValues?.join(", ") || "N/A"}
- Content Goals: ${persona.goals?.join(", ") || "N/A"}
`
    : "- Developing brand persona"
}

REQUIREMENTS:
Generate 8 unique weekly themes in JSON format:
{
  "themes": [
    {
      "title": "Theme Name",
      "description": "Detailed theme description",
      "contentPillars": ["pillar1", "pillar2", "pillar3"],
      "suggestedContent": ["idea1", "idea2", "idea3"],
      "hashtags": ["#theme1", "#theme2"],
      "engagementGoal": "primary engagement objective",
      "difficulty": "easy/medium/hard",
      "seasonality": "relevance to current time"
    }
  ],
  "themeRotation": "suggested rotation schedule",
  "crossPlatformAdaptation": "how to adapt themes across platforms"
}

Focus on variety, brand alignment, and engagement potential.`;

    return prompt;
  }

  /**
   * Generate AI strategy using selected provider
   */
  async generateAIStrategy(prompt, provider = "gpt") {
    try {
      let result;

      if (provider === "gemini") {
        result = await geminiService.generatePersona(prompt);
      } else {
        result = await gptService.generatePersona(prompt);
      }

      return JSON.parse(result.content || result);
    } catch (error) {
      throw new Error(`AI strategy generation failed: ${error.message}`);
    }
  }

  /**
   * Generate AI content suggestions
   */
  async generateAISuggestions(prompt) {
    try {
      const result = await gptService.generatePersona(prompt);
      return JSON.parse(result.content || result);
    } catch (error) {
      throw new Error(`AI suggestion generation failed: ${error.message}`);
    }
  }

  /**
   * Generate timing analysis
   */
  async generateTimingAnalysis(prompt) {
    try {
      const result = await gptService.generatePersona(prompt);
      return JSON.parse(result.content || result);
    } catch (error) {
      throw new Error(`AI timing analysis failed: ${error.message}`);
    }
  }

  /**
   * Generate theme suggestions
   */
  async generateAIThemes(prompt) {
    try {
      const result = await gptService.generatePersona(prompt);
      return JSON.parse(result.content || result);
    } catch (error) {
      throw new Error(`AI theme generation failed: ${error.message}`);
    }
  }

  /**
   * Get historical performance data for a model
   */
  async getHistoricalPerformance(modelId, platform = null) {
    try {
      // Query historical content performance
      const query = {
        modelId,
        isCompleted: true,
        "metrics.engagement": { $exists: true },
      };

      if (platform) {
        query.platform = platform;
      }

      const tasks = await ContentTask.find(query)
        .sort({ scheduledDate: -1 })
        .limit(50);

      if (tasks.length === 0) {
        return null;
      }

      // Calculate performance metrics
      const engagements = tasks.map((t) => t.metrics?.engagement || 0);
      const avgEngagement =
        engagements.reduce((a, b) => a + b, 0) / engagements.length;

      // Find best performing content
      const topTask = tasks.reduce((prev, current) =>
        (prev.metrics?.engagement || 0) > (current.metrics?.engagement || 0)
          ? prev
          : current,
      );

      // Analyze peak times
      const hourlyData = {};
      tasks.forEach((task) => {
        if (task.scheduledDate) {
          const hour = new Date(task.scheduledDate).getHours();
          hourlyData[hour] =
            (hourlyData[hour] || 0) + (task.metrics?.engagement || 0);
        }
      });

      const peakHour = Object.keys(hourlyData).reduce((a, b) =>
        hourlyData[a] > hourlyData[b] ? a : b,
      );

      return {
        avgEngagement: Math.round(avgEngagement),
        topContent: topTask.title,
        peakTimes: [`${peakHour}:00`],
        totalAnalyzed: tasks.length,
        lastAnalyzed: new Date(),
      };
    } catch (error) {
      console.error("Error getting historical performance:", error);
      return null;
    }
  }
}

export default new AIContentService();
