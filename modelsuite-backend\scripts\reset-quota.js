import mongoose from "mongoose";

async function resetQuota() {
  try {
    const mongoUri =
      "mongodb+srv://devanand:<EMAIL>/modelsuite?retryWrites=true&w=majority&appName=Cluster0";
    await mongoose.connect(mongoUri);

    const db = mongoose.connection.db;
    const collection = db.collection("captionquotas");

    // Reset quota for model user
    const modelResult = await collection.updateOne(
      {
        modelId: new mongoose.Types.ObjectId("6877d1d1d4660031e907a8eb"), // Model ID
        date: "2025-08-04",
      },
      { $set: { usageCount: 0 } },
    );

    // Reset quota for agency user
    const agencyResult = await collection.updateOne(
      {
        modelId: new mongoose.Types.ObjectId("68723da81257ef228b214073"), // Agency ID
        date: "2025-08-04",
      },
      { $set: { usageCount: 0 } },
    );

    console.log(
      "✅ Model quota reset:",
      modelResult.modifiedCount,
      "document updated",
    );
    console.log(
      "✅ Agency quota reset:",
      agencyResult.modifiedCount,
      "document updated",
    );
    console.log("🎉 Both quotas reset successfully for testing!");

    await mongoose.disconnect();
  } catch (error) {
    console.error("❌ Error:", error);
  }
}

resetQuota();
