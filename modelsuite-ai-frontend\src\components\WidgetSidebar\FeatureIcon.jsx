import React from "react";
import { useNavigate } from "react-router-dom";

const FeatureIcon = ({ feature, onClick }) => {
  const navigate = useNavigate();

  const handleClick = () => {
    if (feature.isLaunchpad) {
      // Handle launchpad click (open modal)
      onClick && onClick();
    } else {
      // Navigate to feature route
      navigate(feature.route);
    }
  };

  const IconComponent = feature.icon;

  return (
    <div
      onClick={handleClick}
      className="group relative flex items-center justify-center w-12 h-12 rounded-xl bg-black/20 backdrop-blur-sm border border-white/10 cursor-pointer transition-all duration-200 hover:bg-black/30 hover:scale-110 hover:border-white/20"
      title={feature.description || feature.name}
    >
      {/* Icon */}
      {typeof IconComponent === "function" && !feature.isLaunchpad ? (
        <IconComponent className="h-full w-full text-gray-300 group-hover:text-white transition-colors" />
      ) : (
        <IconComponent className="h-full w-full text-gray-300 group-hover:text-white transition-colors" />
      )}

      {/* Hover tooltip */}
      <div className="absolute bottom-full mb-2 px-2 py-1 bg-black/80 text-white text-xs rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
        {feature.name}
      </div>
    </div>
  );
};

export default FeatureIcon;
