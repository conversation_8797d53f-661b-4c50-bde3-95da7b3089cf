import * as React from "react";
import { cn } from "@/lib/utils";
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import * as Icons from "lucide-react";

const TabsWrapper = ({
  tabs = [],
  defaultValue,
  value,
  onValueChange,
  orientation = "horizontal", // "horizontal", "vertical"
  variant = "default", // "default", "pills", "underline"
  size = "default", // "sm", "default", "lg"
  className,
  tabsListClassName,
  tabsContentClassName,
  fullWidth = false,
  sticky = false,
  showBadges = false,
  disabled = false,
  ...props
}) => {
  const getTabsListVariant = () => {
    const baseClasses = cn(
      "bg-[#0f0f0f] border border-gray-800",
      orientation === "vertical" && "flex-col h-fit w-fit",
      fullWidth && orientation === "horizontal" && "w-full",
      sticky && "sticky top-0 z-10 backdrop-blur-sm bg-[#0f0f0f]/90"
    );

    switch (variant) {
      case "pills":
        return cn(baseClasses, "bg-[#1a1a1a] p-1 rounded-lg");
      case "underline":
        return cn(
          baseClasses,
          "bg-transparent border-0 border-b border-gray-800"
        );
      default:
        return baseClasses;
    }
  };

  const getTabsTriggerVariant = () => {
    const baseClasses = cn(
      "text-gray-500 transition-all duration-200",
      "hover:text-gray-300 hover:bg-[#1a1a1a]",
      "data-[state=active]:text-gray-200 data-[state=active]:bg-[#1a1a1a]",
      "disabled:opacity-50 disabled:cursor-not-allowed",
      "focus:outline-none focus:ring-2 focus:ring-gray-600 focus:ring-offset-2 focus:ring-offset-[#0f0f0f]"
    );

    const sizeClasses = {
      sm: "text-xs px-2 py-1 h-7",
      default: "text-sm px-3 py-2 h-9",
      lg: "text-base px-4 py-3 h-11",
    };

    switch (variant) {
      case "pills":
        return cn(
          baseClasses,
          sizeClasses[size],
          "rounded-md",
          "data-[state=active]:bg-gray-700 data-[state=active]:text-gray-200",
          "hover:bg-gray-800"
        );
      case "underline":
        return cn(
          baseClasses,
          sizeClasses[size],
          "border-b-2 border-transparent rounded-none",
          "data-[state=active]:border-gray-500 data-[state=active]:bg-transparent",
          "hover:bg-[#1a1a1a]"
        );
      default:
        return cn(baseClasses, sizeClasses[size], "rounded-sm");
    }
  };

  const getTabsContentVariant = () => {
    return cn(
      "bg-transparent text-gray-300",
      "focus:outline-none focus:ring-2 focus:ring-gray-600 focus:ring-offset-2 focus:ring-offset-[#0f0f0f]",
      orientation === "vertical" && "ml-4"
    );
  };

  // Validate tabs array
  const validTabs = React.useMemo(() => {
    return tabs.filter(
      (tab) => tab && (tab.value || tab.id) && (tab.label || tab.title)
    );
  }, [tabs]);

  if (!validTabs.length) {
    return (
      <div className="text-gray-500 text-sm p-4 text-center">
        No tabs available
      </div>
    );
  }

  const firstTabValue =
    defaultValue || value || validTabs[0].value || validTabs[0].id;

  return (
    <Tabs
      value={value}
      defaultValue={firstTabValue}
      onValueChange={onValueChange}
      orientation={orientation}
      className={cn("w-full", className)}
      {...props}
    >
      <TabsList className={cn(getTabsListVariant(), tabsListClassName)}>
        {validTabs.map((tab) => {
          const tabValue = tab.value || tab.id;
          const tabLabel = tab.label || tab.title;
          const isDisabled = disabled || tab.disabled;

          return (
            <TabsTrigger
              key={tabValue}
              value={tabValue}
              disabled={isDisabled}
              className={cn(
                getTabsTriggerVariant(),
                tab.className,
                fullWidth && orientation === "horizontal" && "flex-1"
              )}
            >
              <div className="flex items-center gap-2">
                {/* Tab Icon */}
                {tab.icon && (
                  <span className="flex-shrink-0">
                    {React.isValidElement(tab.icon) ? (
                      tab.icon
                    ) : typeof tab.icon === "string" ? (
                      React.createElement(Icons[tab.icon] || Icons.Circle, {
                        className: "h-4 w-4",
                      })
                    ) : (
                      <tab.icon className="h-4 w-4" />
                    )}
                  </span>
                )}

                {/* Tab Label */}
                <span className={cn("truncate", !fullWidth && "max-w-32")}>
                  {tabLabel}
                </span>

                {/* Tab Badge */}
                {showBadges && tab.badge && (
                  <Badge
                    variant="secondary"
                    className="ml-1 text-xs bg-gray-800 text-gray-400 border-gray-700"
                  >
                    {tab.badge}
                  </Badge>
                )}

                {/* Tab Count */}
                {tab.count !== undefined && (
                  <Badge
                    variant="outline"
                    className="ml-1 text-xs bg-[#0f0f0f] text-gray-400 border-gray-700"
                  >
                    {tab.count}
                  </Badge>
                )}
              </div>
            </TabsTrigger>
          );
        })}
      </TabsList>

      {/* Tab Contents */}
      {validTabs.map((tab) => {
        const tabValue = tab.value || tab.id;
        return (
          <TabsContent
            key={tabValue}
            value={tabValue}
            className={cn(
              getTabsContentVariant(),
              tabsContentClassName,
              tab.contentClassName
            )}
          >
            {tab.content || tab.children}
          </TabsContent>
        );
      })}
    </Tabs>
  );
};

export default TabsWrapper;
