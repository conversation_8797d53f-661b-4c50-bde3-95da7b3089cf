import ToolEntry from "../models/toolCatalog.js";

export const createTool = async (req, res) => {
  try {
    // Get agency ID from authenticated user
    const agencyId = req.user._id;

    // Create tool data with agency ID
    const toolData = {
      ...req.body,
      agency: agencyId,
      // Set responsible_person to agency ID since it's required
      responsible_person: agencyId,
    };

    const tool = new ToolEntry(toolData);
    await tool.save();

    // Populate the saved tool with user details
    const populatedTool = await ToolEntry.findById(tool._id)
      .populate("users", "display_name email role")
      .populate("responsible_person", "display_name email role");

    // Transform the response to match frontend expectations
    const transformedTool = {
      ...populatedTool.toObject(),
      responsible_person:
        populatedTool.responsible_person?.display_name || "Agency",
    };

    res.status(201).json(transformedTool);
  } catch (err) {
    console.error("Error creating tool:", err);
    res.status(400).json({ error: err.message });
  }
};

export const getTools = async (req, res) => {
  try {
    // Filter by authenticated agency
    const filter = { agency: req.user._id };

    const tools = await ToolEntry.find(filter)
      .populate("users", "display_name email role")
      .populate("responsible_person", "display_name email role");

    // Transform the response to match frontend expectations
    const transformedTools = tools.map((tool) => ({
      ...tool.toObject(),
      responsible_person: tool.responsible_person?.display_name || "Agency",
    }));

    res.json(transformedTools);
  } catch (err) {
    console.error("Error fetching tools:", err);
    res.status(500).json({ error: err.message });
  }
};

// Get a single tool entry by ID
export const getToolById = async (req, res) => {
  try {
    const tool = await ToolEntry.findOne({
      _id: req.params.id,
      agency: req.user._id,
    })
      .populate("users", "display_name email role")
      .populate("responsible_person", "display_name email role");

    if (!tool) return res.status(404).json({ error: "Tool not found" });

    // Transform the response to match frontend expectations
    const transformedTool = {
      ...tool.toObject(),
      responsible_person: tool.responsible_person?.display_name || "Agency",
    };

    res.json(transformedTool);
    return null;
  } catch (err) {
    console.error("Error fetching tool:", err);
    res.status(500).json({ error: err.message });
    return null;
  }
};

// Update a tool entry by ID
export const updateTool = async (req, res) => {
  try {
    const tool = await ToolEntry.findOneAndUpdate(
      { _id: req.params.id, agency: req.user._id },
      req.body,
      { new: true },
    )
      .populate("users", "display_name email role")
      .populate("responsible_person", "display_name email role");

    if (!tool) return res.status(404).json({ error: "Tool not found" });

    // Transform the response to match frontend expectations
    const transformedTool = {
      ...tool.toObject(),
      responsible_person: tool.responsible_person?.display_name || "Agency",
    };

    res.json(transformedTool);
    return null;
  } catch (err) {
    console.error("Error updating tool:", err);
    res.status(400).json({ error: err.message });
    return null;
  }
};

// Delete a tool entry by ID
export const deleteTool = async (req, res) => {
  try {
    const tool = await ToolEntry.findOneAndDelete({
      _id: req.params.id,
      agency: req.user._id,
    });

    if (!tool) return res.status(404).json({ error: "Tool not found" });
    res.json({ message: "Tool deleted" });
    return null;
  } catch (err) {
    console.error("Error deleting tool:", err);
    res.status(500).json({ error: err.message });
    return null;
  }
};
