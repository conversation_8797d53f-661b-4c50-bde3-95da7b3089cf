import { Avatar, AvatarFallback, AvatarImage } from '../../../components/ui/avatar'
import StatusBadge from '../../../reusable/Status/StatusBadge'

import { modelPanelMenu } from "../../../utils/data";

// Map icon names to react-icons components
import { FaRegCommentDots, FaCalendarAlt, FaCheckSquare, FaChartLine, FaDollarSign, FaFileAlt, FaUsers, FaShieldAlt, FaGift, FaBook, FaBullhorn } from "react-icons/fa";

const iconMap = {
  FaRegCommentDots,
  FaCalendarAlt,
  FaCheckSquare,
  FaChartLine,
  FaFileAlt,
  FaBullhorn,
  FaUsers,
  FaShieldAlt,
  FaDollarSign,
  FaGift,
  FaBook,
};

import { Link, useLocation, useParams, useNavigate } from 'react-router-dom';

const ModelPanel = ({model}) => {
  const navigate = useNavigate();
  // Map menu items to use icon components
  const menuItems = modelPanelMenu.map(item => ({
    ...item,
    icon: item.icon && iconMap[item.icon],
  }));

  const location = useLocation();
  const { id: modelId } = useParams();

  return (
  <div className={`bg-[#0f0f0f] border-r border-[#1a1a1a] flex flex-col transition-all duration-300 w-64`}>
      {/* Header */}
      <div className="p-6 border-b border-[#1a1a1a] flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-xs">{model.initials || 'MS'}</span>
          </div>
          <div>
            <h2 className="font-semibold text-white">{model.name || 'Model Profile'}</h2>
          </div>
        </div>
        <button
          className="text-gray-400 hover:text-white"
          onClick={() => navigate('/agency/dashboard')}
        >
          ×
        </button>
      </div>

      {/* Content */}
      <div className="flex-1 px-4">
        <div className="py-6">
          {/* Model Info Card */}
          <div className="mb-6 flex flex-row items-left gap-4 justify-left">
            <Avatar className="w-14 h-14">
            <AvatarImage src="https://github.com/shadcn.png" />
              <AvatarFallback className="bg-gradient-to-br from-purple-500 to-pink-500 text-white text-lg font-medium">
                {model.initials || 'M'}
              </AvatarFallback>
            </Avatar>
            <div className="flex flex-col justify-center">
              <h4 className="text-lg font-semibold text-white mb-1">{model.fullName || 'Model Name'}</h4>
              <StatusBadge status={model.status || "offline"} showIcon={false} showDot>{model.status || "Offline"}</StatusBadge>
            </div>
          </div>
          {/* Model Menu Items (styled as nav links) */}
          <nav className="space-y-1">
            {menuItems.map((item) => {
              const IconComponent = item.icon;
              // Build the route path for each menu item
              let sectionPath = item.label.toLowerCase().replace(/\s+/g, '-');
              // Messenger is both the index and explicit path
              if (sectionPath === 'messenger') sectionPath = '';
              const to = `/agency/model-menu/${modelId}${sectionPath ? `/${sectionPath}` : ''}`;
              const isActive =
                (sectionPath === '' &&
                  (location.pathname === `/agency/model-menu/${modelId}` || location.pathname === `/agency/model-menu/${modelId}/`)) ||
                location.pathname === `/agency/model-menu/${modelId}/${sectionPath}`;
              return (
                <Link
                  key={item.label}
                  to={to}
                  className={`w-full flex items-center gap-3 px-3 py-2.5 rounded-lg transition-all duration-200 text-sm font-medium
                    ${isActive ? "bg-[#2563EB]/20 text-[#2563EB]" : "text-gray-400 hover:bg-[#18181b] hover:text-white"}`}
                >
                  {IconComponent && <IconComponent className="w-4 h-4" />}
                  <span>{item.label}</span>
                </Link>
              );
            })}
          </nav>
        </div>
      </div>
    </div>
  )
}

export default ModelPanel