import express from "express";
import {
  createOrUpdateWeeklySchedule,
  updateWeeklySchedule,
  getWeeklySchedule,
  getWeeklyScheduleByDate,
  getAllWeeklySchedules,
  assignEmployeeToShift,
  publishWeeklySchedule,
  deleteWeeklySchedule,
  getMySchedules,
  addTimespan,
  updateTimespan,
  deleteTimespan,
  addShiftComment,
  getShiftComments,
  deleteShiftComment,
} from "../../controllers/shifts/weeklyScheduleController.js";
import { verifyToken } from "../../middlewares/authMiddleware.js";
import checkPermission from "../../middlewares/permissionCheck.js";

const router = express.Router();

// Apply authentication middleware to all routes
router.use(verifyToken);

// Create or update weekly schedule
router.post(
  "/",
  checkPermission("shifts.create"),
  createOrUpdateWeeklySchedule,
);

// Update weekly schedule
router.put(
  "/:scheduleId",
  checkPermission("shifts.edit"),
  updateWeeklySchedule,
);

// Get weekly schedule by year and week number
router.get(
  "/week/:year/:weekNumber",
  checkPermission("shifts.view"),
  getWeeklySchedule,
);

// Get weekly schedule by date
router.get(
  "/date/:date",
  checkPermission("shifts.view"),
  getWeeklyScheduleByDate,
);

// Get all weekly schedules for agency
router.get("/", checkPermission("shifts.view"), getAllWeeklySchedules);

// Assign employee to shift
router.post("/assign", checkPermission("shifts.assign"), assignEmployeeToShift);

// Publish weekly schedule
router.patch(
  "/:scheduleId/publish",
  checkPermission("shifts.edit"),
  publishWeeklySchedule,
);

// Delete weekly schedule
router.delete(
  "/:scheduleId",
  checkPermission("shifts.delete"),
  deleteWeeklySchedule,
);

// Get current employee's own schedules (no permission check needed as employees can view their own shifts)
router.get("/my-schedules", getMySchedules);

// Add timespan to schedule
router.post("/timespan", checkPermission("shifts.create"), addTimespan);

// Update timespan
router.put(
  "/timespan/:scheduleId/:shiftId",
  checkPermission("shifts.edit"),
  updateTimespan,
);

// Delete timespan
router.delete(
  "/timespan/:scheduleId/:shiftId",
  checkPermission("shifts.delete"),
  deleteTimespan,
);

// Add comment to shift
router.post("/:scheduleId/shift/:shiftId/comment", addShiftComment);

// Get comments for a shift
router.get("/:scheduleId/shift/:shiftId/comments", getShiftComments);

// Delete comment from shift
router.delete(
  "/:scheduleId/shift/:shiftId/comment/:commentId",
  deleteShiftComment,
);

export default router;
