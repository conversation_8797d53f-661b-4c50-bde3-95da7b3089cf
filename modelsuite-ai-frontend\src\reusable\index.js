// Centralized exports for all reusable components

// Card Components
export { default as StatCard } from './Cards/StatCard.jsx';
export { default as MetricGrid } from './Cards/MetricGrid.jsx';

// Modal Components
export { default as ConfirmModal } from './Modals/ConfirmModal.jsx';
export { default as FormModal } from './Modals/FormModal.jsx';
export { default as InfoModal } from './Modals/InfoModal.jsx';

// FileUpload Components
export { default as FileDropzone } from './FileUpload/FileDropzone.jsx';
export { default as ImageUploader } from './FileUpload/ImageUploader.jsx';
export { default as DocumentUploader } from './FileUpload/DocumentUploader.jsx';

// Form Components
export { default as FormWrapper } from './Form/FormWrapper.jsx';
export { default as SelectField } from './Form/SelectField.jsx';
export { default as TextAreaField } from './Form/TextAreaField.jsx';
export { default as CheckboxField } from './Form/CheckboxField.jsx';
export { default as RadioGroupField } from './Form/RadioGroup.jsx';

// Input Components
export { default as InputFormField } from './Input/InputFormField.jsx';
export { default as Toggle } from './Input/Toggle.jsx';

// Loader Components
export { default as Spinner } from './Loader/Spinner.jsx';
export { default as SkeletonLoader } from './Loader/SkeletonLoader.jsx';
export { default as ProgressBar } from './Loader/ProgressBar.jsx';
export { default as LoadingOverlay } from './Loader/LoadingOverlay.jsx';
export { default as LoadingButton } from './Loader/LoadingButton.jsx';

// Navigation Components
export { default as TabsWrapper } from './Navigation/TabsWrapper.jsx';
export { default as BreadcrumbNav } from './Navigation/BreadcrumbNav.jsx';

// Status Components
export { default as StatusBadge } from './Status/StatusBadge.jsx';

// Search Components
export { default as SearchBar } from './Search/SearchBar.jsx';

// DatePicker Components
export { default as DatePicker } from './DatePicker/DatePicker.jsx';

// Avatar Components
export { default as AvatarGroup } from './Avatar/AvatarGroup.jsx';

// EmptyState Components
export { default as EmptyState } from './EmptyState/EmptyState.jsx';

// Group exports for easier importing
import StatCardComponent from './Cards/StatCard.jsx';
import MetricGridComponent from './Cards/MetricGrid.jsx';
import ConfirmModalComponent from './Modals/ConfirmModal.jsx';
import FormModalComponent from './Modals/FormModal.jsx';
import InfoModalComponent from './Modals/InfoModal.jsx';
import FileDropzoneComponent from './FileUpload/FileDropzone.jsx';
import ImageUploaderComponent from './FileUpload/ImageUploader.jsx';
import DocumentUploaderComponent from './FileUpload/DocumentUploader.jsx';
import FormWrapperComponent from './Form/FormWrapper.jsx';
import SelectFieldComponent from './Form/SelectField.jsx';
import TextAreaFieldComponent from './Form/TextAreaField.jsx';
import CheckboxFieldComponent from './Form/CheckboxField.jsx';
import RadioGroupFieldComponent from './Form/RadioGroup.jsx';
import SpinnerComponent from './Loader/Spinner.jsx';
import SkeletonLoaderComponent from './Loader/SkeletonLoader.jsx';
import ProgressBarComponent from './Loader/ProgressBar.jsx';
import LoadingOverlayComponent from './Loader/LoadingOverlay.jsx';
import LoadingButtonComponent from './Loader/LoadingButton.jsx';
import TabsWrapperComponent from './Navigation/TabsWrapper.jsx';
import BreadcrumbNavComponent from './Navigation/BreadcrumbNav.jsx';
import StatusBadgeComponent from './Status/StatusBadge.jsx';
import SearchBarComponent from './Search/SearchBar.jsx';
import DatePickerComponent from './DatePicker/DatePicker.jsx';
import AvatarGroupComponent from './Avatar/AvatarGroup.jsx';
import EmptyStateComponent from './EmptyState/EmptyState.jsx';

export const Cards = {
  StatCard: StatCardComponent,
  MetricGrid: MetricGridComponent,
};

export const Modals = {
  ConfirmModal: ConfirmModalComponent,
  FormModal: FormModalComponent,
  InfoModal: InfoModalComponent,
};

export const FileUpload = {
  FileDropzone: FileDropzoneComponent,
  ImageUploader: ImageUploaderComponent,
  DocumentUploader: DocumentUploaderComponent,
};

export const Form = {
  FormWrapper: FormWrapperComponent,
  SelectField: SelectFieldComponent,
  TextAreaField: TextAreaFieldComponent,
  CheckboxField: CheckboxFieldComponent,
  RadioGroupField: RadioGroupFieldComponent,
};

export const Loader = {
  Spinner: SpinnerComponent,
  SkeletonLoader: SkeletonLoaderComponent,
  ProgressBar: ProgressBarComponent,
  LoadingOverlay: LoadingOverlayComponent,
  LoadingButton: LoadingButtonComponent,
};

export const Navigation = {
  TabsWrapper: TabsWrapperComponent,
  BreadcrumbNav: BreadcrumbNavComponent,
};

export const Status = {
  StatusBadge: StatusBadgeComponent,
};

export const Search = {
  SearchBar: SearchBarComponent,
};

export const DatePickerGroup = {
  DatePicker: DatePickerComponent,
};

export const AvatarComponents = {
  AvatarGroup: AvatarGroupComponent,
};

export const EmptyStateGroup = {
  EmptyState: EmptyStateComponent,
};
