import mongoose from "mongoose";

/**
 * BioGenerationLog Schema - Tracks bio generation attempts and performance
 */
const bioGenerationLogSchema = new mongoose.Schema(
  {
    modelId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ModelUser",
      required: true,
      index: true,
    },
    agencyId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "AgencyUser",
      required: true,
      index: true,
    },
    // Snapshot of persona data at generation time
    personaSnapshot: {
      personaId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "PersonaProfile",
        required: true,
      },
      name: String,
      content: String,
      tags: [String],
      communicationStyle: {
        tone: String,
        language: String,
        formality: String,
      },
    },
    // Snapshot of model data at generation time
    modelSnapshot: {
      username: String,
      fullName: String,
      profileDescription: String,
      interests: [String],
      specialties: [String],
    },
    // The exact prompt sent to AI
    promptUsed: {
      type: String,
      required: true,
    },
    // AI service configuration
    aiService: {
      type: String,
      enum: ["gpt-4", "gpt-3.5-turbo", "gemini-pro", "claude-3"],
      required: true,
    },
    aiConfig: {
      temperature: {
        type: Number,
        default: 0.7,
        min: 0,
        max: 2,
      },
      maxTokens: {
        type: Number,
        default: 1500,
      },
      model: String,
    },
    // Generated bio content
    biosGenerated: {
      short: {
        content: String,
        wordCount: Number,
        characterCount: Number,
      },
      medium: {
        content: String,
        wordCount: Number,
        characterCount: Number,
      },
      long: {
        content: String,
        wordCount: Number,
        characterCount: Number,
      },
    },
    // Performance metrics
    timing: {
      dataPrepTime: {
        type: Number, // milliseconds
        default: 0,
      },
      aiTime: {
        type: Number, // milliseconds
        default: 0,
      },
      total: {
        type: Number, // milliseconds
        default: 0,
      },
    },
    // Success/failure tracking
    success: {
      type: Boolean,
      required: true,
      index: true,
    },
    errorType: {
      type: String,
      enum: [
        "ai_service_error",
        "prompt_error",
        "validation_error",
        "rate_limit",
        "quota_exceeded",
        "network_error",
        "parsing_error",
        "content_filter",
        null,
      ],
      default: null,
    },
    errorMessage: {
      type: String,
      default: null,
    },
    // Generation metadata
    generationMode: {
      type: String,
      enum: ["persona_enhanced", "basic", "regeneration"],
      default: "persona_enhanced",
    },
    requestMetadata: {
      userAgent: String,
      ipAddress: String,
      userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
      },
    },
    // Cost tracking
    cost: {
      inputTokens: {
        type: Number,
        default: 0,
      },
      outputTokens: {
        type: Number,
        default: 0,
      },
      estimatedCost: {
        type: Number,
        default: 0,
        // Store in USD cents to avoid floating point issues
      },
    },
    // Quality metrics
    quality: {
      grammarScore: {
        type: Number,
        min: 0,
        max: 100,
        default: null,
      },
      readabilityScore: {
        type: Number,
        min: 0,
        max: 100,
        default: null,
      },
      sentimentScore: {
        type: Number,
        min: -1,
        max: 1,
        default: null,
      },
      uniquenessScore: {
        type: Number,
        min: 0,
        max: 100,
        default: null,
      },
    },
  },
  {
    timestamps: true,
    collection: "biogenerationlogs",
  },
);

// Indexes for analytics and performance
bioGenerationLogSchema.index({ modelId: 1, createdAt: -1 });
bioGenerationLogSchema.index({ agencyId: 1, createdAt: -1 });
bioGenerationLogSchema.index({ success: 1, createdAt: -1 });
bioGenerationLogSchema.index({ aiService: 1, success: 1 });
bioGenerationLogSchema.index({ generationMode: 1, createdAt: -1 });
bioGenerationLogSchema.index({ errorType: 1 });

// Compound indexes for common queries
bioGenerationLogSchema.index({ agencyId: 1, success: 1, createdAt: -1 });
bioGenerationLogSchema.index({ modelId: 1, success: 1, createdAt: -1 });

// Virtual for calculating success rate
bioGenerationLogSchema.virtual("isSuccessful").get(function () {
  return this.success && !this.errorType;
});

// Instance methods
bioGenerationLogSchema.methods.markAsSuccessful = function (biosData, timing) {
  this.success = true;
  this.errorType = null;
  this.errorMessage = null;
  this.biosGenerated = biosData;
  this.timing = timing;
  return this.save();
};

bioGenerationLogSchema.methods.markAsFailed = function (
  errorType,
  errorMessage,
) {
  this.success = false;
  this.errorType = errorType;
  this.errorMessage = errorMessage;
  return this.save();
};

bioGenerationLogSchema.methods.updateCost = function (
  inputTokens,
  outputTokens,
  costPerToken,
) {
  this.cost.inputTokens = inputTokens;
  this.cost.outputTokens = outputTokens;
  this.cost.estimatedCost = Math.round(
    (inputTokens + outputTokens) * costPerToken * 100,
  ); // Store in cents
  return this.save();
};

// Static methods for analytics
bioGenerationLogSchema.statics.getSuccessRate = function (
  agencyId,
  timeframe = 30,
) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - timeframe);

  return this.aggregate([
    {
      $match: {
        agencyId: new mongoose.Types.ObjectId(agencyId),
        createdAt: { $gte: startDate },
      },
    },
    {
      $group: {
        _id: null,
        total: { $sum: 1 },
        successful: { $sum: { $cond: ["$success", 1, 0] } },
      },
    },
    {
      $project: {
        total: 1,
        successful: 1,
        successRate: {
          $cond: [
            { $eq: ["$total", 0] },
            0,
            { $multiply: [{ $divide: ["$successful", "$total"] }, 100] },
          ],
        },
      },
    },
  ]);
};

bioGenerationLogSchema.statics.getAverageGenerationTime = function (
  agencyId,
  timeframe = 30,
) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - timeframe);

  return this.aggregate([
    {
      $match: {
        agencyId: new mongoose.Types.ObjectId(agencyId),
        createdAt: { $gte: startDate },
        success: true,
      },
    },
    {
      $group: {
        _id: null,
        avgDataPrepTime: { $avg: "$timing.dataPrepTime" },
        avgAiTime: { $avg: "$timing.aiTime" },
        avgTotalTime: { $avg: "$timing.total" },
      },
    },
  ]);
};

bioGenerationLogSchema.statics.getErrorAnalysis = function (
  agencyId,
  timeframe = 30,
) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - timeframe);

  return this.aggregate([
    {
      $match: {
        agencyId: new mongoose.Types.ObjectId(agencyId),
        createdAt: { $gte: startDate },
        success: false,
      },
    },
    {
      $group: {
        _id: "$errorType",
        count: { $sum: 1 },
      },
    },
    {
      $sort: { count: -1 },
    },
  ]);
};

bioGenerationLogSchema.statics.getCostAnalysis = function (
  agencyId,
  timeframe = 30,
) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - timeframe);

  return this.aggregate([
    {
      $match: {
        agencyId: new mongoose.Types.ObjectId(agencyId),
        createdAt: { $gte: startDate },
        success: true,
      },
    },
    {
      $group: {
        _id: null,
        totalCost: { $sum: "$cost.estimatedCost" },
        totalInputTokens: { $sum: "$cost.inputTokens" },
        totalOutputTokens: { $sum: "$cost.outputTokens" },
        avgCostPerGeneration: { $avg: "$cost.estimatedCost" },
      },
    },
  ]);
};

// Pre-save middleware to calculate total timing
bioGenerationLogSchema.pre("save", function (next) {
  if (this.timing && this.timing.dataPrepTime && this.timing.aiTime) {
    this.timing.total = this.timing.dataPrepTime + this.timing.aiTime;
  }
  next();
});

// Ensure JSON output includes virtuals
bioGenerationLogSchema.set("toJSON", { virtuals: true });
bioGenerationLogSchema.set("toObject", { virtuals: true });

const BioGenerationLog = mongoose.model(
  "BioGenerationLog",
  bioGenerationLogSchema,
);

export default BioGenerationLog;
