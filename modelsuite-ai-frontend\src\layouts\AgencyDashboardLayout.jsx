import React from "react";
import ModelSidebar from "../pages/Agency/Dashboard/Sidebar/ModelSidebar";
import GlobalSidebar from "../pages/Agency/Dashboard/Sidebar/GlobalSidebar";
import { Outlet, useParams, useLocation, matchPath } from "react-router-dom";
import ModelPanel from "../pages/Agency/Dashboard/ModelPanel";
import MainHeader from "../pages/Agency/Dashboard/MainHeader";
import { SidebarProvider, useSidebar } from "../contexts/SidebarContext";
import { DashboardLayoutProvider } from "../contexts/DashboardLayoutContext";
import { useSelector } from "react-redux";
import WidgetSidebar from "../components/WidgetSidebar/WidgetSidebar";

const AgencyDashboardContent = () => {
  const { isCollapsed, toggleSidebar } = useSidebar();
  const { id: modelId } = useParams();
  const { modelList } = useSelector((state) => state.fetchModelListReducer);

  const selectedModel = modelList?.find(
    (m) => String(m._id) === String(modelId)
  );

  const location = useLocation();
  // Check if we are in a model-menu route
  const inModelMenu =
    matchPath("/agency/model-menu/:id/*", location.pathname) ||
    matchPath("/agency/model-menu/:id", location.pathname);

  return (
    <div className="flex h-screen bg-[#0a0a0a] text-white overflow-auto">
      {/* Always show ModelSidebar */}
      <ModelSidebar />
      {/* Always show ModelPanel as the middle sidebar if in model-menu route */}
      {inModelMenu ? (
        <ModelPanel model={selectedModel || {}} />
      ) : modelId ? (
        <ModelPanel
          model={selectedModel || {}}
          onClose={() => {
            window.history.back();
          }}
        />
      ) : (
        <GlobalSidebar />
      )}
      {/* Main Content Area */}
      <div className="flex-1 flex flex-col bg-[#0a0a0a] overflow-y-auto">
        <MainHeader onToggleSidebar={toggleSidebar} />
        <DashboardLayoutProvider>
          <Outlet /> {/* This is where DashboardContent will be rendered */}
        </DashboardLayoutProvider>
      </div>

      {/* Widget Sidebar */}
      <WidgetSidebar />
    </div>
  );
};

const AgencyDashboardLayout = () => {
  return (
    <SidebarProvider>
      <AgencyDashboardContent />
    </SidebarProvider>
  );
};

export default AgencyDashboardLayout;
