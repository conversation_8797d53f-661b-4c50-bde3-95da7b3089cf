import express from "express";
import { magicLogin, magicVerify } from "../controllers/magicLinkController.js";
import checkPermission from "../middlewares/permissionCheck.js";

const router = express.Router();

// router.post("/magic-login", checkPermission("auth.login"), magicLogin);
// router.get("/magic-verify", checkPermission("auth.login"), magicVerify);
router.post("/magic-login", magicLogin);
router.get("/magic-verify", magicVerify);

export default router;
