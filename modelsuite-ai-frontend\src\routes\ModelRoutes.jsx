import { Route } from "react-router-dom";
import ProtectedRoute from "../reusable/ProtectedRoute";
// Import your model dashboard layout and components here as needed

// Questionnaire imports for models
import MyAssignmentsPage from "../pages/Model/Questionnaire/MyAssignmentsPage";
import TakeQuestionnairePage from "../pages/Model/Questionnaire/TakeQuestionnairePage";
import ViewResponsesPage from "../pages/Model/Questionnaire/ViewResponsesPage";

export default function ModelRoutes() {
  // Placeholder: update with actual model dashboard routes
  return (
    <>
      <Route element={<ProtectedRoute />}>
        {/* Add your model dashboard routes here, e.g.
        <Route path="/model/dashboard" element={<ModelDashboardLayout />}>
          ...
        </Route>
        */}

        {/* Model Questionnaire routes - can be nested under model dashboard later */}
        <Route path="/model/questionnaires" element={<MyAssignmentsPage />} />
        <Route
          path="/model/questionnaires/take/:assignmentId"
          element={<TakeQuestionnairePage />}
        />
        <Route
          path="/model/questionnaires/responses/:templateId"
          element={<ViewResponsesPage />}
        />
      </Route>
    </>
  );
}
