import WeeklySchedule from "../../models/shifts/WeeklySchedule.js";
import Employee from "../../models/Employee/Employee.js";
import { getWeek, startOfWeek, endOfWeek, format, addDays } from "date-fns";

// Create or update weekly schedule
export const createOrUpdateWeeklySchedule = async (req, res) => {
  try {
    const { weekStartDate, weekEndDate, description } = req.body;
    const agencyId =
      req.user.role === "employee" ? req.user.agencyId : req.user._id;
    const createdBy = req.user._id;
    const createdByModel = req.user.role === "agency" ? "Agency" : "Employee";

    const startDate = new Date(weekStartDate);
    const endDate = new Date(weekEndDate);
    const weekNumber = getWeek(startDate);
    const year = startDate.getFullYear();

    // Find existing schedule or create new one
    let weeklySchedule = await WeeklySchedule.findOne({
      agencyId,
      year,
      weekNumber,
    });

    if (weeklySchedule) {
      // Update existing schedule
      weeklySchedule.weekStartDate = startDate;
      weeklySchedule.weekEndDate = endDate;
      weeklySchedule.description = description;
      weeklySchedule.updatedAt = new Date();
    } else {
      // Create new schedule
      weeklySchedule = new WeeklySchedule({
        agencyId,
        weekStartDate: startDate,
        weekEndDate: endDate,
        weekNumber,
        year,
        description: description || "",
        shifts: [], // Start with empty shifts array
        createdBy,
        createdByModel,
      });
    }

    await weeklySchedule.save();

    res.status(200).json({
      success: true,
      message: "Weekly schedule template created successfully",
      data: weeklySchedule,
    });
  } catch (error) {
    console.error("Error creating/updating weekly schedule:", error);
    res.status(500).json({
      success: false,
      message: "Failed to save weekly schedule",
      error: error.message,
    });
  }
};

// Update existing weekly schedule
export const updateWeeklySchedule = async (req, res) => {
  try {
    const { scheduleId } = req.params;
    const { weekStartDate, weekEndDate, description } = req.body;
    const agencyId =
      req.user.role === "employee" ? req.user.agencyId : req.user._id;

    // Find the existing schedule
    const weeklySchedule = await WeeklySchedule.findOne({
      _id: scheduleId,
      agencyId,
    });

    if (!weeklySchedule) {
      return res.status(404).json({
        success: false,
        message: "Weekly schedule not found",
      });
    }

    // Update the schedule fields
    if (weekStartDate) {
      const startDate = new Date(weekStartDate);
      weeklySchedule.weekStartDate = startDate;
      weeklySchedule.weekNumber = getWeek(startDate);
      weeklySchedule.year = startDate.getFullYear();
    }

    if (weekEndDate) {
      weeklySchedule.weekEndDate = new Date(weekEndDate);
    }

    if (description !== undefined) {
      weeklySchedule.description = description;
    }

    weeklySchedule.updatedAt = new Date();

    await weeklySchedule.save();
    await weeklySchedule.populate("shifts.employeeId", "name email role");

    res.status(200).json({
      success: true,
      message: "Weekly schedule updated successfully",
      data: weeklySchedule,
    });
  } catch (error) {
    console.error("Error updating weekly schedule:", error);
    res.status(500).json({
      success: false,
      message: "Failed to update weekly schedule",
      error: error.message,
    });
  }
};

// Get weekly schedule by week
export const getWeeklySchedule = async (req, res) => {
  try {
    const { year, weekNumber } = req.params;
    const agencyId =
      req.user.role === "employee" ? req.user.agencyId : req.user._id;

    const weeklySchedule = await WeeklySchedule.findByWeek(
      agencyId,
      parseInt(year),
      parseInt(weekNumber),
    );

    if (!weeklySchedule) {
      return res.status(404).json({
        success: false,
        message: "Weekly schedule not found",
      });
    }

    res.status(200).json({
      success: true,
      data: weeklySchedule,
    });
  } catch (error) {
    console.error("Error fetching weekly schedule:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch weekly schedule",
      error: error.message,
    });
  }
};

// Get weekly schedule by date
export const getWeeklyScheduleByDate = async (req, res) => {
  try {
    const { date } = req.params;
    const agencyId =
      req.user.role === "employee" ? req.user.agencyId : req.user._id;

    const targetDate = new Date(date);
    const weekNumber = getWeek(targetDate);
    const year = targetDate.getFullYear();

    const weeklySchedule = await WeeklySchedule.findByWeek(
      agencyId,
      year,
      weekNumber,
    );

    if (!weeklySchedule) {
      // Return empty schedule template for the week
      const weekStart = startOfWeek(targetDate, { weekStartsOn: 1 }); // Monday
      const emptySchedule = {
        agencyId,
        weekStartDate: weekStart,
        weekEndDate: endOfWeek(weekStart),
        weekNumber,
        year,
        status: "draft",
        shifts: [],
        weekIdentifier: `${year}-W${weekNumber.toString().padStart(2, "0")}`,
      };

      return res.status(200).json({
        success: true,
        data: emptySchedule,
      });
    }

    res.status(200).json({
      success: true,
      data: weeklySchedule,
    });
  } catch (error) {
    console.error("Error fetching weekly schedule by date:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch weekly schedule",
      error: error.message,
    });
  }
};

// Get all weekly schedules for an agency
export const getAllWeeklySchedules = async (req, res) => {
  try {
    const agencyId =
      req.user.role === "employee" ? req.user.agencyId : req.user._id;
    const { page = 1, limit = 10, status } = req.query;

    const query = { agencyId };
    if (status) {
      query.status = status;
    }

    const weeklySchedules = await WeeklySchedule.find(query)
      .populate("shifts.employeeId", "name email role")
      .populate("createdBy", "firstName lastName email")
      .sort({ weekStartDate: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await WeeklySchedule.countDocuments(query);

    res.status(200).json({
      success: true,
      data: weeklySchedules,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        itemsPerPage: parseInt(limit),
      },
    });
  } catch (error) {
    console.error("Error fetching weekly schedules:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch weekly schedules",
      error: error.message,
    });
  }
};

// Assign employee to shift
export const assignEmployeeToShift = async (req, res) => {
  try {
    const { scheduleId, shiftIndex, employeeId } = req.body;
    const agencyId =
      req.user.role === "employee" ? req.user.agencyId : req.user._id;

    const weeklySchedule = await WeeklySchedule.findOne({
      _id: scheduleId,
      agencyId,
    });

    if (!weeklySchedule) {
      return res.status(404).json({
        success: false,
        message: "Weekly schedule not found",
      });
    }

    if (shiftIndex >= weeklySchedule.shifts.length) {
      return res.status(400).json({
        success: false,
        message: "Invalid shift index",
      });
    }

    // Verify employee belongs to agency
    if (employeeId) {
      const employee = await Employee.findOne({
        _id: employeeId,
        agencyId,
      });

      if (!employee) {
        return res.status(400).json({
          success: false,
          message: "Employee not found or does not belong to this agency",
        });
      }
    }

    weeklySchedule.shifts[shiftIndex].employeeId = employeeId || null;
    await weeklySchedule.save();
    await weeklySchedule.populate("shifts.employeeId", "name email role");

    res.status(200).json({
      success: true,
      message: employeeId
        ? "Employee assigned successfully"
        : "Employee unassigned successfully",
      data: weeklySchedule,
    });
  } catch (error) {
    console.error("Error assigning employee to shift:", error);
    res.status(500).json({
      success: false,
      message: "Failed to assign employee to shift",
      error: error.message,
    });
  }
};

// Publish weekly schedule
export const publishWeeklySchedule = async (req, res) => {
  try {
    const { scheduleId } = req.params;
    const agencyId =
      req.user.role === "employee" ? req.user.agencyId : req.user._id;

    const weeklySchedule = await WeeklySchedule.findOne({
      _id: scheduleId,
      agencyId,
    });

    if (!weeklySchedule) {
      return res.status(404).json({
        success: false,
        message: "Weekly schedule not found",
      });
    }

    weeklySchedule.status = "published";
    await weeklySchedule.save();

    res.status(200).json({
      success: true,
      message: "Weekly schedule published successfully",
      data: weeklySchedule,
    });
  } catch (error) {
    console.error("Error publishing weekly schedule:", error);
    res.status(500).json({
      success: false,
      message: "Failed to publish weekly schedule",
      error: error.message,
    });
  }
};

// Delete weekly schedule
export const deleteWeeklySchedule = async (req, res) => {
  try {
    const { scheduleId } = req.params;
    const agencyId =
      req.user.role === "employee" ? req.user.agencyId : req.user._id;

    const weeklySchedule = await WeeklySchedule.findOneAndDelete({
      _id: scheduleId,
      agencyId,
    });

    if (!weeklySchedule) {
      return res.status(404).json({
        success: false,
        message: "Weekly schedule not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "Weekly schedule deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting weekly schedule:", error);
    res.status(500).json({
      success: false,
      message: "Failed to delete weekly schedule",
      error: error.message,
    });
  }
};

// Add timespan/shift to weekly schedule
export const addTimespan = async (req, res) => {
  try {
    const {
      weekStartDate,
      day,
      startTime,
      endTime,
      position,
      employeeId,
      isClosingShift,
      breakMinutes,
    } = req.body;

    const agencyId =
      req.user.role === "employee" ? req.user.agencyId : req.user._id;
    const createdBy = req.user._id;
    const createdByModel = req.user.role === "agency" ? "Agency" : "Employee";

    // Calculate the date for the specific day
    const startDate = new Date(weekStartDate);
    const dayIndex = [
      "Monday",
      "Tuesday",
      "Wednesday",
      "Thursday",
      "Friday",
      "Saturday",
      "Sunday",
    ].indexOf(day);
    const shiftDate = addDays(startDate, dayIndex);

    const weekNumber = getWeek(startDate);
    const year = startDate.getFullYear();
    const endDate = endOfWeek(startDate, { weekStartsOn: 1 });

    // Find or create weekly schedule
    let weeklySchedule = await WeeklySchedule.findOne({
      agencyId,
      year,
      weekNumber,
    });

    if (!weeklySchedule) {
      // Create new schedule
      weeklySchedule = new WeeklySchedule({
        agencyId,
        weekStartDate: startDate,
        weekEndDate: endDate,
        weekNumber,
        year,
        description: "",
        shifts: [],
        createdBy,
        createdByModel,
      });
    }

    // Verify employee belongs to agency if provided
    if (employeeId) {
      const employee = await Employee.findOne({
        _id: employeeId,
        agencyId,
      });

      if (!employee) {
        return res.status(400).json({
          success: false,
          message: "Employee not found or does not belong to this agency",
        });
      }
    }

    // Create new shift
    const newShift = {
      day,
      date: shiftDate,
      startTime,
      endTime,
      position: position || "Employee",
      employeeId: employeeId || null,
      isClosingShift: isClosingShift || false,
      breakMinutes: breakMinutes || 0,
    };

    weeklySchedule.shifts.push(newShift);

    // Update status to 'updated' if schedule has at least one shift
    if (weeklySchedule.shifts.length > 0 && weeklySchedule.status === "draft") {
      weeklySchedule.status = "updated";
    }

    await weeklySchedule.save();
    await weeklySchedule.populate("shifts.employeeId", "name email role");

    res.status(201).json({
      success: true,
      message: "Timespan added successfully",
      data: weeklySchedule,
    });
  } catch (error) {
    console.error("Error adding timespan:", error);
    res.status(500).json({
      success: false,
      message: "Failed to add timespan",
      error: error.message,
    });
  }
};

// Update timespan/shift
export const updateTimespan = async (req, res) => {
  try {
    const { scheduleId, shiftId } = req.params;
    const {
      day,
      startTime,
      endTime,
      position,
      employeeId,
      isClosingShift,
      breakMinutes,
    } = req.body;

    const agencyId =
      req.user.role === "employee" ? req.user.agencyId : req.user._id;

    const weeklySchedule = await WeeklySchedule.findOne({
      _id: scheduleId,
      agencyId,
    });

    if (!weeklySchedule) {
      return res.status(404).json({
        success: false,
        message: "Weekly schedule not found",
      });
    }

    const shiftIndex = weeklySchedule.shifts.findIndex(
      (shift) => shift._id.toString() === shiftId,
    );
    if (shiftIndex === -1) {
      return res.status(404).json({
        success: false,
        message: "Shift not found",
      });
    }

    // Verify employee belongs to agency if provided
    if (employeeId) {
      const employee = await Employee.findOne({
        _id: employeeId,
        agencyId,
      });

      if (!employee) {
        return res.status(400).json({
          success: false,
          message: "Employee not found or does not belong to this agency",
        });
      }
    }

    // Update shift
    const shift = weeklySchedule.shifts[shiftIndex];
    if (day) {
      shift.day = day;
      // Recalculate date if day changed
      const dayIndex = [
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday",
        "Sunday",
      ].indexOf(day);
      shift.date = addDays(weeklySchedule.weekStartDate, dayIndex);
    }
    if (startTime) shift.startTime = startTime;
    if (endTime) shift.endTime = endTime;
    if (position) shift.position = position;
    if (employeeId !== undefined) shift.employeeId = employeeId || null;
    if (isClosingShift !== undefined) shift.isClosingShift = isClosingShift;
    if (breakMinutes !== undefined) shift.breakMinutes = breakMinutes;

    // Update status to 'updated' if schedule has at least one shift
    if (weeklySchedule.shifts.length > 0 && weeklySchedule.status === "draft") {
      weeklySchedule.status = "updated";
    }

    await weeklySchedule.save();
    await weeklySchedule.populate("shifts.employeeId", "name email role");

    res.status(200).json({
      success: true,
      message: "Timespan updated successfully",
      data: weeklySchedule,
    });
  } catch (error) {
    console.error("Error updating timespan:", error);
    res.status(500).json({
      success: false,
      message: "Failed to update timespan",
      error: error.message,
    });
  }
};

// Delete timespan/shift
export const deleteTimespan = async (req, res) => {
  try {
    const { scheduleId, shiftId } = req.params;
    const agencyId =
      req.user.role === "employee" ? req.user.agencyId : req.user._id;

    const weeklySchedule = await WeeklySchedule.findOne({
      _id: scheduleId,
      agencyId,
    });

    if (!weeklySchedule) {
      return res.status(404).json({
        success: false,
        message: "Weekly schedule not found",
      });
    }

    const shiftIndex = weeklySchedule.shifts.findIndex(
      (shift) => shift._id.toString() === shiftId,
    );
    if (shiftIndex === -1) {
      return res.status(404).json({
        success: false,
        message: "Shift not found",
      });
    }

    weeklySchedule.shifts.splice(shiftIndex, 1);

    // Update status back to 'draft' if no shifts remain
    if (
      weeklySchedule.shifts.length === 0 &&
      weeklySchedule.status === "updated"
    ) {
      weeklySchedule.status = "draft";
    }

    await weeklySchedule.save();
    await weeklySchedule.populate("shifts.employeeId", "name email role");

    res.status(200).json({
      success: true,
      message: "Timespan deleted successfully",
      data: weeklySchedule,
    });
  } catch (error) {
    console.error("Error deleting timespan:", error);
    res.status(500).json({
      success: false,
      message: "Failed to delete timespan",
      error: error.message,
    });
  }
};

// Get employee schedules
export const getEmployeeSchedules = async (req, res) => {
  try {
    const { employeeId } = req.params;
    const { startDate, endDate } = req.query;
    const agencyId =
      req.user.role === "employee" ? req.user.agencyId : req.user._id;

    const query = {
      agencyId,
      "shifts.employeeId": employeeId,
    };

    if (startDate && endDate) {
      query.weekStartDate = { $gte: new Date(startDate) };
      query.weekEndDate = { $lte: new Date(endDate) };
    }

    const schedules = await WeeklySchedule.find(query)
      .populate("shifts.employeeId", "name email role")
      .sort({ weekStartDate: 1 });

    // Filter shifts to only include those assigned to the specific employee
    const employeeSchedules = schedules.map((schedule) => ({
      ...schedule.toObject(),
      shifts: schedule.shifts.filter(
        (shift) =>
          shift.employeeId && shift.employeeId._id.toString() === employeeId,
      ),
    }));

    res.status(200).json({
      success: true,
      message:
        employeeSchedules.length > 0
          ? "Employee schedules retrieved successfully"
          : "No schedules found for this employee",
      data: employeeSchedules,
    });
  } catch (error) {
    console.error("Error fetching employee schedules:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch employee schedules. Please try again later.",
      error:
        process.env.NODE_ENV === "development"
          ? error.message
          : "Internal server error",
    });
  }
};

// Get current employee's own schedules (for employees to access their own shifts)
export const getMySchedules = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    const employeeId = req.user._id;
    const agencyId = req.user.agencyId;

    if (req.user.role !== "employee") {
      return res.status(403).json({
        success: false,
        message: "This endpoint is only for employees",
      });
    }

    const query = {
      agencyId,
      "shifts.employeeId": employeeId,
    };

    // If date range is provided, filter schedules that overlap with the date range
    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      query.$or = [
        // Schedule starts within the date range
        { weekStartDate: { $gte: start, $lte: end } },
        // Schedule ends within the date range
        { weekEndDate: { $gte: start, $lte: end } },
        // Schedule spans the entire date range
        { weekStartDate: { $lte: start }, weekEndDate: { $gte: end } },
      ];
    }

    const schedules = await WeeklySchedule.find(query)
      .populate("shifts.employeeId", "name email role")
      .sort({ weekStartDate: 1 });

    // Filter shifts to only include those assigned to the current employee
    // and optionally filter by date range if provided
    const employeeSchedules = schedules
      .map((schedule) => {
        let filteredShifts = schedule.shifts.filter(
          (shift) =>
            shift.employeeId &&
            shift.employeeId._id.toString() === employeeId.toString(),
        );

        // If date range is provided, also filter shifts by their individual dates
        if (startDate && endDate) {
          const start = new Date(startDate);
          const end = new Date(endDate);
          filteredShifts = filteredShifts.filter((shift) => {
            const shiftDate = new Date(shift.date);
            return shiftDate >= start && shiftDate <= end;
          });
        }

        return {
          ...schedule.toObject(),
          shifts: filteredShifts,
        };
      })
      .filter((schedule) => schedule.shifts.length > 0); // Only return schedules with shifts

    res.status(200).json({
      success: true,
      message:
        employeeSchedules.length > 0
          ? "Your schedules retrieved successfully"
          : "No schedules found",
      data: employeeSchedules,
    });
  } catch (error) {
    console.error("Error fetching employee schedules:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch your schedules. Please try again later.",
      error:
        process.env.NODE_ENV === "development"
          ? error.message
          : "Internal server error",
    });
  }
};

// Add comment to a shift
export const addShiftComment = async (req, res) => {
  try {
    const { scheduleId, shiftId } = req.params;
    const { content } = req.body;
    const userId = req.user._id;
    const userRole = req.user.role;
    const agencyId = userRole === "employee" ? req.user.agencyId : req.user._id;

    if (!content || content.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: "Comment content is required",
      });
    }

    // Find the weekly schedule
    const weeklySchedule = await WeeklySchedule.findById(scheduleId);
    if (!weeklySchedule) {
      return res.status(404).json({
        success: false,
        message: "Weekly schedule not found",
      });
    }

    // Check if user has access to this schedule
    if (!weeklySchedule.agencyId.equals(agencyId)) {
      return res.status(403).json({
        success: false,
        message: "Access denied",
      });
    }

    // Find the specific shift
    const shift = weeklySchedule.shifts.id(shiftId);
    if (!shift) {
      return res.status(404).json({
        success: false,
        message: "Shift not found",
      });
    }

    // Add the comment
    const newComment = {
      author: userId,
      authorModel: userRole === "employee" ? "Employee" : "Agency",
      content: content.trim(),
      createdAt: new Date(),
    };

    shift.comments.push(newComment);
    await weeklySchedule.save();

    // Populate the author information for the response
    await weeklySchedule.populate({
      path: "shifts.comments.author",
      select: "firstName lastName agencyName",
    });

    const addedComment = shift.comments[shift.comments.length - 1];

    res.status(201).json({
      success: true,
      message: "Comment added successfully",
      data: addedComment,
    });
  } catch (error) {
    console.error("Error adding shift comment:", error);
    res.status(500).json({
      success: false,
      message: "Failed to add comment. Please try again later.",
      error:
        process.env.NODE_ENV === "development"
          ? error.message
          : "Internal server error",
    });
  }
};

// Get comments for a shift
export const getShiftComments = async (req, res) => {
  try {
    const { scheduleId, shiftId } = req.params;
    const userId = req.user._id;
    const userRole = req.user.role;
    const agencyId = userRole === "employee" ? req.user.agencyId : req.user._id;

    // Find the weekly schedule
    const weeklySchedule = await WeeklySchedule.findById(scheduleId).populate({
      path: "shifts.comments.author",
      select: "firstName lastName agencyName",
    });

    if (!weeklySchedule) {
      return res.status(404).json({
        success: false,
        message: "Weekly schedule not found",
      });
    }

    // Check if user has access to this schedule
    if (!weeklySchedule.agencyId.equals(agencyId)) {
      return res.status(403).json({
        success: false,
        message: "Access denied",
      });
    }

    // Find the specific shift
    const shift = weeklySchedule.shifts.id(shiftId);
    if (!shift) {
      return res.status(404).json({
        success: false,
        message: "Shift not found",
      });
    }

    // Sort comments by creation date (newest first)
    const sortedComments = shift.comments.sort(
      (a, b) => new Date(b.createdAt) - new Date(a.createdAt),
    );

    res.status(200).json({
      success: true,
      message: "Comments retrieved successfully",
      data: sortedComments,
    });
  } catch (error) {
    console.error("Error fetching shift comments:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch comments. Please try again later.",
      error:
        process.env.NODE_ENV === "development"
          ? error.message
          : "Internal server error",
    });
  }
};

// Delete a comment from a shift
export const deleteShiftComment = async (req, res) => {
  try {
    const { scheduleId, shiftId, commentId } = req.params;
    const userId = req.user._id;
    const userRole = req.user.role;
    const agencyId = userRole === "employee" ? req.user.agencyId : req.user._id;

    // Find the weekly schedule
    const weeklySchedule = await WeeklySchedule.findById(scheduleId);
    if (!weeklySchedule) {
      return res.status(404).json({
        success: false,
        message: "Weekly schedule not found",
      });
    }

    // Check if user has access to this schedule
    if (!weeklySchedule.agencyId.equals(agencyId)) {
      return res.status(403).json({
        success: false,
        message: "Access denied",
      });
    }

    // Find the specific shift
    const shift = weeklySchedule.shifts.id(shiftId);
    if (!shift) {
      return res.status(404).json({
        success: false,
        message: "Shift not found",
      });
    }

    // Find the comment
    const comment = shift.comments.id(commentId);
    if (!comment) {
      return res.status(404).json({
        success: false,
        message: "Comment not found",
      });
    }

    // Check if user can delete this comment (only author or agency can delete)
    const canDelete = comment.author.equals(userId) || userRole === "agency";
    if (!canDelete) {
      return res.status(403).json({
        success: false,
        message: "You can only delete your own comments",
      });
    }

    // Remove the comment
    shift.comments.pull(commentId);
    await weeklySchedule.save();

    res.status(200).json({
      success: true,
      message: "Comment deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting shift comment:", error);
    res.status(500).json({
      success: false,
      message: "Failed to delete comment. Please try again later.",
      error:
        process.env.NODE_ENV === "development"
          ? error.message
          : "Internal server error",
    });
  }
};
