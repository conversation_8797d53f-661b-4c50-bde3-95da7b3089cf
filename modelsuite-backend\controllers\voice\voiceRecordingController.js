import VoiceRecording from "../../models/voice/VoiceRecording.js";
import VoiceAssignment from "../../models/voice/VoiceAssignment.js";
import VoiceScript from "../../models/voice/VoiceScript.js";
import VoiceDownloadToken from "../../models/voice/VoiceDownloadToken.js";
import ModelUser from "../../models/model.js";
import { asyncHandler } from "../../utils/asyncHandler.js";
import { ApiError } from "../../utils/ApiError.js";
import { ApiResponse } from "../../utils/ApiResponse.js";
import jwt from "jsonwebtoken";
import {
  uploadOnCloudinary,
  deleteFromCloudinary,
} from "../../config/cloudinary.js";
import ffmpegConfig from "../../config/ffmpegConfig.js";
import mongoose from "mongoose";
import fs from "fs";
import path from "path";
import ffmpeg from "fluent-ffmpeg";
import { promisify } from "util";

const unlinkAsync = promisify(fs.unlink);

// Initialize FFmpeg configuration
let ffmpegInitialized = false;
const initializeFFmpeg = async () => {
  if (!ffmpegInitialized) {
    try {
      await ffmpegConfig.initialize();
      ffmpegInitialized = true;
    } catch (error) {
      throw new ApiError(
        500,
        `Audio processing not available: ${error.message}. Please install FFmpeg or configure FFMPEG_PATH/FFPROBE_PATH environment variables.`,
      );
    }
  }
};

/**
 * Upload voice recording for an assignment
 * @route POST /api/v1/voice/recordings/upload/:assignmentId
 * @access Model only
 */
export const uploadRecording = asyncHandler(async (req, res) => {
  // Initialize FFmpeg if not already done
  await initializeFFmpeg();

  const { assignmentId, questionId } = req.params;
  const {
    deviceInfo,
    browserInfo,
    sessionNotes,
    sentenceId,
    sentenceIndex,
    completedPrompt,
    recordingType,
  } = req.body;

  if (!mongoose.Types.ObjectId.isValid(assignmentId)) {
    throw new ApiError(400, "Invalid assignment ID");
  }

  if (req.user.role !== "model") {
    throw new ApiError(403, "Only models can upload recordings");
  }

  if (!req.file) {
    throw new ApiError(400, "Audio file is required");
  }

  // Validate assignment (question-based only)
  const assignment = await VoiceAssignment.findById(assignmentId)
    .populate("questionIds")
    .populate("agencyId", "agencyName");

  if (!assignment || assignment.isDeleted) {
    throw new ApiError(404, "Assignment not found");
  }

  if (assignment.modelId.toString() !== req.user._id.toString()) {
    throw new ApiError(403, "Access denied - assignment not assigned to you");
  }

  if (!["assigned", "in_progress", "recorded"].includes(assignment.status)) {
    throw new ApiError(
      400,
      `Cannot upload recording for assignment in current status: ${assignment.status}. Expected: assigned, in_progress, or recorded`,
    );
  }

  // Validate question-based assignment
  if (!assignment.questionIds || assignment.questionIds.length === 0) {
    throw new ApiError(400, "Assignment must have questions");
  }

  if (!questionId) {
    throw new ApiError(400, "Question ID is required");
  }

  if (!mongoose.Types.ObjectId.isValid(questionId)) {
    throw new ApiError(400, "Invalid question ID");
  }

  // Check if the questionId exists in the assignment's questionIds
  const question = assignment.questionIds.find(
    (q) => q._id.toString() === questionId,
  );

  if (!question) {
    throw new ApiError(404, "Question not found in this assignment");
  }

  try {
    // Validate req.file and req.file.path
    if (!req.file) {
      throw new ApiError(400, "No audio file uploaded");
    }

    // Handle memory storage - create temp file from buffer
    let tempFilePath;
    let shouldCleanupTempFile = false;

    if (req.file.buffer) {
      // Memory storage - create temporary file
      const tempDir = path.join(process.cwd(), "public", "temp");

      // Ensure temp directory exists
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      const tempFileName = `temp_${Date.now()}_${Math.random().toString(36).substring(7)}.${req.file.originalname.split(".").pop()}`;
      tempFilePath = path.join(tempDir, tempFileName);

      // Write buffer to temporary file
      fs.writeFileSync(tempFilePath, req.file.buffer);
      shouldCleanupTempFile = true;
    } else if (req.file.path) {
      // Disk storage - use existing path
      tempFilePath = req.file.path;
    } else {
      throw new ApiError(
        400,
        "Invalid file upload - no buffer or path available",
      );
    }

    // Get audio file metadata using ffmpeg
    const audioMetadata = await getAudioMetadata(tempFilePath);

    // Basic validation - check if audio file has valid metadata
    if (!audioMetadata) {
      throw new ApiError(
        400,
        "Invalid audio file - unable to extract metadata",
      );
    }

    // Check duration - be more permissive for WebM files
    const isWebM = req.file.mimetype === "audio/webm";
    const duration = audioMetadata.duration;

    if (duration && !isNaN(duration)) {
      // For question recordings, allow very short recordings (minimum 0.1 seconds)
      const minDuration = 0.1;

      if (duration < minDuration) {
        throw new ApiError(
          400,
          `Invalid audio file - duration too short (minimum ${minDuration} seconds)`,
        );
      }
    } else if (!isWebM) {
      // Only require duration for non-WebM files
      throw new ApiError(
        400,
        "Invalid audio file - no duration information available",
      );
    }

    // Generate filename for question recording
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const filename = `question_${questionId}_${req.user.username}_${timestamp}.${audioMetadata.format || "webm"}`;

    // Upload to Cloudinary
    const cloudinaryResult = await uploadOnCloudinary(
      tempFilePath,
      `voice-recordings/${req.user.username}`,
      {
        resource_type: "video", // Cloudinary uses 'video' for audio files
        public_id: filename.replace(/\.[^/.]+$/, ""), // Remove extension
      },
    );

    if (!cloudinaryResult) {
      throw new ApiError(500, "Failed to upload audio file");
    }

    // Map format for database storage based on MIME type (more reliable than FFmpeg format detection)
    // Default to 'mp3' for unsupported formats since schema only allows: mp3, wav, m4a, aac, ogg, flac
    let dbFormat = "mp3"; // safe default
    if (
      req.file.mimetype.includes("mp3") ||
      req.file.mimetype.includes("mpeg")
    ) {
      dbFormat = "mp3";
    } else if (req.file.mimetype.includes("wav")) {
      dbFormat = "wav";
    } else if (req.file.mimetype.includes("ogg")) {
      dbFormat = "ogg";
    } else if (
      req.file.mimetype.includes("m4a") ||
      req.file.mimetype.includes("mp4")
    ) {
      dbFormat = "m4a";
    } else if (req.file.mimetype.includes("aac")) {
      dbFormat = "aac";
    } else if (req.file.mimetype.includes("flac")) {
      dbFormat = "flac";
    }
    // Note: webm is not supported by schema, so it defaults to mp3

    // Check for existing recording for this specific question
    let existingRecording = await VoiceRecording.findOne({
      assignmentId,
      questionId,
      isDeleted: false,
    });

    let recording;

    if (existingRecording) {
      // Create new version for question recording
      const versionData = {
        originalFilename: req.file.originalname,
        generatedFilename: filename,
        fileUrl: cloudinaryResult.secure_url,
        cloudinaryPublicId: cloudinaryResult.public_id,
        fileSize: req.file.size,
        duration: audioMetadata.duration,
        format: dbFormat, // Use mapped format instead of raw metadata format
        mimeType: req.file.mimetype,
        bitrate: audioMetadata.bitrate,
        sampleRate: audioMetadata.sampleRate,
        channels: audioMetadata.channels,
        codec: audioMetadata.codec,
        recordingStartTime: new Date(
          Date.now() - audioMetadata.duration * 1000,
        ),
        recordingEndTime: new Date(),
        deviceInfo,
        browserInfo,
        sessionNotes,
        uploadCompletedAt: new Date(),
        submittedAt: new Date(),
        status: "submitted",
        questionTags: question.tags || [], // Include question tags
      };

      recording = await existingRecording.createNewVersion(versionData);
    } else {
      // Create new question recording
      const recordingData = {
        assignmentId,
        questionId,
        questionText: question.text,
        questionTags: question.tags || [], // Include question tags
        modelId: req.user._id,
        agencyId: assignment.agencyId._id,
        originalFilename: req.file.originalname,
        generatedFilename: filename,
        fileUrl: cloudinaryResult.secure_url,
        cloudinaryPublicId: cloudinaryResult.public_id,
        fileSize: req.file.size,
        duration: audioMetadata.duration,
        format: dbFormat, // Use mapped format instead of raw metadata format
        mimeType: req.file.mimetype,
        bitrate: audioMetadata.bitrate,
        sampleRate: audioMetadata.sampleRate,
        channels: audioMetadata.channels,
        codec: audioMetadata.codec,
        recordingStartTime: new Date(
          Date.now() - audioMetadata.duration * 1000,
        ),
        recordingEndTime: new Date(),
        deviceInfo,
        browserInfo,
        sessionNotes,
        uploadCompletedAt: new Date(),
        submittedAt: new Date(),
        status: "submitted",
        version: 1,
      };

      recording = await VoiceRecording.create(recordingData);
    }

    // Format bitrate for schema validation (must be like '128kbps')
    let formattedBitrate = null;
    if (audioMetadata.bitrate && !isNaN(audioMetadata.bitrate)) {
      // Convert bitrate to kbps format if it's a number
      const bitrateNum = parseInt(audioMetadata.bitrate);
      if (bitrateNum > 0) {
        formattedBitrate = `${bitrateNum}kbps`;
      }
    } else if (
      typeof audioMetadata.bitrate === "string" &&
      audioMetadata.bitrate.includes("kbps")
    ) {
      formattedBitrate = audioMetadata.bitrate;
    } else {
      // Default fallback bitrate for schema validation
      formattedBitrate = "128kbps";
    }

    // Update assignment with recording metadata (use updateOne to avoid validation issues)
    const updateData = {
      recordingUrl: cloudinaryResult.secure_url,
      recordingDuration: audioMetadata.duration,
      recordingMetadata: {
        fileSize: req.file.size,
        format: dbFormat, // Use mapped format instead of raw metadata format
        bitrate: formattedBitrate, // Use formatted bitrate
        sampleRate: audioMetadata.sampleRate,
        channels: audioMetadata.channels,
      },
    };

    // Update assignment status if it was just assigned
    if (assignment.status === "assigned") {
      updateData.status = "in_progress";
      updateData.startedAt = new Date();
    }

    await VoiceAssignment.updateOne(
      { _id: assignmentId },
      { $set: updateData },
    );

    // Clean up temporary file
    try {
      if (fs.existsSync(req.file.path)) {
        await unlinkAsync(req.file.path);
      }
    } catch (cleanupError) {
      console.error("Error cleaning up temp file:", cleanupError);
    }

    res.status(201).json(
      new ApiResponse(
        201,
        {
          recording,
          assignment: {
            id: assignmentId,
            status: updateData.status || assignment.status,
            recordingUrl: updateData.recordingUrl,
          },
        },
        "Recording uploaded successfully",
      ),
    );
  } catch (error) {
    // Clean up temporary file on error
    if (req.file && req.file.path) {
      try {
        if (fs.existsSync(req.file.path)) {
          await unlinkAsync(req.file.path);
        }
      } catch (unlinkError) {
        console.error("Error cleaning up temp file:", unlinkError);
      }
    }
    throw error;
  }
});

/**
 * Get all recordings (optionally filtered by assignmentId query param)
 * @route GET /api/v1/voice/recordings
 * @access Model and Agency
 */
export const getAssignmentRecordings = asyncHandler(async (req, res) => {
  const { assignmentId } = req.query;

  if (assignmentId && !mongoose.Types.ObjectId.isValid(assignmentId)) {
    throw new ApiError(400, "Invalid assignment ID");
  }

  // Validate assignment access
  const assignment = assignmentId
    ? await VoiceAssignment.findById(assignmentId)
    : null;

  if (assignmentId && (!assignment || assignment.isDeleted)) {
    throw new ApiError(404, "Assignment not found");
  }

  // Check access permissions
  if (req.user.role === "model") {
    if (
      assignment &&
      assignment.modelId.toString() !== req.user._id.toString()
    ) {
      throw new ApiError(403, "Access denied");
    }
  } else if (req.user.role === "agency") {
    if (
      assignment &&
      assignment.agencyId.toString() !== req.user._id.toString()
    ) {
      throw new ApiError(403, "Access denied");
    }
  } else {
    throw new ApiError(403, "Access denied");
  }

  // Get recordings
  const recordings = await VoiceRecording.find({
    assignmentId,
    isDeleted: false,
  })
    .sort({ version: -1, uploadCompletedAt: -1 })
    .lean();

  // Add formatted data
  const recordingsWithDetails = recordings.map((recording) => ({
    ...recording,
    formattedFileSize: formatFileSize(recording.fileSize),
    formattedDuration: formatDuration(recording.duration),
    qualityScore: calculateQualityScore(recording),
  }));

  res
    .status(200)
    .json(
      new ApiResponse(
        200,
        recordingsWithDetails,
        "Recordings retrieved successfully",
      ),
    );
});

/**
 * Get a single recording by ID
 * @route GET /api/v1/voice/recordings/:recordingId
 * @access Model and Agency
 */
export const getRecordingById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  if (!mongoose.Types.ObjectId.isValid(id)) {
    throw new ApiError(400, "Invalid recording ID");
  }

  const recording = await VoiceRecording.findById(id)
    .populate({
      path: "assignmentId",
      select: "status deadline priority",
      populate: {
        path: "scriptId",
        select: "title category",
      },
    })
    .populate("modelId", "fullName username")
    .populate("agencyId", "agencyName");

  if (!recording || recording.isDeleted) {
    throw new ApiError(404, "Recording not found");
  }

  // Check access permissions
  if (req.user.role === "model") {
    if (recording.modelId._id.toString() !== req.user._id.toString()) {
      throw new ApiError(403, "Access denied");
    }
  } else if (req.user.role === "agency") {
    if (recording.agencyId._id.toString() !== req.user._id.toString()) {
      throw new ApiError(403, "Access denied");
    }
  } else {
    throw new ApiError(403, "Access denied");
  }

  // Mark as accessed (for analytics)
  await recording.markAccessed();

  res.status(200).json(
    new ApiResponse(
      200,
      {
        recording,
        formattedFileSize: formatFileSize(recording.fileSize),
        formattedDuration: formatDuration(recording.duration),
        qualityScore: recording.calculateQualityScore(),
      },
      "Recording retrieved successfully",
    ),
  );
});

/**
 * Delete a recording (soft delete)
 * @route DELETE /api/v1/voice/recordings/:recordingId
 * @access Model and Agency
 */
export const deleteRecording = asyncHandler(async (req, res) => {
  const { id } = req.params;

  if (!mongoose.Types.ObjectId.isValid(id)) {
    throw new ApiError(400, "Invalid recording ID");
  }

  const recording = await VoiceRecording.findById(id);

  if (!recording || recording.isDeleted) {
    throw new ApiError(404, "Recording not found");
  }

  // Check access permissions
  if (req.user.role === "model") {
    if (recording.modelId.toString() !== req.user._id.toString()) {
      throw new ApiError(403, "Access denied");
    }
  } else if (req.user.role === "agency") {
    if (recording.agencyId.toString() !== req.user._id.toString()) {
      throw new ApiError(403, "Access denied");
    }
  } else {
    throw new ApiError(403, "Access denied");
  }

  // Check if recording is part of submitted/approved assignment
  const assignment = await VoiceAssignment.findById(recording.assignmentId);
  if (assignment && ["submitted", "approved"].includes(assignment.status)) {
    throw new ApiError(
      400,
      "Cannot delete recording from submitted or approved assignment",
    );
  }

  // Soft delete the recording
  recording.isDeleted = true;
  recording.deletedAt = new Date();
  await recording.save();

  // Update assignment if this was the current recording
  if (assignment && assignment.recordingUrl === recording.fileUrl) {
    assignment.recordingUrl = null;
    assignment.recordingDuration = null;
    assignment.recordingMetadata = null;

    // Revert status if needed
    if (assignment.status === "in_progress") {
      assignment.status = "assigned";
      assignment.startedAt = null;
    }

    await assignment.save();
  }

  res
    .status(200)
    .json(new ApiResponse(200, null, "Recording deleted successfully"));
});

/**
 * Debug endpoint - Get all recordings (for debugging)
 * @route GET /api/v1/voice/recordings/debug/all
 * @access Authenticated
 */
export const getAllRecordingsDebug = asyncHandler(async (req, res) => {
  const recordings = await VoiceRecording.find({})
    .select("_id originalFilename status isDeleted createdAt")
    .limit(50)
    .sort({ createdAt: -1 });

  const totalCount = await VoiceRecording.countDocuments();
  const activeCount = await VoiceRecording.countDocuments({ isDeleted: false });
  const deletedCount = await VoiceRecording.countDocuments({ isDeleted: true });

  res.status(200).json(
    new ApiResponse(
      200,
      {
        recordings,
        stats: {
          total: totalCount,
          active: activeCount,
          deleted: deletedCount,
        },
      },
      "Debug: All recordings retrieved",
    ),
  );
});

/**
 * Get all recordings for the current model
 * @route GET /api/v1/voice/recordings/model
 * @access Model only
 */
export const getModelRecordings = asyncHandler(async (req, res) => {
  if (req.user.role !== "model") {
    throw new ApiError(403, "Only models can view their recordings");
  }

  const {
    status,
    page = 1,
    limit = 10,
    sortBy = "uploadCompletedAt",
    sortOrder = "desc",
  } = req.query;

  // Build query
  const query = {
    modelId: req.user._id,
    isDeleted: false,
  };

  if (status) {
    const statusArray = Array.isArray(status) ? status : status.split(",");
    query.status = { $in: statusArray };
  }

  // Calculate pagination
  const skip = (parseInt(page) - 1) * parseInt(limit);

  // Build sort object
  const sort = {};
  sort[sortBy] = sortOrder === "desc" ? -1 : 1;

  // Get recordings with populated data
  const recordings = await VoiceRecording.find(query)
    .populate({
      path: "assignmentId",
      select: "status deadline priority",
      populate: {
        path: "scriptId",
        select: "title category tags",
      },
    })
    .sort(sort)
    .skip(skip)
    .limit(parseInt(limit))
    .lean();

  // Get total count for pagination
  const totalRecordings = await VoiceRecording.countDocuments(query);

  // Add formatted data
  const recordingsWithDetails = recordings.map((recording) => ({
    ...recording,
    formattedFileSize: formatFileSize(recording.fileSize),
    formattedDuration: formatDuration(recording.duration),
    qualityScore: calculateQualityScore(recording),
  }));

  res.status(200).json(
    new ApiResponse(
      200,
      {
        recordings: recordingsWithDetails,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalRecordings / parseInt(limit)),
          totalRecordings,
          hasNext: skip + recordings.length < totalRecordings,
          hasPrev: parseInt(page) > 1,
        },
      },
      "Model recordings retrieved successfully",
    ),
  );
});

/**
 * Get all recordings for the current agency
 * @route GET /api/v1/voice/recordings/agency
 * @access Agency only
 */
export const getAgencyRecordings = asyncHandler(async (req, res) => {
  if (req.user.role !== "agency") {
    throw new ApiError(403, "Only agencies can view their recordings");
  }

  const {
    status,
    scriptId,
    modelId,
    page = 1,
    limit = 10,
    sortBy = "submittedAt",
    sortOrder = "desc",
    search,
  } = req.query;

  console.log(`🔍 getAgencyRecordings called with status: ${status}`);

  // For submitted recordings, get from VoiceAssignment collection
  if (status === "submitted" || status === "all") {
    // Build match criteria for VoiceAssignment
    let matchCriteria = {
      agencyId: req.user._id,
      isDeleted: false,
      status: "submitted", // Assignments with submitted status
      recordings: { $exists: true, $ne: [] }, // Must have recordings
    };

    // Add script filter
    if (scriptId && mongoose.Types.ObjectId.isValid(scriptId)) {
      matchCriteria.scriptId = new mongoose.Types.ObjectId(scriptId);
    }

    // Add model filter
    if (modelId && mongoose.Types.ObjectId.isValid(modelId)) {
      matchCriteria.modelId = new mongoose.Types.ObjectId(modelId);
    }

    // Get assignments and populate references
    const assignments = await VoiceAssignment.find(matchCriteria)
      .populate(
        "scriptId",
        "title description scriptType singleLinePrompt scriptText",
      )
      .populate("modelId", "fullName username email")
      .sort({ submittedAt: -1 })
      .lean();

    // Transform assignments to recordings format for frontend
    const recordingsFromAssignments = [];

    assignments.forEach((assignment, assignmentIndex) => {
      // Each assignment can have multiple recordings (though usually just 1)
      assignment.recordings.forEach((recording, recordingIndex) => {
        recordingsFromAssignments.push({
          _id: `${assignment._id}_recording_${recordingIndex}`, // Create unique ID
          assignmentId: assignment._id,
          scriptId: assignment.scriptId._id,
          modelId: assignment.modelId._id,
          agencyId: assignment.agencyId,
          status: "submitted", // All these are submitted
          audioUrl: recording.audioUrl,
          duration: recording.duration || 0,
          fileSize: recording.fileSize || 0,
          uploadedAt: recording.uploadedAt,
          submittedAt: assignment.submittedAt,
          createdAt: assignment.submittedAt,
          script: assignment.scriptId,
          model: assignment.modelId,
          sentenceId: recording.sentenceId,
          version: recording.version || 1,
          formattedFileSize: formatFileSize(recording.fileSize || 0),
          formattedDuration: formatDuration(recording.duration || 0),
          qualityScore: calculateQualityScore(recording),
        });
      });
    });

    // Apply search filter if provided
    let filteredRecordings = recordingsFromAssignments;
    if (search) {
      filteredRecordings = recordingsFromAssignments.filter(
        (recording) =>
          recording.script.title.toLowerCase().includes(search.toLowerCase()) ||
          recording.model.username
            .toLowerCase()
            .includes(search.toLowerCase()) ||
          recording.model.fullName.toLowerCase().includes(search.toLowerCase()),
      );
    }

    // Apply pagination
    const startIndex = (parseInt(page) - 1) * parseInt(limit);
    const endIndex = startIndex + parseInt(limit);
    const paginatedRecordings = filteredRecordings.slice(startIndex, endIndex);

    return res.status(200).json(
      new ApiResponse(
        200,
        {
          recordings: paginatedRecordings,
          pagination: {
            currentPage: parseInt(page),
            totalPages: Math.ceil(filteredRecordings.length / parseInt(limit)),
            totalRecordings: filteredRecordings.length,
            hasNext: endIndex < filteredRecordings.length,
            hasPrev: parseInt(page) > 1,
          },
        },
        "Agency recordings retrieved successfully",
      ),
    );
  }

  // For approved recordings, get from VoiceRecording collection
  if (status === "approved") {
    let matchCriteria = {
      agencyId: req.user._id,
      isDeleted: false,
      status: "approved",
    };

    // Add model filter
    if (modelId && mongoose.Types.ObjectId.isValid(modelId)) {
      matchCriteria.modelId = new mongoose.Types.ObjectId(modelId);
    }

    // Get approved recordings
    const recordings = await VoiceRecording.find(matchCriteria)
      .populate("assignmentId", "title priority deadline")
      .populate("modelId", "fullName username email")
      .sort({ reviewedAt: -1, uploadCompletedAt: -1 })
      .lean();

    // Apply search filter if provided
    let filteredRecordings = recordings;
    if (search) {
      filteredRecordings = recordings.filter(
        (recording) =>
          recording.questionText
            ?.toLowerCase()
            .includes(search.toLowerCase()) ||
          recording.modelId?.username
            ?.toLowerCase()
            .includes(search.toLowerCase()) ||
          recording.modelId?.fullName
            ?.toLowerCase()
            .includes(search.toLowerCase()),
      );
    }

    // Apply pagination
    const startIndex = (parseInt(page) - 1) * parseInt(limit);
    const endIndex = startIndex + parseInt(limit);
    const paginatedRecordings = filteredRecordings.slice(startIndex, endIndex);

    // Format recordings for frontend
    const formattedRecordings = paginatedRecordings.map((recording) => ({
      ...recording,
      formattedFileSize: formatFileSize(recording.fileSize || 0),
      formattedDuration: formatDuration(recording.duration || 0),
      qualityScore: calculateQualityScore(recording),
      assignment: recording.assignmentId,
      model: recording.modelId,
    }));

    return res.status(200).json(
      new ApiResponse(
        200,
        {
          recordings: formattedRecordings,
          pagination: {
            currentPage: parseInt(page),
            totalPages: Math.ceil(filteredRecordings.length / parseInt(limit)),
            totalRecordings: filteredRecordings.length,
            hasNext: endIndex < filteredRecordings.length,
            hasPrev: parseInt(page) > 1,
          },
        },
        "Approved recordings retrieved successfully",
      ),
    );
  }

  // For other statuses, return empty for now (can be implemented later)
  res.status(200).json(
    new ApiResponse(
      200,
      {
        recordings: [],
        pagination: {
          currentPage: parseInt(page),
          totalPages: 0,
          totalRecordings: 0,
          hasNext: false,
          hasPrev: false,
        },
      },
      "No recordings found for specified status",
    ),
  );
});

// Helper functions

/**
 * Get audio metadata using ffmpeg
 */
function getAudioMetadata(filePath) {
  return new Promise((resolve, reject) => {
    // Validate file path input
    if (!filePath || typeof filePath !== "string") {
      reject(
        new ApiError(
          400,
          "Invalid file path provided to audio metadata extraction",
        ),
      );
      return;
    }

    ffmpeg.ffprobe(filePath, (err, metadata) => {
      if (err) {
        // Check if this is an FFmpeg installation issue
        if (err.message.includes("spawn") || err.message.includes("ENOENT")) {
          reject(
            new ApiError(
              500,
              "FFmpeg not found. Please install FFmpeg or set FFMPEG_PATH and FFPROBE_PATH environment variables.",
            ),
          );
        } else {
          reject(
            new ApiError(400, `Invalid audio file format: ${err.message}`),
          );
        }
        return;
      }

      if (!metadata) {
        reject(new ApiError(400, "Unable to extract audio metadata"));
        return;
      }

      // Extract duration from metadata
      let duration = null;
      if (
        metadata.format &&
        metadata.format.duration &&
        metadata.format.duration !== "N/A"
      ) {
        duration = parseFloat(metadata.format.duration);
      } else if (metadata.streams && metadata.streams.length > 0) {
        // Try to get duration from the first audio stream
        const audioStream = metadata.streams.find(
          (stream) => stream.codec_type === "audio",
        );
        if (
          audioStream &&
          audioStream.duration &&
          audioStream.duration !== "N/A"
        ) {
          duration = parseFloat(audioStream.duration);
        }
      }

      // For WebM files, duration might not be available during recording
      // This is normal for browser-recorded WebM files
      const isWebM =
        metadata.format.format_name &&
        metadata.format.format_name.includes("webm");

      if (!duration || isNaN(duration)) {
        if (isWebM) {
          // For WebM files, estimate duration based on file size and assume reasonable bitrate
          // This is a rough estimate, but better than failing
          const fileSizeBytes = metadata.format.size || 0;
          const estimatedBitrate = 64000; // 64 kbps - reasonable for voice recordings
          duration = (fileSizeBytes * 8) / estimatedBitrate; // Convert to seconds
        } else {
          reject(new ApiError(400, "Unable to determine audio file duration"));
          return;
        }
      }

      const audioStream = metadata.streams.find(
        (stream) => stream.codec_type === "audio",
      );

      if (!audioStream) {
        reject(new ApiError(400, "No audio stream found in file"));
        return;
      }

      resolve({
        duration: duration, // Use the extracted duration
        bitrate:
          parseInt(audioStream.bit_rate) || parseInt(metadata.format.bit_rate),
        sampleRate: parseInt(audioStream.sample_rate),
        channels: parseInt(audioStream.channels),
        codec: audioStream.codec_name,
        format: metadata.format.format_name.split(",")[0], // Keep original for reference
      });
    });
  });
}

/**
 * Format file size in human readable format
 */
function formatFileSize(bytes) {
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

/**
 * Format duration in human readable format
 */
function formatDuration(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  }
  return `${minutes}:${secs.toString().padStart(2, "0")}`;
}

/**
 * Calculate quality score based on technical parameters
 */
function calculateQualityScore(recording) {
  let score = 0;

  // Bitrate score (0-30 points)
  if (recording.bitrate >= 320000) score += 30;
  else if (recording.bitrate >= 256000) score += 25;
  else if (recording.bitrate >= 192000) score += 20;
  else if (recording.bitrate >= 128000) score += 15;
  else score += 10;

  // Sample rate score (0-25 points)
  if (recording.sampleRate >= 48000) score += 25;
  else if (recording.sampleRate >= 44100) score += 20;
  else if (recording.sampleRate >= 22050) score += 15;
  else score += 10;

  // File size vs duration ratio (0-20 points)
  const sizePerSecond = recording.fileSize / recording.duration;
  if (sizePerSecond >= 50000) score += 20;
  else if (sizePerSecond >= 30000) score += 15;
  else if (sizePerSecond >= 20000) score += 10;
  else score += 5;

  // Quality metrics if available (0-25 points)
  if (recording.qualityMetrics) {
    const { noiseLevel, volumeLevel, clarityScore } = recording.qualityMetrics;
    if (noiseLevel !== undefined) score += Math.max(0, 10 - noiseLevel);
    if (volumeLevel !== undefined) score += Math.min(10, volumeLevel);
    if (clarityScore !== undefined) score += clarityScore * 5;
  } else {
    score += 15; // Default score if no quality metrics
  }

  return Math.min(100, Math.max(0, score));
}

/**
 * Submit completed assignment
 * @route POST /api/v1/voice/assignments/:id/submit
 * @access Model only
 */
export const submitAssignment = asyncHandler(async (req, res) => {
  const { id: assignmentId } = req.params;
  const { notes } = req.body;

  // Validate model access
  if (req.user.role !== "model") {
    throw new ApiError(403, "Only models can submit assignments");
  }

  if (!mongoose.Types.ObjectId.isValid(assignmentId)) {
    throw new ApiError(400, "Invalid assignment ID");
  }

  // Get assignment and validate access
  const assignment = await VoiceAssignment.findById(assignmentId)
    .populate("scriptId")
    .populate("modelId");

  if (!assignment || assignment.isDeleted) {
    throw new ApiError(404, "Assignment not found");
  }

  if (assignment.modelId._id.toString() !== req.user._id.toString()) {
    throw new ApiError(403, "You can only submit your own assignments");
  }

  if (assignment.status === "completed" || assignment.status === "approved") {
    throw new ApiError(400, "Assignment is already completed");
  }

  // Validate that recordings exist
  const recordings = await VoiceRecording.find({
    assignmentId,
    isDeleted: false,
    isLatestVersion: true, // Fixed: was isCurrentVersion
  });

  if (recordings.length === 0) {
    throw new ApiError(400, "No recordings found for this assignment");
  }

  // For detailed scripts, validate all sentences are recorded
  if (
    assignment.scriptId.scriptType === "detailed" &&
    assignment.scriptId.sentences.length > 0
  ) {
    const recordedSentenceIds = recordings
      .map((r) => r.sentenceId)
      .filter(Boolean);
    const requiredSentenceIds = assignment.scriptId.sentences.map((s) => s.id);

    const missingIds = requiredSentenceIds.filter(
      (id) => !recordedSentenceIds.includes(id),
    );
    if (missingIds.length > 0) {
      throw new ApiError(
        400,
        `Missing recordings for sentences: ${missingIds.join(", ")}`,
      );
    }
  }

  // Update assignment status
  assignment.status = "submitted";
  assignment.submittedAt = new Date();
  if (notes) {
    assignment.modelNotes = notes;
  }

  // Update assignment recordings array
  if (!assignment.recordings) {
    assignment.recordings = [];
  }

  assignment.recordings = recordings.map((recording) => ({
    sentenceId: recording.sentenceId || null,
    audioUrl: recording.fileUrl,
    duration: recording.duration,
    fileSize: recording.fileSize,
    status: "pending", // Use valid enum value - assignment will be "submitted" but recordings start as "pending"
    uploadedAt: recording.uploadCompletedAt,
    version: recording.version,
  }));

  // Save the assignment
  await assignment.save();

  // Update all recordings status
  await VoiceRecording.updateMany(
    { assignmentId, isLatestVersion: true }, // Fixed: was isCurrentVersion
    {
      status: "submitted",
      submittedAt: new Date(),
    },
  );

  await assignment.save();

  res
    .status(200)
    .json(
      new ApiResponse(200, assignment, "Assignment submitted successfully"),
    );
});

/**
 * Download assignment recordings as ZIP
 * @route GET /api/v1/voice/assignments/:id/download
 * @access Agency only
 */
export const downloadAssignmentRecordings = asyncHandler(async (req, res) => {
  const { id: assignmentId } = req.params;

  // Validate agency access
  if (req.user.role !== "agency") {
    throw new ApiError(403, "Only agencies can download recordings");
  }

  if (!mongoose.Types.ObjectId.isValid(assignmentId)) {
    throw new ApiError(400, "Invalid assignment ID");
  }

  // Get assignment and validate access
  const assignment = await VoiceAssignment.findById(assignmentId)
    .populate("scriptId", "title scriptType")
    .populate("modelId", "firstName lastName username");

  if (!assignment || assignment.isDeleted) {
    throw new ApiError(404, "Assignment not found");
  }

  if (assignment.agencyId.toString() !== req.user._id.toString()) {
    throw new ApiError(
      403,
      "You can only download recordings from your own assignments",
    );
  }

  // Get recordings
  const recordings = await VoiceRecording.find({
    assignmentId,
    isDeleted: false,
    isLatestVersion: true, // Fixed: was isCurrentVersion
    status: { $in: ["submitted", "approved"] },
  }).sort({ sentenceOrder: 1, createdAt: 1 });

  if (recordings.length === 0) {
    throw new ApiError(404, "No recordings found for download");
  }

  // Update download tracking
  assignment.downloadCount = (assignment.downloadCount || 0) + 1;
  assignment.lastDownloadedAt = new Date();
  await assignment.save();

  // Track download for each recording
  await Promise.all(
    recordings.map((recording) =>
      recording.markAccessed ? recording.markAccessed() : Promise.resolve(),
    ),
  );

  const modelName =
    assignment.modelId.firstName && assignment.modelId.lastName
      ? `${assignment.modelId.firstName}_${assignment.modelId.lastName}`
      : assignment.modelId.username;

  // If single file, send directly
  if (recordings.length === 1) {
    const recording = recordings[0];
    const filename = `${assignment.scriptId.title}_${modelName}.${recording.format}`;

    res.setHeader("Content-Disposition", `attachment; filename="${filename}"`);
    res.setHeader("Content-Type", recording.mimeType);

    return res.redirect(recording.fileUrl);
  }

  // Multiple files - create ZIP download response
  const zipFilename = `${assignment.scriptId.title}_${modelName}_recordings.zip`;

  res.status(200).json(
    new ApiResponse(
      200,
      {
        downloadUrls: recordings.map((recording, index) => ({
          url: recording.fileUrl,
          filename:
            assignment.scriptId.scriptType === "detailed"
              ? `sentence_${index + 1}_${recording.sentenceId}.${recording.format}`
              : `single_line_recording.${recording.format}`,
          size: recording.fileSize,
          duration: recording.duration,
        })),
        zipFilename,
        totalFiles: recordings.length,
        totalSize: recordings.reduce((sum, r) => sum + r.fileSize, 0),
      },
      "Download URLs generated successfully",
    ),
  );
});

/**
 * Review a recording (approve/reject)
 * @route PUT /api/v1/voice/recordings/:id/review
 * @access Agency only
 */
export const reviewRecording = asyncHandler(async (req, res) => {
  const { id: recordingId } = req.params;
  const { status, comments, rating } = req.body;

  // Validate agency access
  if (req.user.role !== "agency") {
    throw new ApiError(403, "Only agencies can review recordings");
  }

  if (!mongoose.Types.ObjectId.isValid(recordingId)) {
    throw new ApiError(400, "Invalid recording ID");
  }

  if (!["approved", "rejected", "requires_revision"].includes(status)) {
    throw new ApiError(
      400,
      "Invalid status. Must be: approved, rejected, or requires_revision",
    );
  }

  // Get recording and validate access
  const recording = await VoiceRecording.findById(recordingId);

  if (!recording || recording.isDeleted) {
    throw new ApiError(404, "Recording not found");
  }

  if (recording.agencyId.toString() !== req.user._id.toString()) {
    throw new ApiError(
      403,
      "You can only review recordings from your own assignments",
    );
  }

  // Update recording
  if (status === "approved") {
    if (recording.approve) {
      await recording.approve(req.user._id, comments, rating);
    } else {
      recording.status = "approved";
      recording.reviewedAt = new Date();
      recording.reviewedBy = req.user._id;
      recording.reviewComments = comments;
      recording.rating = rating;
      await recording.save();
    }
  } else if (status === "rejected") {
    if (recording.reject) {
      await recording.reject(req.user._id, comments);
    } else {
      recording.status = "rejected";
      recording.reviewedAt = new Date();
      recording.reviewedBy = req.user._id;
      recording.reviewComments = comments;
      await recording.save();
    }
  } else {
    recording.status = status;
    recording.reviewedAt = new Date();
    recording.reviewedBy = req.user._id;
    recording.reviewComments = comments;
    await recording.save();
  }

  // Update assignment recording status
  const assignment = await VoiceAssignment.findById(recording.assignmentId);
  if (assignment && assignment.recordings) {
    const assignmentRecording = assignment.recordings.find(
      (r) =>
        (recording.sentenceId && r.sentenceId === recording.sentenceId) ||
        (!recording.sentenceId && !r.sentenceId),
    );

    if (assignmentRecording) {
      assignmentRecording.status = status;
      assignmentRecording.reviewComments = comments;
      await assignment.save();
    }
  }

  res
    .status(200)
    .json(new ApiResponse(200, recording, "Recording reviewed successfully"));
});

/**
 * Generate temporary download token for a recording
 * @route POST /api/v1/voice/recordings/:id/generate-download-token
 * @access Model and Agency (authenticated)
 */
export const generateDownloadToken = asyncHandler(async (req, res) => {
  const { id } = req.params;

  if (!mongoose.Types.ObjectId.isValid(id)) {
    throw new ApiError(400, "Invalid recording ID");
  }

  const recording = await VoiceRecording.findById(id);

  if (!recording || recording.isDeleted) {
    throw new ApiError(404, "Recording not found");
  }

  // Check access permissions (same logic as existing download endpoint)
  if (req.user.role === "model") {
    if (recording.modelId.toString() !== req.user._id.toString()) {
      throw new ApiError(403, "Access denied");
    }
  } else if (req.user.role === "agency") {
    if (recording.agencyId.toString() !== req.user._id.toString()) {
      throw new ApiError(403, "Access denied");
    }
  } else {
    throw new ApiError(403, "Access denied");
  }

  // Get client IP and user agent for security
  const clientIP =
    req.ip || req.connection.remoteAddress || req.socket.remoteAddress;
  const userAgent = req.get("User-Agent");

  // Calculate expiry time
  const expiryMinutes =
    parseInt(process.env.VOICE_TOKEN_EXPIRY?.replace("m", "")) || 15;
  const expiresAt = new Date(Date.now() + expiryMinutes * 60 * 1000);

  // Create JWT token
  const tokenPayload = {
    recordingId: recording._id.toString(),
    userId: req.user._id.toString(),
    userRole: req.user.role,
    tokenType: "voice_download",
    permissions: ["download"],
    iat: Math.floor(Date.now() / 1000),
  };

  const downloadToken = jwt.sign(tokenPayload, process.env.VOICE_TOKEN_SECRET, {
    expiresIn: process.env.VOICE_TOKEN_EXPIRY || "15m",
  });

  // Store token in database for single-use tracking
  const tokenRecord = new VoiceDownloadToken({
    token: downloadToken,
    recordingId: recording._id,
    userId: req.user._id,
    userRole: req.user.role,
    expiresAt,
    ipAddress: clientIP,
    userAgent,
  });

  await tokenRecord.save();

  // Log token generation for audit
  console.log(
    `Download token generated: User=${req.user._id}, Role=${req.user.role}, Recording=${recording._id}, IP=${clientIP}`,
  );

  res.status(200).json(
    new ApiResponse(
      200,
      {
        downloadToken,
        expiresAt,
        expiresIn: expiryMinutes * 60, // seconds
        recordingId: recording._id,
        originalFilename: recording.originalFilename,
      },
      "Download token generated successfully",
    ),
  );
});

/**
 * Download recording file using temporary token
 * @route GET /api/v1/voice/recordings/download/:token
 * @access Public (with valid download token)
 */
export const downloadWithToken = asyncHandler(async (req, res) => {
  // Token validation is handled by downloadTokenMiddleware
  // req.recording is populated by the middleware
  const recording = req.recording;

  if (!recording || recording.isDeleted) {
    throw new ApiError(404, "Recording not found");
  }

  // Mark recording as accessed
  await recording.markAccessed();

  // Log download for audit
  const clientIP =
    req.ip || req.connection.remoteAddress || req.socket.remoteAddress;
  console.log(
    `File download: Recording=${recording._id}, User=${req.downloadToken.userId}, Role=${req.downloadToken.userRole}, IP=${clientIP}`,
  );

  try {
    // Import axios for making HTTP requests
    const axios = (await import("axios")).default;

    // Fetch the file from Cloudinary
    const response = await axios({
      method: "GET",
      url: recording.fileUrl,
      responseType: "stream",
    });

    // Set headers for file download
    const fileName =
      recording.originalFilename ||
      `recording_${recording._id}.${recording.format || "mp3"}`;
    res.setHeader("Content-Disposition", `attachment; filename="${fileName}"`);
    res.setHeader("Content-Type", recording.mimeType || "audio/mpeg");
    res.setHeader("Content-Length", recording.fileSize || "");

    // Set CORS headers
    res.header("Access-Control-Allow-Origin", "*");
    res.header("Access-Control-Allow-Methods", "GET");
    res.header("Access-Control-Allow-Headers", "Content-Type, Authorization");

    // Pipe the file stream to response
    response.data.pipe(res);
  } catch (error) {
    console.error("Error streaming file:", error);
    throw new ApiError(500, "Failed to download file");
  }
});
