import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "sonner";
import {
  Search,
  Filter,
  Plus,
  Grid3X3,
  List,
  Pin,
  Archive,
  Trash2,
  FileText,
  Calendar,
  User,
  Tag,
  AlertCircle,
  MoreVertical,
  Edit,
  Eye,
  Download,
  BookOpen,
  Star,
  Clock,
} from "lucide-react";

// UI Components
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

// Reusable Components
import {
  SearchBar,
  StatCard,
  SkeletonLoader,
  EmptyState,
  StatusBadge,
} from "@/reusable";

// Redux Actions
import {
  fetchAgencyNotes,
  deleteNote,
  togglePin,
  bulkDelete,
  bulkPin,
  clearNotesError,
} from "@/redux/features/notes/notesSlice";
import { fetchModelList } from "@/redux/features/models/fetchModelListSlice";

// Enhanced Components
import NoteTemplateModal from "../../../components/Notes/NoteTemplateModal";
import RichTextEditor from "../../../components/Notes/RichTextEditor";
import NotePreviewModal from "../../../components/Notes/NotePreviewModal";
import AdvancedFilters from "../../../components/Notes/AdvancedFilters";

// Cache implementation
const useNotesCache = () => {
  const [cache, setCache] = useState(new Map());

  const getCached = useCallback(
    (key) => {
      const cached = cache.get(key);
      if (cached && Date.now() - cached.timestamp < 5 * 60 * 1000) {
        // 5 min cache
        return cached.data;
      }
      return null;
    },
    [cache]
  );

  const setCached = useCallback((key, data) => {
    setCache((prev) => new Map(prev).set(key, { data, timestamp: Date.now() }));
  }, []);

  return { getCached, setCached };
};

const NotesDashboard = () => {
  const dispatch = useDispatch();
  const { getCached, setCached } = useNotesCache();

  // Redux State
  const {
    allIds = [],
    byId = {},
    loading,
  } = useSelector((state) => state.notesReducer || {});
  const { modelList = [] } = useSelector(
    (state) => state.fetchModelListReducer || {}
  );
  const auth = useSelector((state) => state.authReducer || {});

  // Local State
  const [selectedModelId, setSelectedModelId] = useState("");
  const [viewMode, setViewMode] = useState("grid"); // grid, list
  const [selectedNotes, setSelectedNotes] = useState(new Set());
  const [showTemplateModal, setShowTemplateModal] = useState(false);
  const [showNoteEditor, setShowNoteEditor] = useState(false);
  const [editingNote, setEditingNote] = useState(null);
  const [showPreview, setShowPreview] = useState(false);
  const [previewNote, setPreviewNote] = useState(null);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  // Search and Filter State
  const [searchQuery, setSearchQuery] = useState("");
  const [filters, setFilters] = useState({
    priority: "",
    category: "",
    tags: [],
    dateRange: null,
    visibility: "",
    status: "active",
    isPinned: null,
  });

  // Performance optimization - memoized filtered notes
  const filteredNotes = useMemo(() => {
    let notes = allIds.map((id) => byId[id]).filter(Boolean);

    // Apply search
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      notes = notes.filter(
        (note) =>
          note.title?.toLowerCase().includes(query) ||
          note.content?.toLowerCase().includes(query) ||
          note.tags?.some((tag) => tag.toLowerCase().includes(query))
      );
    }

    // Apply filters
    if (filters.priority) {
      notes = notes.filter((note) => note.priority === filters.priority);
    }
    if (filters.category) {
      notes = notes.filter((note) => note.category === filters.category);
    }
    if (filters.tags.length > 0) {
      notes = notes.filter((note) =>
        filters.tags.some((tag) => note.tags?.includes(tag))
      );
    }
    if (filters.visibility) {
      notes = notes.filter((note) => note.visibility === filters.visibility);
    }
    if (filters.isPinned !== null) {
      notes = notes.filter((note) => note.isPinned === filters.isPinned);
    }

    // Sort by pinned first, then by creation date
    return notes.sort((a, b) => {
      if (a.isPinned && !b.isPinned) return -1;
      if (!a.isPinned && b.isPinned) return 1;
      return new Date(b.createdAt) - new Date(a.createdAt);
    });
  }, [allIds, byId, searchQuery, filters]);

  // Statistics
  const stats = useMemo(() => {
    const notes = allIds.map((id) => byId[id]).filter(Boolean);
    return {
      total: notes.length,
      pinned: notes.filter((note) => note.isPinned).length,
      highPriority: notes.filter(
        (note) => note.priority === "high" || note.priority === "critical"
      ).length,
      recent: notes.filter((note) => {
        const dayAgo = new Date();
        dayAgo.setDate(dayAgo.getDate() - 1);
        return new Date(note.createdAt) > dayAgo;
      }).length,
    };
  }, [allIds, byId]);

  // Load initial data
  useEffect(() => {
    dispatch(fetchModelList());
  }, [dispatch]);

  // Load notes when model changes
  useEffect(() => {
    if (selectedModelId) {
      const cacheKey = `notes-${selectedModelId}`;
      const cached = getCached(cacheKey);

      if (cached) {
        // Use cached data temporarily while fetching fresh data
        toast.info("Showing cached data...");
      }

      // Get agencyId from auth state (preferred) or localStorage (fallback)
      const agencyId = auth?.user?._id || localStorage.getItem("agencyId");

      // Debug logging
      console.log("Debug agencyId resolution:", {
        fromAuth: auth?.user?._id,
        fromLocalStorage: localStorage.getItem("agencyId"),
        finalAgencyId: agencyId,
        authUser: auth?.user,
        isAuthenticated: auth?.isAuthenticated,
      });

      if (!agencyId) {
        console.error("No agency ID available - user may not be authenticated");
        toast.error("Please log in to view notes");
        return;
      }

      if (agencyId === "test-agency-id") {
        console.error("Test agency ID detected - using fallback");
        toast.warning("Using test data - please ensure proper authentication");
      }

      dispatch(
        fetchAgencyNotes({
          agencyId,
          params: {
            limit: 50,
            modelId: selectedModelId,
            ...filters,
          },
        })
      ).then((result) => {
        if (result.payload) {
          setCached(cacheKey, result.payload);
        }
      });
    }
    // Note: getCached and setCached are intentionally excluded to prevent infinite loops
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedModelId, dispatch, auth?.user?._id, filters]);

  // Auto-select first model
  useEffect(() => {
    if (!selectedModelId && modelList.length > 0) {
      setSelectedModelId(modelList[0]._id || modelList[0].id);
    }
  }, [modelList, selectedModelId]);

  // Clear errors on component unmount
  useEffect(() => {
    return () => {
      dispatch(clearNotesError());
    };
  }, [dispatch]);

  // Event Handlers
  const handleCreateNote = () => {
    setEditingNote(null);
    setShowNoteEditor(true);
  };

  const handleEditNote = (note) => {
    setEditingNote(note);
    setShowNoteEditor(true);
  };

  const handleDeleteNote = async (noteId) => {
    try {
      await dispatch(deleteNote({ noteId })).unwrap();
      toast.success("Note deleted successfully");
    } catch (error) {
      toast.error(error?.message || "Failed to delete note");
    }
  };

  const handleTogglePin = async (noteId) => {
    try {
      await dispatch(togglePin({ noteId })).unwrap();
      toast.success("Note pin status updated");
    } catch (error) {
      toast.error(error?.message || "Failed to update pin status");
    }
  };

  const handleBulkAction = async (action) => {
    const noteIds = Array.from(selectedNotes);
    if (noteIds.length === 0) {
      toast.error("Please select notes first");
      return;
    }

    try {
      switch (action) {
        case "delete":
          await dispatch(bulkDelete({ noteIds })).unwrap();
          toast.success(`${noteIds.length} notes deleted`);
          break;
        case "pin":
          await dispatch(bulkPin({ noteIds, isPinned: true })).unwrap();
          toast.success(`${noteIds.length} notes pinned`);
          break;
        case "unpin":
          await dispatch(bulkPin({ noteIds, isPinned: false })).unwrap();
          toast.success(`${noteIds.length} notes unpinned`);
          break;
      }
      setSelectedNotes(new Set());
    } catch (error) {
      toast.error(error?.message || `Failed to ${action} notes`);
    }
  };

  const handleSelectNote = (noteId) => {
    setSelectedNotes((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(noteId)) {
        newSet.delete(noteId);
      } else {
        newSet.add(noteId);
      }
      return newSet;
    });
  };

  const handleSelectAll = () => {
    if (selectedNotes.size === filteredNotes.length) {
      setSelectedNotes(new Set());
    } else {
      setSelectedNotes(new Set(filteredNotes.map((note) => note._id)));
    }
  };

  const selectedModel = modelList.find(
    (model) => (model._id || model.id) === selectedModelId
  );

  return (
    <div className="min-h-screen bg-[#0f0f0f] text-white">
      {/* Header */}
      <div className="border-b border-gray-800 bg-[#0f0f0f]/95 backdrop-blur-sm sticky top-0 z-50">
        <div className="max-w-6xl mx-auto p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex flex-col justify-center">
              <h1 className="text-lg font-medium text-white">
                Notes Dashboard
              </h1>
              <p className="text-sm text-gray-400 mt-1">
                Manage notes for your agency models
              </p>
            </div>

            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowTemplateModal(true)}
                className="border-gray-700 text-gray-900 hover:bg-gray-800 hover:text-white hover:border-gray-600 text-sm px-4 py-2 transition-colors"
              >
                <BookOpen className="h-4 w-4 mr-2" />
                Templates
              </Button>

              <Button
                onClick={handleCreateNote}
                className="bg-[#1a1a1a] border border-gray-700 hover:bg-gray-800 text-white text-sm px-6 py-2 transition-colors"
              >
                <Plus className="h-4 w-4 mr-2" />
                New Note
              </Button>
            </div>
          </div>

          {/* Model Selection and Stats */}
          <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card className="bg-[#1a1a1a] border-gray-800">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={selectedModel?.avatar} />
                    <AvatarFallback className="bg-gray-800 text-gray-300 text-sm">
                      {selectedModel?.fullName?.charAt(0) || "M"}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <Select
                      value={selectedModelId}
                      onValueChange={setSelectedModelId}
                    >
                      <SelectTrigger className="border-0 p-0 h-auto bg-transparent text-white w-full">
                        <SelectValue placeholder="Select model">
                          <div className="text-left w-full">
                            <p className="text-sm font-medium text-white truncate">
                              {selectedModel?.fullName || "Select Model"}
                            </p>
                            <p className="text-xs text-gray-400 truncate">
                              {selectedModel?.email}
                            </p>
                          </div>
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent className="bg-[#1a1a1a] border-gray-700">
                        {modelList.map((model) => (
                          <SelectItem
                            key={model._id || model.id}
                            value={model._id || model.id}
                            className="text-gray-300 focus:bg-gray-800 text-sm"
                          >
                            <div className="flex items-center gap-2">
                              <Avatar className="h-6 w-6">
                                <AvatarImage src={model.avatar} />
                                <AvatarFallback className="bg-gray-800 text-gray-300 text-xs">
                                  {model.fullName?.charAt(0)}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                <p className="font-medium">{model.fullName}</p>
                                <p className="text-xs text-gray-400">
                                  {model.email}
                                </p>
                              </div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>

            <StatCard
              title="Total Notes"
              value={stats.total}
              icon={FileText}
              className="bg-[#1a1a1a] border-gray-800"
            />

            <StatCard
              title="Pinned"
              value={stats.pinned}
              icon={Pin}
              className="bg-[#1a1a1a] border-gray-800"
            />

            <StatCard
              title="High Priority"
              value={stats.highPriority}
              icon={AlertCircle}
              className="bg-[#1a1a1a] border-gray-800"
            />
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="max-w-6xl mx-auto p-6 border-b border-gray-800">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <SearchBar
              placeholder="Search notes by title, content, or tags..."
              value={searchQuery}
              onChange={setSearchQuery}
              showFilter={true}
              onFilterChange={() => setShowAdvancedFilters(true)}
              className="bg-[#1a1a1a] border-gray-800"
            />
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowAdvancedFilters(true)}
              className="border-gray-700 text-gray-900 hover:bg-gray-800 hover:text-white hover:border-gray-600 text-sm px-4 transition-colors"
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </Button>

            <div className="flex items-center border border-gray-700 rounded-md">
              <Button
                variant={viewMode === "grid" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("grid")}
                className={`px-3 py-2 text-sm rounded-r-none transition-colors ${
                  viewMode === "grid"
                    ? "bg-[#1a1a1a] border border-gray-700 text-white hover:bg-gray-800"
                    : "text-gray-300 hover:bg-gray-800 hover:text-white"
                }`}
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("list")}
                className={`px-3 py-2 text-sm rounded-l-none border-l-0 transition-colors ${
                  viewMode === "list"
                    ? "bg-[#1a1a1a] border border-gray-700 text-white hover:bg-gray-800"
                    : "text-gray-300 hover:bg-gray-800 hover:text-white"
                }`}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Active Filters */}
        {Object.values(filters).some(Boolean) && (
          <div className="mt-4 flex flex-wrap gap-2">
            {filters.priority && (
              <Badge
                variant="secondary"
                className="bg-gray-800 text-gray-300 border-gray-700 text-xs"
              >
                Priority: {filters.priority}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 ml-2 text-gray-400 hover:text-white hover:bg-gray-700 transition-colors"
                  onClick={() =>
                    setFilters((prev) => ({ ...prev, priority: "" }))
                  }
                >
                  ×
                </Button>
              </Badge>
            )}
            {filters.category && (
              <Badge
                variant="secondary"
                className="bg-gray-800 text-gray-300 border-gray-700 text-xs"
              >
                Category: {filters.category}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 ml-2 text-gray-400 hover:text-white hover:bg-gray-700 transition-colors"
                  onClick={() =>
                    setFilters((prev) => ({ ...prev, category: "" }))
                  }
                >
                  ×
                </Button>
              </Badge>
            )}
            {/* Add more filter badges as needed */}
          </div>
        )}
      </div>

      {/* Bulk Actions Bar */}
      {selectedNotes.size > 0 && (
        <div className="p-4 bg-blue-600/10 border-b border-blue-600/20">
          <div className="max-w-6xl mx-auto">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Checkbox
                  checked={selectedNotes.size === filteredNotes.length}
                  onCheckedChange={handleSelectAll}
                />
                <span className="text-sm">
                  {selectedNotes.size} of {filteredNotes.length} selected
                </span>
              </div>

              <div className="flex items-center gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleBulkAction("pin")}
                  className="border-gray-700 text-gray-300 hover:bg-gray-800 hover:text-white hover:border-gray-600"
                >
                  <Pin className="h-4 w-4 mr-2" />
                  Pin
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleBulkAction("unpin")}
                  className="border-gray-700 text-gray-300 hover:bg-gray-800 hover:text-white hover:border-gray-600"
                >
                  <Pin className="h-4 w-4 mr-2" />
                  Unpin
                </Button>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => handleBulkAction("delete")}
                  className="bg-red-600 hover:bg-red-700 text-white border-red-600 hover:border-red-700"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Content */}
      <div className="p-6">
        <div className="max-w-6xl mx-auto">
          {loading && !allIds.length ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {Array.from({ length: 8 }).map((_, i) => (
                <SkeletonLoader key={i} className="h-24" />
              ))}
            </div>
          ) : filteredNotes.length === 0 ? (
            <EmptyState
              icon={FileText}
              title="No notes found"
              description={
                selectedModelId
                  ? "Create your first note for this model"
                  : "Select a model to view notes"
              }
              onActionClick={selectedModelId ? handleCreateNote : undefined}
              actionText={selectedModelId ? "Create Note" : undefined}
            />
          ) : (
            <NotesGrid
              notes={filteredNotes}
              viewMode={viewMode}
              selectedNotes={selectedNotes}
              onSelectNote={handleSelectNote}
              onEditNote={handleEditNote}
              onDeleteNote={handleDeleteNote}
              onTogglePin={handleTogglePin}
              onPreviewNote={(note) => {
                setPreviewNote(note);
                setShowPreview(true);
              }}
            />
          )}
        </div>
      </div>

      {/* Modals */}
      <NoteTemplateModal
        open={showTemplateModal}
        onClose={() => setShowTemplateModal(false)}
        onSelect={(template) => {
          setEditingNote({
            ...template,
            isTemplate: true,
            visibility: "internal",
          });
          setShowNoteEditor(true);
          setShowTemplateModal(false);
        }}
      />

      <RichTextEditor
        open={showNoteEditor}
        onClose={() => setShowNoteEditor(false)}
        note={editingNote}
        modelId={selectedModelId}
        onSave={() => {
          setShowNoteEditor(false);
          setEditingNote(null);
        }}
      />

      <NotePreviewModal
        open={showPreview}
        onClose={() => setShowPreview(false)}
        note={previewNote}
        onEdit={() => {
          setShowPreview(false);
          handleEditNote(previewNote);
        }}
      />

      <AdvancedFilters
        open={showAdvancedFilters}
        onClose={() => setShowAdvancedFilters(false)}
        filters={filters}
        onFiltersChange={setFilters}
      />
    </div>
  );
};

// Notes Grid Component
const NotesGrid = ({
  notes,
  viewMode,
  selectedNotes,
  onSelectNote,
  onEditNote,
  onDeleteNote,
  onTogglePin,
  onPreviewNote,
}) => {
  if (viewMode === "list") {
    return (
      <div className="space-y-2">
        {notes.map((note) => (
          <NoteListItem
            key={note._id}
            note={note}
            selected={selectedNotes.has(note._id)}
            onSelect={() => onSelectNote(note._id)}
            onEdit={() => onEditNote(note)}
            onDelete={() => onDeleteNote(note._id)}
            onTogglePin={() => onTogglePin(note._id)}
            onPreview={() => onPreviewNote(note)}
          />
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      {notes.map((note) => (
        <div
          key={note._id}
          className="animate-in fade-in-0 slide-in-from-bottom-4 duration-200"
        >
          <NoteCard
            note={note}
            selected={selectedNotes.has(note._id)}
            onSelect={() => onSelectNote(note._id)}
            onEdit={() => onEditNote(note)}
            onDelete={() => onDeleteNote(note._id)}
            onTogglePin={() => onTogglePin(note._id)}
            onPreview={() => onPreviewNote(note)}
          />
        </div>
      ))}
    </div>
  );
};

// Note Card Component
const NoteCard = ({
  note,
  selected,
  onSelect,
  onEdit,
  onDelete,
  onTogglePin,
  onPreview,
}) => {
  const getPriorityColor = (priority) => {
    switch (priority) {
      case "critical":
        return "bg-red-500";
      case "high":
        return "bg-orange-500";
      case "medium":
        return "bg-yellow-500";
      case "low":
        return "bg-green-500";
      default:
        return "bg-gray-500";
    }
  };

  return (
    <Card
      className={`bg-[#1a1a1a] border-gray-800 hover:border-gray-700 transition-all cursor-pointer group ${
        selected ? "ring-2 ring-blue-500" : ""
      } ${note.isPinned ? "border-yellow-500/50" : ""}`}
    >
      <CardContent className="p-3" onClick={onPreview}>
        <div className="flex items-start justify-between mb-2">
          <div className="flex items-center gap-2">
            <Checkbox
              checked={selected}
              onCheckedChange={onSelect}
              onClick={(e) => e.stopPropagation()}
              className="h-3 w-3"
            />
            {note.isPinned && <Pin className="h-3 w-3 text-yellow-500" />}
            <div
              className={`w-2 h-2 rounded-full ${getPriorityColor(
                note.priority
              )}`}
            />
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-5 w-5 p-0 opacity-0 group-hover:opacity-100 transition-opacity text-gray-400 hover:text-white hover:bg-gray-800"
                onClick={(e) => e.stopPropagation()}
              >
                <MoreVertical className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="end"
              className="bg-[#1a1a1a] border-gray-700"
            >
              <DropdownMenuItem
                onClick={onPreview}
                className="text-gray-300 hover:bg-gray-800 hover:text-white"
              >
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={onEdit}
                className="text-gray-300 hover:bg-gray-800 hover:text-white"
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={onTogglePin}
                className="text-gray-300 hover:bg-gray-800 hover:text-white"
              >
                <Pin className="h-4 w-4 mr-2" />
                {note.isPinned ? "Unpin" : "Pin"}
              </DropdownMenuItem>
              <DropdownMenuSeparator className="bg-gray-700" />
              <DropdownMenuItem
                onClick={onDelete}
                className="text-red-400 hover:bg-red-900/20 hover:text-red-300"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <h3 className="text-sm font-medium line-clamp-1 text-white mb-1">
          {note.title}
        </h3>

        <p className="text-gray-400 text-xs line-clamp-1 mb-2">
          {note.content}
        </p>

        <div className="flex items-center justify-between text-xs text-gray-500">
          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3" />
            <span className="text-xs">
              {new Date(note.createdAt).toLocaleDateString()}
            </span>
          </div>
          {note.tags && note.tags.length > 0 && (
            <div className="flex gap-1">
              {note.tags.slice(0, 1).map((tag) => (
                <Badge
                  key={tag}
                  variant="secondary"
                  className="text-xs bg-gray-800 border-gray-700 text-gray-400 px-1 py-0 h-4"
                >
                  {tag}
                </Badge>
              ))}
              {note.tags.length > 1 && (
                <Badge
                  variant="secondary"
                  className="text-xs bg-gray-700 text-gray-500 px-1 py-0 h-4"
                >
                  +{note.tags.length - 1}
                </Badge>
              )}
            </div>
          )}
        </div>

        {note.attachments && note.attachments.length > 0 && (
          <div className="flex items-center gap-1 mt-2 text-xs text-gray-500">
            <FileText className="h-3 w-3" />
            {note.attachments.length} attachment
            {note.attachments.length > 1 ? "s" : ""}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Note List Item Component
const NoteListItem = ({
  note,
  selected,
  onSelect,
  onEdit,
  onDelete,
  onTogglePin,
  onPreview,
}) => {
  const getPriorityColor = (priority) => {
    switch (priority) {
      case "critical":
        return "border-red-500";
      case "high":
        return "border-orange-500";
      case "medium":
        return "border-yellow-500";
      case "low":
        return "border-green-500";
      default:
        return "border-gray-500";
    }
  };

  return (
    <Card
      className={`bg-[#1a1a1a] border-gray-800 hover:border-gray-700 transition-all ${
        selected ? "ring-2 ring-blue-500" : ""
      } ${note.isPinned ? "border-yellow-500/50" : ""}`}
    >
      <CardContent className="p-4">
        <div className="flex items-center gap-4">
          <Checkbox checked={selected} onCheckedChange={onSelect} />

          <div
            className={`w-1 h-12 rounded-full ${getPriorityColor(
              note.priority
            ).replace("border-", "bg-")}`}
          />

          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              {note.isPinned && <Pin className="h-4 w-4 text-yellow-500" />}
              <h3
                className="text-sm font-medium truncate cursor-pointer text-white"
                onClick={onPreview}
              >
                {note.title}
              </h3>
            </div>
            <p className="text-xs text-gray-400 line-clamp-2 mb-2">
              {note.content}
            </p>
            <div className="flex items-center gap-4 text-xs text-gray-500">
              <span>{new Date(note.createdAt).toLocaleDateString()}</span>
              <StatusBadge status={note.category} variant="secondary" />
              {note.attachments?.length > 0 && (
                <span>{note.attachments.length} files</span>
              )}
            </div>
          </div>

          <div className="flex items-center gap-2">
            {note.tags &&
              note.tags.slice(0, 2).map((tag) => (
                <Badge
                  key={tag}
                  variant="secondary"
                  className="text-xs bg-[#1a1a1a] border-gray-800 text-gray-300"
                >
                  {tag}
                </Badge>
              ))}
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 text-gray-400 hover:text-white hover:bg-gray-800"
              >
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="end"
              className="bg-[#1a1a1a] border-gray-700"
            >
              <DropdownMenuItem
                onClick={onPreview}
                className="text-gray-300 hover:bg-gray-800 hover:text-white"
              >
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={onEdit}
                className="text-gray-300 hover:bg-gray-800 hover:text-white"
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={onTogglePin}
                className="text-gray-300 hover:bg-gray-800 hover:text-white"
              >
                <Pin className="h-4 w-4 mr-2" />
                {note.isPinned ? "Unpin" : "Pin"}
              </DropdownMenuItem>
              <DropdownMenuSeparator className="bg-gray-700" />
              <DropdownMenuItem
                onClick={onDelete}
                className="text-red-400 hover:bg-red-900/20 hover:text-red-300"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardContent>
    </Card>
  );
};

export default NotesDashboard;
