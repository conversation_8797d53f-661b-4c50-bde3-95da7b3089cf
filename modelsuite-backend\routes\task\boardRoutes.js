import express from "express";
import * as boardController from "../../controllers/task/boardController.js";
import * as listController from "../../controllers/task/listController.js";
import * as cardController from "../../controllers/task/cardController.js";
import { verifyToken } from "../../middlewares/authMiddleware.js";
import checkPermission from "../../middlewares/permissionCheck.js";
import multer from "multer";

const router = express.Router();

// Configure multer for memory storage
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    // Allow images, videos, and PDFs
    if (
      file.mimetype.startsWith("image/") ||
      file.mimetype.startsWith("video/") ||
      file.mimetype === "application/pdf"
    ) {
      cb(null, true);
    } else {
      cb(
        new Error(
          "Invalid file type. Only images, videos, and PDFs are allowed.",
        ),
      );
    }
  },
});

// Apply auth middleware to all routes
router.use(verifyToken);

// Board routes
router.post(
  "/creat-board",
  checkPermission("tasks.create"),
  boardController.createBoard,
);
router.get(
  "/boards",
  checkPermission("tasks.view"),
  boardController.getAgencyBoards,
);
router.get(
  "/boards/:boardId",
  checkPermission("tasks.view"),
  boardController.getBoardById,
);
router.put(
  "/boards/:boardId",
  checkPermission("tasks.edit"),
  boardController.updateBoard,
);
router.delete(
  "/boards/:boardId",
  checkPermission("tasks.delete"),
  boardController.deleteBoard,
);

// List routes
router.post(
  "/boards/:boardId/lists",
  checkPermission("tasks.create"),
  listController.createList,
);
router.put(
  "/lists/:listId",
  checkPermission("tasks.edit"),
  listController.updateList,
);
router.delete(
  "/lists/:listId",
  checkPermission("tasks.delete"),
  listController.deleteList,
);

// Card routes
router.post(
  "/lists/:listId/cards",
  checkPermission("tasks.create"),
  cardController.createCard,
);
router.get(
  "/cards/:cardId",
  checkPermission("tasks.view"),
  cardController.getCardById,
);
router.put(
  "/cards/:cardId",
  checkPermission("tasks.edit"),
  cardController.updateCard,
);
router.delete(
  "/cards/:cardId",
  checkPermission("tasks.delete"),
  cardController.deleteCard,
);
router.put(
  "/cards/:cardId/move",
  checkPermission("tasks.edit"),
  cardController.moveCard,
);
router.put(
  "/cards/:cardId/hold",
  checkPermission("tasks.edit"),
  cardController.putCardOnHold,
);

// Individual task routes
router.get(
  "/tasks",
  checkPermission("tasks.view"),
  cardController.getIndividualTasks,
);
router.post(
  "/tasks",
  checkPermission("tasks.create"),
  cardController.createIndividualTask,
);

// Attachment routes
router.post(
  "/cards/:cardId/attachments",
  upload.single("file"),
  checkPermission("tasks.create"),
  cardController.addAttachment,
);
router.get(
  "/cards/:cardId/attachments",
  checkPermission("tasks.view"),
  cardController.getAttachments,
);
router.delete(
  "/cards/:cardId/attachments/:attachmentId",
  checkPermission("tasks.delete"),
  cardController.deleteAttachment,
);

// Comment routes
router.get(
  "/cards/:cardId/comments",
  checkPermission("tasks.view"),
  cardController.getCardComments,
);
router.post(
  "/cards/:cardId/comments",
  checkPermission("tasks.create"),
  cardController.addComment,
);

export default router;
