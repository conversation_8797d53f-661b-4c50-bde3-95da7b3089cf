import React from "react";
import {
  Eye,
  Edit,
  Pin,
  Share,
  Download,
  Calendar,
  User,
  Tag,
  FileText,
  Paperclip,
  Star,
  Clock,
  X,
} from "lucide-react";

// UI Components
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent } from "@/components/ui/card";

// Reusable Components
import { StatusBadge } from "@/reusable";

const NotePreviewModal = ({ open, onClose, note, onEdit }) => {
  if (!note) return null;

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "critical":
        return "text-red-400 bg-red-500/10 border-red-500/20";
      case "high":
        return "text-orange-400 bg-orange-500/10 border-orange-500/20";
      case "medium":
        return "text-yellow-400 bg-yellow-500/10 border-yellow-500/20";
      case "low":
        return "text-green-400 bg-green-500/10 border-green-500/20";
      default:
        return "text-gray-400 bg-gray-500/10 border-gray-500/20";
    }
  };

  const getVisibilityInfo = (visibility) => {
    switch (visibility) {
      case "shared_with_model":
        return {
          label: "Shared with Model",
          icon: User,
          color: "text-blue-400",
        };
      case "neutral":
        return { label: "Neutral", icon: Eye, color: "text-gray-400" };
      default:
        return {
          label: "Internal Only",
          icon: FileText,
          color: "text-yellow-400",
        };
    }
  };

  const renderContent = () => {
    if (note.contentType === "markdown") {
      // Basic markdown rendering
      let html = note.content
        .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold">$1</strong>')
        .replace(/\*(.*?)\*/g, '<em class="italic">$1</em>')
        .replace(
          /`(.*?)`/g,
          '<code class="bg-gray-800 px-2 py-1 rounded text-sm font-mono">$1</code>'
        )
        .replace(
          /^> (.*$)/gm,
          '<blockquote class="border-l-4 border-gray-600 pl-4 italic text-gray-300 my-2">$1</blockquote>'
        )
        .replace(/^- (.*$)/gm, '<li class="ml-4">• $1</li>')
        .replace(/^\d+\. (.*$)/gm, '<li class="ml-4">$1</li>')
        .replace(
          /\[(.*?)\]\((.*?)\)/g,
          '<a href="$2" class="text-blue-400 hover:underline" target="_blank" rel="noopener noreferrer">$1</a>'
        )
        .replace(
          /!\[(.*?)\]\((.*?)\)/g,
          '<img src="$2" alt="$1" class="max-w-full h-auto rounded my-2" />'
        )
        .replace(/\n\n/g, '</p><p class="mb-4">')
        .replace(/\n/g, "<br />");

      return (
        <div
          className="prose prose-invert max-w-none text-gray-300 leading-relaxed"
          dangerouslySetInnerHTML={{ __html: `<p class="mb-4">${html}</p>` }}
        />
      );
    }

    if (note.contentType === "html") {
      return (
        <div
          className="prose prose-invert max-w-none text-gray-300"
          dangerouslySetInnerHTML={{ __html: note.content }}
        />
      );
    }

    // Plain text
    return (
      <div className="whitespace-pre-wrap text-gray-300 leading-relaxed">
        {note.content}
      </div>
    );
  };

  const visibilityInfo = getVisibilityInfo(note.visibility);
  const VisibilityIcon = visibilityInfo.icon;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] bg-[#18181b] border-[#27272a] text-white">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Eye className="h-5 w-5" />
              Note Preview
              {note.isPinned && <Pin className="h-4 w-4 text-yellow-500" />}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-6 w-6 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Note Header */}
          <div className="space-y-4">
            <h1 className="text-2xl font-bold text-white">{note.title}</h1>

            {/* Metadata Row */}
            <div className="flex flex-wrap items-center gap-4 text-sm text-gray-400">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                <span>
                  Created {new Date(note.createdAt).toLocaleDateString()}
                </span>
              </div>

              {note.lastEditedAt && (
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  <span>
                    Modified {new Date(note.lastEditedAt).toLocaleDateString()}
                  </span>
                </div>
              )}

              <div className="flex items-center gap-2">
                <VisibilityIcon className={`h-4 w-4 ${visibilityInfo.color}`} />
                <span className={visibilityInfo.color}>
                  {visibilityInfo.label}
                </span>
              </div>
            </div>

            {/* Priority and Category */}
            <div className="flex flex-wrap items-center gap-3">
              <Badge className={`${getPriorityColor(note.priority)} border`}>
                <Star className="h-3 w-3 mr-1" />
                {note.priority.charAt(0).toUpperCase() +
                  note.priority.slice(1)}{" "}
                Priority
              </Badge>

              <StatusBadge
                status={note.category}
                variant="secondary"
                className="bg-gray-800 border-gray-700"
              />

              {note.customCategory && (
                <Badge variant="outline" className="border-gray-700">
                  {note.customCategory}
                </Badge>
              )}
            </div>

            {/* Tags */}
            {note.tags && note.tags.length > 0 && (
              <div className="flex flex-wrap gap-2">
                <Tag className="h-4 w-4 text-gray-400" />
                {note.tags.map((tag) => (
                  <Badge key={tag} variant="secondary" className="text-xs">
                    #{tag}
                  </Badge>
                ))}
              </div>
            )}
          </div>

          <Separator />

          {/* Note Content */}
          <div className="bg-[#1a1a1a] border border-[#27272a] rounded-lg p-6">
            <div className="max-h-[50vh] overflow-y-auto">
              {renderContent()}
            </div>
          </div>

          {/* Attachments */}
          {note.attachments && note.attachments.length > 0 && (
            <div className="space-y-3">
              <h3 className="text-lg font-semibold text-white flex items-center gap-2">
                <Paperclip className="h-4 w-4" />
                Attachments ({note.attachments.length})
              </h3>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {note.attachments.map((attachment, index) => (
                  <Card key={index} className="bg-[#1a1a1a] border-gray-800">
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <div className="flex-shrink-0">
                          {attachment.type?.startsWith("image/") ? (
                            <img
                              src={attachment.url}
                              alt={attachment.originalName}
                              className="w-12 h-12 object-cover rounded"
                            />
                          ) : (
                            <div className="w-12 h-12 bg-gray-800 rounded flex items-center justify-center">
                              <FileText className="h-6 w-6 text-gray-400" />
                            </div>
                          )}
                        </div>

                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-white truncate">
                            {attachment.originalName}
                          </p>
                          <p className="text-xs text-gray-400">
                            {attachment.size
                              ? `${(attachment.size / 1024).toFixed(1)} KB`
                              : "Unknown size"}
                          </p>
                        </div>

                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => window.open(attachment.url, "_blank")}
                          className="flex-shrink-0"
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* Author Information */}
          {(note.createdBy || note.lastEditedBy) && (
            <div className="space-y-3">
              <h3 className="text-lg font-semibold text-white">
                Author Information
              </h3>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {note.createdBy && (
                  <div className="flex items-center gap-3 p-3 bg-[#1a1a1a] border border-gray-800 rounded-lg">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={note.createdBy.userId?.avatar} />
                      <AvatarFallback>
                        {note.createdBy.userId?.fullName?.charAt(0) ||
                          note.createdBy.userId?.agencyName?.charAt(0) ||
                          "U"}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="text-sm font-medium text-white">
                        Created by
                      </p>
                      <p className="text-xs text-gray-400">
                        {note.createdBy.userId?.fullName ||
                          note.createdBy.userId?.agencyName ||
                          "Unknown"}
                      </p>
                    </div>
                  </div>
                )}

                {note.lastEditedBy && (
                  <div className="flex items-center gap-3 p-3 bg-[#1a1a1a] border border-gray-800 rounded-lg">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={note.lastEditedBy.userId?.avatar} />
                      <AvatarFallback>
                        {note.lastEditedBy.userId?.fullName?.charAt(0) ||
                          note.lastEditedBy.userId?.agencyName?.charAt(0) ||
                          "U"}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="text-sm font-medium text-white">
                        Last edited by
                      </p>
                      <p className="text-xs text-gray-400">
                        {note.lastEditedBy.userId?.fullName ||
                          note.lastEditedBy.userId?.agencyName ||
                          "Unknown"}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Reminders and Linked Actions */}
          {((note.reminders && note.reminders.length > 0) ||
            (note.linkedActions && note.linkedActions.length > 0)) && (
            <div className="space-y-4">
              {note.reminders && note.reminders.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold text-white mb-3">
                    Reminders
                  </h3>
                  <div className="space-y-2">
                    {note.reminders.map((reminder, index) => (
                      <div
                        key={index}
                        className="flex items-center gap-3 p-3 bg-[#1a1a1a] border border-gray-800 rounded-lg"
                      >
                        <Clock className="h-4 w-4 text-blue-400" />
                        <div className="flex-1">
                          <p className="text-sm text-white">
                            {new Date(reminder.reminderDate).toLocaleString()}
                          </p>
                          {reminder.isCompleted && (
                            <p className="text-xs text-green-400">Completed</p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {note.linkedActions && note.linkedActions.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold text-white mb-3">
                    Linked Actions
                  </h3>
                  <div className="space-y-2">
                    {note.linkedActions.map((action, index) => (
                      <div
                        key={index}
                        className="flex items-center gap-3 p-3 bg-[#1a1a1a] border border-gray-800 rounded-lg"
                      >
                        <FileText className="h-4 w-4 text-purple-400" />
                        <div className="flex-1">
                          <p className="text-sm font-medium text-white">
                            {action.actionTitle}
                          </p>
                          {action.actionDescription && (
                            <p className="text-xs text-gray-400">
                              {action.actionDescription}
                            </p>
                          )}
                          <div className="flex items-center gap-2 mt-1">
                            <Badge variant="outline" className="text-xs">
                              {action.actionType}
                            </Badge>
                            {action.dueDate && (
                              <span className="text-xs text-gray-400">
                                Due:{" "}
                                {new Date(action.dueDate).toLocaleDateString()}
                              </span>
                            )}
                            {action.isCompleted && (
                              <Badge className="text-xs bg-green-600">
                                Completed
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Actions */}
          <div className="flex flex-wrap gap-3 pt-4 border-t border-[#27272a]">
            <Button onClick={onEdit}>
              <Edit className="h-4 w-4 mr-2" />
              Edit Note
            </Button>

            <Button variant="outline">
              <Share className="h-4 w-4 mr-2" />
              Share
            </Button>

            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>

            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default NotePreviewModal;
