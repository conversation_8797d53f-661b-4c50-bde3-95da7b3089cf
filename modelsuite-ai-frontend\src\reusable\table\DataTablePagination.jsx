import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";

const DataTablePagination = ({ table, data }) => {
  const onPageSizeChange = (value) => {
    table.setPageSize(Number(value));
    table.setPageIndex(0);
  };

  return (
    <div className="flex items-center justify-between mt-6">
      <div className="flex items-center gap-x-4">
        <div className="flex w-[100px] items-center justify-center text-sm font-medium text-white">
          Page {table.getState().pagination.pageIndex + 1} of{" "}
          {table.getPageCount()}
        </div>{" "}
        <div className="flex items-center gap-x-1">
          <Button
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
            variant="outline"
            className="px-2 py-4"
            size="sm"
          >
            <ChevronLeft className="w-6 h-6" />
          </Button>
          <Button
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
            variant="outline"
            className="px-2 py-4"
            size="sm"
          >
            <ChevronRight className="w-6 h-6" />
          </Button>
        </div>
      </div>
    </div>
  );
};
export default DataTablePagination;
