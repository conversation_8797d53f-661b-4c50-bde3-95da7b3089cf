// Simple script to reset daily caption quotas for development
import mongoose from "mongoose";
import dotenv from "dotenv";

dotenv.config();

async function resetTodayQuotas() {
  try {
    await mongoose.connect(process.env.MONGO_URI);
    console.log("✅ Connected to MongoDB");

    // Delete today's quota records to reset usage
    const today = new Date().toISOString().split("T")[0];
    const result = await mongoose.connection.db
      .collection("captionquotas")
      .deleteMany({ date: today });

    console.log(
      `✅ Reset complete! Deleted ${result.deletedCount} quota records for ${today}`,
    );
    console.log(`� All models can now generate captions again today.`);

    await mongoose.disconnect();
    console.log("� Disconnected from MongoDB");
    process.exit(0);
  } catch (error) {
    console.error("❌ Error resetting quotas:", error);
    process.exit(1);
  }
}

resetTodayQuotas();
