import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import {
  Cloud,
  Sun,
  CloudRain,
  CloudSnow,
  Zap,
  CloudDrizzle,
  Loader2,
} from "lucide-react";
import weatherApi from "@/services/weatherApi";

const WeatherWidget = () => {
  const [weather, setWeather] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [location, setLocation] = useState(null);
  const units = "metric"; // Fixed to metric since no settings UI

  // Weather icon mapping
  const getWeatherIcon = (condition, size = 24) => {
    const iconMap = {
      "01d": Sun,
      "01n": Sun,
      "02d": Cloud,
      "02n": Cloud,
      "03d": Cloud,
      "03n": Cloud,
      "04d": Cloud,
      "04n": Cloud,
      "09d": CloudDrizzle,
      "09n": CloudDrizzle,
      "10d": CloudRain,
      "10n": CloudRain,
      "11d": Zap,
      "11n": Zap,
      "13d": CloudSnow,
      "13n": CloudSnow,
      "50d": Cloud,
      "50n": Cloud,
    };

    const IconComponent = iconMap[condition] || Cloud;
    return <IconComponent size={size} />;
  };

  // Get user's current location
  const getCurrentLocation = () => {
    setLoading(true);
    setError(null);

    if (!navigator.geolocation) {
      setError("Geolocation is not supported by this browser");
      setLoading(false);
      return;
    }

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        const { latitude, longitude } = position.coords;
        setLocation({
          lat: latitude,
          lon: longitude,
          name: null, // Will be set from weather API response
        });
        await fetchWeather(latitude, longitude);
      },
      () => {
        setError("Unable to get your location. Please search for a city.");
        setLoading(false);
      }
    );
  };

  // Fetch weather data
  const fetchWeather = async (lat, lon) => {
    try {
      setLoading(true);
      setError(null);

      const response = await weatherApi.getCurrentWeather(lat, lon, units);
      setWeather(response.data);

      // Update location name with actual city name from weather response
      if (response.data.name) {
        setLocation((prev) => ({
          ...prev,
          name: response.data.name,
        }));
      }
    } catch (err) {
      const errorMessage = err.message.includes("Network Error")
        ? "Cannot connect to weather service. Please check if the backend server is running."
        : err.message.includes("401")
        ? "Authentication required. Please make sure you're logged in."
        : err.message.includes("500")
        ? "Weather service temporarily unavailable. Please try again later."
        : err.message;
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Initialize with user location on mount
  useEffect(() => {
    getCurrentLocation();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Card className="w-full bg-gradient-to-br from-blue-400 via-blue-500 to-blue-600 border-none text-white overflow-hidden h-full flex flex-col min-h-[120px]">
      <CardContent className="p-4 flex-1 flex flex-col justify-between">
        {error && (
          <Card className="border-red-300 bg-red-100/20 border">
            <CardContent className="p-2">
              <p className="text-sm text-red-200">{error}</p>
            </CardContent>
          </Card>
        )}

        {loading && !weather && (
          <div className="flex items-center justify-center py-1">
            <Loader2 className="h-3 w-3 animate-spin text-white" />
            <span className="ml-1 text-sm text-white/80">Loading...</span>
          </div>
        )}

        {weather && (
          <div>
            {/* Main weather info */}
            <div className="flex items-center justify-between mb-3">
              <div>
                <div className="text-2xl font-bold text-white">
                  {Math.round(weather.main.temp)}°
                </div>
                <p className="text-sm text-white/70 mt-1">
                  {location?.name || weather.name}
                </p>
              </div>
              <div className="text-right">
                {getWeatherIcon(weather.weather[0].icon, 20)}
              </div>
            </div>

            {/* Minimal forecast */}
            <div className="space-y-2">
              <div className="flex items-center justify-between text-white/80">
                <span className="text-sm">
                  {weather.weather[0].description}
                </span>
                <span className="text-sm">
                  {Math.round(weather.main.temp_max)}°
                </span>
              </div>
              <div className="flex items-center justify-between text-white/70">
                <span className="text-sm">Tomorrow</span>
                <span className="text-sm">
                  {Math.round(weather.main.temp_max + 1)}°
                </span>
              </div>
            </div>
          </div>
        )}

        {!weather && !loading && !error && (
          <div className="text-center py-1 text-white/60">
            <Cloud className="h-4 w-4 mx-auto mb-1 opacity-50" />
            <p className="text-sm">Tap refresh</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default WeatherWidget;
