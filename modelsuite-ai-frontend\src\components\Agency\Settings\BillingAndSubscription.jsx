import React from "react";
import { Button } from "@/components/ui/button";

const BillingAndSubscription = () => {
  return (
    <div className="w-full flex flex-col md:flex-row gap-8 pl-2">
      {/* Left column: Current Plan + Payment Method */}
      <div className="flex-1 flex flex-col gap-6 min-w-[300px]">
        {/* Current Plan */}
        <div className="bg-[#161616] rounded-lg p-6 flex flex-col md:items-start border border-[#232323]">
          <div className="text-lg font-semibold text-white mb-1">Current Plan</div>
          <div className="text-base text-gray-100">Pro</div>
          <div className="text-sm text-gray-400">$49 <span className="text-gray-500">/ month</span></div>
          <div className="text-xs text-gray-400 mt-1">Status: <span className="text-green-500">Active</span></div>
          <Button className="mt-4 px-5 py-2 rounded-md text-sm font-semibold bg-[#2563EB] text-white hover:bg-[#2563EB]/90 self-start">Change Plan</Button>
        </div>
        {/* Payment Method */}
        <div className="bg-[#161616] rounded-lg p-6 flex flex-col md:items-start border border-[#232323]">
          <div className="text-base text-gray-100 mb-2">•••• 1xxx 4242</div>
          <Button className="px-5 py-2 rounded-md text-sm font-semibold bg-[#2563EB] text-white hover:bg-[#2563EB]/90 self-start">Update Payment Method</Button>
        </div>
      </div>
      {/* Right column: Billing History + Cancel Plan */}
      <div className="flex-[2] flex flex-col gap-6 min-w-[340px]">
        {/* Billing History */}
        <div className="bg-[#161616] rounded-lg p-6 border border-[#232323]">
          <div className="text-lg font-semibold text-white mb-4">Billing History</div>
          <div className="overflow-x-auto">
            <table className="min-w-full text-left text-sm">
              <thead>
                <tr className="text-gray-400">
                  <th className="py-2 pr-4 font-medium">Date</th>
                  <th className="py-2 pr-4 font-medium">Invoice</th>
                  <th className="py-2 pr-4 font-medium">Amount</th>
                  <th className="py-2 font-medium">Download</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-t border-[#232323]">
                  <td className="py-2 pr-4 text-gray-300">2025-07-01</td>
                  <td className="py-2 pr-4 text-gray-300">#INV-00423</td>
                  <td className="py-2 pr-4 text-gray-300">$49.00</td>
                  <td className="py-2">
                    <Button variant="secondary" className="px-3 py-1 text-xs rounded bg-[#232323] text-white">PDF</Button>
                  </td>
                </tr>
                <tr className="border-t border-[#232323]">
                  <td className="py-2 pr-4 text-gray-300">2025-06-01</td>
                  <td className="py-2 pr-4 text-gray-300">#INV-00391</td>
                  <td className="py-2 pr-4 text-gray-300">$49.00</td>
                  <td className="py-2">
                    <Button variant="secondary" className="px-3 py-1 text-xs rounded bg-[#232323] text-white">PDF</Button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        {/* Cancel Plan */}
        <div className="bg-[#161616] rounded-lg p-6 border border-[#232323] flex flex-col md:flex-row md:items-center md:justify-between">
          <div className="text-gray-300 text-sm mb-4 md:mb-0">
            Need to cancel your subscription? <br />You can do so here.
          </div>
          <Button variant="destructive" className="px-6 py-2 rounded-md text-sm font-semibold">Cancel Plan</Button>
        </div>
      </div>
    </div>
  );
};

export default BillingAndSubscription;
