import express from "express";
import {
  sendOtpForRegistration,
  verifyOtpAndRegister,
  login,
  verifyOtpAndLogin,
  getModelById,
  getAllModels,
  toggleMfa,
  updateModel,
  deleteModel,
  forgotPasswordSendOtp,
  forgotPasswordVerifyOtp,
  isAcceptingEmail,
  logoutUser,
  changeModelEmail,
  verifyNewModelEmail,
  changePasswordSendOtp,
  verifyOtpAndChangePassword,
  uploadModelAvatar,
  bulkUpdateStatus,
  bulkUpdateTags,
  bulkDeleteModels,
  bulkRemoveFromAgency,
  exportModelsCSV,
} from "../controllers/modelController.js";
import { verifyRole, verifyToken } from "../middlewares/authMiddleware.js";
import checkPermission from "../middlewares/permissionCheck.js";
import upload from "../middlewares/multer.js";

// //rate-limiter middlewares
// import { authLimiter } from "../middlewares/limiters/authLimiter.middleware.js";
// import { apiLimiter } from "../middlewares/limiters/apiLimiter.middleware.js";
// import { otpLimiter } from "../middlewares/limiters/otpLimiter.middlerware.js";
// import { strictLimiter } from "../middlewares/limiters/strictLimiter.middleware.js";

const router = express.Router();

// router.post("/register/send-otp", otpLimiter, sendOtpForRegistration);
router.post("/register/send-otp", sendOtpForRegistration);

// router.post("/register/verify-otp",authLimiter, verifyOtpAndRegister);
router.post("/register/verify-otp", verifyOtpAndRegister);

router.post("/login", login);
router.post("/login/verify-otp", verifyOtpAndLogin);

router.post("/forgot-password", forgotPasswordSendOtp);
router.post("/forgot-password/verify-otp", forgotPasswordVerifyOtp);

// Bulk Operations Routes - MUST be before /:id routes to avoid conflicts
router.put(
  "/bulk-update-status",
  verifyToken,
  verifyRole(["agency", "employee"]),
  checkPermission("models.edit"),
  bulkUpdateStatus,
);

router.put(
  "/bulk-update-tags",
  verifyToken,
  verifyRole(["agency", "employee"]),
  checkPermission("models.edit"),
  bulkUpdateTags,
);

router.delete(
  "/bulk-delete",
  verifyToken,
  verifyRole(["agency", "employee"]),
  checkPermission("models.delete"),
  bulkRemoveFromAgency,
);

router.get(
  "/",
  verifyToken,
  verifyRole(["agency", "employee"]),
  checkPermission("models.view"),
  getAllModels,
);
router.get("/:id", verifyToken, getModelById);
router.put(
  "/:id",
  verifyToken,
  verifyRole(["agency", "employee"]),
  checkPermission("models.edit"),
  updateModel,
);
router.post("/toggle-mfa", verifyToken, toggleMfa);
router.post("/logout", verifyToken, logoutUser);

router.patch("/change-email", verifyToken, changeModelEmail);
router.post("/verify-new-email", verifyToken, verifyNewModelEmail);

router.patch("/change-password", verifyToken, changePasswordSendOtp);
router.post("/change-password-verify", verifyToken, verifyOtpAndChangePassword);

//this is just a temporary controller for registeration testing
//this deletes a model from the database NOT agency
router.delete("/delete-model", deleteModel);

router.post("/update-is-accepting-email", verifyToken, isAcceptingEmail);

// POST /model/upload-avatar
router.post(
  "/upload-avatar",
  verifyToken,
  verifyRole(["model"]),
  upload.single("avatar"),
  uploadModelAvatar,
);

router.get(
  "/export/csv",
  verifyToken,
  verifyRole(["agency", "employee"]),
  checkPermission("models.view"),
  exportModelsCSV,
);

export default router;
