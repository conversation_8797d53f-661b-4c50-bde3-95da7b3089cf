import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

const SignAndSecurity = () => {
  const [form, setForm] = useState({
    oldPassword: "",
    newPassword: "",
  });

  const handleChange = (e) => {
    setForm((prev) => ({ ...prev, [e.target.name]: e.target.value }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // TODO: Add password change logic
    console.log(form);
  };

  return (
    <form onSubmit={handleSubmit} className="w-full max-w-md pl-2">
      <div className="mb-6">
        <label
          htmlFor="oldPassword"
          className="block text-sm font-medium text-gray-300 mb-1"
        >
          Old Password
        </label>
        <Input
          id="oldPassword"
          name="oldPassword"
          type="password"
          placeholder="Enter old password"
          value={form.oldPassword}
          onChange={handleChange}
          className="bg-[#1a1a1a] border border-[#2a2a2a] text-white focus:border-[#2563EB] focus:ring-1 focus:ring-[#2563EB]"
          autoComplete="off"
        />
      </div>
      <div className="mb-6">
        <label
          htmlFor="newPassword"
          className="block text-sm font-medium text-gray-300 mb-1"
        >
          New Password
        </label>
        <Input
          id="newPassword"
          name="newPassword"
          type="password"
          placeholder="Enter new password"
          value={form.newPassword}
          onChange={handleChange}
          className="bg-[#1a1a1a] border border-[#2a2a2a] text-white focus:border-[#2563EB] focus:ring-1 focus:ring-[#2563EB]"
          autoComplete="off"
        />
      </div>
      <div className="flex justify-end mt-8 pr-2">
        <Button
          type="submit"
          className="px-6 py-2 rounded-md text-sm font-semibold bg-[#2563EB] text-white hover:bg-[#2563EB]/90 focus:ring-2 focus:ring-[#2563EB] focus:ring-offset-2"
        >
          Change Password
        </Button>
      </div>
    </form>
  );
};

export default SignAndSecurity;
