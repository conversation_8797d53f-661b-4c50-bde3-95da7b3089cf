import React, { useState, useEffect, useCallback } from "react";
import { useDispatch } from "react-redux";
import { toast } from "sonner";
import {
  Bold,
  Italic,
  Underline,
  List,
  ListOrdered,
  Quote,
  Code,
  Link,
  Image,
  Upload,
  Save,
  X,
  Eye,
  Edit3,
  <PERSON>clip,
  Hash,
  Star,
  AlertCircle,
} from "lucide-react";

// UI Components
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";

// Reusable Components
import { FileDropzone, LoadingButton } from "@/reusable";

// Redux Actions
import { createNote, updateNote } from "@/redux/features/notes/notesSlice";
import notesApi from "@/services/notesApi";

const RichTextEditor = ({ open, onClose, note, modelId, onSave }) => {
  const dispatch = useDispatch();

  // Form State
  const [formData, setFormData] = useState({
    title: "",
    content: "",
    contentType: "markdown",
    priority: "medium",
    category: "general",
    customCategory: "",
    tags: [],
    visibility: "internal",
    attachments: [],
  });

  const [currentTag, setCurrentTag] = useState("");
  const [previewMode, setPreviewMode] = useState(false);
  const [loading, setLoading] = useState(false);

  // Initialize form data when note changes
  useEffect(() => {
    if (note) {
      setFormData({
        title: note.title || "",
        content: note.content || "",
        contentType: note.contentType || "markdown",
        priority: note.priority || "medium",
        category: note.category || "general",
        customCategory: note.customCategory || "",
        tags: note.tags || [],
        // Force internal visibility
        visibility: "internal",
        attachments: note.attachments || [],
      });
    } else {
      setFormData({
        title: "",
        content: "",
        contentType: "markdown",
        priority: "medium",
        category: "general",
        customCategory: "",
        tags: [],
        visibility: "internal",
        attachments: [],
      });
    }
  }, [note]);

  // Text formatting functions
  const insertText = useCallback(
    (before, after = "", placeholder = "") => {
      const textarea = document.getElementById("note-content");
      if (!textarea) return;

      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const selectedText = formData.content.substring(start, end);
      const textToInsert = selectedText || placeholder;

      const newContent =
        formData.content.substring(0, start) +
        before +
        textToInsert +
        after +
        formData.content.substring(end);

      setFormData((prev) => ({ ...prev, content: newContent }));

      // Reset cursor position
      setTimeout(() => {
        textarea.focus();
        const newPosition =
          start + before.length + textToInsert.length + after.length;
        textarea.setSelectionRange(newPosition, newPosition);
      }, 0);
    },
    [formData.content]
  );

  const formatBold = () => insertText("**", "**", "bold text");
  const formatItalic = () => insertText("*", "*", "italic text");
  const formatUnderline = () => insertText("<u>", "</u>", "underlined text");
  const formatCode = () => insertText("`", "`", "code");
  const formatQuote = () => insertText("> ", "", "quote");
  const formatUnorderedList = () => insertText("- ", "", "list item");
  const formatOrderedList = () => insertText("1. ", "", "list item");
  const formatLink = () => insertText("[", "](url)", "link text");
  const formatImage = () => insertText("![", "](image-url)", "alt text");

  // Tag management
  const addTag = () => {
    if (
      currentTag.trim() &&
      !formData.tags.includes(currentTag.trim().toLowerCase())
    ) {
      setFormData((prev) => ({
        ...prev,
        tags: [...prev.tags, currentTag.trim().toLowerCase()],
      }));
      setCurrentTag("");
    }
  };

  const removeTag = (tagToRemove) => {
    setFormData((prev) => ({
      ...prev,
      tags: prev.tags.filter((tag) => tag !== tagToRemove),
    }));
  };

  // File upload handling
  const handleFileUpload = async (files) => {
    if (!files.length) return;

    const uploadPromises = files.map(async (file) => {
      try {
        const response = await notesApi.uploadNoteAttachment(
          note?._id || "temp",
          file
        );
        return {
          url: response.data.url,
          originalName: file.name,
          size: file.size,
          type: file.type,
          uploadedAt: new Date(),
        };
      } catch {
        toast.error(`Failed to upload ${file.name}`);
        return null;
      }
    });

    const uploadedFiles = (await Promise.all(uploadPromises)).filter(Boolean);

    setFormData((prev) => ({
      ...prev,
      attachments: [...prev.attachments, ...uploadedFiles],
    }));

    toast.success(`${uploadedFiles.length} file(s) uploaded successfully`);
  };

  const removeAttachment = (index) => {
    setFormData((prev) => ({
      ...prev,
      attachments: prev.attachments.filter((_, i) => i !== index),
    }));
  };

  // Save note
  const handleSave = async () => {
    if (!formData.title.trim() || !formData.content.trim()) {
      toast.error("Title and content are required");
      return;
    }

    if (!modelId) {
      toast.error("Please select a model");
      return;
    }

    setLoading(true);

    try {
      const noteData = {
        ...formData,
        modelId,
        agencyId: localStorage.getItem("agencyId") || "",
      };

      // Always ensure visibility is internal (agency-only)
      noteData.visibility = "internal";

      if (note?._id) {
        await dispatch(
          updateNote({ noteId: note._id, data: noteData })
        ).unwrap();
        toast.success("Note updated successfully");
      } else {
        await dispatch(createNote({ payload: noteData })).unwrap();
        toast.success("Note created successfully");
      }

      onSave?.();
    } catch (error) {
      toast.error(error?.message || "Failed to save note");
    } finally {
      setLoading(false);
    }
  };

  // Render markdown preview
  const renderPreview = () => {
    if (formData.contentType !== "markdown") {
      return (
        <div
          className="prose prose-invert max-w-none"
          dangerouslySetInnerHTML={{ __html: formData.content }}
        />
      );
    }

    // Basic markdown rendering
    let html = formData.content
      .replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>")
      .replace(/\*(.*?)\*/g, "<em>$1</em>")
      .replace(/`(.*?)`/g, '<code class="bg-gray-800 px-1 rounded">$1</code>')
      .replace(
        /^> (.*$)/gm,
        '<blockquote class="border-l-4 border-gray-600 pl-4 italic">$1</blockquote>'
      )
      .replace(/^- (.*$)/gm, "<ul><li>$1</li></ul>")
      .replace(/^\d+\. (.*$)/gm, "<ol><li>$1</li></ol>")
      .replace(
        /\[(.*?)\]\((.*?)\)/g,
        '<a href="$2" class="text-blue-400 hover:underline">$1</a>'
      )
      .replace(
        /!\[(.*?)\]\((.*?)\)/g,
        '<img src="$2" alt="$1" class="max-w-full h-auto rounded" />'
      )
      .replace(/\n/g, "<br />");

    return (
      <div
        className="prose prose-invert max-w-none"
        dangerouslySetInnerHTML={{ __html: html }}
      />
    );
  };

  const categories = [
    "general",
    "performance",
    "behavior",
    "feedback",
    "reminder",
    "meeting",
    "contract",
    "payment",
  ];

  const priorities = [
    { value: "low", label: "Low", color: "bg-green-500" },
    { value: "medium", label: "Medium", color: "bg-yellow-500" },
    { value: "high", label: "High", color: "bg-orange-500" },
    { value: "critical", label: "Critical", color: "bg-red-500" },
  ];

  // Visibility options removed — notes are internal-only by policy

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="!w-[45vw] !max-w-[45vw] max-h-[90vh] bg-[#18181b] border-[#27272a] text-white">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-white text-lg">
            <Edit3 className="h-5 w-5" />
            {note ? "Edit Note" : "Create Note"}
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-4 lg:items-start">
          {/* Main Editor */}
          <div className="lg:col-span-2 space-y-4">
            {/* Title */}
            <div>
              <Label htmlFor="title" className="text-white text-sm">
                Title
              </Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, title: e.target.value }))
                }
                placeholder="Enter note title..."
                className="bg-[#1a1a1a] border-[#27272a] text-white placeholder:text-gray-400 mt-1"
              />
            </div>

            {/* Content */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <Label className="text-white text-sm">Content</Label>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setPreviewMode(!previewMode)}
                  >
                    {previewMode ? (
                      <Edit3 className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                    {previewMode ? "Edit" : "Preview"}
                  </Button>
                </div>
              </div>

              <Tabs value={previewMode ? "preview" : "edit"} className="w-full">
                <TabsList className="grid w-full grid-cols-2 bg-[#1a1a1a] border border-[#27272a]">
                  <TabsTrigger
                    value="edit"
                    onClick={() => setPreviewMode(false)}
                    className="text-gray-300 dark:text-gray-300 data-[state=active]:text-gray-900"
                  >
                    Edit
                  </TabsTrigger>
                  <TabsTrigger
                    value="preview"
                    onClick={() => setPreviewMode(true)}
                    className="text-gray-300 dark:text-gray-300 data-[state=active]:text-gray-900"
                  >
                    Preview
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="edit" className="mt-2">
                  {/* Formatting Toolbar */}
                  <div className="flex flex-wrap gap-1 p-2 bg-[#1a1a1a] border border-[#27272a] rounded-t-md">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={formatBold}
                      className="h-8 w-8"
                    >
                      <Bold className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={formatItalic}
                      className="h-8 w-8"
                    >
                      <Italic className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={formatUnderline}
                      className="h-8 w-8"
                    >
                      <Underline className="h-4 w-4" />
                    </Button>
                    <Separator orientation="vertical" className="mx-1 h-6" />
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={formatUnorderedList}
                      className="h-8 w-8"
                    >
                      <List className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={formatOrderedList}
                      className="h-8 w-8"
                    >
                      <ListOrdered className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={formatQuote}
                      className="h-8 w-8"
                    >
                      <Quote className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={formatCode}
                      className="h-8 w-8"
                    >
                      <Code className="h-4 w-4" />
                    </Button>
                    <Separator orientation="vertical" className="mx-1 h-6" />
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={formatLink}
                      className="h-8 w-8"
                    >
                      <Link className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={formatImage}
                      className="h-8 w-8"
                    >
                      <Image className="h-4 w-4" />
                    </Button>
                  </div>

                  <Textarea
                    id="note-content"
                    value={formData.content}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        content: e.target.value,
                      }))
                    }
                    placeholder="Write your note content here... (Markdown supported)"
                    className="min-h-[200px] h-[200px] bg-[#1a1a1a] border-[#27272a] rounded-t-none border-t-0 resize-none font-mono text-white placeholder:text-gray-400"
                  />
                </TabsContent>

                <TabsContent value="preview" className="mt-2">
                  <div className="min-h-[200px] h-[200px] p-4 bg-[#1a1a1a] border border-[#27272a] rounded-md overflow-auto">
                    {formData.content ? (
                      renderPreview()
                    ) : (
                      <p className="text-gray-500">
                        Nothing to preview yet. Start writing to see a preview.
                      </p>
                    )}
                  </div>
                </TabsContent>
              </Tabs>
            </div>

            {/* File Attachments */}
            <div>
              <Label className="text-white text-sm">Attachments</Label>
              <FileDropzone
                onUpload={handleFileUpload}
                multiple={true}
                accept="image/*,application/pdf,.doc,.docx,.txt"
                className="mt-2"
              />

              {formData.attachments.length > 0 && (
                <div className="mt-2 space-y-2">
                  {formData.attachments.map((attachment, index) => (
                    <div
                      key={index}
                      className="flex items-center gap-2 p-2 bg-[#1a1a1a] border border-[#27272a] rounded"
                    >
                      <Paperclip className="h-4 w-4 text-gray-400" />
                      <span className="flex-1 text-sm">
                        {attachment.originalName}
                      </span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeAttachment(index)}
                        className="h-6 w-6 p-0 text-red-400 hover:text-red-300"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6 lg:pt-[26px]">
            {/* Actions */}
            <div className="flex flex-col gap-2">
              <LoadingButton
                onClick={handleSave}
                isLoading={loading}
                variant="primary"
              >
                <Save className="h-4 w-4 mr-2" />
                {note ? "Update Note" : "Create Note"}
              </LoadingButton>
              <Button
                variant="outline"
                onClick={onClose}
                className="text-gray-800 border-gray-600 hover:bg-white/5"
              >
                Cancel
              </Button>
            </div>

            {/* Priority */}
            <div>
              <Label className="text-white text-sm">Priority</Label>
              <Select
                value={formData.priority}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, priority: value }))
                }
              >
                <SelectTrigger className="bg-[#1a1a1a] border-[#27272a] text-white mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-[#0f1720] border-[#27272a] text-white">
                  {priorities.map((priority) => (
                    <SelectItem
                      key={priority.value}
                      value={priority.value}
                      className="text-white hover:bg-white/10 data-[state=checked]:bg-white/20 data-[state=checked]:ring-1 data-[state=checked]:ring-white/20"
                    >
                      <div className="flex items-center gap-2">
                        <div
                          className={`w-2 h-2 rounded-full ${priority.color}`}
                        />
                        {priority.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Category */}
            <div>
              <Label className="text-white text-sm">Category</Label>
              <Select
                value={formData.category}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, category: value }))
                }
              >
                <SelectTrigger className="bg-[#1a1a1a] border-[#27272a] text-white mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-[#0f1720] border-[#27272a] text-white">
                  {categories.map((category) => (
                    <SelectItem
                      key={category}
                      value={category}
                      className="text-white hover:bg-white/5 data-[state=checked]:bg-white/10"
                    >
                      {category.charAt(0).toUpperCase() + category.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Custom Category */}
            {formData.category === "custom" && (
              <div>
                <Label className="text-white text-sm">Custom Category</Label>
                <Input
                  value={formData.customCategory}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      customCategory: e.target.value,
                    }))
                  }
                  placeholder="Enter custom category"
                  className="bg-[#1a1a1a] border-[#27272a] text-white placeholder:text-gray-400 mt-1"
                />
              </div>
            )}

            {/* Visibility removed: notes are internal-only by policy */}

            {/* Tags */}
            <div>
              <Label className="text-white text-sm">Tags</Label>
              <div className="flex gap-2 mt-1">
                <Input
                  value={currentTag}
                  onChange={(e) => setCurrentTag(e.target.value)}
                  placeholder="Add tag..."
                  className="bg-[#1a1a1a] border-[#27272a] text-white placeholder:text-gray-400 flex-1"
                  onKeyPress={(e) =>
                    e.key === "Enter" && (e.preventDefault(), addTag())
                  }
                />
                <Button
                  onClick={addTag}
                  size="sm"
                  variant="outline"
                  className="text-gray-600 hover:text-dark"
                >
                  <Hash className="h-4 w-4" />
                </Button>
              </div>

              {formData.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {formData.tags.map((tag) => (
                    <Badge key={tag} variant="secondary">
                      {tag}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeTag(tag)}
                        className="h-4 w-4 p-0 ml-1 text-gray-400 hover:text-red-300 hover:bg-red-900/20"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>

            {/* Content Type */}
            <div>
              <Label className="text-white text-sm">Content Type</Label>
              <Select
                value={formData.contentType}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, contentType: value }))
                }
              >
                <SelectTrigger className="bg-[#1a1a1a] border-[#27272a] text-white mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-[#0f1720] border-[#27272a] text-white">
                  <SelectItem
                    className="text-white hover:bg-white/5 data-[state=checked]:bg-white/10"
                    value="plain"
                  >
                    Plain Text
                  </SelectItem>
                  <SelectItem
                    className="text-white hover:bg-white/5 data-[state=checked]:bg-white/10"
                    value="markdown"
                  >
                    Markdown
                  </SelectItem>
                  <SelectItem
                    className="text-white hover:bg-white/5 data-[state=checked]:bg-white/10"
                    value="html"
                  >
                    HTML
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default RichTextEditor;
