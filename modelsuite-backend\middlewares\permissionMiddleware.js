import { isValidPermission } from "../config/permissions.js";

/**
 * Middleware to check if the authenticated user has the required permission
 * @param {string} requiredPermission - The permission to check for
 * @returns {Function} Express middleware function
 */
export const checkPermission = (requiredPermission) => {
  return async (req, res, next) => {
    try {
      // Ensure user is authenticated
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: "Authentication required",
        });
      }

      // Validate the permission format
      if (!isValidPermission(requiredPermission)) {
        return res.status(500).json({
          success: false,
          message: "Invalid permission specified",
        });
      }

      // Agency users have full access to all permissions
      if (req.user.role === "agency") {
        return next();
      }

      // Model users with agencyId also have access to persona features
      if (req.user.role === "model" && req.user.agencyId) {
        return next();
      }

      // For employees, check their specific permissions
      if (req.user.role === "employee") {
        const userPermissions = req.user.permissions || [];

        // Check if user has the required permission
        const hasPermission = userPermissions.includes(requiredPermission);

        // Check for denied permissions (permissions starting with '-')
        const deniedPermission = `-${requiredPermission}`;
        const isDenied = userPermissions.includes(deniedPermission);

        if (isDenied) {
          return res.status(403).json({
            success: false,
            message: `Access denied: Permission '${requiredPermission}' is explicitly denied`,
          });
        }

        if (!hasPermission) {
          return res.status(403).json({
            success: false,
            message: `Access denied: Permission '${requiredPermission}' required`,
          });
        }

        return next();
      }

      // For other roles (model, admin, etc.), deny access to persona features
      return res.status(403).json({
        success: false,
        message: "Access denied: Agency or employee role required",
      });
    } catch (error) {
      console.error("Permission check error:", error);
      return res.status(500).json({
        success: false,
        message: "Internal server error during permission check",
      });
    }
  };
};

/**
 * Middleware to check if user has any of the specified permissions
 * @param {string[]} permissions - Array of permissions to check for
 * @returns {Function} Express middleware function
 */
export const checkAnyPermission = (permissions) => {
  return async (req, res, next) => {
    try {
      // Ensure user is authenticated
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: "Authentication required",
        });
      }

      // Agency users have full access
      if (req.user.role === "agency") {
        return next();
      }

      // Model users with agencyId also have access
      if (req.user.role === "model" && req.user.agencyId) {
        return next();
      }

      // For employees, check if they have any of the required permissions
      if (req.user.role === "employee") {
        const userPermissions = req.user.permissions || [];

        const hasAnyPermission = permissions.some((permission) => {
          const isDenied = userPermissions.includes(`-${permission}`);
          const hasPermission = userPermissions.includes(permission);
          return hasPermission && !isDenied;
        });

        if (!hasAnyPermission) {
          return res.status(403).json({
            success: false,
            message: `Access denied: One of these permissions required: ${permissions.join(", ")}`,
          });
        }

        return next();
      }

      // For other roles, deny access
      return res.status(403).json({
        success: false,
        message: "Access denied: Agency or employee role required",
      });
    } catch (error) {
      console.error("Permission check error:", error);
      return res.status(500).json({
        success: false,
        message: "Internal server error during permission check",
      });
    }
  };
};

export default { checkPermission, checkAnyPermission };
