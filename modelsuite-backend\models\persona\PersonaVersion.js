import mongoose from "mongoose";

/**
 * PersonaVersion Schema - Stores version history for persona profiles
 * Maintains up to 5 versions per model with automatic archiving
 */
const personaVersionSchema = new mongoose.Schema(
  {
    // Reference to the current persona
    currentPersonaId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "PersonaProfile",
      required: true,
    },

    // Model this version belongs to
    modelId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ModelUser",
      required: true,
    },

    // Agency managing this persona
    agencyId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      required: true,
    },

    // Version information
    versionNumber: {
      type: Number,
      required: true,
    },

    // Complete persona data snapshot at this version
    personaSnapshot: {
      personaText: {
        type: String,
        required: true,
      },
      tags: {
        type: [String],
        required: true,
      },
      demographics: {
        age: String,
        location: String,
      },
      interests: {
        type: [String],
        default: [],
      },
      goals: {
        type: [String],
        default: [],
      },
      painPoints: {
        type: [String],
        default: [],
      },
      psychographics: {
        values: [String],
        customValues: [String],
        lifestyle: String,
        personality: [String],
        motivations: [String],
      },
      behavioral: {
        postingStyle: String,
        postingFrequency: String,
        contentPreferences: [String],
        engagementStyle: String,
        peakActivityTimes: [String],
      },
      communicationStyle: {
        tone: String,
        vocabulary: String,
        emojiUsage: String,
        hashtagStrategy: String,
      },
    },

    // Version metadata
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      refPath: "createdByModel",
    },

    createdByModel: {
      type: String,
      required: true,
      enum: ["Agency", "Employee"],
    },

    // Changes made in this version
    changeLog: {
      changeType: {
        type: String,
        enum: ["created", "regenerated", "manual_edit", "ai_enhancement"],
        required: true,
      },
      changeDescription: {
        type: String,
        maxlength: 500,
        trim: true,
      },
      changedFields: {
        type: [String],
        default: [],
      },
      previousVersion: {
        type: Number,
      },
    },

    // Generation context for this version
    generationContext: {
      aiEngine: {
        type: String,
        enum: ["gpt-4", "gemini-vision", "manual"],
      },
      promptVersion: {
        type: String,
      },
      dataSourcesUsed: {
        profileData: Boolean,
        questionnaireData: Boolean,
        captionAnalysis: Boolean,
        userTags: Boolean,
      },
      processingTime: {
        type: Number, // in milliseconds
      },
    },

    // Quality metrics for this version
    qualityMetrics: {
      completenessScore: {
        type: Number,
        min: 0,
        max: 100,
      },
      agencyRating: {
        type: Number,
        min: 1,
        max: 5,
      },
      usageCount: {
        type: Number,
        default: 0,
      },
      lastUsedAt: {
        type: Date,
      },
    },

    // Comparison with previous version
    versionComparison: {
      improvementAreas: {
        type: [String],
        default: [],
      },
      regressionAreas: {
        type: [String],
        default: [],
      },
      overallImprovement: {
        type: String,
        enum: [
          "significant_improvement",
          "slight_improvement",
          "no_change",
          "slight_regression",
          "significant_regression",
        ],
      },
    },

    // Status and archiving
    status: {
      type: String,
      enum: ["active", "archived", "deprecated"],
      default: "active",
    },

    isCurrentVersion: {
      type: Boolean,
      default: false,
    },

    archivedAt: {
      type: Date,
    },

    archivedReason: {
      type: String,
      enum: [
        "version_limit_exceeded",
        "manual_archive",
        "quality_issues",
        "superseded",
      ],
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  },
);

// Indexes for performance
personaVersionSchema.index({ modelId: 1, versionNumber: -1 });
personaVersionSchema.index({ currentPersonaId: 1 });
personaVersionSchema.index({ agencyId: 1, createdAt: -1 });
personaVersionSchema.index({ status: 1, isCurrentVersion: 1 });
personaVersionSchema.index({ "qualityMetrics.agencyRating": -1 });

// Virtual for version age
personaVersionSchema.virtual("versionAge").get(function () {
  return Math.floor((Date.now() - this.createdAt) / (1000 * 60 * 60 * 24));
});

// Virtual for quality summary
personaVersionSchema.virtual("qualitySummary").get(function () {
  const completeness = this.qualityMetrics.completenessScore || 0;
  const rating = this.qualityMetrics.agencyRating || 3;
  const usage = this.qualityMetrics.usageCount || 0;

  let score = 0;
  score += completeness * 0.4; // 40% weight
  score += (rating / 5) * 100 * 0.3; // 30% weight
  score += Math.min(usage / 10, 1) * 100 * 0.3; // 30% weight

  return Math.round(score);
});

// Static method to get version history for a model
personaVersionSchema.statics.getVersionHistory = function (
  modelId,
  options = {},
) {
  const query = this.find({ modelId });

  if (options.includeArchived !== true) {
    query.where({ status: { $ne: "archived" } });
  }

  return query
    .sort({ versionNumber: -1 })
    .limit(options.limit || 10)
    .populate("createdBy", "fullName username");
};

// Static method to get current version
personaVersionSchema.statics.getCurrentVersion = function (modelId) {
  return this.findOne({
    modelId,
    isCurrentVersion: true,
    status: "active",
  });
};

// Static method to compare versions
personaVersionSchema.statics.compareVersions = function (
  modelId,
  version1,
  version2,
) {
  return this.find({
    modelId,
    versionNumber: { $in: [version1, version2] },
  }).sort({ versionNumber: 1 });
};

// Static method to archive old versions
personaVersionSchema.statics.archiveOldVersions = async function (
  modelId,
  keepCount = 5,
) {
  const versions = await this.find({ modelId, status: "active" }).sort({
    versionNumber: -1,
  });

  if (versions.length > keepCount) {
    const versionsToArchive = versions.slice(keepCount);

    for (const version of versionsToArchive) {
      version.status = "archived";
      version.archivedAt = new Date();
      version.archivedReason = "version_limit_exceeded";
      await version.save();
    }

    return versionsToArchive.length;
  }

  return 0;
};

// Instance method to mark as current version
personaVersionSchema.methods.setAsCurrentVersion = async function () {
  // Remove current version flag from other versions
  await this.constructor.updateMany(
    { modelId: this.modelId },
    { isCurrentVersion: false },
  );

  // Set this as current version
  this.isCurrentVersion = true;
  this.status = "active";
  return this.save();
};

// Instance method to calculate improvement over previous version
personaVersionSchema.methods.calculateImprovement = async function () {
  if (this.versionNumber === 1) {
    this.versionComparison.overallImprovement = "no_change";
    return;
  }

  const previousVersion = await this.constructor.findOne({
    modelId: this.modelId,
    versionNumber: this.versionNumber - 1,
  });

  if (!previousVersion) return;

  const currentQuality = this.qualitySummary;
  const previousQuality = previousVersion.qualitySummary;
  const difference = currentQuality - previousQuality;

  if (difference >= 20) {
    this.versionComparison.overallImprovement = "significant_improvement";
  } else if (difference >= 5) {
    this.versionComparison.overallImprovement = "slight_improvement";
  } else if (difference >= -5) {
    this.versionComparison.overallImprovement = "no_change";
  } else if (difference >= -20) {
    this.versionComparison.overallImprovement = "slight_regression";
  } else {
    this.versionComparison.overallImprovement = "significant_regression";
  }
};

// Pre-save middleware
personaVersionSchema.pre("save", async function (next) {
  // Calculate improvement if not already done
  if (
    this.isModified("qualityMetrics") &&
    !this.versionComparison.overallImprovement
  ) {
    await this.calculateImprovement();
  }

  next();
});

const PersonaVersion = mongoose.model("PersonaVersion", personaVersionSchema);

export default PersonaVersion;
