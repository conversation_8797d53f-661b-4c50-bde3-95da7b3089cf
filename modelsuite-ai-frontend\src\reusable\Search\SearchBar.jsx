import * as React from "react";
import { cn } from "@/lib/utils";
import { Search, X, Filter, ChevronDown } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from "@/components/ui/dropdown-menu";

const SearchBar = ({
  placeholder = "Search...",
  value = "",
  onChange,
  onSearch,
  onClear,
  onFilterChange,
  debounceMs = 300,
  showFilter = false,
  showClear = true,
  filters = [],
  activeFilters = [],
  suggestions = [],
  showSuggestions = false,
  size = "default", // "sm", "default", "lg"
  variant = "default", // "default", "minimal", "bordered"
  disabled = false,
  loading = false,
  className,
  inputClassName,
  ...props
}) => {
  const [searchValue, setSearchValue] = React.useState(value);
  const [showSuggestionsDropdown, setShowSuggestionsDropdown] =
    React.useState(false);
  const [focusedSuggestion, setFocusedSuggestion] = React.useState(-1);
  const inputRef = React.useRef(null);
  const suggestionsRef = React.useRef(null);

  // Debounced search
  const debouncedSearch = React.useCallback(
    React.useMemo(() => {
      let timeoutId;
      return (searchTerm) => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
          if (onChange) {
            onChange(searchTerm);
          }
          if (onSearch) {
            onSearch(searchTerm);
          }
        }, debounceMs);
      };
    }, [onChange, onSearch, debounceMs]),
    [onChange, onSearch, debounceMs]
  );

  // Handle input change
  const handleInputChange = (e) => {
    const newValue = e.target.value;
    setSearchValue(newValue);
    debouncedSearch(newValue);

    if (showSuggestions && suggestions.length > 0) {
      setShowSuggestionsDropdown(newValue.length > 0);
      setFocusedSuggestion(-1);
    }
  };

  // Handle clear
  const handleClear = () => {
    setSearchValue("");
    setShowSuggestionsDropdown(false);
    setFocusedSuggestion(-1);
    if (onClear) {
      onClear();
    }
    if (onChange) {
      onChange("");
    }
    inputRef.current?.focus();
  };

  // Handle search submit
  const handleSearch = (searchTerm = searchValue) => {
    if (onSearch) {
      onSearch(searchTerm);
    }
    setShowSuggestionsDropdown(false);
  };

  // Handle keyboard navigation
  const handleKeyDown = (e) => {
    if (!showSuggestionsDropdown || suggestions.length === 0) {
      if (e.key === "Enter") {
        handleSearch();
      }
      return;
    }

    switch (e.key) {
      case "ArrowDown":
        e.preventDefault();
        setFocusedSuggestion((prev) =>
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case "ArrowUp":
        e.preventDefault();
        setFocusedSuggestion((prev) =>
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;
      case "Enter":
        e.preventDefault();
        if (focusedSuggestion >= 0) {
          const suggestion = suggestions[focusedSuggestion];
          const suggestionValue =
            typeof suggestion === "string" ? suggestion : suggestion.value;
          setSearchValue(suggestionValue);
          handleSearch(suggestionValue);
        } else {
          handleSearch();
        }
        break;
      case "Escape":
        setShowSuggestionsDropdown(false);
        setFocusedSuggestion(-1);
        inputRef.current?.blur();
        break;
    }
  };

  // Handle suggestion click
  const handleSuggestionClick = (suggestion) => {
    const suggestionValue =
      typeof suggestion === "string" ? suggestion : suggestion.value;
    setSearchValue(suggestionValue);
    handleSearch(suggestionValue);
  };

  // Handle filter change
  const handleFilterChange = (filter) => {
    if (onFilterChange) {
      onFilterChange(filter);
    }
  };

  // Size configurations
  const sizeConfig = {
    sm: {
      container: "h-8",
      input: "text-sm px-8 py-1.5",
      icon: "h-4 w-4",
      button: "h-6 w-6",
    },
    default: {
      container: "h-10",
      input: "text-sm px-10 py-2",
      icon: "h-4 w-4",
      button: "h-7 w-7",
    },
    lg: {
      container: "h-12",
      input: "text-base px-12 py-3",
      icon: "h-5 w-5",
      button: "h-8 w-8",
    },
  };

  const sizes = sizeConfig[size] || sizeConfig.default;

  // Variant styles
  const getVariantClasses = () => {
    switch (variant) {
      case "minimal":
        return "border-0 bg-transparent focus-within:bg-[#1a1a1a]/50";
      case "bordered":
        return "border-2 border-gray-600 focus-within:border-gray-500";
      default:
        return "border border-gray-800 bg-[#1a1a1a] focus-within:border-gray-700 text-white";
    }
  };

  // Sync external value
  React.useEffect(() => {
    setSearchValue(value);
  }, [value]);

  // Close suggestions on outside click
  React.useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target)
      ) {
        setShowSuggestionsDropdown(false);
        setFocusedSuggestion(-1);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const hasActiveFilters = activeFilters && activeFilters.length > 0;

  return (
    <div className={cn("relative", className)} ref={suggestionsRef}>
      <div
        className={cn(
          "relative rounded-lg transition-all duration-200",
          sizes.container,
          getVariantClasses(),
          disabled && "opacity-50 cursor-not-allowed"
        )}
      >
        {/* Search Icon */}
        <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
          <Search className={cn(sizes.icon, loading && "animate-spin")} />
        </div>

        {/* Input */}
        <Input
          ref={inputRef}
          type="text"
          placeholder={placeholder}
          value={searchValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => {
            if (showSuggestions && suggestions.length > 0 && searchValue) {
              setShowSuggestionsDropdown(true);
            }
          }}
          disabled={disabled}
          className={cn(
            "border-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0",
            "placeholder:text-gray-500 text-gray-300",
            sizes.input,
            inputClassName
          )}
          {...props}
        />

        {/* Clear Button */}
        {showClear && searchValue && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className={cn(
              "absolute right-2 top-1/2 -translate-y-1/2",
              "hover:bg-gray-800 text-gray-500 hover:text-gray-300",
              "p-0 rounded-sm",
              sizes.button,
              showFilter && "right-12"
            )}
            onClick={handleClear}
            disabled={disabled}
          >
            <X className={sizes.icon} />
          </Button>
        )}

        {/* Filter Button */}
        {showFilter && filters.length > 0 && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className={cn(
                  "absolute right-2 top-1/2 -translate-y-1/2",
                  "hover:bg-gray-800 text-gray-500 hover:text-gray-300",
                  "p-0 rounded-sm",
                  sizes.button,
                  hasActiveFilters && "text-gray-300 bg-gray-800"
                )}
                disabled={disabled}
              >
                <Filter className={sizes.icon} />
                {hasActiveFilters && (
                  <span className="ml-1 text-xs bg-gray-600 text-gray-300 px-1 rounded-full">
                    {activeFilters.length}
                  </span>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="end"
              className="bg-[#1a1a1a] border-gray-800"
            >
              <DropdownMenuLabel className="text-gray-400 text-xs">
                Filter by
              </DropdownMenuLabel>
              <DropdownMenuSeparator className="bg-gray-800" />
              {filters.map((filter, index) => (
                <DropdownMenuItem
                  key={index}
                  onClick={() => handleFilterChange(filter)}
                  className={cn(
                    "text-gray-300 focus:bg-gray-800 focus:text-gray-200 cursor-pointer text-sm",
                    activeFilters.includes(filter.value) &&
                      "bg-gray-800 text-gray-200"
                  )}
                >
                  {filter.icon && <filter.icon className="mr-2 h-4 w-4" />}
                  {filter.label}
                  {filter.count && (
                    <span className="ml-auto text-xs text-gray-500">
                      {filter.count}
                    </span>
                  )}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>

      {/* Suggestions Dropdown */}
      {showSuggestionsDropdown && suggestions.length > 0 && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-[#1a1a1a] border border-gray-800 rounded-lg shadow-lg z-50 max-h-60 overflow-auto">
          {suggestions.map((suggestion, index) => {
            const suggestionObj =
              typeof suggestion === "string"
                ? { value: suggestion, label: suggestion }
                : suggestion;

            return (
              <button
                key={index}
                type="button"
                className={cn(
                  "w-full px-4 py-2 text-left text-gray-300 hover:bg-gray-800 focus:bg-gray-800 text-sm",
                  "border-0 bg-transparent focus:outline-none",
                  focusedSuggestion === index && "bg-gray-800",
                  index === 0 && "rounded-t-lg",
                  index === suggestions.length - 1 && "rounded-b-lg"
                )}
                onClick={() => handleSuggestionClick(suggestion)}
                onMouseEnter={() => setFocusedSuggestion(index)}
              >
                <div className="flex items-center justify-between">
                  <span className="truncate">{suggestionObj.label}</span>
                  {suggestionObj.category && (
                    <span className="text-xs text-gray-500 ml-2">
                      {suggestionObj.category}
                    </span>
                  )}
                </div>
                {suggestionObj.description && (
                  <div className="text-xs text-gray-500 mt-1 truncate">
                    {suggestionObj.description}
                  </div>
                )}
              </button>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default SearchBar;
