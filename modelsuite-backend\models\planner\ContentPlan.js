import mongoose from "mongoose";

/**
 * ContentPlan Schema - Weekly content planning for models
 * Organizes content goals and tasks for strategic content creation
 */
const contentPlanSchema = new mongoose.Schema(
  {
    // Plan identification
    name: {
      type: String,
      required: true,
      maxlength: 200,
      trim: true,
    },

    // Week planning details
    weekStart: {
      type: Date,
      required: true,
      index: true,
    },

    weekEnd: {
      type: Date,
      required: true,
    },

    // Associated goals
    goals: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "ContentGoal",
      },
    ],

    // Associated tasks
    tasks: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "ContentTask",
      },
    ],

    // Plan status and workflow
    status: {
      type: String,
      enum: ["draft", "active", "completed", "archived"],
      default: "draft",
      index: true,
    },

    // Planning metadata
    totalTasksPlanned: {
      type: Number,
      default: 0,
    },

    tasksCompleted: {
      type: Number,
      default: 0,
    },

    // Content themes and focus areas
    themes: [
      {
        name: {
          type: String,
          required: true,
        },
        description: {
          type: String,
          maxlength: 500,
        },
        color: {
          type: String,
          default: "#6366f1", // Default theme color
        },
      },
    ],

    // Platform distribution
    platformDistribution: {
      instagram: { type: Number, default: 0 },
      tiktok: { type: Number, default: 0 },
      facebook: { type: Number, default: 0 },
      twitter: { type: Number, default: 0 },
      linkedin: { type: Number, default: 0 },
    },

    // Content type distribution
    contentTypeDistribution: {
      image: { type: Number, default: 0 },
      video: { type: Number, default: 0 },
      carousel: { type: Number, default: 0 },
      reel: { type: Number, default: 0 },
      story: { type: Number, default: 0 },
    },

    // Auto-scheduling settings
    autoSchedulingEnabled: {
      type: Boolean,
      default: false,
    },

    schedulingPreferences: {
      preferredTimes: [
        {
          day: {
            type: String,
            enum: [
              "monday",
              "tuesday",
              "wednesday",
              "thursday",
              "friday",
              "saturday",
              "sunday",
            ],
          },
          times: [
            {
              hour: { type: Number, min: 0, max: 23 },
              minute: { type: Number, min: 0, max: 59 },
            },
          ],
        },
      ],
      timezone: {
        type: String,
        default: "UTC",
      },
      minPostGap: {
        type: Number, // Hours between posts
        default: 4,
      },
      maxPostsPerDay: {
        type: Number,
        default: 3,
      },
    },

    // Performance tracking
    performance: {
      totalReach: {
        type: Number,
        default: 0,
      },
      totalEngagement: {
        type: Number,
        default: 0,
      },
      avgEngagementRate: {
        type: Number,
        default: 0,
      },
      completionRate: {
        type: Number,
        default: 0,
      },
      lastUpdated: {
        type: Date,
        default: Date.now,
      },
    },

    // AI assistance
    aiAssisted: {
      type: Boolean,
      default: false,
    },

    aiRecommendations: {
      // AI Strategy Data
      strategy: {
        type: mongoose.Schema.Types.Mixed,
        default: null,
      },

      // Content Suggestions by Platform
      contentSuggestions: {
        type: mongoose.Schema.Types.Mixed,
        default: {},
      },

      // Timing Analysis
      timingAnalysis: {
        type: mongoose.Schema.Types.Mixed,
        default: {},
      },

      // Optimizations Applied
      optimizations: {
        type: mongoose.Schema.Types.Mixed,
        default: {},
      },

      // Legacy array format for backwards compatibility
      recommendations: [
        {
          type: {
            type: String,
            enum: ["timing", "content", "hashtags", "platform", "theme"],
          },
          recommendation: {
            type: String,
            maxlength: 500,
          },
          confidence: {
            type: Number,
            min: 0,
            max: 100,
          },
          applied: {
            type: Boolean,
            default: false,
          },
          createdAt: {
            type: Date,
            default: Date.now,
          },
        },
      ],

      // Metadata
      generatedAt: {
        type: Date,
        default: Date.now,
      },

      lastOptimized: {
        type: Date,
        default: null,
      },

      provider: {
        type: String,
        enum: ["gpt", "gemini"],
        default: "gpt",
      },
    },

    // Ownership and permissions
    agencyId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      required: true,
      index: true,
    },

    modelId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ModelUser",
      required: true,
      index: true,
    },

    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
    },

    createdByType: {
      type: String,
      required: true,
      enum: ["Agency", "ModelUser", "Employee"],
    },

    // Collaboration
    collaborators: [
      {
        userId: {
          type: mongoose.Schema.Types.ObjectId,
          required: true,
        },
        userType: {
          type: String,
          enum: ["Agency", "ModelUser", "Employee"],
          required: true,
        },
        role: {
          type: String,
          enum: ["viewer", "editor", "manager"],
          default: "viewer",
        },
        addedAt: {
          type: Date,
          default: Date.now,
        },
      },
    ],

    // Versioning and backup
    version: {
      type: Number,
      default: 1,
    },

    previousVersions: [
      {
        versionNumber: Number,
        data: mongoose.Schema.Types.Mixed,
        savedAt: Date,
        savedBy: mongoose.Schema.Types.ObjectId,
      },
    ],

    // Plan notes and description
    description: {
      type: String,
      maxlength: 1000,
      trim: true,
    },

    notes: {
      type: String,
      maxlength: 2000,
      trim: true,
    },

    // Template and reusability
    isTemplate: {
      type: Boolean,
      default: false,
    },

    templateName: {
      type: String,
      maxlength: 100,
    },

    basedOnTemplate: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ContentPlan",
    },

    // Archive and deletion
    isArchived: {
      type: Boolean,
      default: false,
      index: true,
    },

    archivedAt: {
      type: Date,
    },

    archivedBy: {
      type: mongoose.Schema.Types.ObjectId,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  },
);

// Compound indexes for efficient queries
contentPlanSchema.index({ agencyId: 1, modelId: 1, weekStart: 1 });
contentPlanSchema.index({ agencyId: 1, status: 1, createdAt: -1 });
contentPlanSchema.index({ modelId: 1, weekStart: 1, isArchived: 1 });
contentPlanSchema.index({ weekStart: 1, weekEnd: 1 });

// Virtual for completion percentage
contentPlanSchema.virtual("completionPercentage").get(function () {
  if (this.totalTasksPlanned === 0) return 0;
  return Math.round((this.tasksCompleted / this.totalTasksPlanned) * 100);
});

// Virtual for week number
contentPlanSchema.virtual("weekNumber").get(function () {
  const date = new Date(this.weekStart);
  const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
  const pastDaysOfYear = (date - firstDayOfYear) / 86400000;
  return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
});

// Virtual for days remaining in week
contentPlanSchema.virtual("daysRemaining").get(function () {
  const now = new Date();
  const weekEnd = new Date(this.weekEnd);

  if (weekEnd <= now) return 0;

  return Math.ceil((weekEnd - now) / (1000 * 60 * 60 * 24));
});

// Virtual for plan health score
contentPlanSchema.virtual("healthScore").get(function () {
  let score = 0;
  let factors = 0;

  // Completion rate factor (40%)
  score += this.completionPercentage * 0.4;
  factors += 40;

  // Goal alignment factor (30%)
  if (this.goals.length > 0) {
    score += 30;
  }
  factors += 30;

  // Content distribution factor (20%)
  const platformCount = Object.values(this.platformDistribution).filter(
    (count) => count > 0,
  ).length;
  const contentTypeCount = Object.values(this.contentTypeDistribution).filter(
    (count) => count > 0,
  ).length;

  if (platformCount >= 2) score += 10;
  if (contentTypeCount >= 2) score += 10;
  factors += 20;

  // Engagement performance factor (10%)
  if (this.performance.avgEngagementRate > 3) {
    // Above 3% is good
    score += 10;
  }
  factors += 10;

  return Math.min(Math.round(score), 100);
});

// Pre-save middleware
contentPlanSchema.pre("save", function (next) {
  // Calculate week end if not set
  if (!this.weekEnd && this.weekStart) {
    this.weekEnd = new Date(this.weekStart);
    this.weekEnd.setDate(this.weekEnd.getDate() + 6);
  }

  // Update completion rate
  if (this.totalTasksPlanned > 0) {
    this.performance.completionRate =
      (this.tasksCompleted / this.totalTasksPlanned) * 100;
  }

  // Auto-activate plan if all requirements are met
  if (
    this.status === "draft" &&
    this.goals.length > 0 &&
    this.tasks.length > 0
  ) {
    this.status = "active";
  }

  // Auto-complete plan if all tasks are done and week is over
  const now = new Date();
  if (
    this.status === "active" &&
    this.weekEnd < now &&
    this.completionPercentage === 100
  ) {
    this.status = "completed";
  }

  next();
});

// Post-save middleware to create backup version
contentPlanSchema.post("save", function (doc) {
  if (doc.version > 1) {
    // Create backup of previous version
    doc.previousVersions.push({
      versionNumber: doc.version - 1,
      data: doc.toObject(),
      savedAt: new Date(),
      savedBy: doc.createdBy,
    });

    // Keep only last 5 versions
    if (doc.previousVersions.length > 5) {
      doc.previousVersions = doc.previousVersions.slice(-5);
    }
  }
});

// Static methods
contentPlanSchema.statics.findCurrentWeekPlan = function (agencyId, modelId) {
  const now = new Date();
  const startOfWeek = new Date(now);
  startOfWeek.setDate(now.getDate() - now.getDay()); // Sunday
  startOfWeek.setHours(0, 0, 0, 0);

  const endOfWeek = new Date(startOfWeek);
  endOfWeek.setDate(endOfWeek.getDate() + 6);
  endOfWeek.setHours(23, 59, 59, 999);

  return this.findOne({
    agencyId,
    modelId,
    weekStart: { $gte: startOfWeek, $lte: endOfWeek },
    isArchived: false,
  })
    .populate("goals", "type targetAudience contentTone")
    .populate("tasks", "title date status mediaType");
};

contentPlanSchema.statics.findPlansByDateRange = function (
  agencyId,
  startDate,
  endDate,
) {
  return this.find({
    agencyId,
    weekStart: { $gte: startDate, $lte: endDate },
    isArchived: false,
  })
    .sort({ weekStart: 1 })
    .populate("modelId", "username fullName")
    .populate("goals", "type status")
    .select(
      "name weekStart status totalTasksPlanned tasksCompleted performance",
    );
};

contentPlanSchema.statics.getAgencyAnalytics = function (
  agencyId,
  timeframe = 30,
) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - timeframe);

  return this.aggregate([
    {
      $match: {
        agencyId: new mongoose.Types.ObjectId(agencyId),
        createdAt: { $gte: startDate },
        isArchived: false,
      },
    },
    {
      $group: {
        _id: null,
        totalPlans: { $sum: 1 },
        activePlans: {
          $sum: { $cond: [{ $eq: ["$status", "active"] }, 1, 0] },
        },
        completedPlans: {
          $sum: { $cond: [{ $eq: ["$status", "completed"] }, 1, 0] },
        },
        avgCompletionRate: { $avg: "$performance.completionRate" },
        totalTasks: { $sum: "$totalTasksPlanned" },
        totalTasksCompleted: { $sum: "$tasksCompleted" },
        avgEngagementRate: { $avg: "$performance.avgEngagementRate" },
      },
    },
  ]);
};

// Instance methods
contentPlanSchema.methods.addGoal = function (goalId) {
  if (!this.goals.includes(goalId)) {
    this.goals.push(goalId);
    return this.save();
  }
  return Promise.resolve(this);
};

contentPlanSchema.methods.addTask = function (taskId) {
  if (!this.tasks.includes(taskId)) {
    this.tasks.push(taskId);
    this.totalTasksPlanned = this.tasks.length;
    return this.save();
  }
  return Promise.resolve(this);
};

contentPlanSchema.methods.updateTaskCompletion = function () {
  // This should be called when tasks are updated
  return this.populate("tasks").then((plan) => {
    const completedTasks = plan.tasks.filter(
      (task) => task.status === "published" || task.status === "completed",
    ).length;

    this.tasksCompleted = completedTasks;
    this.performance.lastUpdated = new Date();

    return this.save();
  });
};

contentPlanSchema.methods.generateWeeklyReport = function () {
  return this.populate(["goals", "tasks"]).then((plan) => {
    const report = {
      planId: plan._id,
      weekStart: plan.weekStart,
      weekEnd: plan.weekEnd,
      completion: {
        percentage: plan.completionPercentage,
        tasksCompleted: plan.tasksCompleted,
        totalTasks: plan.totalTasksPlanned,
      },
      performance: plan.performance,
      healthScore: plan.healthScore,
      goals: plan.goals.map((goal) => ({
        id: goal._id,
        type: goal.type,
        status: goal.status,
        progress: goal.progress,
      })),
      tasks: plan.tasks.map((task) => ({
        id: task._id,
        title: task.title,
        status: task.status,
        platforms: task.platforms.map((p) => p.name),
        engagement: task.totalEngagement,
      })),
    };

    return report;
  });
};

contentPlanSchema.methods.createFromTemplate = function (templateId) {
  return this.constructor.findById(templateId).then((template) => {
    if (!template || !template.isTemplate) {
      throw new Error("Invalid template");
    }

    // Copy template data
    this.name = `${template.name} - ${new Date().toISOString().split("T")[0]}`;
    this.themes = [...template.themes];
    this.schedulingPreferences = { ...template.schedulingPreferences };
    this.platformDistribution = { ...template.platformDistribution };
    this.contentTypeDistribution = { ...template.contentTypeDistribution };
    this.basedOnTemplate = templateId;

    return this.save();
  });
};

const ContentPlan = mongoose.model("ContentPlan", contentPlanSchema);
export default ContentPlan;
