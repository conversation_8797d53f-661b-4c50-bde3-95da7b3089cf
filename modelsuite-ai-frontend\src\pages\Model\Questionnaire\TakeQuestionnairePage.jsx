import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams, useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { ArrowLeft, Save, Send } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Form } from "@/components/ui/form";
import { Progress } from "@/components/ui/progress";
import ButtonLoader from "@/reusable/Loader/ButtonLoader";

import {
  fetchMyAssignments,
  submitAnswers,
  updateFormProgress,
} from "@/redux/features/questionnaire/questionnaireSlice";

export default function TakeQuestionnairePage() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { assignmentId } = useParams();

  const { myAssignments, answerLoading } = useSelector(
    (state) => state.questionnaireReducer
  );

  const [currentSectionIndex, setCurrentSectionIndex] = useState(0);
  const [assignment, setAssignment] = useState(null);

  const form = useForm({
    defaultValues: {},
  });

  useEffect(() => {
    dispatch(fetchMyAssignments());
  }, [dispatch]);

  useEffect(() => {
    if (myAssignments && assignmentId) {
      const foundAssignment = myAssignments.find((a) => a._id === assignmentId);
      setAssignment(foundAssignment);

      if (foundAssignment?.templateId?.sections) {
        // Initialize form with empty values for all questions
        const initialValues = {};
        foundAssignment.templateId.sections.forEach((section, sIndex) => {
          section.questions.forEach((question, qIndex) => {
            const fieldName = `section_${sIndex}_question_${qIndex}`;
            initialValues[fieldName] = "";
          });
        });
        form.reset(initialValues);
      }
    }
  }, [myAssignments, assignmentId, form]);

  const handleSaveProgress = async () => {
    const formData = form.getValues();

    // Update progress in Redux
    const progress = {
      currentSection: currentSectionIndex,
      answers: formData,
      lastSaved: new Date().toISOString(),
    };

    dispatch(updateFormProgress({ questionnaireId: assignmentId, progress }));

    // TODO: Save to backend
    console.log("Saving progress:", progress);
  };

  const handleSubmitQuestionnaire = async () => {
    const formData = form.getValues();

    // Convert form data to backend format
    const answers = [];

    assignment.templateId.sections.forEach((section, sIndex) => {
      section.questions.forEach((question, qIndex) => {
        const fieldName = `section_${sIndex}_question_${qIndex}`;
        const answer = formData[fieldName];

        if (answer) {
          answers.push({
            questionId: question._id,
            sectionId: section._id,
            answer: answer,
          });
        }
      });
    });

    const submissionData = {
      assignmentId: assignmentId,
      templateId: assignment.templateId._id,
      answers: answers,
    };

    try {
      await dispatch(submitAnswers(submissionData)).unwrap();
      navigate("/model/questionnaires");
    } catch (error) {
      console.error("Failed to submit questionnaire:", error);
    }
  };

  const nextSection = () => {
    if (currentSectionIndex < assignment.templateId.sections.length - 1) {
      setCurrentSectionIndex(currentSectionIndex + 1);
    }
  };

  const prevSection = () => {
    if (currentSectionIndex > 0) {
      setCurrentSectionIndex(currentSectionIndex - 1);
    }
  };

  if (!assignment) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate("/model/questionnaires")}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Assignments
          </Button>
          <div>
            <h1 className="text-2xl font-semibold tracking-tight">
              Loading Questionnaire...
            </h1>
          </div>
        </div>
      </div>
    );
  }

  const currentSection = assignment.templateId.sections[currentSectionIndex];
  const progress =
    ((currentSectionIndex + 1) / assignment.templateId.sections.length) * 100;
  const isLastSection =
    currentSectionIndex === assignment.templateId.sections.length - 1;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate("/model/questionnaires")}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Assignments
          </Button>
          <div>
            <h1 className="text-2xl font-semibold tracking-tight">
              {assignment.templateId.title}
            </h1>
            <p className="text-muted-foreground">
              {assignment.templateId.description}
            </p>
          </div>
        </div>
        <Button variant="outline" onClick={handleSaveProgress}>
          <Save className="mr-2 h-4 w-4" />
          Save Progress
        </Button>
      </div>

      {/* Progress */}
      <Card>
        <CardContent className="pt-6">
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Progress</span>
              <span>{Math.round(progress)}% Complete</span>
            </div>
            <Progress value={progress} className="w-full" />
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>
                Section {currentSectionIndex + 1} of{" "}
                {assignment.templateId.sections.length}
              </span>
              <span>{currentSection.title}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Current Section */}
      <Card>
        <CardHeader>
          <CardTitle>{currentSection.title}</CardTitle>
          {currentSection.description && (
            <CardDescription>{currentSection.description}</CardDescription>
          )}
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form className="space-y-6">
              {currentSection.questions.map((question, qIndex) => {
                const fieldName = `section_${currentSectionIndex}_question_${qIndex}`;

                return (
                  <div key={qIndex} className="space-y-2">
                    <label className="text-sm font-medium">
                      {question.questionText}
                      {question.isRequired && (
                        <span className="text-red-500 ml-1">*</span>
                      )}
                    </label>

                    {question.questionType === "text" && (
                      <input
                        type="text"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        {...form.register(fieldName, {
                          required: question.isRequired,
                        })}
                      />
                    )}

                    {question.questionType === "textarea" && (
                      <textarea
                        rows={4}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        {...form.register(fieldName, {
                          required: question.isRequired,
                        })}
                      />
                    )}

                    {question.questionType === "number" && (
                      <input
                        type="number"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        {...form.register(fieldName, {
                          required: question.isRequired,
                        })}
                      />
                    )}

                    {question.questionType === "email" && (
                      <input
                        type="email"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        {...form.register(fieldName, {
                          required: question.isRequired,
                        })}
                      />
                    )}

                    {question.questionType === "date" && (
                      <input
                        type="date"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        {...form.register(fieldName, {
                          required: question.isRequired,
                        })}
                      />
                    )}

                    {/* Add more question types as needed */}
                  </div>
                );
              })}
            </form>
          </Form>
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={prevSection}
          disabled={currentSectionIndex === 0}
        >
          Previous Section
        </Button>

        <div className="flex items-center gap-4">
          {isLastSection ? (
            <Button
              onClick={handleSubmitQuestionnaire}
              disabled={answerLoading}
            >
              {answerLoading ? (
                <ButtonLoader />
              ) : (
                <>
                  <Send className="mr-2 h-4 w-4" />
                  Submit Questionnaire
                </>
              )}
            </Button>
          ) : (
            <Button onClick={nextSection}>Next Section</Button>
          )}
        </div>
      </div>
    </div>
  );
}
