import BioProfile from "../../models/bio/BioProfile.js";
import BioGenerationLog from "../../models/bio/BioGenerationLog.js";
import { ApiError } from "../../utils/ApiError.js";

/**
 * Bio Analytics Service - Provides analytics and insights for bio generation
 */
class BioAnalyticsService {
  /**
   * Get top-rated bios for an agency
   * @param {string} agencyId - Agency ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Top-rated bios
   */
  async getTopRatedBios(agencyId, options = {}) {
    try {
      const { limit = 10, minRating = 4, includeMetrics = true } = options;

      const topBios = await BioProfile.getTopRated(agencyId, limit);

      if (includeMetrics) {
        // Add additional metrics to each bio
        for (const bio of topBios) {
          bio.metrics = await this.getBioMetrics(bio._id);
        }
      }

      return topBios;
    } catch (error) {
      console.error("Failed to get top-rated bios:", error);
      throw error;
    }
  }

  /**
   * Analyze tone distribution across bios
   * @param {string} agencyId - Agency ID
   * @param {Object} options - Analysis options
   * @returns {Promise<Object>} Tone distribution analysis
   */
  async analyzeToneDistribution(agencyId, options = {}) {
    try {
      const { timeframe = 30, includeArchived = false } = options;

      const startDate = new Date();
      startDate.setDate(startDate.getDate() - timeframe);

      const matchQuery = {
        agencyId: agencyId,
        createdAt: { $gte: startDate },
      };

      if (!includeArchived) {
        matchQuery.isArchived = false;
      }

      const toneDistribution = await BioProfile.aggregate([
        { $match: matchQuery },
        {
          $group: {
            _id: "$metadata.tone",
            count: { $sum: 1 },
            avgRating: { $avg: "$performance.rating" },
            avgUsage: { $avg: "$performance.usageCount" },
          },
        },
        { $sort: { count: -1 } },
      ]);

      // Calculate percentages
      const totalBios = toneDistribution.reduce(
        (sum, item) => sum + item.count,
        0,
      );

      const analysis = toneDistribution.map((item) => ({
        tone: item._id || "unspecified",
        count: item.count,
        percentage:
          totalBios > 0 ? Math.round((item.count / totalBios) * 100) : 0,
        avgRating: Math.round((item.avgRating || 0) * 10) / 10,
        avgUsage: Math.round(item.avgUsage || 0),
      }));

      // Add insights
      const insights = this.generateToneInsights(analysis);

      return {
        distribution: analysis,
        totalBios,
        timeframe,
        insights,
        summary: {
          mostPopularTone: analysis[0]?.tone || "none",
          highestRatedTone:
            analysis.sort((a, b) => b.avgRating - a.avgRating)[0]?.tone ||
            "none",
          diversityScore: this.calculateDiversityScore(analysis),
        },
      };
    } catch (error) {
      console.error("Failed to analyze tone distribution:", error);
      throw error;
    }
  }

  /**
   * Get generation statistics
   * @param {string} agencyId - Agency ID
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Generation statistics
   */
  async getGenerationStats(agencyId, options = {}) {
    try {
      const { timeframe = 30, includeFailures = true } = options;

      const startDate = new Date();
      startDate.setDate(startDate.getDate() - timeframe);

      // Get basic generation stats
      const [successStats, errorStats, timingStats, costStats] =
        await Promise.all([
          this.getSuccessStatistics(agencyId, startDate),
          this.getErrorStatistics(agencyId, startDate),
          this.getTimingStatistics(agencyId, startDate),
          this.getCostStatistics(agencyId, startDate),
        ]);

      // Get generation trends
      const trends = await this.getGenerationTrends(agencyId, startDate);

      // Calculate performance metrics
      const performance = this.calculatePerformanceMetrics(
        successStats,
        timingStats,
      );

      return {
        timeframe,
        period: {
          start: startDate,
          end: new Date(),
        },
        overview: {
          totalGenerations: successStats.total + errorStats.total,
          successfulGenerations: successStats.successful,
          failedGenerations: errorStats.total,
          successRate: successStats.successRate,
        },
        performance,
        timing: timingStats,
        costs: costStats,
        errors: errorStats.breakdown,
        trends,
        insights: this.generatePerformanceInsights(performance, errorStats),
      };
    } catch (error) {
      console.error("Failed to get generation stats:", error);
      throw error;
    }
  }

  /**
   * Get bio usage analytics
   * @param {string} agencyId - Agency ID
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Usage analytics
   */
  async getBioUsageAnalytics(agencyId, options = {}) {
    try {
      const { timeframe = 30 } = options;

      const startDate = new Date();
      startDate.setDate(startDate.getDate() - timeframe);

      const usageStats = await BioProfile.aggregate([
        {
          $match: {
            agencyId: agencyId,
            createdAt: { $gte: startDate },
            isArchived: false,
          },
        },
        {
          $group: {
            _id: null,
            totalBios: { $sum: 1 },
            totalUsage: { $sum: "$performance.usageCount" },
            avgUsagePerBio: { $avg: "$performance.usageCount" },
            maxUsage: { $max: "$performance.usageCount" },
            activeBios: {
              $sum: {
                $cond: [{ $gt: ["$performance.usageCount", 0] }, 1, 0],
              },
            },
          },
        },
      ]);

      const stats = usageStats[0] || {
        totalBios: 0,
        totalUsage: 0,
        avgUsagePerBio: 0,
        maxUsage: 0,
        activeBios: 0,
      };

      // Get most used bios
      const mostUsed = await BioProfile.getMostUsed(agencyId, 5);

      // Calculate engagement metrics
      const engagementRate =
        stats.totalBios > 0
          ? Math.round((stats.activeBios / stats.totalBios) * 100)
          : 0;

      return {
        timeframe,
        overview: {
          totalBios: stats.totalBios,
          activeBios: stats.activeBios,
          totalUsage: stats.totalUsage,
          engagementRate,
        },
        metrics: {
          avgUsagePerBio: Math.round(stats.avgUsagePerBio * 10) / 10,
          maxUsage: stats.maxUsage,
          usageDistribution: await this.getUsageDistribution(
            agencyId,
            startDate,
          ),
        },
        topPerformers: mostUsed,
        insights: this.generateUsageInsights(stats, engagementRate),
      };
    } catch (error) {
      console.error("Failed to get usage analytics:", error);
      throw error;
    }
  }

  /**
   * Get model-specific bio analytics
   * @param {string} modelId - Model ID
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Model bio analytics
   */
  async getModelBioAnalytics(modelId, options = {}) {
    try {
      const { timeframe = 90 } = options;

      const startDate = new Date();
      startDate.setDate(startDate.getDate() - timeframe);

      // Get all bios for this model
      const bios = await BioProfile.find({
        modelId,
        createdAt: { $gte: startDate },
      }).sort({ createdAt: -1 });

      if (bios.length === 0) {
        return {
          modelId,
          totalBios: 0,
          message: "No bios found for this model",
        };
      }

      // Calculate metrics
      const currentBio = bios.find((bio) => !bio.isArchived);
      const totalVersions = bios.length;
      const avgRating = bios
        .filter((bio) => bio.performance.rating)
        .reduce(
          (sum, bio, _, arr) => sum + bio.performance.rating / arr.length,
          0,
        );

      // Get generation history
      const generationHistory = await BioGenerationLog.find({
        modelId,
        createdAt: { $gte: startDate },
      })
        .sort({ createdAt: -1 })
        .limit(10);

      // Calculate improvement trends
      const trends = this.calculateImprovementTrends(bios);

      return {
        modelId,
        current: currentBio,
        history: {
          totalVersions,
          avgRating: Math.round(avgRating * 10) / 10,
          generations: generationHistory.length,
          successRate: this.calculateModelSuccessRate(generationHistory),
        },
        performance: {
          currentUsage: currentBio?.performance.usageCount || 0,
          currentRating: currentBio?.performance.rating || null,
          lastUsed: currentBio?.performance.lastUsed || null,
        },
        trends,
        recommendations: this.generateModelRecommendations(currentBio, trends),
      };
    } catch (error) {
      console.error("Failed to get model bio analytics:", error);
      throw error;
    }
  }

  // Helper methods for analytics calculations

  async getSuccessStatistics(agencyId, startDate) {
    const stats = await BioGenerationLog.getSuccessRate(
      agencyId,
      Math.ceil((Date.now() - startDate.getTime()) / (1000 * 60 * 60 * 24)),
    );
    return stats[0] || { total: 0, successful: 0, successRate: 0 };
  }

  async getErrorStatistics(agencyId, startDate) {
    const errors = await BioGenerationLog.getErrorAnalysis(
      agencyId,
      Math.ceil((Date.now() - startDate.getTime()) / (1000 * 60 * 60 * 24)),
    );

    const total = errors.reduce((sum, error) => sum + error.count, 0);

    return {
      total,
      breakdown: errors,
      mostCommonError: errors[0]?._id || "none",
    };
  }

  async getTimingStatistics(agencyId, startDate) {
    const timing = await BioGenerationLog.getAverageGenerationTime(
      agencyId,
      Math.ceil((Date.now() - startDate.getTime()) / (1000 * 60 * 60 * 24)),
    );

    return (
      timing[0] || {
        avgDataPrepTime: 0,
        avgAiTime: 0,
        avgTotalTime: 0,
      }
    );
  }

  async getCostStatistics(agencyId, startDate) {
    const costs = await BioGenerationLog.getCostAnalysis(
      agencyId,
      Math.ceil((Date.now() - startDate.getTime()) / (1000 * 60 * 60 * 24)),
    );

    const stats = costs[0] || {
      totalCost: 0,
      totalInputTokens: 0,
      totalOutputTokens: 0,
      avgCostPerGeneration: 0,
    };

    return {
      totalCostUSD: stats.totalCost / 100, // Convert from cents
      avgCostUSD: stats.avgCostPerGeneration / 100,
      totalTokens: stats.totalInputTokens + stats.totalOutputTokens,
      inputTokens: stats.totalInputTokens,
      outputTokens: stats.totalOutputTokens,
    };
  }

  async getGenerationTrends(agencyId, startDate) {
    const daily = await BioGenerationLog.aggregate([
      {
        $match: {
          agencyId: agencyId,
          createdAt: { $gte: startDate },
        },
      },
      {
        $group: {
          _id: {
            $dateToString: { format: "%Y-%m-%d", date: "$createdAt" },
          },
          total: { $sum: 1 },
          successful: { $sum: { $cond: ["$success", 1, 0] } },
        },
      },
      { $sort: { _id: 1 } },
    ]);

    return daily.map((day) => ({
      date: day._id,
      total: day.total,
      successful: day.successful,
      successRate:
        day.total > 0 ? Math.round((day.successful / day.total) * 100) : 0,
    }));
  }

  calculatePerformanceMetrics(successStats, timingStats) {
    return {
      reliability: successStats.successRate || 0,
      speed: {
        avgDataPrepTime: Math.round(timingStats.avgDataPrepTime || 0),
        avgAiTime: Math.round(timingStats.avgAiTime || 0),
        avgTotalTime: Math.round(timingStats.avgTotalTime || 0),
      },
      efficiency: this.calculateEfficiencyScore(
        timingStats,
        successStats.successRate,
      ),
    };
  }

  calculateEfficiencyScore(timing, successRate) {
    // Simple efficiency calculation based on speed and success rate
    const avgTime = timing.avgTotalTime || 5000; // Default 5 seconds
    const timeScore = Math.max(0, 100 - avgTime / 100); // Better score for faster times
    const reliabilityScore = successRate || 0;

    return Math.round(timeScore * 0.3 + reliabilityScore * 0.7);
  }

  generateToneInsights(distribution) {
    const insights = [];

    if (distribution.length === 0) {
      insights.push("No bio data available for tone analysis");
      return insights;
    }

    const mostPopular = distribution[0];
    const leastPopular = distribution[distribution.length - 1];

    insights.push(
      `${mostPopular.tone} is the most popular tone (${mostPopular.percentage}% of bios)`,
    );

    if (distribution.length > 1) {
      insights.push(
        `${leastPopular.tone} is the least used tone (${leastPopular.percentage}% of bios)`,
      );
    }

    const highRated = distribution.filter((d) => d.avgRating >= 4);
    if (highRated.length > 0) {
      insights.push(`${highRated.length} tone(s) have ratings of 4+ stars`);
    }

    return insights;
  }

  generatePerformanceInsights(performance, errorStats) {
    const insights = [];

    if (performance.reliability >= 95) {
      insights.push("Excellent generation reliability");
    } else if (performance.reliability >= 80) {
      insights.push("Good generation reliability - some room for improvement");
    } else {
      insights.push("Generation reliability needs attention");
    }

    if (performance.speed.avgTotalTime < 3000) {
      insights.push("Fast generation times");
    } else if (performance.speed.avgTotalTime > 10000) {
      insights.push("Generation times are slower than optimal");
    }

    if (errorStats.total > 0) {
      insights.push(`Most common error: ${errorStats.mostCommonError}`);
    }

    return insights;
  }

  generateUsageInsights(stats, engagementRate) {
    const insights = [];

    if (engagementRate >= 80) {
      insights.push("High bio engagement - most bios are being actively used");
    } else if (engagementRate >= 50) {
      insights.push(
        "Moderate bio engagement - some bios may need optimization",
      );
    } else {
      insights.push(
        "Low bio engagement - consider improving bio quality or relevance",
      );
    }

    if (stats.avgUsagePerBio >= 10) {
      insights.push("Strong average usage per bio");
    } else if (stats.avgUsagePerBio < 2) {
      insights.push("Low average usage - bios may not be meeting user needs");
    }

    return insights;
  }

  calculateDiversityScore(distribution) {
    if (distribution.length <= 1) return 0;

    // Calculate entropy-based diversity score
    const total = distribution.reduce((sum, item) => sum + item.count, 0);
    if (total === 0) return 0;

    const entropy = distribution.reduce((sum, item) => {
      const p = item.count / total;
      return sum - p * Math.log2(p);
    }, 0);

    const maxEntropy = Math.log2(distribution.length);
    return Math.round((entropy / maxEntropy) * 100);
  }

  async getBioMetrics(bioId) {
    // Get additional metrics for a specific bio
    return {
      views: 0, // Placeholder - implement view tracking
      downloads: 0, // Placeholder - implement download tracking
      shares: 0, // Placeholder - implement share tracking
    };
  }

  async getUsageDistribution(agencyId, startDate) {
    const distribution = await BioProfile.aggregate([
      {
        $match: {
          agencyId: agencyId,
          createdAt: { $gte: startDate },
          isArchived: false,
        },
      },
      {
        $bucket: {
          groupBy: "$performance.usageCount",
          boundaries: [0, 1, 5, 10, 25, 50, 100],
          default: "100+",
          output: {
            count: { $sum: 1 },
          },
        },
      },
    ]);

    return distribution;
  }

  calculateImprovementTrends(bios) {
    if (bios.length < 2) return { trend: "insufficient_data" };

    // Sort by creation date
    const sortedBios = bios.sort((a, b) => a.createdAt - b.createdAt);

    // Calculate rating trend
    const ratings = sortedBios
      .filter((bio) => bio.performance.rating)
      .map((bio) => bio.performance.rating);

    if (ratings.length < 2) return { trend: "no_rating_data" };

    const latest =
      ratings.slice(-3).reduce((sum, rating) => sum + rating, 0) /
      ratings.slice(-3).length;
    const earlier =
      ratings.slice(0, 3).reduce((sum, rating) => sum + rating, 0) /
      ratings.slice(0, 3).length;

    const improvement = latest - earlier;

    return {
      trend:
        improvement > 0.5
          ? "improving"
          : improvement < -0.5
            ? "declining"
            : "stable",
      improvement: Math.round(improvement * 10) / 10,
      latestAvg: Math.round(latest * 10) / 10,
      earlierAvg: Math.round(earlier * 10) / 10,
    };
  }

  calculateModelSuccessRate(generationHistory) {
    if (generationHistory.length === 0) return 0;

    const successful = generationHistory.filter((gen) => gen.success).length;
    return Math.round((successful / generationHistory.length) * 100);
  }

  generateModelRecommendations(currentBio, trends) {
    const recommendations = [];

    if (!currentBio) {
      recommendations.push("Generate a bio for this model to get started");
      return recommendations;
    }

    if (currentBio.performance.usageCount === 0) {
      recommendations.push("Bio hasn't been used yet - consider promoting it");
    }

    if (currentBio.performance.rating && currentBio.performance.rating < 3) {
      recommendations.push(
        "Low rating - consider regenerating with different persona",
      );
    }

    if (trends.trend === "declining") {
      recommendations.push("Performance declining - try regenerating bio");
    }

    if (
      !currentBio.performance.lastUsed ||
      Date.now() - currentBio.performance.lastUsed.getTime() >
        30 * 24 * 60 * 60 * 1000
    ) {
      recommendations.push(
        "Bio hasn't been used recently - might need updating",
      );
    }

    return recommendations;
  }
}

export default new BioAnalyticsService();
