import React from "react";
import { useSelector } from "react-redux";
import DraggableWidgetItem from "./DraggableWidgetItem";
import {
  selectAvailableWidgets,
  selectWidgetLoading,
  selectWidgetError,
} from "@/redux/features/widgetSidebar/widgetSidebarSlice";

const WidgetIconList = () => {
  const availableWidgets = useSelector(selectAvailableWidgets);
  const loading = useSelector(selectWidgetLoading);
  const error = useSelector(selectWidgetError);

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-gray-400 text-sm">Loading widgets...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-900/20 border border-red-900/30 rounded-md p-3 text-red-400 text-sm mb-4">
        {error}
      </div>
    );
  }

  if (availableWidgets.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <p className="text-sm">No widgets available</p>
        <p className="text-xs mt-1 opacity-75">
          Widgets will appear here when they become available
        </p>
      </div>
    );
  }

  return (
    <div className="flex flex-row items-center gap-6">
      {/* Widget List - Horizontal Dock */}
      {availableWidgets.map((widget) => (
        <DraggableWidgetItem key={widget.type} widget={widget} />
      ))}
    </div>
  );
};

export default WidgetIconList;
