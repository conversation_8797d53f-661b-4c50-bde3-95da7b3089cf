// Configuration constants for the Smart Caption Generator
export const CAPTION_CONFIG = {
  // Quota settings
  DAILY_QUOTA: parseInt(process.env.CAPTION_DAILY_QUOTA) || 5,

  // AI Service settings
  GPT_TEMPERATURE: parseFloat(process.env.GPT_TEMPERATURE) || 0.8,
  GPT_MAX_TOKENS: parseInt(process.env.GPT_MAX_TOKENS) || 500,

  // NSFW detection
  NSFW_THRESHOLD: parseFloat(process.env.NSFW_THRESHOLD) || 0.7,
  MAX_CAPTIONS_PER_REQUEST: parseInt(process.env.MAX_CAPTIONS_PER_REQUEST) || 5,

  // API timeouts and retries
  API_TIMEOUT: parseInt(process.env.API_TIMEOUT) || 30000, // 30 seconds
  MAX_RETRIES: parseInt(process.env.MAX_RETRIES) || 2,
  RETRY_DELAY: parseInt(process.env.RETRY_DELAY) || 1000, // 1 second

  // File processing
  MAX_FILE_SIZE: parseInt(process.env.MAX_FILE_SIZE) || 20 * 1024 * 1024, // 20MB

  // Pagination limits
  MAX_HISTORY_LIMIT: parseInt(process.env.MAX_HISTORY_LIMIT) || 100,
  DEFAULT_HISTORY_LIMIT: parseInt(process.env.DEFAULT_HISTORY_LIMIT) || 10,
};

// Validation constants
export const VALIDATION = {
  ALLOWED_MEDIA_TYPES: ["image", "video"],
  ALLOWED_IMAGE_MIMES: ["image/jpeg", "image/jpg", "image/png", "image/webp"],
  ALLOWED_VIDEO_MIMES: ["video/mp4", "video/mov", "video/avi", "video/webm"],
};
