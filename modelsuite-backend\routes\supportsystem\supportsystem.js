import express from "express";
import { verifyToken } from "../../middlewares/authMiddleware.js";
import checkPermission from "../../middlewares/permissionCheck.js";
import {
  createSupportStaff,
  updateSupportStaff,
  getAllSupportStaff,
  getSupportStaffById,
  deleteSupportStaff,
  filterBySupportArea,
  getAllPrimarySupportAreas,
  //   registerSocketHandlers,
  getRecentChatWithModel,
  countUnseenMessages,
  getRecentNotificationFromStaff,
  countUnseenNotifications,
  markMessagesAsRead,
  markNotificationsAsRead,
  getSupportStaffByModelId,
} from "../../controllers/supportsystem/supportsystem.js";

const router = express.Router();

//for models dont touch it if you think its for the agency side...no they are strictly for model side

router.get("/:modelid", verifyToken, getSupportStaffByModelId);
// ============== SUPPORT STAFF ROUTES ==============
router.post(
  "/",
  verifyToken,
  checkPermission("support.create"),
  createSupportStaff,
); // Create staff
router.get(
  "/",
  verifyToken,
  checkPermission("support.view"),
  getAllSupportStaff,
); // Get all staff
router.get(
  "/filter",
  verifyToken,
  checkPermission("support.view"),
  filterBySupportArea,
); // Filter by support area
router.get(
  "/allroles",
  verifyToken,
  checkPermission("support.view"),
  getAllPrimarySupportAreas,
); // Get first element of support_areas
router.get(
  "/:id",
  verifyToken,
  checkPermission("support.view"),
  getSupportStaffById,
); // Get single staff
router.put(
  "/:id",
  verifyToken,
  checkPermission("support.edit"),
  updateSupportStaff,
); // Update staff
router.delete(
  "/:id",
  verifyToken,
  checkPermission("support.delete"),
  deleteSupportStaff,
); // Delete staff

// ============== CHAT ROUTES ==============
router.get(
  "/chat/recent/:modelId/:staffId",
  verifyToken,
  checkPermission("support.view"),
  getRecentChatWithModel,
); // Recent chat between model & staff
router.get(
  "/chat/unseen/:fromModelId/:toStaffId",
  verifyToken,
  checkPermission("support.view"),
  countUnseenMessages,
); // Count unseen chats
router.put(
  "/chat/mark-read/:fromModelId/:toStaffId",
  verifyToken,
  checkPermission("support.edit"),
  markMessagesAsRead,
); // Mark chats as read

// ============== NOTIFICATION ROUTES ==============
router.get(
  "/notification/recent/:staffId/:modelId",
  getRecentNotificationFromStaff,
); // Recent notification
router.get(
  "/notification/unseen/:fromStaffId/:toModelId",
  countUnseenNotifications,
); // Count unseen notifications
router.put(
  "/notification/mark-read/:fromStaffId/:toModelId",
  markNotificationsAsRead,
); // Mark notifications as read

export default router;
