import { DataTable } from "@/reusable/table/DataTable";
import React from "react";
import { skillMatrixColumns } from "./skillmatrixTable/skillMatrixColumns";

const SkillMatrixPage = () => {
    const skillMatrixData = [
    {
      id: "1",
      name: "<PERSON><PERSON>",
      communicating: true,
      socialMedia: true,
      photoshop: false,
      creativeDesign: true,
      videoEditing: false,
      chatting: true,
      manager: false,
    },
    {
      id: "2",
      name: "<PERSON><PERSON> Verma",
      communicating: true,
      socialMedia: false,
      photoshop: true,
      creativeDesign: true,
      videoEditing: true,
      chatting: true,
      manager: true,
    },
    {
      id: "3",
      name: "<PERSON><PERSON>",
      communicating: false,
      socialMedia: true,
      photoshop: false,
      creativeDesign: false,
      videoEditing: true,
      chatting: false,
      manager: false,
    },
    {
      id: "4",
      name: "<PERSON><PERSON><PERSON>",
      communicating: true,
      socialMedia: true,
      photoshop: true,
      creativeDesign: false,
      videoEditing: false,
      chatting: true,
      manager: false,
    },
  ];
  
  return (
   
    <DataTable columns={skillMatrixColumns} data={skillMatrixData} />
  );
};

export default SkillMatrixPage;
