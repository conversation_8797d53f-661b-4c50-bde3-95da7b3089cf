import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams, useNavigate } from "react-router-dom";
import {
  ArrowLeft,
  Download,
  Users,
  CheckCircle,
  Clock,
  BarChart3,
  FileText,
  Calendar,
  TrendingUp,
} from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { DataTable } from "@/reusable/table/DataTable";
import StatusBadge from "@/reusable/Status/StatusBadge";

import {
  fetchTemplateAnalytics,
  fetchTemplateById,
  exportQuestionnairePDF,
} from "@/redux/features/questionnaire/questionnaireSlice";

// Recharts components
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
} from "recharts";

export default function AnalyticsPage() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { templateId } = useParams();

  const { analytics, analyticsLoading, selectedTemplate } = useSelector(
    (state) => state.questionnaireReducer
  );

  const [selectedResponse, setSelectedResponse] = useState(null);
  const [responsesPagination, setResponsesPagination] = useState({
    pageIndex: 0,
    pageSize: 5,
  });

  useEffect(() => {
    if (templateId) {
      dispatch(fetchTemplateAnalytics(templateId));
      dispatch(fetchTemplateById(templateId));
    }
  }, [dispatch, templateId]);

  const handleExportResponsePDF = async (modelId) => {
    try {
      await dispatch(exportQuestionnairePDF({ modelId, templateId })).unwrap();
    } catch (error) {
      console.error("Failed to export PDF:", error);
      alert("Failed to export PDF. Please try again.");
    }
  };

  // Chart data
  const statusData = analytics
    ? [
        {
          name: "Completed",
          value: analytics.completedResponses || 0,
          color: "#22c55e",
        },
        {
          name: "In Progress",
          value: analytics.inProgressResponses || 0,
          color: "#f59e0b",
        },
        {
          name: "Pending",
          value: analytics.pendingResponses || 0,
          color: "#ef4444",
        },
      ]
    : [];

  const responseTrendsData = analytics?.responseTrends || [];

  // Table columns for individual responses
  const responseColumns = [
    {
      accessorKey: "modelName",
      header: "Model",
      cell: ({ row }) => (
        <div className="flex flex-col">
          <span className="font-medium text-white">
            {row.original.modelName}
          </span>
          <span className="text-sm text-gray-400">
            {row.original.modelEmail}
          </span>
        </div>
      ),
    },
    {
      accessorKey: "submittedAt",
      header: "Submitted",
      cell: ({ row }) => (
        <span className="text-sm text-gray-300">
          {row.original.submittedAt
            ? new Date(row.original.submittedAt).toLocaleDateString()
            : "Not submitted"}
        </span>
      ),
    },
    {
      accessorKey: "timeTaken",
      header: "Time Taken",
      cell: ({ row }) => (
        <span className="text-sm text-gray-300">
          {row.original.timeTaken ? `${row.original.timeTaken} days` : "N/A"}
        </span>
      ),
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <div className="flex gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleExportResponsePDF(row.original.modelId)}
            className="bg-[#18181b] border-[#3f3f46] text-white hover:bg-[#27272a] hover:text-white"
          >
            <Download className="mr-1 h-3 w-3" />
            PDF
          </Button>
        </div>
      ),
    },
  ];

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-[#18181b] border border-[#27272a] p-3 rounded-lg shadow-lg">
          <p className="text-white font-medium">{label}</p>
          {payload.map((entry, index) => (
            <p
              key={index}
              className="text-gray-300"
              style={{ color: entry.color }}
            >
              {`${entry.dataKey}: ${entry.value}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <div className="p-4">
      <div className="max-w-7xl mx-auto space-y-4">
        {/* Back Button */}
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate("/agency/questionnaires")}
            className="text-gray-400 hover:text-white hover:bg-[#27272a]"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Templates
          </Button>
        </div>

        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold tracking-tight text-white">
              Template Analytics
            </h1>
            <p className="text-gray-400">
              {selectedTemplate?.title || "Loading template..."}
            </p>
          </div>
        </div>

        {/* Overview Cards */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card className="bg-[#18181b] border-[#27272a]">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-300">
                Total Assignments
              </CardTitle>
              <Users className="h-4 w-4 text-gray-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                {analytics?.totalAssignments || 0}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-[#18181b] border-[#27272a]">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-300">
                Completed
              </CardTitle>
              <CheckCircle className="h-4 w-4 text-gray-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                {analytics?.completedResponses || 0}
              </div>
              <p className="text-xs text-gray-400">
                {analytics?.completionRate
                  ? `${analytics.completionRate.toFixed(1)}%`
                  : "0%"}{" "}
                completion rate
              </p>
            </CardContent>
          </Card>

          <Card className="bg-[#18181b] border-[#27272a]">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-300">
                In Progress
              </CardTitle>
              <Clock className="h-4 w-4 text-gray-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                {analytics?.inProgressResponses || 0}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-[#18181b] border-[#27272a]">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-300">
                Avg. Completion Time
              </CardTitle>
              <BarChart3 className="h-4 w-4 text-gray-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                {analytics?.averageCompletionTime || "N/A"}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Analytics Tabs */}
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList className="bg-[#18181b] border-[#27272a]">
            <TabsTrigger
              value="overview"
              className="text-gray-300 data-[state=active]:text-white data-[state=active]:bg-[#27272a]"
            >
              Overview
            </TabsTrigger>
            <TabsTrigger
              value="responses"
              className="text-gray-300 data-[state=active]:text-white data-[state=active]:bg-[#27272a]"
            >
              Individual Responses
            </TabsTrigger>
            <TabsTrigger
              value="trends"
              className="text-gray-300 data-[state=active]:text-white data-[state=active]:bg-[#27272a]"
            >
              Trends & Insights
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              {/* Status Distribution Pie Chart */}
              <Card className="bg-[#18181b] border-[#27272a]">
                <CardHeader>
                  <CardTitle className="text-white">
                    Response Status Distribution
                  </CardTitle>
                  <CardDescription className="text-gray-400">
                    Current status of all assignments
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {analyticsLoading ? (
                    <div className="h-64 flex items-center justify-center">
                      <p className="text-gray-400">Loading chart...</p>
                    </div>
                  ) : statusData.length > 0 ? (
                    <div className="space-y-4">
                      <ResponsiveContainer width="100%" height={280}>
                        <PieChart>
                          <Pie
                            data={statusData}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            outerRadius={90}
                            fill="#8884d8"
                            dataKey="value"
                          >
                            {statusData.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            ))}
                          </Pie>
                          <Tooltip content={<CustomTooltip />} />
                        </PieChart>
                      </ResponsiveContainer>
                      {/* Legend */}
                      <div className="flex flex-wrap gap-4 justify-center">
                        {statusData.map((entry, index) => (
                          <div key={index} className="flex items-center gap-2">
                            <div
                              className="w-3 h-3 rounded-full"
                              style={{ backgroundColor: entry.color }}
                            />
                            <span className="text-sm text-gray-300">
                              {entry.name}: {entry.value}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div className="h-64 flex items-center justify-center">
                      <p className="text-gray-400">No data available</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Completion Progress */}
              <Card className="bg-[#18181b] border-[#27272a]">
                <CardHeader>
                  <CardTitle className="text-white">
                    Completion Progress
                  </CardTitle>
                  <CardDescription className="text-gray-400">
                    Overall template completion metrics
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-400">
                        Completion Rate
                      </span>
                      <span className="text-sm font-medium text-white">
                        {analytics?.completionRate
                          ? `${analytics.completionRate.toFixed(1)}%`
                          : "0%"}
                      </span>
                    </div>
                    <Progress
                      value={analytics?.completionRate || 0}
                      className="bg-[#27272a]"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4 mt-6">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-white">
                        {analytics?.completedResponses || 0}
                      </div>
                      <div className="text-sm text-gray-400">Completed</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-white">
                        {(analytics?.totalAssignments || 0) -
                          (analytics?.completedResponses || 0)}
                      </div>
                      <div className="text-sm text-gray-400">Remaining</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="responses" className="space-y-4">
            <Card className="bg-[#18181b] border-[#27272a]">
              <CardHeader>
                <CardTitle className="text-white">
                  Individual Responses
                </CardTitle>
                <CardDescription className="text-gray-400">
                  Detailed view of each model's questionnaire response with PDF
                  export
                </CardDescription>
              </CardHeader>
              <CardContent>
                {analyticsLoading ? (
                  <p className="text-gray-400">Loading responses...</p>
                ) : analytics?.individualResponses?.length > 0 ? (
                  <DataTable
                    columns={responseColumns}
                    data={analytics.individualResponses}
                    pagination={responsesPagination}
                    setPagination={setResponsesPagination}
                    disableHover={true}
                    hasPagination={true}
                    showPaginationControls={true}
                  />
                ) : (
                  <p className="text-gray-400 text-center py-8">
                    No responses available yet
                  </p>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="trends" className="space-y-4">
            <Card className="bg-[#18181b] border-[#27272a]">
              <CardHeader>
                <CardTitle className="text-white">Response Trends</CardTitle>
                <CardDescription className="text-gray-400">
                  Response completion over the last 4 weeks
                </CardDescription>
              </CardHeader>
              <CardContent>
                {analyticsLoading ? (
                  <div className="h-64 flex items-center justify-center">
                    <p className="text-gray-400">Loading trends...</p>
                  </div>
                ) : responseTrendsData.length > 0 ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={responseTrendsData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#27272a" />
                      <XAxis dataKey="week" stroke="#9ca3af" fontSize={12} />
                      <YAxis stroke="#9ca3af" fontSize={12} />
                      <Tooltip content={<CustomTooltip />} />
                      <Line
                        type="monotone"
                        dataKey="completed"
                        stroke="#22c55e"
                        strokeWidth={2}
                        dot={{ fill: "#22c55e", r: 4 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="h-64 flex items-center justify-center">
                    <p className="text-gray-400">No trend data available</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
