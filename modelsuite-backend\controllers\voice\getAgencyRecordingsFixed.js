/**
 * TEMPORARY DEBUG FILE - DO NOT DELETE
 * This file contains the fixed version of getAgencyRecordings function
 * Created during debugging session to fix assignment recordings not showing in review center
 * The fix has been integrated into voiceRecordingController.js
 * Keeping this as backup in case rollback is needed
 */

import VoiceAssignment from "../../models/voice/VoiceAssignment.js";
import { asyncHand<PERSON> } from "../../utils/asyncHandler.js";
import { ApiError } from "../../utils/ApiError.js";
import { ApiResponse } from "../../utils/ApiResponse.js";
import mongoose from "mongoose";

// Helper functions (assuming these exist in the original file)
const formatFileSize = (bytes) => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

const formatDuration = (seconds) => {
  if (!seconds) return "0:00";
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins}:${secs.toString().padStart(2, "0")}`;
};

const calculateQualityScore = (recording) => {
  // Simple quality score calculation
  if (!recording || !recording.duration) return 0;

  let score = 50; // Base score

  // Duration bonus (prefer 30-120 seconds)
  if (recording.duration >= 30 && recording.duration <= 120) {
    score += 30;
  } else if (recording.duration < 30) {
    score += recording.duration;
  } else {
    score += Math.max(0, 30 - (recording.duration - 120) / 10);
  }

  // File size considerations (assuming good quality around 1MB per minute)
  const expectedSize = recording.duration * 1024 * 16; // Rough estimate
  if (recording.fileSize) {
    const sizeRatio = recording.fileSize / expectedSize;
    if (sizeRatio >= 0.5 && sizeRatio <= 2) {
      score += 20;
    }
  }

  return Math.min(100, Math.max(0, Math.round(score)));
};

/**
 * Get agency recordings (FIXED VERSION)
 * @route GET /api/v1/voice/recordings/agency
 * @access Agency only
 */
export const getAgencyRecordings = asyncHandler(async (req, res) => {
  if (req.user.role !== "agency") {
    throw new ApiError(403, "Only agencies can view their recordings");
  }

  const {
    status,
    scriptId,
    modelId,
    page = 1,
    limit = 10,
    sortBy = "submittedAt",
    sortOrder = "desc",
    search,
  } = req.query;

  console.log(`🔍 getAgencyRecordings called with status: ${status}`);

  // For submitted recordings, get from VoiceAssignment collection
  if (status === "submitted" || status === "all") {
    // Build match criteria for VoiceAssignment
    let matchCriteria = {
      agencyId: req.user._id,
      isDeleted: false,
      status: "submitted", // Assignments with submitted status
      recordings: { $exists: true, $ne: [] }, // Must have recordings
    };

    console.log(`🔍 Match criteria:`, matchCriteria);

    // Add script filter
    if (scriptId && mongoose.Types.ObjectId.isValid(scriptId)) {
      matchCriteria.scriptId = new mongoose.Types.ObjectId(scriptId);
    }

    // Add model filter
    if (modelId && mongoose.Types.ObjectId.isValid(modelId)) {
      matchCriteria.modelId = new mongoose.Types.ObjectId(modelId);
    }

    // Get assignments and populate references
    const assignments = await VoiceAssignment.find(matchCriteria)
      .populate(
        "scriptId",
        "title description scriptType singleLinePrompt scriptText",
      )
      .populate("modelId", "fullName username email")
      .sort({ submittedAt: -1 })
      .lean();

    console.log(`🔍 Found ${assignments.length} assignments with recordings`);

    // Transform assignments to recordings format for frontend
    const recordingsFromAssignments = [];

    assignments.forEach((assignment, assignmentIndex) => {
      // Each assignment can have multiple recordings (though usually just 1)
      assignment.recordings.forEach((recording, recordingIndex) => {
        recordingsFromAssignments.push({
          _id: `${assignment._id}_recording_${recordingIndex}`, // Create unique ID
          assignmentId: assignment._id,
          scriptId: assignment.scriptId._id,
          modelId: assignment.modelId._id,
          agencyId: assignment.agencyId,
          status: "submitted", // All these are submitted
          audioUrl: recording.audioUrl,
          duration: recording.duration || 0,
          fileSize: recording.fileSize || 0,
          uploadedAt: recording.uploadedAt,
          submittedAt: assignment.submittedAt,
          createdAt: assignment.submittedAt,
          script: assignment.scriptId,
          model: assignment.modelId,
          sentenceId: recording.sentenceId,
          version: recording.version || 1,
          formattedFileSize: formatFileSize(recording.fileSize || 0),
          formattedDuration: formatDuration(recording.duration || 0),
          qualityScore: calculateQualityScore(recording),
        });
      });
    });

    console.log(
      `🔍 Transformed ${recordingsFromAssignments.length} assignment recordings`,
    );

    // Apply search filter if provided
    let filteredRecordings = recordingsFromAssignments;
    if (search) {
      filteredRecordings = recordingsFromAssignments.filter(
        (recording) =>
          recording.script.title.toLowerCase().includes(search.toLowerCase()) ||
          recording.model.username
            .toLowerCase()
            .includes(search.toLowerCase()) ||
          recording.model.fullName.toLowerCase().includes(search.toLowerCase()),
      );
    }

    // Apply pagination
    const startIndex = (parseInt(page) - 1) * parseInt(limit);
    const endIndex = startIndex + parseInt(limit);
    const paginatedRecordings = filteredRecordings.slice(startIndex, endIndex);

    return res.status(200).json(
      new ApiResponse(
        200,
        {
          recordings: paginatedRecordings,
          pagination: {
            currentPage: parseInt(page),
            totalPages: Math.ceil(filteredRecordings.length / parseInt(limit)),
            totalRecordings: filteredRecordings.length,
            hasNext: endIndex < filteredRecordings.length,
            hasPrev: parseInt(page) > 1,
          },
        },
        "Agency recordings retrieved successfully",
      ),
    );
  }

  // For other statuses, return empty for now (can be implemented later)
  res.status(200).json(
    new ApiResponse(
      200,
      {
        recordings: [],
        pagination: {
          currentPage: parseInt(page),
          totalPages: 0,
          totalRecordings: 0,
          hasNext: false,
          hasPrev: false,
        },
      },
      "No recordings found for specified status",
    ),
  );
});
