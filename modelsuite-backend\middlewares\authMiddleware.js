import jwt from "jsonwebtoken";
import ModelUser from "../models/model.js";
import Agency from "../models/agency.js";
import Employee from "../models/Employee/Employee.js";

export const verifyToken = async (req, res, next) => {
  try {
    // Get token from cookie or Authorization header
    const token =
      req.cookies?.accessToken ||
      req.header("Authorization")?.replace("Bearer ", "");

    if (!token) {
      return res.status(401).json({ message: "Unauthorized User." });
    }

    let decodedToken;

    try {
      decodedToken = jwt.verify(token, process.env.LOGIN_ACCESS_TOKEN_SECRET);
    } catch (error) {
      if (error.name === "TokenExpiredError") {
        return res
          .status(401)
          .json({ message: "Token expired. Please log in again." });
      }
      return res.status(401).json({ message: "Invalid token." });
    }

    let user;

    // Switch based on role in token
    switch (decodedToken.role) {
      case "model":
        user = await ModelUser.findById(decodedToken._id).select(
          "-password -loginRefreshToken",
        );
        break;

      case "agency":
        user = await Agency.findById(decodedToken._id).select(
          "-password -loginRefreshToken",
        );
        break;

      case "employee":
        user = await Employee.findById(decodedToken._id).select(
          "-password -loginRefreshToken",
        );
        break;

      default:
        return res
          .status(401)
          .json({ message: "Invalid role in token. Unauthorized." });
    }

    if (!user) {
      return res.status(401).json({ message: "Invalid Access Token." });
    }

    // Attach user and role to request
    req.user = user;
    req.user.role = decodedToken.role;
    // For employees, also attach effectivePermissions from token
    if (decodedToken.role === "employee") {
      req.user.effectivePermissions = decodedToken.effectivePermissions;
      req.user.subRole = decodedToken.subRole; // Employee sub-role (manager, viewer, etc.)
    }

    next();
  } catch (error) {
    console.error("verifyToken error:", error);
    return res.status(401).json({ message: "Unauthorized User." });
  }
};

export const verifyRole = (roles) => {
  return (req, res, next) => {
    if (!roles.includes(req.user?.role)) {
      return res.status(403).json({ error: "Forbidden: Access denied" });
    }
    next();
  };
};
