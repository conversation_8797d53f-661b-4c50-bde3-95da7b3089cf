import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axiosInstance from "@/config/axiosInstance";

export const fetchModelList = createAsyncThunk("fetchModelList",async (_, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.get("/agency/agency-models");
      return response.data.data.models;
    } catch (error) {
      return rejectWithValue(error.response.data);
    }
  }
);

const fetchModelListSlice = createSlice({
  name: "fetchModelList",
  initialState: {
    modelList: [],
    loading: false,
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchModelList.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchModelList.fulfilled, (state, action) => {
        state.loading = false;
        state.modelList = action.payload;
      })
      .addCase(fetchModelList.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default fetchModelListSlice.reducer;
