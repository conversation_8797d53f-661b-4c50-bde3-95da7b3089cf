import { ApiError } from "../../utils/ApiError.js";
import { ApiResponse } from "../../utils/ApiResponse.js";
import PersonaService from "../../services/personaService.js";
import PersonaProfile from "../../models/persona/PersonaProfile.js";
import PersonaUsageLog from "../../models/persona/PersonaUsageLog.js";
import QuotaService from "../../services/quotaService.js";
import PDFGenerator from "../../utils/pdfGeneratorWrapper.js";
import { asyncHandler } from "../../utils/asyncHandler.js";

/**
 * PersonaController - Handles persona generation and management endpoints
 * Integrates with quota system and permission controls
 */
class PersonaController {
  /**
   * Generate a new persona profile for a model
   */
  static generatePersona = asyncHandler(async (req, res) => {
    const { modelId } = req.body;
    // Extract user data and handle the case where agencyId might be undefined for agency role
    let { agencyId, _id: userId, role } = req.user;

    // For agency role users, if agencyId is undefined, use their userId as the agencyId
    if (role === "agency" && !agencyId) {
      agencyId = userId;
      console.log(
        "Agency role with undefined agencyId - using userId as agencyId:",
        agencyId,
      );
    }

    // Validate required fields
    if (!modelId) {
      throw new ApiError(400, "Model ID is required");
    }

    // Check if model belongs to the agency or is unassigned (same logic as getAgencyModels)
    const ModelUser = (await import("../../models/model.js")).default;

    // First, check if model exists at all
    const anyModel = await ModelUser.findById(modelId);
    if (!anyModel) {
      throw new ApiError(404, "Model not found");
    }

    const model = await ModelUser.findOne({
      _id: modelId,
      $or: [
        { agencyId: agencyId },
        { agencyId: null },
        { agencyId: { $exists: false } },
      ],
      role: "model",
    });

    if (!model) {
      throw new ApiError(
        404,
        "Model not found or doesn't belong to your agency",
      );
    }

    // Check quota for persona generation
    const quotaCheck = await QuotaService.checkQuota(agencyId, "persona");
    if (!quotaCheck.canGenerate) {
      throw new ApiError(
        429,
        `Persona generation quota exceeded. ${quotaCheck.message}`,
      );
    }

    try {
      // Generate persona
      const persona = await PersonaService.generatePersona(
        modelId,
        agencyId,
        userId,
        role === "employee" ? "Employee" : "Agency",
      );

      // Update quota
      await QuotaService.updateQuota(agencyId, "persona", 1);

      // Note: No usage logging needed during generation as this is creation, not usage

      // Populate model data for response
      await persona.populate("modelId", "fullName username");

      res
        .status(201)
        .json(new ApiResponse(201, persona, "Persona generated successfully"));
    } catch (error) {
      throw new ApiError(500, `Failed to generate persona: ${error.message}`);
    }
  });

  /**
   * Save a new persona profile for a model
   */
  static savePersona = asyncHandler(async (req, res) => {
    const { modelId, content, tags = [] } = req.body;
    // Extract user data and handle the case where agencyId might be undefined for agency role
    let { agencyId, _id: userId, role } = req.user;

    // For agency role users, if agencyId is undefined, use their userId as the agencyId
    if (role === "agency" && !agencyId) {
      agencyId = userId;
      console.log(
        "Agency role with undefined agencyId - using userId as agencyId:",
        agencyId,
      );
    }

    // Validate required fields
    if (!modelId) {
      throw new ApiError(400, "Model ID is required");
    }

    if (!content || content.trim().length === 0) {
      throw new ApiError(400, "Persona content is required");
    }

    // Check if model belongs to the agency or is unassigned (same logic as getAgencyModels)
    const ModelUser = (await import("../../models/model.js")).default;

    // First, check if model exists at all
    const anyModel = await ModelUser.findById(modelId);
    if (!anyModel) {
      throw new ApiError(404, "Model not found");
    }

    const model = await ModelUser.findOne({
      _id: modelId,
      $or: [
        { agencyId: agencyId },
        { agencyId: null },
        { agencyId: { $exists: false } },
      ],
      role: "model",
    });

    if (!model) {
      throw new ApiError(
        404,
        "Model not found or doesn't belong to your agency",
      );
    }

    try {
      // Save persona using PersonaService
      const persona = await PersonaService.savePersona(
        modelId,
        agencyId,
        userId,
        role === "employee" ? "Employee" : "Agency",
        {
          personaText: content,
          tags: tags || [],
        },
      );

      // Populate model data for response
      await persona.populate("modelId", "fullName username");

      res
        .status(201)
        .json(new ApiResponse(201, persona, "Persona saved successfully"));
    } catch (error) {
      throw new ApiError(500, `Failed to save persona: ${error.message}`);
    }
  });

  /**
   * Get all personas for the agency
   */
  static getPersonas = asyncHandler(async (req, res) => {
    // Extract user data and handle the case where agencyId might be undefined for agency role
    let { agencyId, _id: userId, role } = req.user;

    // For agency role users, if agencyId is undefined, use their userId as the agencyId
    if (role === "agency" && !agencyId) {
      agencyId = userId;
      console.log(
        "getPersonas: Agency role with undefined agencyId - using userId as agencyId:",
        agencyId,
      );
    }

    const { modelId, tags, page = 1, limit = 10 } = req.query;

    const options = {};
    if (modelId) options.modelId = modelId;
    if (tags) options.tags = tags.split(",");

    const personas = await PersonaService.getPersonasForAgency(
      agencyId,
      options,
    );

    // Pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedPersonas = personas.slice(startIndex, endIndex);

    res.status(200).json(
      new ApiResponse(
        200,
        {
          personas: paginatedPersonas,
          pagination: {
            current: parseInt(page),
            total: Math.ceil(personas.length / limit),
            count: paginatedPersonas.length,
            totalRecords: personas.length,
          },
        },
        "Personas retrieved successfully",
      ),
    );
  });

  /**
   * Get a specific persona by ID
   */
  static getPersonaById = asyncHandler(async (req, res) => {
    const { personaId } = req.params;
    // Extract user data and handle the case where agencyId might be undefined for agency role
    let { agencyId, _id: userId, role } = req.user;

    // For agency role users, if agencyId is undefined, use their userId as the agencyId
    if (role === "agency" && !agencyId) {
      agencyId = userId;
      console.log(
        "getPersonaById: Agency role with undefined agencyId - using userId as agencyId:",
        agencyId,
      );
    }

    const persona = await PersonaProfile.findOne({ _id: personaId, agencyId })
      .populate("modelId", "fullName username")
      .populate("createdBy", "name email");

    if (!persona) {
      throw new ApiError(404, "Persona not found");
    }

    res
      .status(200)
      .json(new ApiResponse(200, persona, "Persona retrieved successfully"));
  });

  /**
   * Update an existing persona
   */
  static updatePersona = asyncHandler(async (req, res) => {
    const { personaId } = req.params;
    const { agencyId, _id: userId } = req.user;
    const updateData = req.body;

    // Validate persona exists and belongs to agency
    const persona = await PersonaProfile.findOne({ _id: personaId, agencyId });
    if (!persona) {
      throw new ApiError(404, "Persona not found");
    }

    // Validate update data
    const allowedFields = [
      "personaText",
      "tags",
      "demographics",
      "interests",
      "goals",
      "painPoints",
    ];
    const filteredData = {};

    for (const field of allowedFields) {
      if (updateData[field] !== undefined) {
        filteredData[field] = updateData[field];
      }
    }

    if (Object.keys(filteredData).length === 0) {
      throw new ApiError(400, "No valid fields to update");
    }

    try {
      const updatedPersona = await PersonaService.updatePersona(
        personaId,
        filteredData,
        userId,
      );

      await updatedPersona.populate("modelId", "fullName username");

      res
        .status(200)
        .json(
          new ApiResponse(200, updatedPersona, "Persona updated successfully"),
        );
    } catch (error) {
      throw new ApiError(500, `Failed to update persona: ${error.message}`);
    }
  });

  /**
   * Delete a persona
   */
  static deletePersona = asyncHandler(async (req, res) => {
    const { personaId } = req.params;
    const { agencyId } = req.user;

    const persona = await PersonaProfile.findOne({ _id: personaId, agencyId });
    if (!persona) {
      throw new ApiError(404, "Persona not found");
    }

    // Delete persona and related usage logs
    await Promise.all([
      PersonaProfile.findByIdAndDelete(personaId),
      PersonaUsageLog.deleteMany({ personaId }),
    ]);

    res
      .status(200)
      .json(new ApiResponse(200, null, "Persona deleted successfully"));
  });

  /**
   * Export persona as PDF
   */
  static exportPersona = asyncHandler(async (req, res) => {
    const { personaId } = req.params;
    const { agencyId, _id: userId, role } = req.user;

    const persona = await PersonaProfile.findOne({ _id: personaId, agencyId })
      .populate("modelId", "fullName username")
      .populate("createdBy", "name email");

    if (!persona) {
      throw new ApiError(404, "Persona not found");
    }

    try {
      // Generate PDF (placeholder for now - will implement PDF generation)
      const pdfBuffer = await this.generatePersonaPDF(persona);

      // Log usage
      await PersonaService.logUsage(
        personaId,
        "export_pdf",
        { exportedBy: userId },
        userId,
        role === "employee" ? "Employee" : "Agency",
        agencyId,
        persona.modelId._id,
      );

      res.setHeader("Content-Type", "application/pdf");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="persona-${
          persona.modelId.username
        }-${Date.now()}.pdf"`,
      );

      res.send(pdfBuffer);
    } catch (error) {
      throw new ApiError(500, `Failed to export persona: ${error.message}`);
    }
  });

  /**
   * Get quota status for persona generation
   */
  static getQuotaStatus = asyncHandler(async (req, res) => {
    // Extract user data and handle the case where agencyId might be undefined for agency role
    let { agencyId, _id: userId, role } = req.user;

    // For agency role users, if agencyId is undefined, use their userId as the agencyId
    if (role === "agency" && !agencyId) {
      agencyId = userId;
      console.log(
        "getQuotaStatus: Agency role with undefined agencyId - using userId as agencyId:",
        agencyId,
      );
    }

    try {
      const quotaStatus = await QuotaService.getQuotaStatus(
        agencyId,
        "persona",
      );

      res
        .status(200)
        .json(
          new ApiResponse(
            200,
            quotaStatus,
            "Quota status retrieved successfully",
          ),
        );
    } catch (error) {
      throw new ApiError(500, `Failed to get quota status: ${error.message}`);
    }
  });

  /**
   * Generate PDF for persona using proper PDF generator
   */
  static async generatePersonaPDF(persona) {
    try {
      // Use the PDFGenerator singleton instance (not constructor)
      // Prepare persona data in a format similar to questionnaire data
      const personaData = {
        fullName: persona.modelId.fullName,
        username: persona.modelId.username,
        email: persona.modelId.email || "N/A",
      };

      // Format persona information as "answers" for the PDF generator
      const formattedAnswers = [
        {
          question: "Persona Description",
          answer: persona.personaText,
        },
        {
          question: "Tags",
          answer: persona.tags.join(", "),
        },
        {
          question: "Demographics - Age",
          answer: persona.demographics?.age || "N/A",
        },
        {
          question: "Demographics - Location",
          answer: persona.demographics?.location || "N/A",
        },
        {
          question: "Interests",
          answer: persona.interests?.join(", ") || "N/A",
        },
        {
          question: "Goals",
          answer: persona.goals?.join(", ") || "N/A",
        },
        {
          question: "Pain Points",
          answer: persona.painPoints?.join(", ") || "N/A",
        },
        {
          question: "Usage Count",
          answer: persona.usageCount.toString(),
        },
        {
          question: "Last Used",
          answer: persona.lastUsedAt
            ? persona.lastUsedAt.toDateString()
            : "Never",
        },
        {
          question: "AI Engine Used",
          answer: persona.aiEngineUsed,
        },
        {
          question: "Generated Date",
          answer: persona.createdAt.toDateString(),
        },
      ];

      // Template data for the PDF
      const templateData = {
        title: "Persona Profile Report",
        description: `Detailed persona profile for ${persona.modelId.fullName}`,
      };

      // PDF generation options
      const pdfOptions = {
        email: "<EMAIL>",
        phone: "+****************",
        website: "www.modelsuite.ai",
        format: "A4",
      };

      // Generate the PDF using the questionnaire report generator
      const pdfBuffer = await PDFGenerator.generateQuestionnaireReport(
        personaData,
        formattedAnswers,
        templateData,
        pdfOptions,
      );

      return pdfBuffer;
    } catch (error) {
      console.error("Error generating persona PDF:", error);
      throw new Error(`Failed to generate persona PDF: ${error.message}`);
    }
  }

  /**
   * Collect feedback on persona quality
   */
  static submitPersonaFeedback = asyncHandler(async (req, res) => {
    const { personaId } = req.params;
    const {
      rating,
      feedback,
      usefulness,
      improvements,
      accuracy,
      completeness,
      brandAlignment,
    } = req.body;
    let { _id: userId, role, agencyId } = req.user;

    // Handle agency role agencyId
    if (role === "agency" && !agencyId) {
      agencyId = userId;
    }

    // Validate required fields
    if (!rating) {
      throw new ApiError(400, "Rating is required");
    }

    // Validate persona exists and user has access
    const persona = await PersonaProfile.findById(personaId);
    if (!persona) {
      throw new ApiError(404, "Persona not found");
    }

    // Create feedback record with all required fields matching the schema
    const PersonaFeedback = (
      await import("../../models/persona/PersonaFeedback.js")
    ).default;
    const feedbackRecord = await PersonaFeedback.create({
      personaId,
      modelId: persona.modelId,
      agencyId: agencyId,
      providedBy: userId,
      providedByModel: role === "employee" ? "Employee" : "Agency",
      rating: parseInt(rating),
      textFeedback: feedback || "",
      feedbackCategories: {
        accuracy: accuracy ? parseInt(accuracy) : undefined,
        usefulness: usefulness ? parseInt(usefulness) : undefined,
        completeness: completeness ? parseInt(completeness) : undefined,
        brandAlignment: brandAlignment ? parseInt(brandAlignment) : undefined,
      },
      improvementAreas: improvements ? [improvements] : [],
    });

    // Update persona's feedback statistics
    await PersonaService.updateFeedbackStats(personaId);

    res
      .status(201)
      .json(
        new ApiResponse(201, feedbackRecord, "Feedback submitted successfully"),
      );
  });

  /**
   * Get feedback for a specific persona
   */
  static getPersonaFeedback = asyncHandler(async (req, res) => {
    const { personaId } = req.params;
    let { _id: userId, role, agencyId } = req.user;

    // Handle agency role agencyId
    if (role === "agency" && !agencyId) {
      agencyId = userId;
    }

    // Validate persona exists and user has access
    const persona = await PersonaProfile.findById(personaId);
    if (!persona) {
      throw new ApiError(404, "Persona not found");
    }

    // Get feedback records for this persona and agency
    const PersonaFeedback = (
      await import("../../models/persona/PersonaFeedback.js")
    ).default;

    const feedback = await PersonaFeedback.find({
      personaId,
      agencyId,
    })
      .populate("providedBy", "name email")
      .sort({ createdAt: -1 })
      .limit(50); // Limit to recent 50 feedback entries

    res
      .status(200)
      .json(new ApiResponse(200, feedback, "Feedback retrieved successfully"));
  });

  /**
   * Get version history for a persona
   */
  static getPersonaVersions = asyncHandler(async (req, res) => {
    const { personaId } = req.params;
    const { page = 1, limit = 10 } = req.query;

    // Get persona to verify access
    const persona = await PersonaProfile.findById(personaId);
    if (!persona) {
      throw new ApiError(404, "Persona not found");
    }

    // Get all versions for this persona's model
    const versions = await PersonaProfile.find({
      modelId: persona.modelId,
      agencyId: persona.agencyId,
    })
      .sort({ version: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .populate("modelId", "username fullName")
      .select(
        "version personaText tags createdAt usageCount lastUsedAt isActive",
      );

    const totalVersions = await PersonaProfile.countDocuments({
      modelId: persona.modelId,
      agencyId: persona.agencyId,
    });

    res.status(200).json(
      new ApiResponse(
        200,
        {
          versions,
          pagination: {
            current: parseInt(page),
            total: Math.ceil(totalVersions / limit),
            count: versions.length,
            totalRecords: totalVersions,
          },
        },
        "Version history retrieved successfully",
      ),
    );
  });

  /**
   * Regenerate persona with versioning
   */
  static regeneratePersonaWithVersion = asyncHandler(async (req, res) => {
    const { personaId } = req.params;
    const { modelId, tags = [], preserveElements = [] } = req.body;
    let { agencyId, _id: userId, role } = req.user;

    if (role === "agency" && !agencyId) {
      agencyId = userId;
    }

    // Get original persona
    const originalPersona = await PersonaProfile.findById(personaId);
    if (!originalPersona) {
      throw new ApiError(404, "Original persona not found");
    }

    // Check quota
    const quotaCheck = await QuotaService.checkQuota(agencyId, "persona");
    if (!quotaCheck.canGenerate) {
      throw new ApiError(
        429,
        `Persona regeneration quota exceeded. ${quotaCheck.message}`,
      );
    }

    try {
      // Generate new version with preserved elements
      const newPersona = await PersonaService.regenerateWithVersion(
        personaId,
        req.body.preservationOptions || {},
        userId,
        role === "employee" ? "Employee" : "Agency",
      );

      // Deactivate previous version
      await PersonaProfile.findByIdAndUpdate(personaId, { isActive: false });

      // Update quota
      await QuotaService.updateQuota(agencyId, "persona", 1);

      res
        .status(201)
        .json(
          new ApiResponse(
            201,
            newPersona,
            "Persona regenerated successfully with version control",
          ),
        );
    } catch (error) {
      throw new ApiError(500, `Failed to regenerate persona: ${error.message}`);
    }
  });

  /**
   * Activate a specific persona version
   */
  static activatePersonaVersion = asyncHandler(async (req, res) => {
    const { versionId } = req.params;
    const { _id: userId, role } = req.user;

    const persona = await PersonaProfile.findById(versionId);
    if (!persona) {
      throw new ApiError(404, "Persona version not found");
    }

    // Deactivate all other versions for this model
    await PersonaProfile.updateMany(
      { modelId: persona.modelId, agencyId: persona.agencyId },
      { isActive: false },
    );

    // Activate this version
    persona.isActive = true;
    persona.lastUsedAt = new Date();
    await persona.save();

    res
      .status(200)
      .json(
        new ApiResponse(200, persona, "Persona version activated successfully"),
      );
  });

  /**
   * Delete a specific persona version
   */
  static deletePersonaVersion = asyncHandler(async (req, res) => {
    const { versionId } = req.params;

    const persona = await PersonaProfile.findById(versionId);
    if (!persona) {
      throw new ApiError(404, "Persona version not found");
    }

    // Don't allow deletion of the only version
    const versionCount = await PersonaProfile.countDocuments({
      modelId: persona.modelId,
      agencyId: persona.agencyId,
    });

    if (versionCount <= 1) {
      throw new ApiError(400, "Cannot delete the only version of a persona");
    }

    // If deleting active version, activate the latest other version
    if (persona.isActive) {
      const latestOther = await PersonaProfile.findOne({
        modelId: persona.modelId,
        agencyId: persona.agencyId,
        _id: { $ne: versionId },
      }).sort({ createdAt: -1 });

      if (latestOther) {
        latestOther.isActive = true;
        await latestOther.save();
      }
    }

    await PersonaProfile.findByIdAndDelete(versionId);

    res
      .status(200)
      .json(new ApiResponse(200, null, "Persona version deleted successfully"));
  });

  /**
   * Get analytics for personas or a specific persona
   */
  static getAnalytics = asyncHandler(async (req, res) => {
    let { agencyId, _id: userId, role } = req.user;

    // For agency role users, if agencyId is undefined, use their userId as the agencyId
    if (role === "agency" && !agencyId) {
      agencyId = userId;
    }

    // Check if personaId is in params (specific persona) or query (optional filter)
    const { personaId } = req.params;
    const { personaId: queryPersonaId } = req.query;
    const targetPersonaId = personaId || queryPersonaId;

    try {
      let analytics;

      if (targetPersonaId) {
        // Get analytics for specific persona
        const persona = await PersonaProfile.findOne({
          _id: targetPersonaId,
          agencyId,
        }).populate("modelId", "fullName username");

        if (!persona) {
          throw new ApiError(404, "Persona not found");
        }

        // Generate analytics for specific persona
        analytics = await PersonaService.getPersonaAnalytics(
          agencyId,
          targetPersonaId,
        );
      } else {
        // Get general analytics for all personas
        analytics = await PersonaService.getPersonaAnalytics(agencyId);
      }

      res
        .status(200)
        .json(
          new ApiResponse(200, analytics, "Analytics retrieved successfully"),
        );
    } catch (error) {
      throw new ApiError(500, `Failed to get analytics: ${error.message}`);
    }
  });
}

export default PersonaController;
