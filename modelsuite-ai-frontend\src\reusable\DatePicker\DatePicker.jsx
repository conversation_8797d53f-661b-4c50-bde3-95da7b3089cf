import * as React from "react";
import { cn } from "@/lib/utils";
import { Calendar, Clock, X, ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";

const DatePicker = ({
  value,
  onChange,
  placeholder = "Select date...",
  format = "PPP", // Date format
  showTime = false,
  showClear = true,
  disabled = false,
  size = "default", // "sm", "default", "lg"
  variant = "default", // "default", "outline", "ghost"
  className,
  calendarClassName,
  ...props
}) => {
  const [open, setOpen] = React.useState(false);
  const [selectedDate, setSelectedDate] = React.useState(value);
  const [timeValue, setTimeValue] = React.useState("");

  // Initialize time from date if showTime and value exists
  React.useEffect(() => {
    if (showTime && value) {
      const date = new Date(value);
      const hours = date.getHours().toString().padStart(2, "0");
      const minutes = date.getMinutes().toString().padStart(2, "0");
      setTimeValue(`${hours}:${minutes}`);
    }
  }, [value, showTime]);

  // Handle date selection
  const handleDateSelect = (date) => {
    if (!date) return;

    let finalDate = date;

    // If time is enabled and set, combine date with time
    if (showTime && timeValue) {
      const [hours, minutes] = timeValue.split(":");
      finalDate = new Date(date);
      finalDate.setHours(parseInt(hours), parseInt(minutes));
    }

    setSelectedDate(finalDate);
    if (onChange) {
      onChange(finalDate);
    }

    // Close popover if time is not enabled
    if (!showTime) {
      setOpen(false);
    }
  };

  // Handle time change
  const handleTimeChange = (e) => {
    const newTime = e.target.value;
    setTimeValue(newTime);

    if (selectedDate && newTime) {
      const [hours, minutes] = newTime.split(":");
      const newDate = new Date(selectedDate);
      newDate.setHours(parseInt(hours), parseInt(minutes));

      setSelectedDate(newDate);
      if (onChange) {
        onChange(newDate);
      }
    }
  };

  // Handle clear
  const handleClear = (e) => {
    e.stopPropagation();
    setSelectedDate(null);
    setTimeValue("");
    if (onChange) {
      onChange(null);
    }
  };

  // Format display value
  const getDisplayValue = () => {
    if (!selectedDate) return "";

    const date = new Date(selectedDate);
    const dateStr = date.toLocaleDateString();

    if (showTime) {
      const timeStr = date.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
        hour12: false,
      });
      return `${dateStr} ${timeStr}`;
    }

    return dateStr;
  };

  // Size configurations
  const sizeConfig = {
    sm: {
      input: "h-8 px-3 text-sm",
      icon: "h-4 w-4",
      button: "h-6 w-6",
    },
    default: {
      input: "h-10 px-3 text-sm",
      icon: "h-4 w-4",
      button: "h-7 w-7",
    },
    lg: {
      input: "h-12 px-4 text-base",
      icon: "h-5 w-5",
      button: "h-8 w-8",
    },
  };

  const sizes = sizeConfig[size] || sizeConfig.default;

  // Variant styles
  const getVariantClasses = () => {
    switch (variant) {
      case "outline":
        return "border-2 border-gray-600 bg-transparent focus-within:border-gray-500";
      case "ghost":
        return "border-0 bg-transparent focus-within:bg-[#1a1a1a]";
      default:
        return "border border-gray-700 bg-[#1a1a1a] focus-within:border-gray-600";
    }
  };

  // Sync external value
  React.useEffect(() => {
    setSelectedDate(value);
  }, [value]);

  return (
    <div className={cn("relative", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <div
            className={cn(
              "relative rounded-lg transition-all duration-200 cursor-pointer",
              getVariantClasses(),
              disabled && "opacity-50 cursor-not-allowed",
              sizes.input
            )}
          >
            {/* Calendar Icon */}
            <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
              <Calendar className={sizes.icon} />
            </div>

            {/* Input Display */}
            <Input
              type="text"
              placeholder={placeholder}
              value={getDisplayValue()}
              readOnly
              disabled={disabled}
              className={cn(
                "border-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 cursor-pointer",
                "placeholder:text-gray-500 text-gray-300 pl-10",
                showClear && selectedDate && "pr-10"
              )}
              {...props}
            />

            {/* Clear Button */}
            {showClear && selectedDate && !disabled && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className={cn(
                  "absolute right-2 top-1/2 -translate-y-1/2",
                  "hover:bg-gray-800 text-gray-500 hover:text-gray-300",
                  "p-0 rounded-sm",
                  sizes.button
                )}
                onClick={handleClear}
              >
                <X className={sizes.icon} />
              </Button>
            )}
          </div>
        </PopoverTrigger>

        <PopoverContent
          className={cn(
            "w-auto p-0 bg-[#1a1a1a] border-gray-700",
            calendarClassName
          )}
          align="start"
        >
          <CalendarComponent
            mode="single"
            selected={selectedDate}
            onSelect={handleDateSelect}
            disabled={disabled}
            className="bg-[#1a1a1a] text-gray-300"
            classNames={{
              months:
                "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
              month: "space-y-4",
              caption:
                "flex justify-center pt-1 relative items-center text-gray-300",
              caption_label: "text-sm font-medium",
              nav: "space-x-1 flex items-center",
              nav_button: cn(
                "h-7 w-7 bg-transparent p-0 text-gray-500 hover:text-gray-300 hover:bg-gray-800 rounded-sm"
              ),
              nav_button_previous: "absolute left-1",
              nav_button_next: "absolute right-1",
              table: "w-full border-collapse space-y-1",
              head_row: "flex",
              head_cell:
                "text-gray-500 rounded-md w-9 font-normal text-[0.8rem]",
              row: "flex w-full mt-2",
              cell: "text-center text-sm p-0 relative [&:has([aria-selected])]:bg-gray-800 first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
              day: cn(
                "h-9 w-9 p-0 font-normal text-gray-400 hover:bg-gray-800 hover:text-gray-300 rounded-sm",
                "aria-selected:bg-gray-700 aria-selected:text-gray-200 aria-selected:opacity-100"
              ),
              day_selected:
                "bg-gray-700 text-gray-200 hover:bg-gray-700 hover:text-gray-200 focus:bg-gray-700 focus:text-gray-200",
              day_today: "bg-gray-800 text-gray-300",
              day_outside: "text-gray-600 opacity-50",
              day_disabled: "text-gray-600 opacity-50",
              day_range_middle:
                "aria-selected:bg-gray-800 aria-selected:text-gray-300",
              day_hidden: "invisible",
            }}
            components={{
              IconLeft: ({ ...props }) => <ChevronLeft className="h-4 w-4" />,
              IconRight: ({ ...props }) => <ChevronRight className="h-4 w-4" />,
            }}
          />

          {/* Time Picker */}
          {showTime && (
            <div className="p-3 border-t border-gray-700">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-gray-500" />
                <Input
                  type="time"
                  value={timeValue}
                  onChange={handleTimeChange}
                  className="bg-[#0f0f0f] border-gray-700 text-gray-300 focus:border-gray-600"
                  disabled={disabled || !selectedDate}
                />
              </div>
            </div>
          )}

          {/* Action Buttons for Time Picker */}
          {showTime && (
            <div className="p-3 border-t border-gray-700 flex gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => setOpen(false)}
                className="flex-1 border-gray-600 text-gray-300 hover:bg-gray-800"
              >
                Done
              </Button>
            </div>
          )}
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default DatePicker;
