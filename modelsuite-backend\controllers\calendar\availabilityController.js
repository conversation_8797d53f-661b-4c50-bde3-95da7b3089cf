import Availability from "../../models/calendar/availabilityModel.js";

// ✅ Create absence (Only ModelUser)
export const createAbsence = async (req, res) => {
  try {
    if (req.user.role !== "model") {
      return res
        .status(403)
        .json({ success: false, message: "Only models can create absences" });
    }

    const { startDate, endDate, note } = req.body;

    if (new Date(startDate) > new Date(endDate)) {
      return res.status(400).json({
        success: false,
        message: "Start date cannot be after end date",
      });
    }

    // Prevent overlapping absences
    const overlap = await Availability.findOne({
      userId: req.user._id,
      startDate: { $lte: new Date(endDate) },
      endDate: { $gte: new Date(startDate) },
    });

    if (overlap) {
      return res.status(400).json({
        success: false,
        message: "Absence overlaps with existing period",
      });
    }

    const absence = await Availability.create({
      userId: req.user._id,
      startDate,
      endDate,
      note,
    });

    res.status(201).json({ success: true, absence });
  } catch (error) {
    console.error("Create Absence Error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create absence",
      error: error.message,
    });
  }
};

// ✅ Get self absences (ModelUser → own absences)
export const getSelfAbsences = async (req, res) => {
  try {
    const absences = await Availability.find({ userId: req.user._id }).sort({
      startDate: 1,
    });
    res.status(200).json({ success: true, absences });
  } catch (error) {
    console.error("Get Self Absences Error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch absences",
      error: error.message,
    });
  }
};

// ✅ Get model absences (Agency → any model)
export const getModelAbsences = async (req, res) => {
  try {
    const { modelId } = req.params;

    const absences = await Availability.find({ userId: modelId }).sort({
      startDate: 1,
    });
    res.status(200).json({ success: true, absences });
  } catch (error) {
    console.error("Get Model Absences Error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch model absences",
      error: error.message,
    });
  }
};

// ✅ Update absence (ModelUser only, and only their own)
export const updateAbsence = async (req, res) => {
  try {
    if (req.user.role !== "model") {
      return res
        .status(403)
        .json({ success: false, message: "Only models can update absences" });
    }

    const { id } = req.params;
    const { startDate, endDate, note } = req.body;

    if (new Date(startDate) > new Date(endDate)) {
      return res.status(400).json({
        success: false,
        message: "Start date cannot be after end date",
      });
    }

    const absence = await Availability.findById(id);
    if (!absence) {
      return res
        .status(404)
        .json({ success: false, message: "Absence not found" });
    }

    if (absence.userId.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: "Not authorized to update this absence",
      });
    }

    // Check for overlaps with other absences (excluding the current one)
    const overlap = await Availability.findOne({
      userId: req.user._id,
      _id: { $ne: id },
      startDate: { $lte: new Date(endDate) },
      endDate: { $gte: new Date(startDate) },
    });

    if (overlap) {
      return res.status(400).json({
        success: false,
        message: "Absence overlaps with existing period",
      });
    }

    const updatedAbsence = await Availability.findByIdAndUpdate(
      id,
      { startDate, endDate, note },
      { new: true },
    );

    res.status(200).json({ success: true, absence: updatedAbsence });
  } catch (error) {
    console.error("Update Absence Error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to update absence",
      error: error.message,
    });
  }
};

// ✅ Delete absence (ModelUser only, and only their own)
export const deleteAbsence = async (req, res) => {
  try {
    if (req.user.role !== "model") {
      return res
        .status(403)
        .json({ success: false, message: "Only models can delete absences" });
    }

    const { id } = req.params;

    const absence = await Availability.findById(id);
    if (!absence) {
      return res
        .status(404)
        .json({ success: false, message: "Absence not found" });
    }

    if (absence.userId.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: "Not authorized to delete this absence",
      });
    }

    await Availability.findByIdAndDelete(id);
    res
      .status(200)
      .json({ success: true, message: "Absence deleted successfully" });
  } catch (error) {
    console.error("Delete Absence Error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to delete absence",
      error: error.message,
    });
  }
};
