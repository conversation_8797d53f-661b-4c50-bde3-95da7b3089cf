import React, { useState, useRef, useEffect } from "react";
import { useDispatch } from "react-redux";
import { X, Move } from "lucide-react";
import {
  removeWidget,
  updateWidgetPosition,
} from "@/redux/features/dashboard/dashboardSlice";
import { widgetRegistry } from "@/components/WidgetSidebar/widgetRegistry.jsx";

const PlacedWidget = ({ widget }) => {
  const dispatch = useDispatch();
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const widgetRef = useRef(null);

  const registeredWidget = widgetRegistry[widget.type];

  const handleMouseDown = (e) => {
    // Only allow dragging from the header/move handle
    if (!e.target.closest("[data-widget-drag-handle]")) return;

    setIsDragging(true);
    const rect = widgetRef.current.getBoundingClientRect();
    setDragOffset({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    });

    e.preventDefault();
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Global mouse event listeners for dragging
  useEffect(() => {
    const handleMouseMoveGlobal = (e) => {
      if (!isDragging) return;

      const dashboardRect =
        widgetRef.current.parentElement.getBoundingClientRect();
      const newPosition = {
        x: e.clientX - dashboardRect.left - dragOffset.x,
        y: e.clientY - dashboardRect.top - dragOffset.y,
      };

      // Constrain to dashboard bounds
      const constrainedPosition = {
        x: Math.max(0, Math.min(newPosition.x, dashboardRect.width - 300)),
        y: Math.max(0, Math.min(newPosition.y, dashboardRect.height - 200)),
      };

      dispatch(
        updateWidgetPosition({
          widgetId: widget.id,
          position: constrainedPosition,
        })
      );
    };

    if (isDragging) {
      document.addEventListener("mousemove", handleMouseMoveGlobal);
      document.addEventListener("mouseup", handleMouseUp);

      return () => {
        document.removeEventListener("mousemove", handleMouseMoveGlobal);
        document.removeEventListener("mouseup", handleMouseUp);
      };
    }
  }, [isDragging, dragOffset, widget.id, dispatch]);

  const handleRemove = () => {
    dispatch(removeWidget(widget.id));
  };

  if (!registeredWidget) {
    // Fallback for unknown widget types
    return (
      <div
        ref={widgetRef}
        className="absolute bg-red-100 border-2 border-red-300 rounded-lg p-4 shadow-lg"
        style={{
          left: widget.position.x,
          top: widget.position.y,
          width: 300,
          height: 200,
        }}
      >
        <div className="flex items-center justify-between mb-2">
          <span className="text-red-600 font-medium">Unknown Widget</span>
          <button
            onClick={handleRemove}
            className="text-red-400 hover:text-red-600"
          >
            <X size={16} />
          </button>
        </div>
        <p className="text-red-500 text-sm">
          Widget type "{widget.type}" not found
        </p>
      </div>
    );
  }

  const WidgetComponent = registeredWidget.component;

  return (
    <div
      ref={widgetRef}
      className={`
        absolute bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden
        ${isDragging ? "shadow-2xl scale-105 z-50" : "z-10"}
        transition-shadow duration-200
      `}
      style={{
        left: widget.position.x,
        top: widget.position.y,
        width: registeredWidget.defaultSize?.width || 300,
        height: registeredWidget.defaultSize?.height || 200,
      }}
      onMouseDown={handleMouseDown}
    >
      {/* Widget Header */}
      <div
        className="flex items-center justify-between p-3 bg-gray-50 border-b border-gray-200 cursor-move"
        data-widget-drag-handle
      >
        <div className="flex items-center gap-2">
          <Move size={14} className="text-gray-400" />
          <span className="text-sm font-medium text-gray-700">
            {registeredWidget.title}
          </span>
        </div>

        <button
          onClick={handleRemove}
          className="text-gray-400 hover:text-red-500 transition-colors"
          title="Remove widget"
        >
          <X size={16} />
        </button>
      </div>

      {/* Widget Content */}
      <div className="p-3 h-full overflow-auto">
        <WidgetComponent />
      </div>
    </div>
  );
};

export default PlacedWidget;
