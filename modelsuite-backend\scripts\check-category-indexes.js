import mongoose from "mongoose";

async function checkCategoryIndexes() {
  try {
    // Use the actual MongoDB URI from .env (MongoDB Atlas)
    const mongoUri =
      "mongodb+srv://devanand:<EMAIL>/modelsuite?retryWrites=true&w=majority&appName=Cluster0";
    await mongoose.connect(mongoUri);
    console.log("🔍 Connected to MongoDB Atlas database");

    const db = mongoose.connection.db;
    const collection = db.collection("contentcategories");

    console.log("\n🗂️ Current indexes on contentcategories collection:");
    const indexes = await collection.indexes();
    indexes.forEach((index, i) => {
      console.log(
        `${i + 1}. ${index.name}: ${JSON.stringify(index.key)} ${index.unique ? "(UNIQUE)" : ""}`,
      );
      if (index.partialFilterExpression) {
        console.log(
          `   Partial Filter: ${JSON.stringify(index.partialFilterExpression)}`,
        );
      }
    });

    // Check for documents with null name field
    console.log("\n🔍 Checking for documents with null 'name' field:");
    const nullNameDocs = await collection.find({ name: null }).toArray();
    console.log(
      `Found ${nullNameDocs.length} documents with null 'name' field`,
    );

    if (nullNameDocs.length > 0) {
      console.log("Sample documents with null name:");
      nullNameDocs.slice(0, 3).forEach((doc, i) => {
        console.log(`${i + 1}.`, {
          _id: doc._id,
          agencyId: doc.agencyId,
          label: doc.label,
          name: doc.name,
          platform: doc.platform,
        });
      });
    }

    // Check for documents with agencyId matching the error
    console.log(
      "\n🔍 Checking for documents with agencyId '68723da81257ef228b214073':",
    );
    const agencyDocs = await collection
      .find({
        agencyId: new mongoose.Types.ObjectId("68723da81257ef228b214073"),
      })
      .toArray();
    console.log(`Found ${agencyDocs.length} documents for this agency`);

    if (agencyDocs.length > 0) {
      console.log("Documents for this agency:");
      agencyDocs.forEach((doc, i) => {
        console.log(`${i + 1}.`, {
          _id: doc._id,
          agencyId: doc.agencyId,
          label: doc.label,
          name: doc.name,
          platform: doc.platform,
          isActive: doc.isActive,
        });
      });
    }

    await mongoose.disconnect();
  } catch (error) {
    console.error("❌ Error:", error);
  }
}

checkCategoryIndexes();
