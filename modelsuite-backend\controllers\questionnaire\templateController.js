import Template from "../../models/questionnaire/Template.js";

export const createTemplate = async (req, res) => {
  try {
    const { title, description, sections, isActive } = req.body;

    if (req.user.role !== "agency") {
      return res
        .status(403)
        .json({ error: "Only agencies can create templates" });
    }

    // Validate required fields
    if (!title) {
      return res.status(400).json({ message: "Title is required" });
    }

    // Transform sections to handle field mapping (text -> label)
    const transformedSections = sections?.map(section => ({
      ...section,
      questions: section.questions?.map(question => {
        const transformedQuestion = {
          ...question,
          label: question.text || question.label || "", // Handle both text and label fields
        };
        
        // Remove text field to avoid duplication
        delete transformedQuestion.text;
        
        return transformedQuestion;
      }) || []
    })) || [];

    const newTemplate = await Template.create({
      title,
      description,
      sections: transformedSections,
      isActive: isActive !== undefined ? isActive : true,
      agencyId: req.user.id,
      createdBy: req.user.id,
    });

    res.status(201).json({
      success: true,
      data: newTemplate,
      message: "Template created successfully"
    });
  } catch (error) {
    console.error("Error creating template:", error);
    res.status(500).json({ 
      success: false,
      message: "Failed to create template", 
      error: error.message 
    });
  }
};

export const getTemplates = async (req, res) => {
  try {
    if (req.user.role !== "agency") {
      return res
        .status(403)
        .json({ error: "Only agencies can view templates" });
    }

    const templates = await Template.find({
      $or: [{ agencyId: null }, { agencyId: req.user.id }],
    });

    res.json(templates);
  } catch (error) {
    res.status(500).json({ message: "Failed to fetch templates" });
  }
};

export const getTemplateById = async (req, res) => {
  try {
    const template = await Template.findById(req.params.id);

    if (!template) {
      return res.status(404).json({ message: "Template not found" });
    }

    res.json(template);
  } catch (error) {
    res.status(500).json({ message: "Failed to fetch template" });
  }
};

export const updateTemplate = async (req, res) => {
  try {
    if (req.user.role !== "agency") {
      return res
        .status(403)
        .json({ error: "Only agencies can update templates" });
    }

    const { title, description, sections, isActive } = req.body;
    
    // Transform sections to handle field mapping (text -> label)
    const transformedSections = sections?.map(section => ({
      ...section,
      questions: section.questions?.map(question => ({
        ...question,
        label: question.text || question.label, // Handle both text and label fields
      }))
    })) || [];

    const updateData = {
      title,
      description,
      sections: transformedSections,
    };
    
    if (isActive !== undefined) {
      updateData.isActive = isActive;
    }

    const updatedTemplate = await Template.findOneAndUpdate(
      { _id: req.params.id, agencyId: req.user.id },
      updateData,
      { new: true },
    );

    if (!updatedTemplate) {
      return res
        .status(404)
        .json({ message: "Template not found or not owned" });
    }

    res.json(updatedTemplate);
  } catch (error) {
    console.error("Error updating template:", error);
    res.status(500).json({ message: "Failed to update template", error: error.message });
  }
};

export const deleteTemplate = async (req, res) => {
  try {
    if (req.user.role !== "agency") {
      return res
        .status(403)
        .json({ error: "Only agencies can delete templates" });
    }

    const deletedTemplate = await Template.findOneAndDelete({
      _id: req.params.id,
      agencyId: req.user.id,
    });

    if (!deletedTemplate) {
      return res
        .status(404)
        .json({ message: "Template not found or not owned" });
    }

    res.json({ message: "Template deleted successfully" });
  } catch (error) {
    res.status(500).json({ message: "Failed to delete template" });
  }
};
