import express from "express";
import {
  createEventForModel,
  getModelCalendarEvents,
  deleteEventFromModel,
} from "../../controllers/google/eventController.js";
import { verifyToken } from "../../middlewares/authMiddleware.js";
import checkPermission from "../../middlewares/permissionCheck.js";

const router = express.Router();

// All routes require auth
router.use(verifyToken);

router.post("/create", createEventForModel);
router.get("/get/:modelId", getModelCalendarEvents);
router.delete(
  "/delete/:eventId",
  checkPermission("calendar.delete"),
  deleteEventFromModel,
);

export default router;
