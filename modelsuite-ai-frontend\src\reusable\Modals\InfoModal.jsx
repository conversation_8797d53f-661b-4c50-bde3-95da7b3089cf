import * as React from "react";
import { cn } from "@/lib/utils";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Info, User, FileText, Settings } from "lucide-react";

const InfoModal = ({
  isOpen,
  onClose,
  title,
  data = {},
  fields = [],
  variant = "default",
  size = "default",
  className,
  actions,
  ...props
}) => {
  // Variant configurations
  const variants = {
    default: {
      icon: Info,
      iconColor: "text-blue-500",
    },
    user: {
      icon: User,
      iconColor: "text-green-500",
    },
    document: {
      icon: FileText,
      iconColor: "text-purple-500",
    },
    settings: {
      icon: Settings,
      iconColor: "text-gray-500",
    },
  };

  // Size configurations
  const sizeClasses = {
    sm: "sm:max-w-md",
    default: "sm:max-w-lg",
    lg: "sm:max-w-2xl",
  };

  const config = variants[variant] || variants.default;
  const IconComponent = config.icon;

  // Render field value
  const renderFieldValue = (field, value) => {
    if (field.type === "badge") {
      return (
        <span
          className={cn(
            "inline-flex items-center rounded-full px-2 py-1 text-xs font-medium",
            field.badgeClasses || "bg-muted text-muted-foreground"
          )}
        >
          {value}
        </span>
      );
    }

    if (field.type === "list" && Array.isArray(value)) {
      return (
        <div className="space-y-1">
          {value.map((item, index) => (
            <div key={index} className="text-sm text-foreground">
              • {item}
            </div>
          ))}
        </div>
      );
    }

    return <span className="text-sm text-foreground">{value || "—"}</span>;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose} {...props}>
      <DialogContent
        className={cn(sizeClasses[size], "max-h-[90vh]", className)}
      >
        <DialogHeader className="space-y-4">
          {/* Icon */}
          <div className="flex justify-center">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-muted">
              <IconComponent className={cn("h-6 w-6", config.iconColor)} />
            </div>
          </div>

          {/* Title */}
          <DialogTitle className="text-center text-xl font-semibold">
            {title}
          </DialogTitle>
        </DialogHeader>

        {/* Content */}
        <div className="space-y-4 overflow-y-auto">
          {fields.length > 0 ? (
            // Structured data with fields
            <div className="space-y-4">
              {fields.map((field, index) => (
                <div key={index} className="space-y-2">
                  <label className="text-sm font-medium text-muted-foreground">
                    {field.label}
                  </label>
                  {renderFieldValue(field, data[field.key])}
                </div>
              ))}
            </div>
          ) : (
            // Raw data display
            <div className="space-y-4">
              {Object.entries(data).map(([key, value]) => (
                <div key={key} className="space-y-2">
                  <label className="text-sm font-medium text-muted-foreground capitalize">
                    {key
                      .replace(/([A-Z])/g, " $1")
                      .replace(/^./, (str) => str.toUpperCase())}
                  </label>
                  <div className="text-sm text-foreground">{value || "—"}</div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <DialogFooter>
          {actions ? (
            <div className="flex gap-2 w-full">{actions}</div>
          ) : (
            <Button onClick={onClose} className="w-full">
              Close
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default InfoModal;
