import mongoose from "mongoose";

const captionQuotaSchema = new mongoose.Schema(
  {
    modelId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    date: {
      type: String,
      required: true, // YYYY-MM-DD format
    },
    usageCount: {
      type: Number,
      default: 0,
      max: parseInt(process.env.CAPTION_DAILY_QUOTA) || 5,
    },
    lastUsed: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  },
);

// Compound index for efficient lookups
captionQuotaSchema.index({ modelId: 1, date: 1 }, { unique: true });

export default mongoose.model("CaptionQuota", captionQuotaSchema);
