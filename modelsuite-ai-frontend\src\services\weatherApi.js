import axiosInstance from "@/config/axiosInstance";

const weatherApi = {
  getCurrentWeather: async (lat, lon, units = "metric") => {
    try {
      const response = await axiosInstance.get("/weather/current", {
        params: { lat, lon, units },
      });
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.message || "Failed to fetch weather data"
      );
    }
  },
};

export default weatherApi;
