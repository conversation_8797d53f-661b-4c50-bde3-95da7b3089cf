import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RefreshCw } from "lucide-react";
import {
  fetchDashboardStats,
  selectDashboardLoading,
  selectDashboardError,
} from "@/redux/features/dashboard/dashboardSlice";
import DashboardCanvas from "@/components/widgets/DashboardCanvas";
import DragDropManager from "@/components/widgets/DragDropManager";
import WidgetToggleButton from "@/components/WidgetSidebar/WidgetToggleButton";

const DashboardContent = () => {
  const dispatch = useDispatch();
  const loading = useSelector(selectDashboardLoading);
  const error = useSelector(selectDashboardError);

  const [isRefreshing, setIsRefreshing] = useState(false);

  useEffect(() => {
    // Initialize dashboard data
    dispatch(fetchDashboardStats());
  }, [dispatch]);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await dispatch(fetchDashboardStats()).unwrap();
    } catch (err) {
      console.error("Refresh failed:", err);
    } finally {
      setIsRefreshing(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-[#0f0f0f] p-4 lg:p-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-white text-center py-12">
            Loading dashboard...
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-[#0f0f0f] p-4 lg:p-8">
        <div className="max-w-7xl mx-auto">
          <div className="bg-[#1a1a1a] border border-red-900/20 rounded-lg p-6 text-center">
            <p className="text-red-400 mb-4">
              Failed to load dashboard data: {error}
            </p>
            <button
              onClick={handleRefresh}
              className="px-4 py-2 bg-[#0f0f0f] border border-gray-700 rounded-md text-gray-300 hover:text-white hover:border-gray-600 transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  const handleWidgetDrop = (widget, position) => {
    console.log("Widget dropped on dashboard:", widget, position);
  };

  return (
    <DragDropManager>
      <div className="min-h-screen bg-[#0f0f0f] p-4">
        <div className="max-w-7xl mx-auto space-y-4">
          {/* Header */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div>
              <h1 className="text-2xl font-semibold text-white">Dashboard</h1>
              <p className="text-gray-400 mt-1">
                Overview of your agency performance
              </p>
            </div>

            <div className="flex items-center gap-3">
              <button
                onClick={handleRefresh}
                disabled={isRefreshing || loading}
                className="flex items-center gap-2 px-4 py-2 bg-[#1a1a1a] border border-gray-700 rounded-md text-gray-300 hover:text-white hover:border-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <RefreshCw
                  className={`w-4 h-4 ${isRefreshing ? "animate-spin" : ""}`}
                />
                {isRefreshing ? "Refreshing..." : "Refresh"}
              </button>
            </div>
          </div>

          {/* Dashboard Canvas */}
          <DashboardCanvas onWidgetDrop={handleWidgetDrop} />
        </div>
      </div>

      {/* Widget Toggle Button - Fixed positioned below profile */}
      <WidgetToggleButton />
    </DragDropManager>
  );
};

export default DashboardContent;
