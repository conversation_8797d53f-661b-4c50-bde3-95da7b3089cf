import * as React from "react";
import { cn } from "@/lib/utils";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Search, X } from "lucide-react";
import { Input } from "@/components/ui/input";

const SelectField = ({
  control,
  name,
  label,
  placeholder = "Select an option",
  description,
  options = [],
  multiple = false,
  searchable = false,
  clearable = false,
  disabled = false,
  required = false,
  className,
  ...props
}) => {
  const [searchTerm, setSearchTerm] = React.useState("");
  const [isOpen, setIsOpen] = React.useState(false);

  // Filter options based on search term
  const filteredOptions = React.useMemo(() => {
    if (!searchable || !searchTerm) return options;
    return options.filter(
      (option) =>
        option.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
        option.value.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [options, searchTerm, searchable]);

  const renderSingleSelect = (field) => (
    <Select
      value={field.value || ""}
      onValueChange={field.onChange}
      disabled={disabled}
      onOpenChange={setIsOpen}
    >
      <SelectTrigger
        className={cn(
          "w-full bg-gray-900 border-gray-700 text-white",
          "hover:border-gray-600 focus:border-gray-500 focus:ring-1 focus:ring-gray-500",
          className
        )}
      >
        <div className="flex items-center justify-between w-full">
          <SelectValue placeholder={placeholder} />
          {clearable && field.value && (
            <X
              className="h-4 w-4 text-gray-400 hover:text-white cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                field.onChange("");
              }}
            />
          )}
        </div>
      </SelectTrigger>
      <SelectContent className="z-50 bg-gray-900 border-gray-700 shadow-lg">
        {searchable && (
          <div className="p-2 border-b border-gray-700">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search options..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8 bg-gray-800 border-gray-600 text-white placeholder:text-gray-400"
                onClick={(e) => e.stopPropagation()}
              />
            </div>
          </div>
        )}
        {filteredOptions.length === 0 ? (
          <div className="p-4 text-center text-gray-400">
            {searchTerm ? "No options found" : "No options available"}
          </div>
        ) : (
          filteredOptions.map((option) => (
            <SelectItem
              key={option.value}
              value={option.value}
              className="text-white hover:bg-gray-800 focus:bg-gray-800 cursor-pointer"
            >
              <div className="flex items-center gap-2">
                {option.icon && (
                  <span className="flex-shrink-0">{option.icon}</span>
                )}
                <span>{option.label}</span>
                {option.description && (
                  <span className="text-xs text-gray-400 ml-2">
                    {option.description}
                  </span>
                )}
              </div>
            </SelectItem>
          ))
        )}
      </SelectContent>
    </Select>
  );

  const renderMultiSelect = (field) => {
    const selectedValues = field.value || [];
    const selectedOptions = options.filter((option) =>
      selectedValues.includes(option.value)
    );

    const handleSelect = (value) => {
      const currentValues = field.value || [];
      const newValues = currentValues.includes(value)
        ? currentValues.filter((v) => v !== value)
        : [...currentValues, value];
      field.onChange(newValues);
    };

    const removeOption = (valueToRemove) => {
      const newValues = (field.value || []).filter((v) => v !== valueToRemove);
      field.onChange(newValues);
    };

    return (
      <div className="space-y-2">
        {/* Selected Values Display */}
        {selectedOptions.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {selectedOptions.map((option) => (
              <Badge
                key={option.value}
                variant="secondary"
                className="bg-gray-700 text-gray-200 hover:bg-gray-600"
              >
                {option.label}
                <X
                  className="h-3 w-3 ml-1 cursor-pointer hover:text-white"
                  onClick={() => removeOption(option.value)}
                />
              </Badge>
            ))}
          </div>
        )}

        {/* Select Dropdown */}
        <Select onValueChange={handleSelect} disabled={disabled}>
          <SelectTrigger
            className={cn(
              "w-full bg-gray-900 border-gray-700 text-white",
              "hover:border-gray-600 focus:border-gray-500 focus:ring-1 focus:ring-gray-500",
              className
            )}
          >
            <SelectValue
              placeholder={
                selectedOptions.length > 0
                  ? `${selectedOptions.length} selected`
                  : placeholder
              }
            />
          </SelectTrigger>
          <SelectContent className="z-50 bg-gray-900 border-gray-700 shadow-lg">
            {searchable && (
              <div className="p-2 border-b border-gray-700">
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search options..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-8 bg-gray-800 border-gray-600 text-white placeholder:text-gray-400"
                    onClick={(e) => e.stopPropagation()}
                  />
                </div>
              </div>
            )}
            {filteredOptions.map((option) => {
              const isSelected = selectedValues.includes(option.value);
              return (
                <SelectItem
                  key={option.value}
                  value={option.value}
                  className="text-white hover:bg-gray-800 focus:bg-gray-800 cursor-pointer"
                >
                  <div className="flex items-center gap-2">
                    <div
                      className={cn(
                        "w-4 h-4 border rounded border-gray-500 flex items-center justify-center",
                        isSelected && "bg-gray-600 border-gray-500"
                      )}
                    >
                      {isSelected && (
                        <div className="w-2 h-2 bg-white rounded-sm" />
                      )}
                    </div>
                    {option.icon && (
                      <span className="flex-shrink-0">{option.icon}</span>
                    )}
                    <span>{option.label}</span>
                  </div>
                </SelectItem>
              );
            })}
          </SelectContent>
        </Select>
      </div>
    );
  };

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className="space-y-2">
          {label && (
            <FormLabel className="text-sm font-medium text-white">
              {label}
              {required && <span className="text-red-400 ml-1">*</span>}
            </FormLabel>
          )}
          <FormControl>
            <div>
              {multiple ? renderMultiSelect(field) : renderSingleSelect(field)}
            </div>
          </FormControl>
          {description && (
            <FormDescription className="text-xs text-gray-400">
              {description}
            </FormDescription>
          )}
          <FormMessage className="text-xs text-red-400" />
        </FormItem>
      )}
      {...props}
    />
  );
};

export default SelectField;
