import express from "express";
import {
  inviteEmployee,
  activateEmployee,
  createEmployee,
  getEmployees,
  searchEmployees,
  deleteEmployee,
  getPermissionsAndRoles,
} from "../../controllers/Employee/employeeController.js";
import { verifyRole, verifyToken } from "../../middlewares/authMiddleware.js";
import checkPermission from "../../middlewares/permissionCheck.js";

const router = express.Router();

router.post(
  "/invite",
  verifyToken,
  verifyRole(["agency", "employee"]),
  checkPermission("employee.invite"),
  inviteEmployee,
);
router.post(
  "/create",
  verifyToken,
  verifyRole(["agency", "employee"]),
  checkPermission("employee.invite"),
  createEmployee,
);
router.post("/activate", activateEmployee);
router.delete(
  "/:id",
  verifyToken,
  verifyRole(["agency", "employee"]),
  checkPermission("employee.delete"),
  deleteEmployee,
);

// New routes
router.get(
  "/list",
  verifyToken,
  verifyRole(["agency", "employee"]),
  checkPermission("employee.view"),
  getEmployees,
);
router.get(
  "/search",
  verifyToken,
  verifyRole(["agency", "employee"]),
  checkPermission("employee.view"),
  searchEmployees,
);
router.get(
  "/permissions-roles",
  verifyToken,
  verifyRole(["agency", "employee"]),
  checkPermission("employee.view"),
  getPermissionsAndRoles,
);

export default router;
