import mongoose from "mongoose";

/**
 * ContentTask Schema - Individual content tasks within a content plan
 * Represents specific posts/content pieces to be created and scheduled
 */
const contentTaskSchema = new mongoose.Schema(
  {
    // Plan association
    planId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ContentPlan",
      required: true,
      index: true,
    },

    // Scheduling information
    date: {
      type: Date,
      required: true,
      index: true,
    },

    scheduledTime: {
      type: Date,
      index: true,
    },

    // Content details
    title: {
      type: String,
      required: true,
      maxlength: 200,
      trim: true,
    },

    caption: {
      type: String,
      required: true,
      maxlength: 2200, // Instagram max
      trim: true,
    },

    // Media information
    mediaUrl: {
      type: String,
      trim: true,
    },

    mediaType: {
      type: String,
      enum: ["image", "video", "carousel", "reel", "story"],
      required: true,
    },

    // Platform-specific settings
    platforms: [
      {
        name: {
          type: String,
          enum: ["instagram", "tiktok", "facebook", "twitter", "linkedin"],
          required: true,
        },
        scheduled: {
          type: Boolean,
          default: false,
        },
        published: {
          type: Boolean,
          default: false,
        },
        publishedAt: {
          type: Date,
        },
        postId: {
          type: String, // External platform post ID
        },
        metrics: {
          likes: { type: Number, default: 0 },
          comments: { type: Number, default: 0 },
          shares: { type: Number, default: 0 },
          reach: { type: Number, default: 0 },
          impressions: { type: Number, default: 0 },
          lastUpdated: { type: Date, default: Date.now },
        },
      },
    ],

    // Hashtags and keywords
    hashtags: {
      type: [String],
      validate: {
        validator: function (hashtags) {
          return hashtags.length <= 30; // Instagram limit
        },
        message: "Maximum 30 hashtags allowed",
      },
    },

    keywords: {
      type: [String],
      maxlength: 10,
    },

    // Auto-scheduling information
    autoScheduled: {
      type: Boolean,
      default: false,
    },

    // Google Calendar integration
    calendarEventId: {
      type: String,
      index: true,
    },

    calendarSynced: {
      type: Boolean,
      default: false,
    },

    // Task status and priority
    status: {
      type: String,
      enum: ["draft", "scheduled", "published", "failed", "cancelled"],
      default: "draft",
      index: true,
    },

    priority: {
      type: String,
      enum: ["low", "medium", "high", "urgent"],
      default: "medium",
    },

    // Content goal alignment
    goalIds: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "ContentGoal",
      },
    ],

    // AI generation metadata
    aiGenerated: {
      caption: {
        type: Boolean,
        default: false,
      },
      hashtags: {
        type: Boolean,
        default: false,
      },
      title: {
        type: Boolean,
        default: false,
      },
    },

    generationPrompt: {
      type: String,
      maxlength: 1000,
    },

    // Performance tracking
    performance: {
      totalEngagement: {
        type: Number,
        default: 0,
      },
      engagementRate: {
        type: Number,
        default: 0,
      },
      bestPerformingPlatform: {
        type: String,
      },
      lastMetricsUpdate: {
        type: Date,
        default: Date.now,
      },
    },

    // Content approval workflow
    approval: {
      required: {
        type: Boolean,
        default: false,
      },
      status: {
        type: String,
        enum: ["pending", "approved", "rejected", "revision_needed"],
        default: "pending",
      },
      approvedBy: {
        type: mongoose.Schema.Types.ObjectId,
      },
      approvedByType: {
        type: String,
        enum: ["Agency", "ModelUser", "Employee"],
      },
      approvedAt: {
        type: Date,
      },
      rejectionReason: {
        type: String,
        maxlength: 500,
      },
    },

    // Ownership tracking
    agencyId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      required: true,
      index: true,
    },

    modelId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ModelUser",
      required: true,
      index: true,
    },

    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
    },

    createdByType: {
      type: String,
      required: true,
      enum: ["Agency", "ModelUser", "Employee"],
    },

    // Task notes and comments
    notes: {
      type: String,
      maxlength: 1000,
    },

    // Versioning
    version: {
      type: Number,
      default: 1,
    },

    isArchived: {
      type: Boolean,
      default: false,
      index: true,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  },
);

// Compound indexes for efficient queries
contentTaskSchema.index({ planId: 1, date: 1 });
contentTaskSchema.index({ agencyId: 1, modelId: 1, status: 1 });
contentTaskSchema.index({ agencyId: 1, date: 1, status: 1 });
contentTaskSchema.index({ modelId: 1, scheduledTime: 1 });
contentTaskSchema.index({ calendarEventId: 1 });
contentTaskSchema.index({ "platforms.name": 1, "platforms.scheduled": 1 });

// Virtual for total engagement across platforms
contentTaskSchema.virtual("totalEngagement").get(function () {
  return this.platforms.reduce((total, platform) => {
    return (
      total +
      (platform.metrics.likes || 0) +
      (platform.metrics.comments || 0) +
      (platform.metrics.shares || 0)
    );
  }, 0);
});

// Virtual for days until scheduled
contentTaskSchema.virtual("daysUntilScheduled").get(function () {
  if (!this.scheduledTime) return null;

  const now = new Date();
  const scheduled = new Date(this.scheduledTime);
  const diffTime = scheduled - now;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  return diffDays;
});

// Virtual for engagement rate calculation
contentTaskSchema.virtual("engagementRate").get(function () {
  const totalReach = this.platforms.reduce((total, platform) => {
    return total + (platform.metrics.reach || 0);
  }, 0);

  if (totalReach === 0) return 0;

  const totalEngagement = this.totalEngagement;
  return ((totalEngagement / totalReach) * 100).toFixed(2);
});

// Pre-save middleware
contentTaskSchema.pre("save", function (next) {
  // Auto-update performance metrics
  this.performance.totalEngagement = this.totalEngagement;
  this.performance.engagementRate = parseFloat(this.engagementRate);
  this.performance.lastMetricsUpdate = new Date();

  // Find best performing platform
  let bestPlatform = null;
  let bestEngagement = 0;

  this.platforms.forEach((platform) => {
    const engagement =
      (platform.metrics.likes || 0) +
      (platform.metrics.comments || 0) +
      (platform.metrics.shares || 0);
    if (engagement > bestEngagement) {
      bestEngagement = engagement;
      bestPlatform = platform.name;
    }
  });

  this.performance.bestPerformingPlatform = bestPlatform;

  // Validate scheduled time is in the future for drafts
  if (
    this.status === "scheduled" &&
    this.scheduledTime &&
    this.scheduledTime <= new Date()
  ) {
    return next(new Error("Scheduled time must be in the future"));
  }

  next();
});

// Static methods
contentTaskSchema.statics.findUpcomingTasks = function (
  agencyId,
  modelId,
  days = 7,
) {
  const startDate = new Date();
  const endDate = new Date();
  endDate.setDate(endDate.getDate() + days);

  return this.find({
    agencyId,
    modelId,
    scheduledTime: { $gte: startDate, $lte: endDate },
    status: { $in: ["scheduled", "draft"] },
    isArchived: false,
  })
    .sort({ scheduledTime: 1 })
    .populate("planId", "weekStart goals")
    .populate("goalIds", "type targetAudience");
};

contentTaskSchema.statics.findTasksByStatus = function (
  agencyId,
  status,
  limit = 20,
) {
  return this.find({
    agencyId,
    status,
    isArchived: false,
  })
    .sort({ createdAt: -1 })
    .limit(limit)
    .populate("modelId", "username fullName")
    .populate("planId", "weekStart");
};

contentTaskSchema.statics.getTaskMetrics = function (agencyId, dateRange) {
  const { startDate, endDate } = dateRange;

  return this.aggregate([
    {
      $match: {
        agencyId: new mongoose.Types.ObjectId(agencyId),
        date: { $gte: startDate, $lte: endDate },
        status: "published",
      },
    },
    {
      $group: {
        _id: null,
        totalTasks: { $sum: 1 },
        totalEngagement: { $sum: "$performance.totalEngagement" },
        avgEngagementRate: { $avg: "$performance.engagementRate" },
        platformBreakdown: {
          $push: {
            $map: {
              input: "$platforms",
              as: "platform",
              in: {
                name: "$$platform.name",
                engagement: {
                  $add: [
                    { $ifNull: ["$$platform.metrics.likes", 0] },
                    { $ifNull: ["$$platform.metrics.comments", 0] },
                    { $ifNull: ["$$platform.metrics.shares", 0] },
                  ],
                },
              },
            },
          },
        },
      },
    },
  ]);
};

// Instance methods
contentTaskSchema.methods.scheduleTask = function (
  scheduledTime,
  platforms = [],
) {
  this.scheduledTime = scheduledTime;
  this.status = "scheduled";

  if (platforms.length > 0) {
    platforms.forEach((platformName) => {
      const platform = this.platforms.find((p) => p.name === platformName);
      if (platform) {
        platform.scheduled = true;
      }
    });
  }

  return this.save();
};

contentTaskSchema.methods.publishTask = function (
  platformName,
  postId,
  metrics = {},
) {
  const platform = this.platforms.find((p) => p.name === platformName);

  if (platform) {
    platform.published = true;
    platform.publishedAt = new Date();
    platform.postId = postId;
    platform.metrics = { ...platform.metrics, ...metrics };
  }

  // If all platforms are published, mark task as published
  const allPublished = this.platforms.every((p) => p.published);
  if (allPublished) {
    this.status = "published";
  }

  return this.save();
};

contentTaskSchema.methods.updateMetrics = function (platformName, metrics) {
  const platform = this.platforms.find((p) => p.name === platformName);

  if (platform) {
    platform.metrics = {
      ...platform.metrics,
      ...metrics,
      lastUpdated: new Date(),
    };
  }

  return this.save();
};

const ContentTask = mongoose.model("ContentTask", contentTaskSchema);
export default ContentTask;
