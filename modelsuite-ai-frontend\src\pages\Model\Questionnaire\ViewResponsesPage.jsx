import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams, useNavigate } from "react-router-dom";
import { ArrowLeft, Download } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";

import {
  fetchMyAnswers,
  exportQuestionnairePDF,
  fetchTemplateById,
} from "@/redux/features/questionnaire/questionnaireSlice";

export default function ViewResponsesPage() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { templateId } = useParams();

  const { currentAnswers, selectedTemplate, answerLoading } = useSelector(
    (state) => state.questionnaireReducer
  );

  useEffect(() => {
    if (templateId) {
      dispatch(fetchMyAnswers(templateId));
      dispatch(fetchTemplateById(templateId));
    }
  }, [dispatch, templateId]);

  const handleExportPDF = async () => {
    try {
      // Assuming we need modelId - this should come from auth state
      const modelId = "current-model-id"; // Replace with actual model ID from auth
      await dispatch(exportQuestionnairePDF({ modelId, templateId })).unwrap();
    } catch (error) {
      console.error("Failed to export PDF:", error);
    }
  };

  if (answerLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate("/model/questionnaires")}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Assignments
          </Button>
          <div>
            <h1 className="text-2xl font-semibold tracking-tight">
              Loading Responses...
            </h1>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate("/model/questionnaires")}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Assignments
          </Button>
          <div>
            <h1 className="text-2xl font-semibold tracking-tight">
              My Responses
            </h1>
            <p className="text-muted-foreground">
              {selectedTemplate?.title || "Loading template..."}
            </p>
          </div>
        </div>
        <Button onClick={handleExportPDF}>
          <Download className="mr-2 h-4 w-4" />
          Export PDF
        </Button>
      </div>

      {/* Response Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Response Summary</CardTitle>
          <CardDescription>
            Your submitted responses for this questionnaire
          </CardDescription>
        </CardHeader>
        <CardContent>
          {currentAnswers ? (
            <div className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground">
                    Submitted On
                  </h4>
                  <p className="text-sm">
                    {new Date(currentAnswers.submittedAt).toLocaleDateString()}
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground">
                    Status
                  </h4>
                  <p className="text-sm">Completed</p>
                </div>
              </div>
            </div>
          ) : (
            <p className="text-muted-foreground">
              No responses found for this questionnaire.
            </p>
          )}
        </CardContent>
      </Card>

      {/* Responses by Section */}
      {currentAnswers && selectedTemplate?.sections && (
        <div className="space-y-6">
          {selectedTemplate.sections.map((section, sectionIndex) => (
            <Card key={sectionIndex}>
              <CardHeader>
                <CardTitle>{section.title}</CardTitle>
                {section.description && (
                  <CardDescription>{section.description}</CardDescription>
                )}
              </CardHeader>
              <CardContent className="space-y-4">
                {section.questions.map((question, questionIndex) => {
                  // Find the answer for this question
                  const answer = currentAnswers.answers?.find(
                    (a) => a.questionId === question._id
                  );

                  return (
                    <div key={questionIndex} className="space-y-2">
                      <h4 className="text-sm font-medium">
                        {question.questionText}
                      </h4>
                      <div className="ml-4 p-3 bg-muted rounded-md">
                        {answer ? (
                          <p className="text-sm">{answer.answer}</p>
                        ) : (
                          <p className="text-sm text-muted-foreground">
                            No answer provided
                          </p>
                        )}
                      </div>
                      {questionIndex < section.questions.length - 1 && (
                        <Separator className="my-4" />
                      )}
                    </div>
                  );
                })}
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* No Responses Message */}
      {!currentAnswers && !answerLoading && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <p className="text-lg text-muted-foreground mb-4">
              You haven't submitted responses for this questionnaire yet.
            </p>
            <Button onClick={() => navigate("/model/questionnaires")}>
              View Available Questionnaires
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
