import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { fetchModelList } from "@/redux/features/models/fetchModelListSlice";
import { Avatar, AvatarFallback } from "../../../../components/ui/avatar";
import { Plus } from "lucide-react";
import { FormModal, SearchBar } from "@/reusable";
import ModelAddModalBody from "./ModelAddModalBody";

// Helper to get initials from a name
const getInitials = (name) => {
  if (!name) return "";
  const parts = name.split(" ");
  return parts.length > 1
    ? (parts[0][0] + parts[1][0]).toUpperCase()
    : parts[0][0].toUpperCase();
};

const ModelSidebar = () => {
  const [openModal, setOpenModal] = useState(false);
  const navigate = useNavigate();
  const [collapsed, setCollapsed] = useState(true);
  const [search, setSearch] = useState("");
  const dispatch = useDispatch();
  const { modelList, loading, error } = useSelector(
    (state) => state.fetchModelListReducer
  );

  useEffect(() => {
    dispatch(fetchModelList());
  }, [dispatch]);

  // Refetch models after successful add
  const { success: addSuccess } = useSelector(
    (state) => state.inviteModelReducer || {}
  );
  useEffect(() => {
    if (addSuccess) {
      dispatch(fetchModelList());
      setOpenModal(false);
    }
  }, [addSuccess, dispatch]);

  // Filter models by search
  const filteredModels = modelList?.filter((model) => {
    const fullName = [model.firstName, model.lastName]
      .filter(Boolean)
      .join(" ");
    return fullName.toLowerCase().includes(search.toLowerCase());
  });

  return (
    <>
      <div
        className={`group bg-[#111111] border-r border-[#1a1a1a] flex flex-col items-center py-6 transition-all duration-300 ease-in-out ${
          collapsed ? "w-16" : "w-64"
        } min-h-screen relative`}
      >
        {/* Collapse/Expand Toggle - centered and only on hover */}
        <button
          className="absolute left-full top-1/2 -translate-y-1/2 ml-[-12px] z-20 bg-[#1a1a1a] border border-[#222] rounded-full w-6 h-6 flex items-center justify-center text-gray-400 opacity-0 group-hover:opacity-100 hover:text-white transition-all duration-200"
          onClick={() => setCollapsed((prev) => !prev)}
          aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}
        >
          <span
            className={`transform transition-transform duration-300 ${
              collapsed ? "" : "rotate-180"
            }`}
          >
            ▶
          </span>
        </button>
        {/* Logo */}
        <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mb-8">
          <span className="text-white font-bold text-xs">MS</span>
        </div>
        {/* Search Bar/Icon */}
        <div className="mb-6 w-full flex justify-center">
          {collapsed ? (
            <button
              className="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-white"
              onClick={() => setCollapsed(false)}
              aria-label="Expand to search"
            >
              <svg
                width="20"
                height="20"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="lucide lucide-search"
              >
                <circle cx="11" cy="11" r="8" />
                <path d="m21 21-4.3-4.3" />
              </svg>
            </button>
          ) : (
            <div className="ml-2 mr-2">
              <SearchBar
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                placeholder="Search models..."
                className="w-full"
              />
            </div>
          )}
        </div>
        {/* Model Avatars/Names */}
        <div className="flex flex-col space-y-4 flex-1 w-full items-center">
          {loading && (
            <div className="text-gray-400 text-xs mb-4">Loading...</div>
          )}
          {error && (
            <div className="text-red-500 text-xs mb-4">
              Error loading models
            </div>
          )}
          {filteredModels && filteredModels.length > 0
            ? filteredModels.map((model) => (
                <button
                  key={model._id || model.id}
                  className={`group flex items-center w-full px-2 py-1 rounded-lg hover:bg-[#18181b] transition-colors ${
                    collapsed ? "justify-center" : ""
                  }`}
                  onClick={() => navigate(`/agency/model-menu/${model._id}`)}
                >
                  <div className="w-10 h-10 rounded-full border-2 transition-all duration-200 flex-shrink-0">
                    <Avatar className="w-full h-full">
                      {model.avatar ? (
                        <img
                          src={model.avatar}
                          alt={model.fullName}
                          className="w-full h-full rounded-full object-cover"
                        />
                      ) : (
                        <AvatarFallback className="bg-gradient-to-br from-gray-700 to-gray-800 text-white text-xs font-medium">
                          {getInitials(model.fullName)}
                        </AvatarFallback>
                      )}
                    </Avatar>
                  </div>
                  {/* Name (only expanded) */}
                  {!collapsed && (
                    <span className="ml-4 text-white text-sm font-medium truncate max-w-[130px] text-left">
                      {model.fullName}
                    </span>
                  )}
                </button>
              ))
            : !loading && (
                <div className="text-gray-500 text-xs">No models found</div>
              )}
        </div>
        {/* Add button (always visible) */}
        <button
          className="w-10 h-10 mt-8 rounded-full border-2 border-dashed border-gray-600 flex items-center justify-center hover:border-gray-500 transition-colors"
          onClick={() => setOpenModal(true)}
        >
          <Plus className="w-4 h-4 text-gray-500" />
        </button>
        {/* Add Model Modal */}
        <FormModal
          isOpen={openModal}
          onClose={() => setOpenModal(false)}
          title="Select a Model"
          size="default"
        >
          <ModelAddModalBody />
        </FormModal>
      </div>
    </>
  );
};

export default ModelSidebar;
