import ModelCategoryAssignment from "../../models/contentupload/ModelCategoryAssignment.js";
import ContentCategory from "../../models/contentupload/ContentCategory.js";
import ContentUpload from "../../models/contentupload/ContentUpload.js";
import { ApiError } from "../../utils/ApiError.js";

/**
 * Reminder Calculation Service - Handles due date calculation,
 * reminder scheduling, overdue detection, and escalation logic
 */
class ReminderCalculationService {
  /**
   * Calculate next due date based on category frequency and last upload
   * @param {string} assignmentId - Assignment ID
   * @param {Date} baseDate - Base date for calculation (defaults to now)
   * @returns {Promise<Date>} Next due date
   */
  static async calculateNextDueDate(assignmentId, baseDate = new Date()) {
    try {
      const assignment =
        await ModelCategoryAssignment.findById(assignmentId).populate(
          "categoryId",
        );

      if (!assignment) {
        throw new ApiError(404, "Assignment not found");
      }

      const category = assignment.categoryId;
      const frequency =
        assignment.customReminderFrequency || category.reminderFrequency;

      // Get the last upload for this assignment
      const lastUpload = await ContentUpload.findOne({
        modelId: assignment.modelId,
        categoryId: assignment.categoryId,
        agencyId: assignment.agencyId,
        status: { $in: ["uploaded", "approved"] },
      }).sort({ createdAt: -1 });

      // Use last upload date or assignment date as base
      const calculationBase = lastUpload
        ? lastUpload.createdAt
        : assignment.assignedAt;

      return this.addFrequencyToDate(calculationBase, frequency);
    } catch (error) {
      if (error instanceof ApiError) throw error;
      throw new ApiError(
        500,
        `Failed to calculate next due date: ${error.message}`,
      );
    }
  }

  /**
   * Calculate next reminder date based on due date and reminder settings
   * @param {string} assignmentId - Assignment ID
   * @param {Date} dueDate - Due date
   * @returns {Promise<Date>} Next reminder date
   */
  static async calculateNextReminderDate(assignmentId, dueDate) {
    try {
      const assignment = await ModelCategoryAssignment.findById(
        assignmentId,
      ).populate(["categoryId", "agencyId"]);

      if (!assignment) {
        throw new ApiError(404, "Assignment not found");
      }

      // Agency configuration has been removed, use default values
      const reminderType =
        assignment.customReminderType ||
        assignment.categoryId.reminderType ||
        "before";

      const reminderOffset = 24; // Default 24 hours

      switch (reminderType) {
        case "before":
          return new Date(dueDate.getTime() - reminderOffset * 60 * 60 * 1000);

        case "on_due":
          return new Date(dueDate.getTime());

        case "after":
          return new Date(dueDate.getTime() + reminderOffset * 60 * 60 * 1000);

        case "weekly":
          // Send reminder every week until upload
          const now = new Date();
          const weeklyInterval = 7 * 24 * 60 * 60 * 1000; // 7 days in ms

          if (now < dueDate) {
            // Before due date - send weekly reminders
            const weeksUntilDue = Math.floor((dueDate - now) / weeklyInterval);
            return weeksUntilDue > 0
              ? new Date(now.getTime() + weeklyInterval)
              : new Date(dueDate.getTime() - 24 * 60 * 60 * 1000); // 1 day before
          } else {
            // After due date - send weekly overdue reminders
            return new Date(now.getTime() + weeklyInterval);
          }

        default:
          return new Date(dueDate.getTime() - 24 * 60 * 60 * 1000); // 1 day before
      }
    } catch (error) {
      if (error instanceof ApiError) throw error;
      throw new ApiError(
        500,
        `Failed to calculate next reminder date: ${error.message}`,
      );
    }
  }

  /**
   * Get all overdue assignments for an agency
   * @param {string} agencyId - Agency ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Overdue assignments
   */
  static async getOverdueAssignments(agencyId, options = {}) {
    try {
      const {
        modelId = null,
        categoryId = null,
        priority = null,
        daysPastDue = null,
        page = 1,
        limit = 50,
      } = options;

      const query = {
        agencyId,
        isActive: true,
        nextDueDate: { $lt: new Date() },
      };

      if (modelId) query.modelId = modelId;
      if (categoryId) query.categoryId = categoryId;
      if (priority) query.priority = priority;

      if (daysPastDue) {
        const cutoffDate = new Date(
          Date.now() - daysPastDue * 24 * 60 * 60 * 1000,
        );
        query.nextDueDate = { $lt: cutoffDate };
      }

      const skip = (page - 1) * limit;

      const [assignments, total] = await Promise.all([
        ModelCategoryAssignment.find(query)
          .populate("modelId", "firstName lastName email")
          .populate("categoryId", "label platform reminderFrequency")
          .sort({ nextDueDate: 1 }) // Oldest overdue first
          .skip(skip)
          .limit(limit),
        ModelCategoryAssignment.countDocuments(query),
      ]);

      // Add overdue days calculation
      const now = new Date();
      const overdueAssignments = assignments.map((assignment) => {
        const daysPastDue = Math.floor(
          (now - assignment.nextDueDate) / (24 * 60 * 60 * 1000),
        );
        return {
          ...assignment.toObject(),
          daysPastDue,
          isOverdue: true,
        };
      });

      return {
        assignments: overdueAssignments,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      throw new ApiError(
        500,
        `Failed to get overdue assignments: ${error.message}`,
      );
    }
  }

  /**
   * Get assignments due soon for an agency
   * @param {string} agencyId - Agency ID
   * @param {number} daysAhead - Days ahead to check (default: 7)
   * @returns {Promise<Array>} Assignments due soon
   */
  static async getAssignmentsDueSoon(agencyId, daysAhead = 7) {
    try {
      const now = new Date();
      const futureDate = new Date(
        now.getTime() + daysAhead * 24 * 60 * 60 * 1000,
      );

      const assignments = await ModelCategoryAssignment.find({
        agencyId,
        isActive: true,
        nextDueDate: {
          $gte: now,
          $lte: futureDate,
        },
      })
        .populate("modelId", "firstName lastName email")
        .populate("categoryId", "label platform reminderFrequency")
        .sort({ nextDueDate: 1 });

      // Add days until due calculation
      const dueSoonAssignments = assignments.map((assignment) => {
        const daysUntilDue = Math.ceil(
          (assignment.nextDueDate - now) / (24 * 60 * 60 * 1000),
        );
        return {
          ...assignment.toObject(),
          daysUntilDue,
          isDueSoon: true,
        };
      });

      return dueSoonAssignments;
    } catch (error) {
      throw new ApiError(
        500,
        `Failed to get assignments due soon: ${error.message}`,
      );
    }
  }

  /**
   * Update due dates for all assignments after upload
   * @param {string} modelId - Model ID
   * @param {string} categoryId - Category ID
   * @param {string} agencyId - Agency ID
   * @returns {Promise<Object>} Updated assignment
   */
  static async updateDueDateAfterUpload(modelId, categoryId, agencyId) {
    try {
      const assignment = await ModelCategoryAssignment.findOne({
        modelId,
        categoryId,
        agencyId,
        isActive: true,
      });

      if (!assignment) {
        throw new ApiError(404, "Assignment not found");
      }

      // Calculate new due date based on upload date
      const newDueDate = await this.calculateNextDueDate(
        assignment._id,
        new Date(),
      );

      // Update assignment
      assignment.nextDueDate = newDueDate;
      assignment.lastUploadDate = new Date();
      assignment.totalUploads += 1;
      assignment.remindersSent = 0; // Reset reminder count
      assignment.lastReminderSent = null;

      await assignment.save();

      return assignment;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      throw new ApiError(
        500,
        `Failed to update due date after upload: ${error.message}`,
      );
    }
  }

  /**
   * Get reminder escalation level based on overdue days
   * @param {number} daysPastDue - Days past due
   * @param {Object} escalationSettings - Agency escalation settings
   * @returns {Object} Escalation level info
   */
  static getEscalationLevel(daysPastDue, escalationSettings = {}) {
    const defaultSettings = {
      level1: { days: 1, label: "First Reminder", urgency: "low" },
      level2: { days: 3, label: "Second Reminder", urgency: "medium" },
      level3: { days: 7, label: "Urgent Reminder", urgency: "high" },
      level4: { days: 14, label: "Critical Reminder", urgency: "critical" },
    };

    const settings = { ...defaultSettings, ...escalationSettings };

    if (daysPastDue >= settings.level4.days) {
      return { level: 4, ...settings.level4 };
    } else if (daysPastDue >= settings.level3.days) {
      return { level: 3, ...settings.level3 };
    } else if (daysPastDue >= settings.level2.days) {
      return { level: 2, ...settings.level2 };
    } else if (daysPastDue >= settings.level1.days) {
      return { level: 1, ...settings.level1 };
    } else {
      return { level: 0, label: "On Time", urgency: "none" };
    }
  }

  /**
   * Get assignments that need reminders sent
   * @param {string} agencyId - Agency ID
   * @returns {Promise<Array>} Assignments needing reminders
   */
  static async getAssignmentsNeedingReminders(agencyId) {
    try {
      const now = new Date();

      // Find assignments where next reminder date has passed
      const assignments = await ModelCategoryAssignment.find({
        agencyId,
        isActive: true,
        $or: [
          // Due date has passed (overdue)
          { nextDueDate: { $lt: now } },
          // Reminder date has passed
          { nextReminderDate: { $lte: now } },
        ],
      })
        .populate("modelId", "firstName lastName email")
        .populate("categoryId", "label platform reminderFrequency reminderType")
        .sort({ nextDueDate: 1 });

      // Filter and categorize reminders
      const reminderAssignments = assignments.map((assignment) => {
        const daysPastDue =
          assignment.nextDueDate < now
            ? Math.floor((now - assignment.nextDueDate) / (24 * 60 * 60 * 1000))
            : 0;

        const escalation = this.getEscalationLevel(daysPastDue);

        return {
          ...assignment.toObject(),
          daysPastDue,
          escalation,
          isOverdue: daysPastDue > 0,
          needsReminder: true,
        };
      });

      return reminderAssignments;
    } catch (error) {
      throw new ApiError(
        500,
        `Failed to get assignments needing reminders: ${error.message}`,
      );
    }
  }

  /**
   * Mark reminder as sent for an assignment
   * @param {string} assignmentId - Assignment ID
   * @param {string} reminderType - Type of reminder sent
   * @returns {Promise<Object>} Updated assignment
   */
  static async markReminderSent(assignmentId, reminderType = "email") {
    try {
      const assignment = await ModelCategoryAssignment.findById(assignmentId);
      if (!assignment) {
        throw new ApiError(404, "Assignment not found");
      }

      assignment.remindersSent += 1;
      assignment.lastReminderSent = new Date();

      // Calculate next reminder date
      const nextReminderDate = await this.calculateNextReminderDate(
        assignmentId,
        assignment.nextDueDate,
      );
      assignment.nextReminderDate = nextReminderDate;

      await assignment.save();

      return assignment;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      throw new ApiError(
        500,
        `Failed to mark reminder as sent: ${error.message}`,
      );
    }
  }

  /**
   * Add frequency interval to a date
   * @param {Date} baseDate - Base date
   * @param {string} frequency - Frequency string (e.g., 'daily', 'weekly', 'monthly')
   * @returns {Date} New date with frequency added
   */
  static addFrequencyToDate(baseDate, frequency) {
    const date = new Date(baseDate);

    switch (frequency.toLowerCase()) {
      case "daily":
        date.setDate(date.getDate() + 1);
        break;
      case "weekly":
        date.setDate(date.getDate() + 7);
        break;
      case "biweekly":
        date.setDate(date.getDate() + 14);
        break;
      case "monthly":
        date.setMonth(date.getMonth() + 1);
        break;
      case "quarterly":
        date.setMonth(date.getMonth() + 3);
        break;
      case "yearly":
        date.setFullYear(date.getFullYear() + 1);
        break;
      default:
        // Default to weekly if frequency is not recognized
        date.setDate(date.getDate() + 7);
    }

    return date;
  }

  /**
   * Bulk update due dates for multiple assignments
   * @param {Array} assignmentIds - Array of assignment IDs
   * @returns {Promise<Object>} Bulk update results
   */
  static async bulkUpdateDueDates(assignmentIds) {
    const results = {
      successful: [],
      failed: [],
    };

    for (const assignmentId of assignmentIds) {
      try {
        const newDueDate = await this.calculateNextDueDate(assignmentId);

        await ModelCategoryAssignment.findByIdAndUpdate(assignmentId, {
          nextDueDate: newDueDate,
          remindersSent: 0,
          lastReminderSent: null,
        });

        results.successful.push({ assignmentId, newDueDate });
      } catch (error) {
        results.failed.push({ assignmentId, error: error.message });
      }
    }

    return results;
  }
}

export default ReminderCalculationService;
