// add model slice
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axiosInstance from "@/config/axiosInstance";

// Thunk to add a model to the agency
export const inviteModel = createAsyncThunk("inviteModel",async ({ modelToAdd }, { rejectWithValue }) => {
    console.log(modelToAdd)
    try {
      const response = await axiosInstance.post(
        "/agency/invite-model",
        { modelId: modelToAdd._id }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.error);
    }
  }
);

const inviteModelSlice = createSlice({
  name: "inviteModelSlice",
  initialState: {
    inviteModel: null,
    loading: false,
    error: null,
    success: false,
  },
  reducers: {
    resetAddModelState: (state) => {
      state.inviteModel = null;
      state.loading = false;
      state.error = null;
      state.success = false;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(inviteModel.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(inviteModel.fulfilled, (state, action) => {
        state.loading = false;
        state.inviteModel = action.payload || action.payload.message
        state.success = true;
      })
      .addCase(inviteModel.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload
      state.success = false;
      });
  },
});

export const { resetAddModelState } = inviteModelSlice.actions;
export default inviteModelSlice.reducer;