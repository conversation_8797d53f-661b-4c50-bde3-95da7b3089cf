import * as React from "react";
import { cn } from "@/lib/utils";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Camera, Upload, X, RotateCcw, Crop } from "lucide-react";

const ImageUploader = ({
  currentImage = null,
  aspectRatio = "1:1", // "1:1", "16:9", "4:3", "free"
  maxSize = 5 * 1024 * 1024, // 5MB default
  quality = 0.8,
  onUpload,
  onRemove,
  disabled = false,
  showCrop = false,
  className,
  ...props
}) => {
  const [image, setImage] = React.useState(currentImage);
  const [preview, setPreview] = React.useState(currentImage);
  const [dragActive, setDragActive] = React.useState(false);
  const [uploading, setUploading] = React.useState(false);
  const [error, setError] = React.useState(null);
  const fileInputRef = React.useRef(null);

  // Calculate aspect ratio
  const getAspectRatio = () => {
    switch (aspectRatio) {
      case "1:1":
        return 1;
      case "16:9":
        return 16 / 9;
      case "4:3":
        return 4 / 3;
      default:
        return null;
    }
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // Validate image file
  const validateImage = (file) => {
    if (!file.type.startsWith("image/")) {
      return "Please select a valid image file";
    }
    if (file.size > maxSize) {
      return `Image size must be less than ${formatFileSize(maxSize)}`;
    }
    return null;
  };

  // Process image upload
  const processImageUpload = async (file) => {
    const validationError = validateImage(file);
    if (validationError) {
      setError(validationError);
      return;
    }

    setError(null);
    setUploading(true);

    try {
      // Create preview
      const previewUrl = URL.createObjectURL(file);
      setPreview(previewUrl);

      // Upload image
      if (onUpload) {
        const result = await onUpload(file);
        setImage(result || previewUrl);
      } else {
        setImage(previewUrl);
      }
    } catch (err) {
      setError(err.message || "Failed to upload image");
      setPreview(null);
    } finally {
      setUploading(false);
    }
  };

  // Handle file drop
  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (disabled) return;

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      processImageUpload(files[0]);
    }
  };

  // Handle file input change
  const handleFileChange = (e) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      processImageUpload(files[0]);
    }
  };

  // Drag handlers
  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  // Remove image
  const removeImage = () => {
    if (preview && preview !== currentImage) {
      URL.revokeObjectURL(preview);
    }
    setImage(null);
    setPreview(null);
    setError(null);
    if (onRemove) {
      onRemove();
    }
  };

  // Open file dialog
  const openFileDialog = () => {
    if (fileInputRef.current && !disabled) {
      fileInputRef.current.click();
    }
  };

  // Get container style based on aspect ratio
  const getContainerStyle = () => {
    const ratio = getAspectRatio();
    if (!ratio) return {};
    return {
      paddingBottom: `${(1 / ratio) * 100}%`,
    };
  };

  return (
    <div className={cn("space-y-4", className)} {...props}>
      {/* Image Upload Area */}
      <Card
        className={cn(
          "relative overflow-hidden transition-all duration-200 cursor-pointer border-2 border-dashed bg-[#0f0f0f] border-gray-800",
          dragActive && "border-gray-600 bg-[#1a1a1a]",
          disabled && "cursor-not-allowed opacity-50",
          !disabled && "hover:border-gray-700",
          preview && "border-solid border-gray-700"
        )}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={!preview ? openFileDialog : undefined}
      >
        <CardContent className="p-0">
          <div
            className="relative w-full"
            style={preview ? {} : getContainerStyle()}
          >
            {preview ? (
              /* Image Preview */
              <div className="relative">
                <img
                  src={preview}
                  alt="Preview"
                  className={cn(
                    "w-full h-full object-cover",
                    getAspectRatio() && "aspect-[var(--aspect-ratio)]"
                  )}
                  style={
                    getAspectRatio()
                      ? { "--aspect-ratio": getAspectRatio() }
                      : {}
                  }
                />

                {/* Image Overlay Controls */}
                <div className="absolute inset-0 bg-black/50 opacity-0 hover:opacity-100 transition-opacity duration-200 flex items-center justify-center gap-2">
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      openFileDialog();
                    }}
                    disabled={disabled || uploading}
                  >
                    <RotateCcw className="h-4 w-4 mr-1" />
                    Replace
                  </Button>
                  {showCrop && (
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        // Implement crop functionality
                      }}
                      disabled={disabled || uploading}
                    >
                      <Crop className="h-4 w-4 mr-1" />
                      Crop
                    </Button>
                  )}
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      removeImage();
                    }}
                    disabled={disabled || uploading}
                  >
                    <X className="h-4 w-4 mr-1" />
                    Remove
                  </Button>
                </div>

                {/* Upload Progress */}
                {uploading && (
                  <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                    <div className="flex items-center gap-2 text-gray-300">
                      <div className="h-5 w-5 animate-spin rounded-full border-2 border-current border-t-transparent" />
                      <span className="text-sm">Uploading...</span>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              /* Upload Prompt */
              <div
                className={cn(
                  "flex flex-col items-center justify-center p-8 text-center",
                  getAspectRatio() && "absolute inset-0"
                )}
              >
                <div className="space-y-4">
                  <div className="flex justify-center">
                    {dragActive ? (
                      <Upload className="h-12 w-12 text-gray-400" />
                    ) : (
                      <Camera className="h-12 w-12 text-gray-500" />
                    )}
                  </div>
                  <div>
                    <p className="text-lg font-medium text-gray-300">
                      {dragActive ? "Drop image here" : "Upload Image"}
                    </p>
                    <p className="text-sm text-gray-500 mt-1">
                      Drag & drop an image or click to browse
                    </p>
                    <p className="text-xs text-gray-600 mt-1">
                      Max size: {formatFileSize(maxSize)}
                      {aspectRatio !== "free" &&
                        ` • Aspect ratio: ${aspectRatio}`}
                    </p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={disabled}
                    className="border-gray-600 bg-[#1a1a1a] text-gray-400 hover:bg-gray-800 hover:text-gray-300 hover:border-gray-500"
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Browse Images
                  </Button>
                </div>
              </div>
            )}
          </div>
        </CardContent>

        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileChange}
          className="hidden"
          disabled={disabled}
        />
      </Card>

      {/* Error Message */}
      {error && (
        <Card className="border-red-600 bg-red-900/20">
          <CardContent className="p-3">
            <p className="text-sm text-red-400">{error}</p>
          </CardContent>
        </Card>
      )}

      {/* Image Info */}
      {preview && !error && (
        <div className="text-xs text-gray-400 space-y-1">
          <p>✓ Image uploaded successfully</p>
          {aspectRatio !== "free" && <p>Aspect ratio: {aspectRatio}</p>}
        </div>
      )}
    </div>
  );
};

export default ImageUploader;
