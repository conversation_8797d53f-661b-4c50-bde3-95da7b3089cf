import React from "react";

const QuestionnaireIcon = ({ className }) => {
  return (
    <svg
      width="100%"
      height="100%"
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <defs>
        {/* Background gradient - dark purple */}
        <radialGradient id="questionnaireBgGrad" cx="50%" cy="30%" r="80%">
          <stop offset="0%" stopColor="#2D1B69" />
          <stop offset="50%" stopColor="#1A1B4A" />
          <stop offset="100%" stopColor="#0F1B3C" />
        </radialGradient>

        {/* Form border gradient - bright magenta */}
        <linearGradient
          id="questionnaireFormBorder"
          x1="0%"
          y1="0%"
          x2="100%"
          y2="100%"
        >
          <stop offset="0%" stopColor="#FF1A8C" />
          <stop offset="50%" stopColor="#E91E63" />
          <stop offset="100%" stopColor="#9C27B0" />
        </linearGradient>

        {/* Checkmark gradient - cyan to blue */}
        <linearGradient
          id="questionnaireCheckGrad"
          x1="0%"
          y1="0%"
          x2="100%"
          y2="100%"
        >
          <stop offset="0%" stopColor="#00E5FF" />
          <stop offset="100%" stopColor="#2196F3" />
        </linearGradient>

        {/* Circle bullet gradient - magenta */}
        <linearGradient
          id="questionnaireBulletGrad"
          x1="0%"
          y1="0%"
          x2="100%"
          y2="100%"
        >
          <stop offset="0%" stopColor="#E91E63" />
          <stop offset="100%" stopColor="#9C27B0" />
        </linearGradient>

        {/* Text line gradient */}
        <linearGradient
          id="questionnaireTextGrad"
          x1="0%"
          y1="0%"
          x2="100%"
          y2="0%"
        >
          <stop offset="0%" stopColor="#9C27B0" />
          <stop offset="100%" stopColor="#673AB7" />
        </linearGradient>

        {/* Neon glow filter */}
        <filter
          id="questionnaireGlow"
          x="-50%"
          y="-50%"
          width="200%"
          height="200%"
        >
          <feGaussianBlur stdDeviation="8" result="blur" />
          <feMerge>
            <feMergeNode in="blur" />
            <feMergeNode in="SourceGraphic" />
          </feMerge>
        </filter>
      </defs>

      {/* Rounded square background */}
      <rect
        x="32"
        y="32"
        width="960"
        height="960"
        rx="180"
        ry="180"
        fill="url(#questionnaireBgGrad)"
      />

      {/* Main form container */}
      <rect
        x="200"
        y="200"
        width="624"
        height="624"
        rx="40"
        ry="40"
        fill="none"
        stroke="url(#questionnaireFormBorder)"
        strokeWidth="20"
        filter="url(#questionnaireGlow)"
      />

      {/* First item - checkmark */}
      <g transform="translate(280, 320)">
        {/* Checkmark */}
        <path
          d="M 0 20 L 20 40 L 60 0"
          fill="none"
          stroke="url(#questionnaireCheckGrad)"
          strokeWidth="16"
          strokeLinecap="round"
          strokeLinejoin="round"
          filter="url(#questionnaireGlow)"
        />
        {/* Text line */}
        <rect
          x="100"
          y="15"
          width="300"
          height="12"
          rx="6"
          fill="url(#questionnaireTextGrad)"
        />
      </g>

      {/* Second item - checkmark */}
      <g transform="translate(280, 450)">
        {/* Checkmark */}
        <path
          d="M 0 20 L 20 40 L 60 0"
          fill="none"
          stroke="url(#questionnaireCheckGrad)"
          strokeWidth="16"
          strokeLinecap="round"
          strokeLinejoin="round"
          filter="url(#questionnaireGlow)"
        />
        {/* Text line */}
        <rect
          x="100"
          y="15"
          width="280"
          height="12"
          rx="6"
          fill="url(#questionnaireTextGrad)"
        />
      </g>

      {/* Third item - circle bullet */}
      <g transform="translate(280, 580)">
        {/* Circle bullet */}
        <circle
          cx="30"
          cy="25"
          r="20"
          fill="none"
          stroke="url(#questionnaireBulletGrad)"
          strokeWidth="12"
          filter="url(#questionnaireGlow)"
        />
        {/* Text line */}
        <rect
          x="100"
          y="15"
          width="320"
          height="12"
          rx="6"
          fill="url(#questionnaireTextGrad)"
        />
      </g>
    </svg>
  );
};

export default QuestionnaireIcon;
