import React, { useState } from "react";
import {
  Filter,
  X,
  Calendar,
  Tag,
  Star,
  Eye,
  FileText,
  Clock,
  User,
  RotateCcw,
} from "lucide-react";

// UI Components
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

// Reusable Components
import { DatePicker } from "@/reusable";

const AdvancedFilters = ({ open, onClose, filters, onFiltersChange }) => {
  const [localFilters, setLocalFilters] = useState(filters);

  // Available options
  const priorities = [
    { value: "", label: "All Priorities" },
    { value: "low", label: "Low", color: "bg-green-500" },
    { value: "medium", label: "Medium", color: "bg-yellow-500" },
    { value: "high", label: "High", color: "bg-orange-500" },
    { value: "critical", label: "Critical", color: "bg-red-500" },
  ];

  const categories = [
    { value: "", label: "All Categories" },
    { value: "general", label: "General", icon: FileText },
    { value: "performance", label: "Performance", icon: Star },
    { value: "behavior", label: "Behavior", icon: User },
    { value: "feedback", label: "Feedback", icon: Star },
    { value: "reminder", label: "Reminder", icon: Clock },
    { value: "meeting", label: "Meeting", icon: Calendar },
    { value: "contract", label: "Contract", icon: FileText },
    { value: "payment", label: "Payment", icon: FileText },
  ];

  const visibilityOptions = [
    { value: "", label: "All Visibility" },
    { value: "internal", label: "Internal Only", icon: Eye },
    { value: "shared_with_model", label: "Shared with Model", icon: User },
    { value: "neutral", label: "Neutral", icon: FileText },
  ];

  const statusOptions = [
    { value: "active", label: "Active" },
    { value: "archived", label: "Archived" },
    { value: "deleted", label: "Deleted" },
  ];

  const pinnedOptions = [
    { value: null, label: "All Notes" },
    { value: true, label: "Pinned Only" },
    { value: false, label: "Not Pinned" },
  ];

  // Common tags (you might want to fetch these from your API)
  const commonTags = [
    "urgent",
    "follow-up",
    "important",
    "review",
    "feedback",
    "performance",
    "meeting",
    "contract",
    "payment",
    "reminder",
  ];

  const handleFilterChange = (key, value) => {
    setLocalFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleTagToggle = (tag) => {
    setLocalFilters((prev) => ({
      ...prev,
      tags: prev.tags.includes(tag)
        ? prev.tags.filter((t) => t !== tag)
        : [...prev.tags, tag],
    }));
  };

  const handleApplyFilters = () => {
    onFiltersChange(localFilters);
    onClose();
  };

  const handleResetFilters = () => {
    const resetFilters = {
      priority: "",
      category: "",
      tags: [],
      dateRange: null,
      visibility: "",
      status: "active",
      isPinned: null,
    };
    setLocalFilters(resetFilters);
    onFiltersChange(resetFilters);
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (localFilters.priority) count++;
    if (localFilters.category) count++;
    if (localFilters.tags.length > 0) count++;
    if (localFilters.dateRange) count++;
    if (localFilters.visibility) count++;
    if (localFilters.status !== "active") count++;
    if (localFilters.isPinned !== null) count++;
    return count;
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(val) => {
        // Only call parent's onClose when the dialog is closing.
        if (val === false) onClose();
      }}
    >
      <DialogContent className="max-w-2xl max-h-[90vh] bg-[#18181b] border-[#27272a] text-white">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Advanced Filters
              {getActiveFiltersCount() > 0 && (
                <Badge variant="secondary" className="bg-blue-600 text-white">
                  {getActiveFiltersCount()}
                </Badge>
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-6 w-6 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6 max-h-[70vh] overflow-y-auto">
          {/* Priority Filter */}
          <Card className="bg-[#1a1a1a] border-gray-800">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2 text-white">
                <Star className="h-4 w-4" />
                Priority
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0 text-white">
              <Select
                value={localFilters.priority || "__all"}
                onValueChange={(value) =>
                  handleFilterChange("priority", value === "__all" ? "" : value)
                }
              >
                <SelectTrigger className="bg-[#0f0f0f] border-gray-700 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="text-white">
                  {priorities.map((priority) => (
                    <SelectItem
                      key={String(priority.value)}
                      value={priority.value === "" ? "__all" : priority.value}
                    >
                      <div className="flex items-center gap-2">
                        {priority.color && (
                          <div
                            className={`w-2 h-2 rounded-full ${priority.color}`}
                          />
                        )}
                        {priority.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </CardContent>
          </Card>

          {/* Category Filter */}
          <Card className="bg-[#1a1a1a] border-gray-800">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2 text-white">
                <FileText className="h-4 w-4" />
                Category
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0 text-white">
              <Select
                value={localFilters.category || "__all"}
                onValueChange={(value) =>
                  handleFilterChange("category", value === "__all" ? "" : value)
                }
              >
                <SelectTrigger className="bg-[#0f0f0f] border-gray-700 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="text-white">
                  {categories.map((category) => (
                    <SelectItem
                      key={String(category.value)}
                      value={category.value === "" ? "__all" : category.value}
                    >
                      <div className="flex items-center gap-2">
                        {category.icon && <category.icon className="h-3 w-3" />}
                        {category.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </CardContent>
          </Card>

          {/* Visibility Filter */}
          <Card className="bg-[#1a1a1a] border-gray-800">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2 text-white">
                <Eye className="h-4 w-4" />
                Visibility
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0 text-white">
              <Select
                value={localFilters.visibility || "__all"}
                onValueChange={(value) =>
                  handleFilterChange(
                    "visibility",
                    value === "__all" ? "" : value
                  )
                }
              >
                <SelectTrigger className="bg-[#0f0f0f] border-gray-700 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="text-white">
                  {visibilityOptions.map((option) => (
                    <SelectItem
                      key={String(option.value)}
                      value={option.value === "" ? "__all" : option.value}
                    >
                      <div className="flex items-center gap-2">
                        {option.icon && <option.icon className="h-3 w-3" />}
                        {option.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </CardContent>
          </Card>

          {/* Status Filter */}
          <Card className="bg-[#1a1a1a] border-gray-800">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2 text-white">
                <Clock className="h-4 w-4" />
                Status
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0 text-white">
              <Select
                value={localFilters.status}
                onValueChange={(value) => handleFilterChange("status", value)}
              >
                <SelectTrigger className="bg-[#0f0f0f] border-gray-700 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="text-white">
                  {statusOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </CardContent>
          </Card>

          {/* Pinned Filter */}
          <Card className="bg-[#1a1a1a] border-gray-800">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2 text-white">
                <Star className="h-4 w-4" />
                Pinned Status
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0 text-white">
              <div className="space-y-2">
                {pinnedOptions.map((option) => (
                  <div
                    key={String(option.value)}
                    className="flex items-center space-x-2"
                  >
                    <Checkbox
                      id={`pinned-${option.value}`}
                      checked={localFilters.isPinned === option.value}
                      onCheckedChange={() =>
                        handleFilterChange("isPinned", option.value)
                      }
                    />
                    <Label
                      htmlFor={`pinned-${option.value}`}
                      className="text-sm font-normal cursor-pointer"
                    >
                      {option.label}
                    </Label>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Tags Filter */}
          <Card className="bg-[#1a1a1a] border-gray-800">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2 text-white">
                <Tag className="h-4 w-4" />
                Tags
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0 text-white">
              <div className="space-y-3">
                <div className="flex flex-wrap gap-2">
                  {commonTags.map((tag) => (
                    <Badge
                      key={tag}
                      variant={
                        localFilters.tags.includes(tag) ? "default" : "outline"
                      }
                      className={`cursor-pointer transition-colors ${
                        localFilters.tags.includes(tag)
                          ? "bg-blue-600 hover:bg-blue-700"
                          : "hover:bg-gray-800"
                      }`}
                      onClick={() => handleTagToggle(tag)}
                    >
                      #{tag}
                    </Badge>
                  ))}
                </div>

                {localFilters.tags.length > 0 && (
                  <div className="space-y-2">
                    <Label className="text-xs text-white">Selected Tags:</Label>
                    <div className="flex flex-wrap gap-1">
                      {localFilters.tags.map((tag) => (
                        <Badge
                          key={tag}
                          variant="default"
                          className="bg-blue-600 text-white"
                        >
                          #{tag}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleTagToggle(tag)}
                            className="h-4 w-4 p-0 ml-1 hover:bg-blue-700"
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Date Range Filter */}
          <Card className="bg-[#1a1a1a] border-gray-800">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Date Range
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0 text-white">
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <Label className="text-xs text-white mb-1 block">From</Label>
                  <DatePicker
                    value={localFilters.dateRange?.from}
                    onChange={(date) =>
                      handleFilterChange("dateRange", {
                        ...localFilters.dateRange,
                        from: date,
                      })
                    }
                    placeholder="Start date"
                    className="bg-[#0f0f0f] border-gray-700"
                  />
                </div>
                <div>
                  <Label className="text-xs text-white mb-1 block">To</Label>
                  <DatePicker
                    value={localFilters.dateRange?.to}
                    onChange={(date) =>
                      handleFilterChange("dateRange", {
                        ...localFilters.dateRange,
                        to: date,
                      })
                    }
                    placeholder="End date"
                    className="bg-[#0f0f0f] border-gray-700"
                  />
                </div>
              </div>

              {localFilters.dateRange && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleFilterChange("dateRange", null)}
                  className="mt-2 h-6 text-xs text-white hover:text-gray-300"
                >
                  Clear Date Range
                </Button>
              )}
            </CardContent>
          </Card>
        </div>

        <Separator className="border-gray-800" />

        {/* Actions */}
        <div className="flex justify-between gap-3">
          <Button
            variant="outline"
            onClick={handleResetFilters}
            className="flex items-center gap-2"
          >
            <RotateCcw className="h-4 w-4" />
            Reset All
          </Button>

          <div className="flex gap-2">
            <Button className="text-white" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button
              onClick={handleApplyFilters}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              Apply Filters
              {getActiveFiltersCount() > 0 && (
                <Badge
                  variant="secondary"
                  className="ml-2 bg-blue-600 text-white"
                >
                  {getActiveFiltersCount()}
                </Badge>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AdvancedFilters;
