import * as React from "react";
import { cn } from "@/lib/utils";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

const FormModal = ({
  isOpen,
  onClose,
  title,
  children,
  size = "default",
  className,
  ...props
}) => {
  // Size configurations
  const sizeClasses = {
    sm: "sm:max-w-md",
    default: "sm:max-w-lg",
    lg: "sm:max-w-2xl",
    xl: "sm:max-w-4xl",
    full: "sm:max-w-[90vw]",
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose} {...props}>
      <DialogContent
        className={cn(
          sizeClasses[size],
          "max-h-[90vh] overflow-hidden bg-[#0F0F0F] border-2 border-[#1a1a1a]",
          className
        )}
      >
        <DialogHeader className="space-y-3">
          <DialogTitle className="text-lg font-semibold text-white">{title}</DialogTitle>
        </DialogHeader>

        {/* Scrollable content area with custom scrollbar */}
        <div
          className="overflow-y-auto pr-2 -mr-2 custom-scrollbar"
          style={{
            scrollbarWidth: 'thin',
            scrollbarColor: '#232324 #18181b',
            maxHeight: '60vh',
          }}
        >
          {children}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default FormModal;
