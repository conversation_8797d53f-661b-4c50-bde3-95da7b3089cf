import gptService from "./captionServices/gptService.js";
import geminiService from "./captionServices/geminiService.js";
import PersonaProfile from "../models/persona/PersonaProfile.js";
import PersonaUsageLog from "../models/persona/PersonaUsageLog.js";
import ModelAnswer from "../models/questionnaire/ModelAnswer.js";
import Template from "../models/questionnaire/Template.js";
import ModelUser from "../models/model.js";
import UserProfile from "../models/UserProfile.js";
import CaptionAnalysisService from "./captionAnalysisService.js";

/**
 * PersonaService - Handles AI-powered persona generation and management
 * Integrates with existing GPT and Gemini services for content generation
 */
class PersonaService {
  /**
   * Generate a persona profile using AI based on questionnaire answers and model data
   */
  static async generatePersona(modelId, agencyId, createdBy, createdByModel) {
    try {
      // Fetch model data
      const model =
        await ModelUser.findById(modelId).select("fullName username");

      if (!model) {
        throw new Error("Model not found");
      }

      // Fetch user profile data for bio and profile enhancement fields
      const userProfile = await UserProfile.findOne({ user: modelId }).select(
        "bio display_name job_title role_description interests goals demographics archetype tone audience",
      );

      // Fetch questionnaire answers
      const answers = await this.getQuestionnaireAnswers(modelId);

      // Analyze caption history for tone and content patterns
      const captionAnalysis =
        await CaptionAnalysisService.analyzeCaptionHistory(modelId, 20);

      // Prepare AI prompt with enhanced data
      const prompt = this.buildPersonaPrompt(
        model,
        userProfile,
        answers,
        captionAnalysis,
      );

      // Generate persona using AI (GPT first, Gemini fallback)
      let aiResult;
      let aiEngine;

      try {
        const gptResponse = await gptService.generatePersona(prompt);
        aiResult = gptResponse.content || gptResponse;
        aiEngine = "gpt-4";
      } catch (gptError) {
        console.log("GPT failed, falling back to Gemini:", gptError.message);
        try {
          const geminiResponse = await geminiService.generatePersona(prompt);
          aiResult = geminiResponse.content || geminiResponse;
          aiEngine = "gemini-vision";
        } catch (geminiError) {
          console.error("Both GPT and Gemini failed:", geminiError.message);
          throw new Error(
            `AI services unavailable: GPT - ${gptError.message}, Gemini - ${geminiError.message}`,
          );
        }
      }

      // Parse AI response
      const parsedPersona = this.parsePersonaResponse(aiResult);
      console.log(
        "Parsed persona from AI:",
        JSON.stringify(parsedPersona, null, 2),
      );

      // Validate and map AI response to schema constraints
      const validatedPersona = this.validateAndMapPersonaData(parsedPersona);
      console.log(
        "Validated persona data:",
        JSON.stringify(validatedPersona, null, 2),
      );

      // Check for emojis or links in personaText before saving
      const emojiRegex =
        /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu;
      const urlRegex = /(https?:\/\/[^\s]+)/g;

      if (emojiRegex.test(validatedPersona.personaText)) {
        console.log("WARNING: Persona text contains emojis - cleaning it");
        validatedPersona.personaText = validatedPersona.personaText
          .replace(emojiRegex, "")
          .trim();
      }

      if (urlRegex.test(validatedPersona.personaText)) {
        console.log("WARNING: Persona text contains URLs - cleaning it");
        validatedPersona.personaText = validatedPersona.personaText
          .replace(urlRegex, "")
          .trim();
      }

      // Create persona profile with validated data
      const personaData = {
        personaText: validatedPersona.personaText,
        tags: validatedPersona.tags,
        demographics: validatedPersona.demographics,
        interests: validatedPersona.interests,
        goals: validatedPersona.goals,
        painPoints: validatedPersona.painPoints,
        psychographics: validatedPersona.psychographics,
        communicationStyle: validatedPersona.communicationStyle,
        aiGeneratedFrom: {
          questionnaireAnswers: answers,
          profileData: {
            fullName: model.fullName,
            username: model.username,
            bio: userProfile?.bio,
            displayName: userProfile?.display_name,
            jobTitle: userProfile?.job_title,
            roleDescription: userProfile?.role_description,
            interests: userProfile?.interests,
            goals: userProfile?.goals,
            demographics: userProfile?.demographics,
            archetype: userProfile?.archetype,
            tone: userProfile?.tone,
            audience: userProfile?.audience,
          },
          captionAnalysis: captionAnalysis,
        },
        aiEngineUsed: aiEngine,
        modelId,
        agencyId,
        createdBy,
        createdByModel,
      };

      console.log(
        "Creating persona with data:",
        JSON.stringify(personaData, null, 2),
      );

      const persona = new PersonaProfile(personaData);
      console.log("Persona instance created:", persona);

      try {
        const savedPersona = await persona.save();
        console.log("Persona saved successfully:", savedPersona._id);
        return savedPersona;
      } catch (saveError) {
        console.error("Persona save failed with error:", saveError.message);
        console.error("Full save error:", saveError);
        throw new Error(`Failed to save persona: ${saveError.message}`);
      }
    } catch (error) {
      console.error("Persona generation failed:", error);
      throw error;
    }
  }

  /**
   * Fetch relevant questionnaire answers for a model
   */
  static async getQuestionnaireAnswers(modelId) {
    try {
      const answers = await ModelAnswer.find({ modelId })
        .populate({
          path: "templateId",
          select: "title sections",
        })
        .sort({ createdAt: -1 })
        .limit(5); // Get latest 5 questionnaire responses

      const formattedAnswers = {};

      for (const answer of answers) {
        if (answer.templateId) {
          const templateTitle = answer.templateId.title;
          formattedAnswers[templateTitle] = {};

          // Extract answers by section and question
          for (const section of answer.templateId.sections) {
            for (const question of section.questions) {
              const answerValue = answer.answers.get(question._id.toString());
              if (
                answerValue !== undefined &&
                answerValue !== null &&
                answerValue !== ""
              ) {
                formattedAnswers[templateTitle][question.questionText] =
                  answerValue;
              }
            }
          }
        }
      }

      return formattedAnswers;
    } catch (error) {
      console.error("Failed to fetch questionnaire answers:", error);
      return {};
    }
  }

  /**
   * Build AI prompt for persona generation with enhanced data sources
   */
  static buildPersonaPrompt(model, userProfile, answers, captionAnalysis) {
    const prompt = `Generate a detailed audience persona for the following model/influencer.

MODEL INFORMATION:
Name: ${model.fullName}
Username: ${model.username}
Display Name: ${userProfile?.display_name || "Not provided"}
Bio: ${userProfile?.bio || "Not provided"}
Job Title: ${userProfile?.job_title || "Not provided"}
Role Description: ${userProfile?.role_description || "Not provided"}
Interests: ${userProfile?.interests?.join(", ") || "Not provided"}
Goals: ${userProfile?.goals?.join(", ") || "Not provided"}
Demographics: ${userProfile?.demographics || "Not provided"}
Brand Archetype: ${userProfile?.archetype || "Not provided"}
Content Tone: ${userProfile?.tone || "Not provided"}
Target Audience: ${userProfile?.audience || "Not provided"}

QUESTIONNAIRE RESPONSES:
${this.formatAnswersForPrompt(answers)}

${CaptionAnalysisService.formatAnalysisForPrompt(captionAnalysis)}

INSTRUCTIONS:
Create a comprehensive audience persona that represents the ideal follower/fan of this model. The persona should be:
- Written in a single, flowing paragraph (maximum 150 words)
- Professional and marketing-focused
- Based on the model's profile data, questionnaire responses, and caption history analysis
- Consider the content tone, style, and themes from caption analysis
- Free of emojis, links, or special characters
- Focused on demographics, interests, goals, and pain points
- Reflect the audience that would resonate with the model's content style and tone

For psychographics.values, use ONLY these exact values:
"Adventurous", "Security-focused", "Ambitious", "Creative", "Traditional", "Independent", "Family-oriented", "Status-conscious", "Authentic", "Innovative", "Collaborative", "Competitive", "Balanced", "Risk-taker", "Conservative"

For communicationStyle.tone, use ONLY these exact values:
"Professional", "Casual", "Playful", "Inspirational", "Educational", "Authentic"

For communicationStyle.hashtagStrategy, use ONLY these exact values:
"Trending-focused", "Niche-specific", "Minimal", "Brand-consistent", "Mixed"

Return the response in this exact JSON format:
{
  "personaText": "Single paragraph describing the ideal audience member...",
  "tags": ["tag1", "tag2", "tag3", "tag4", "tag5"],
  "demographics": {
    "age": "age range",
    "location": "primary location"
  },
  "interests": ["interest1", "interest2", "interest3"],
  "goals": ["goal1", "goal2", "goal3"],
  "painPoints": ["pain1", "pain2", "pain3"],
  "psychographics": {
    "values": ["Authentic", "Creative"],
    "personality": ["trait1", "trait2"],
    "motivations": ["motivation1", "motivation2"]
  },
  "communicationStyle": {
    "tone": "Inspirational",
    "emojiUsage": "Moderate emoji usage",
    "hashtagStrategy": "Niche-specific"
  }
}

Ensure the tags are descriptive and relevant (minimum 3 required).`;

    return prompt;
  }

  /**
   * Format questionnaire answers for AI prompt
   */
  static formatAnswersForPrompt(answers) {
    if (!answers || Object.keys(answers).length === 0) {
      return "No questionnaire responses available.";
    }

    let formatted = "";
    for (const [templateTitle, questions] of Object.entries(answers)) {
      formatted += `\n${templateTitle}:\n`;
      for (const [question, answer] of Object.entries(questions)) {
        formatted += `- ${question}: ${answer}\n`;
      }
    }

    return formatted;
  }

  /**
   * Parse AI response and validate structure
   */
  static parsePersonaResponse(aiResponse) {
    try {
      // console.log("Raw AI Response:", aiResponse);
      // console.log("AI Response Type:", typeof aiResponse);

      let jsonStr;

      // Handle different response types
      if (typeof aiResponse === "object") {
        // If it's already an object, check if it has the expected structure
        if (aiResponse && typeof aiResponse === "object") {
          // console.log("Response is already an object");
          const parsed = aiResponse;

          // Validate required fields
          if (
            !parsed.personaText ||
            !parsed.tags ||
            !Array.isArray(parsed.tags)
          ) {
            throw new Error("Invalid persona structure from AI");
          }

          // Ensure minimum requirements
          if (parsed.tags.length < 3) {
            throw new Error("Insufficient tags provided by AI");
          }

          // Note: personaText can be longer than 150 characters for detailed personas
          // Only truncate if extremely long (over 2000 characters)
          if (parsed.personaText.length > 2000) {
            parsed.personaText = parsed.personaText.substring(0, 1997) + "...";
          }

          // Set defaults for optional fields
          parsed.demographics = parsed.demographics || {};
          parsed.interests = parsed.interests || [];
          parsed.goals = parsed.goals || [];
          parsed.painPoints = parsed.painPoints || [];

          return parsed;
        }
      } else if (typeof aiResponse === "string") {
        // Handle string responses
        jsonStr = aiResponse.trim();

        // Check if it's already a clean JSON string
        if (!jsonStr.startsWith("{") || !jsonStr.endsWith("}")) {
          // Try to extract from markdown
          const jsonMatch = aiResponse.match(
            /```(?:json)?\s*({[\s\S]*?})\s*```/,
          );
          if (jsonMatch) {
            jsonStr = jsonMatch[1];
          } else {
            // Try to find JSON object in the response
            const startIndex = aiResponse.indexOf("{");
            const lastIndex = aiResponse.lastIndexOf("}");
            if (startIndex !== -1 && lastIndex !== -1) {
              jsonStr = aiResponse.substring(startIndex, lastIndex + 1);
            }
          }
        }
      } else {
        // Handle indexed character format (like the one we saw)
        if (typeof aiResponse === "object" && aiResponse !== null) {
          // Convert indexed object to string
          const keys = Object.keys(aiResponse).sort(
            (a, b) => parseInt(a) - parseInt(b),
          );
          jsonStr = keys.map((key) => aiResponse[key]).join("");
        } else {
          throw new Error("Unsupported AI response format");
        }
      }

      // console.log("Final JSON string to parse:", jsonStr);
      const parsed = JSON.parse(jsonStr);

      // Validate required fields
      if (!parsed.personaText || !parsed.tags || !Array.isArray(parsed.tags)) {
        throw new Error("Invalid persona structure from AI");
      }

      // Ensure minimum requirements
      if (parsed.tags.length < 3) {
        throw new Error("Insufficient tags provided by AI");
      }

      // Note: personaText can be longer than 150 characters for detailed personas
      // Only truncate if extremely long (over 2000 characters)
      if (parsed.personaText.length > 2000) {
        parsed.personaText = parsed.personaText.substring(0, 1997) + "...";
      }

      // Set defaults for optional fields
      parsed.demographics = parsed.demographics || {};
      parsed.interests = parsed.interests || [];
      parsed.goals = parsed.goals || [];
      parsed.painPoints = parsed.painPoints || [];

      return parsed;
    } catch (error) {
      console.error("Failed to parse AI persona response:", error);
      console.error("Error details:", error.message);
      console.error("AI Response that failed to parse:", aiResponse);
      throw new Error(`Invalid AI response format: ${error.message}`);
    }
  }

  /**
   * Validate and map AI-generated persona data to match schema constraints
   */
  static validateAndMapPersonaData(parsedPersona) {
    const validatedData = {
      personaText: parsedPersona.personaText,
      tags: parsedPersona.tags || [],
      demographics: parsedPersona.demographics || {},
      interests: parsedPersona.interests || [],
      goals: parsedPersona.goals || [],
      painPoints: parsedPersona.painPoints || [],
    };

    // Map communication style values to valid enums
    if (parsedPersona.communicationStyle) {
      validatedData.communicationStyle = {};

      // Map tone values
      if (parsedPersona.communicationStyle.tone) {
        const toneMapping = {
          professional: "Professional",
          casual: "Casual",
          playful: "Playful",
          inspirational: "Inspirational",
          inspiring: "Inspirational",
          motivating: "Inspirational",
          "inspirational and motivating": "Inspirational",
          educational: "Educational",
          authentic: "Authentic",
        };

        const lowerTone = parsedPersona.communicationStyle.tone.toLowerCase();
        validatedData.communicationStyle.tone =
          toneMapping[lowerTone] || "Authentic";
      }

      // Map hashtag strategy values
      if (parsedPersona.communicationStyle.hashtagStrategy) {
        const hashtagMapping = {
          "trending-focused": "Trending-focused",
          trending: "Trending-focused",
          "niche-specific": "Niche-specific",
          niche: "Niche-specific",
          strategic: "Niche-specific",
          "strategic use of fitness and lifestyle hashtags": "Niche-specific",
          minimal: "Minimal",
          "brand-consistent": "Brand-consistent",
          mixed: "Mixed",
        };

        const lowerHashtag =
          parsedPersona.communicationStyle.hashtagStrategy.toLowerCase();
        validatedData.communicationStyle.hashtagStrategy =
          hashtagMapping[lowerHashtag] || "Mixed";
      }

      // Set emoji usage if provided
      if (parsedPersona.communicationStyle.emojiUsage) {
        const emojiMapping = {
          "heavy emoji user": "Heavy emoji user",
          heavy: "Heavy emoji user",
          "moderate emoji usage": "Moderate emoji usage",
          moderate: "Moderate emoji usage",
          "minimal emoji usage": "Minimal emoji usage",
          minimal: "Minimal emoji usage",
          "no emojis": "No emojis",
          none: "No emojis",
        };

        const lowerEmoji =
          parsedPersona.communicationStyle.emojiUsage.toLowerCase();
        validatedData.communicationStyle.emojiUsage =
          emojiMapping[lowerEmoji] || "Moderate emoji usage";
      }
    }

    // Map psychographics values to valid enums
    if (parsedPersona.psychographics) {
      validatedData.psychographics = {};

      if (
        parsedPersona.psychographics.values &&
        Array.isArray(parsedPersona.psychographics.values)
      ) {
        const validValues = [
          "Adventurous",
          "Security-focused",
          "Ambitious",
          "Creative",
          "Traditional",
          "Independent",
          "Family-oriented",
          "Status-conscious",
          "Authentic",
          "Innovative",
          "Collaborative",
          "Competitive",
          "Balanced",
          "Risk-taker",
          "Conservative",
        ];

        const valueMapping = {
          health: "Balanced",
          wellness: "Balanced",
          authenticity: "Authentic",
          motivation: "Ambitious",
          motivating: "Ambitious",
          inspiring: "Creative",
          adventure: "Adventurous",
          security: "Security-focused",
          ambition: "Ambitious",
          creativity: "Creative",
          tradition: "Traditional",
          independence: "Independent",
          family: "Family-oriented",
          status: "Status-conscious",
          innovation: "Innovative",
          collaboration: "Collaborative",
          competition: "Competitive",
          balance: "Balanced",
          risk: "Risk-taker",
          conservative: "Conservative",
        };

        validatedData.psychographics.values =
          parsedPersona.psychographics.values
            .map((value) => {
              const lowerValue = value.toLowerCase();
              return (
                valueMapping[lowerValue] ||
                validValues.find((v) => v.toLowerCase().includes(lowerValue)) ||
                null
              );
            })
            .filter(Boolean)
            .slice(0, 5); // Limit to 5 values

        // Ensure we have at least one valid value
        if (validatedData.psychographics.values.length === 0) {
          validatedData.psychographics.values = ["Authentic", "Creative"];
        }
      }

      // Copy other psychographics fields
      if (parsedPersona.psychographics.lifestyle) {
        validatedData.psychographics.lifestyle =
          parsedPersona.psychographics.lifestyle;
      }
      if (parsedPersona.psychographics.personality) {
        validatedData.psychographics.personality =
          parsedPersona.psychographics.personality;
      }
      if (parsedPersona.psychographics.motivations) {
        validatedData.psychographics.motivations =
          parsedPersona.psychographics.motivations;
      }
    }

    return validatedData;
  }

  /**
   * Update existing persona with new data
   */
  static async updatePersona(personaId, updateData, updatedBy) {
    try {
      const persona = await PersonaProfile.findById(personaId);
      if (!persona) {
        throw new Error("Persona not found");
      }

      // Add version tracking
      persona.addVersion(updatedBy, "Manual update");

      // Update fields
      Object.assign(persona, updateData);

      await persona.save();
      return persona;
    } catch (error) {
      console.error("Failed to update persona:", error);
      throw error;
    }
  }

  /**
   * Log persona usage for analytics
   */
  static async logUsage(
    personaId,
    usageType,
    context,
    usedBy,
    usedByModel,
    agencyId,
    modelId,
  ) {
    try {
      await PersonaUsageLog.logUsage({
        personaId,
        usageType,
        context,
        usedBy,
        usedByModel,
        agencyId,
        modelId,
      });
    } catch (error) {
      console.error("Failed to log persona usage:", error);
      // Don't throw - usage logging shouldn't break main functionality
    }
  }

  /**
   * Get personas for an agency with filtering options
   */
  static async getPersonasForAgency(agencyId, options = {}) {
    try {
      return await PersonaProfile.findByAgency(agencyId, options);
    } catch (error) {
      console.error("Failed to fetch personas:", error);
      throw error;
    }
  }

  /**
   * Get persona analytics for dashboard
   */
  static async getPersonaAnalytics(agencyId, personaId = null) {
    try {
      if (personaId) {
        // Get analytics for specific persona
        const persona = await PersonaProfile.findOne({
          _id: personaId,
          agencyId,
        }).populate("modelId", "fullName username");

        if (!persona) {
          throw new Error("Persona not found");
        }

        // Get usage logs for this specific persona
        const usageLogs = await PersonaUsageLog.find({ personaId })
          .sort({ timestamp: -1 })
          .limit(30); // Last 30 usage entries

        // Calculate persona-specific metrics
        const totalViews = usageLogs.length;
        const uniqueUsers = new Set(usageLogs.map((log) => log.userId)).size;
        const lastUsed =
          usageLogs.length > 0 ? usageLogs[0].timestamp : persona.createdAt;

        // Calculate usage trend (last 7 days vs previous 7 days)
        const now = new Date();
        const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        const previous7Days = new Date(
          now.getTime() - 14 * 24 * 60 * 60 * 1000,
        );

        const recentUsage = usageLogs.filter(
          (log) => log.timestamp >= last7Days,
        ).length;
        const previousUsage = usageLogs.filter(
          (log) => log.timestamp >= previous7Days && log.timestamp < last7Days,
        ).length;

        const trend =
          previousUsage > 0
            ? (((recentUsage - previousUsage) / previousUsage) * 100).toFixed(1)
            : recentUsage > 0
              ? 100
              : 0;

        // Generate mock performance data for charts
        const performanceData = this.generatePersonaPerformanceData(
          persona,
          usageLogs,
        );

        return {
          persona: {
            id: persona._id,
            content: persona.personaText,
            tags: persona.tags,
            modelName: persona.modelId?.fullName || persona.modelId?.username,
            createdAt: persona.createdAt,
            updatedAt: persona.updatedAt,
          },
          metrics: {
            totalViews,
            uniqueUsers,
            lastUsed,
            trend: parseFloat(trend),
            daysSinceCreated: Math.floor(
              (now - persona.createdAt) / (1000 * 60 * 60 * 24),
            ),
            daysSinceUpdate: Math.floor(
              (now - persona.updatedAt) / (1000 * 60 * 60 * 24),
            ),
          },
          charts: performanceData,
        };
      } else {
        // Get general analytics for all personas (existing logic)
        const [topPersonas, usageAnalytics] = await Promise.all([
          PersonaUsageLog.getTopPersonas(agencyId, 5),
          PersonaUsageLog.getUsageAnalytics(agencyId),
        ]);

        // Calculate freshness metrics
        const personas = await PersonaProfile.find({ agencyId })
          .select("createdAt updatedAt")
          .sort({ updatedAt: -1 });

        const freshnessData = personas.map((persona) => ({
          id: persona._id,
          daysSinceUpdate: Math.floor(
            (Date.now() - persona.updatedAt) / (1000 * 60 * 60 * 24),
          ),
        }));

        return {
          topPersonas,
          usageAnalytics,
          freshnessData,
          totalPersonas: personas.length,
        };
      }
    } catch (error) {
      console.error("Failed to get persona analytics:", error);
      throw error;
    }
  }

  /**
   * Generate performance data for a specific persona
   */
  static generatePersonaPerformanceData(persona, usageLogs) {
    const now = new Date();
    const last30Days = [];

    // Generate data for last 30 days
    for (let i = 29; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const dayUsage = usageLogs.filter((log) => {
        const logDate = new Date(log.timestamp);
        return logDate.toDateString() === date.toDateString();
      }).length;

      last30Days.push({
        date: date.toISOString().split("T")[0],
        views: dayUsage,
        engagement: Math.min(dayUsage * 15 + Math.random() * 10, 100), // Mock engagement
      });
    }

    // Generate tag performance data
    const tagPerformance =
      persona.tags?.map((tag) => ({
        tag,
        performance: Math.floor(Math.random() * 40) + 60, // Mock performance 60-100%
        color: `hsl(${Math.floor(Math.random() * 360)}, 70%, 50%)`,
      })) || [];

    // Generate usage distribution
    const usageDistribution = [
      { name: "Social Media", value: 35 + Math.floor(Math.random() * 20) },
      { name: "Email Marketing", value: 25 + Math.floor(Math.random() * 15) },
      { name: "Content Creation", value: 20 + Math.floor(Math.random() * 15) },
      { name: "Advertising", value: 15 + Math.floor(Math.random() * 10) },
      { name: "Other", value: 5 + Math.floor(Math.random() * 5) },
    ];

    return {
      dailyUsage: last30Days,
      tagPerformance,
      usageDistribution,
    };
  }

  /**
   * Get personas for an agency with filtering options
   */
  static async getPersonasForAgency(agencyId, options = {}) {
    try {
      console.log("PersonaService.getPersonasForAgency called with:", {
        agencyId,
        options,
      });

      const query = { agencyId };

      // Apply filters if provided
      if (options.modelId) {
        query.modelId = options.modelId;
      }

      if (options.tags && options.tags.length > 0) {
        query.tags = { $in: options.tags };
      }

      console.log("Query to be executed:", query);

      const personas = await PersonaProfile.find(query)
        .populate("modelId", "fullName username")
        .populate("createdBy", "name email")
        .sort({ createdAt: -1 })
        .lean();

      console.log(`Found ${personas.length} personas for agency ${agencyId}`);

      // Transform personas to match expected format
      const transformedPersonas = personas.map((persona) => ({
        _id: persona._id,
        content: persona.personaText, // Map personaText to content for frontend compatibility
        personaText: persona.personaText,
        tags: persona.tags || [],
        demographics: persona.demographics || {},
        interests: persona.interests || [],
        goals: persona.goals || [],
        painPoints: persona.painPoints || [],
        model: persona.modelId,
        modelId: persona.modelId,
        createdAt: persona.createdAt,
        updatedAt: persona.updatedAt,
        version: persona.version,
        isGenerated: persona.aiEngineUsed !== "manual",
        aiEngineUsed: persona.aiEngineUsed,
      }));

      console.log("Transformed personas:", transformedPersonas.length);
      return transformedPersonas;
    } catch (error) {
      console.error("Failed to get personas for agency:", error);
      throw error;
    }
  }

  /**
   * Save a manually created persona profile for a model
   */
  static async savePersona(
    modelId,
    agencyId,
    createdBy,
    createdByModel,
    personaData,
  ) {
    try {
      // Validate required fields
      if (
        !personaData.personaText ||
        personaData.personaText.trim().length === 0
      ) {
        throw new Error("Persona text is required");
      }

      // Create persona profile with manual creation flag
      const persona = new PersonaProfile({
        personaText: personaData.personaText.trim(),
        tags: personaData.tags || [],
        demographics: personaData.demographics || {},
        interests: personaData.interests || [],
        goals: personaData.goals || [],
        painPoints: personaData.painPoints || [],
        aiGeneratedFrom: null, // Not AI generated
        aiEngineUsed: "manual", // Manually created
        modelId,
        agencyId,
        createdBy,
        createdByModel,
      });

      await persona.save();
      return persona;
    } catch (error) {
      console.error("Persona save failed:", error);
      throw error;
    }
  }

  /**
   * Update feedback statistics for a persona
   */
  static async updateFeedbackStats(personaId) {
    try {
      const PersonaFeedback = (
        await import("../models/persona/PersonaFeedback.js")
      ).default;

      const stats = await PersonaFeedback.getFeedbackSummary(personaId);
      const feedbackData = stats[0] || {
        averageRating: 0,
        totalFeedback: 0,
        positiveCount: 0,
        negativeCount: 0,
        avgAccuracy: 0,
        avgUsefulness: 0,
        avgCompleteness: 0,
        avgBrandAlignment: 0,
      };

      await PersonaProfile.findByIdAndUpdate(personaId, {
        $set: {
          "feedbackStats.averageRating": feedbackData.averageRating,
          "feedbackStats.totalFeedback": feedbackData.totalFeedback,
          "feedbackStats.positiveCount": feedbackData.positiveCount,
          "feedbackStats.negativeCount": feedbackData.negativeCount,
          "feedbackStats.lastUpdated": new Date(),
        },
      });

      return feedbackData;
    } catch (error) {
      console.error("Failed to update feedback stats:", error);
      throw error;
    }
  }

  /**
   * Regenerate persona with version tracking
   */
  static async regenerateWithVersion(
    personaId,
    regenerationOptions,
    regeneratedBy,
    regeneratedByModel,
  ) {
    try {
      const persona = await PersonaProfile.findById(personaId);
      if (!persona) {
        throw new Error("Persona not found");
      }

      // Create version snapshot of current persona
      await persona.addVersion(regeneratedBy, "Pre-regeneration snapshot");

      // Get model data for regeneration
      const model = await ModelUser.findById(persona.modelId).select(
        "fullName username",
      );

      const userProfile = await UserProfile.findOne({
        user: persona.modelId,
      }).select(
        "bio display_name job_title role_description interests goals demographics archetype tone audience",
      );

      const answers = await this.getQuestionnaireAnswers(persona.modelId);
      const captionAnalysis =
        await CaptionAnalysisService.analyzeCaptionHistory(persona.modelId, 20);

      // Build enhanced prompt with preservation options
      const prompt = this.buildRegenerationPrompt(
        model,
        userProfile,
        answers,
        captionAnalysis,
        persona,
        regenerationOptions,
      );

      // Generate new version using AI
      let aiResult;
      let aiEngine;
      try {
        const gptResponse = await gptService.generatePersona(prompt);
        aiResult = gptResponse.content || gptResponse;
        aiEngine = "gpt-4";
      } catch (gptError) {
        console.log("GPT generation failed, trying Gemini:", gptError.message);
        const geminiResponse = await geminiService.generatePersona(prompt);
        aiResult = geminiResponse.content || geminiResponse;
        aiEngine = "gemini-vision";
      }

      const parsedPersona = this.parsePersonaResponse(aiResult);
      const validatedData = this.validateAndMapPersonaData(parsedPersona);

      // Apply preservation options
      const finalData = this.applyPreservationOptions(
        validatedData,
        persona,
        regenerationOptions,
      );

      // Update persona with new data
      Object.assign(persona, finalData);
      persona.version += 1;
      persona.lastRegeneratedAt = new Date();
      persona.lastRegeneratedBy = regeneratedBy;
      persona.lastRegeneratedByModel = regeneratedByModel;
      persona.aiEngineUsed = aiEngine;

      await persona.save();

      // Create version entry for new regeneration
      await persona.addVersion(
        regeneratedBy,
        "Regenerated with preserved elements",
      );

      return persona;
    } catch (error) {
      console.error("Failed to regenerate persona with version:", error);
      throw error;
    }
  }

  /**
   * Build regeneration prompt with preservation options
   */
  static buildRegenerationPrompt(
    model,
    userProfile,
    answers,
    captionAnalysis,
    currentPersona,
    preservationOptions,
  ) {
    let preservationText = "";

    if (preservationOptions) {
      if (
        preservationOptions.preserveTone &&
        currentPersona.communicationStyle?.tone
      ) {
        preservationText += `\nIMPORTANT: Preserve the current communication tone: "${currentPersona.communicationStyle.tone}"`;
      }

      if (
        preservationOptions.preserveValues &&
        currentPersona.psychographics?.values
      ) {
        preservationText += `\nIMPORTANT: Preserve these core values: ${currentPersona.psychographics.values.join(
          ", ",
        )}`;
      }

      if (
        preservationOptions.preserveInterests &&
        currentPersona.interests?.length > 0
      ) {
        preservationText += `\nIMPORTANT: Maintain these interests: ${currentPersona.interests.join(
          ", ",
        )}`;
      }

      if (preservationOptions.customPreservation) {
        preservationText += `\nADDITIONAL PRESERVATION REQUIREMENTS: ${preservationOptions.customPreservation}`;
      }
    }

    const basePrompt = this.buildPersonaPrompt(
      model,
      userProfile,
      answers,
      captionAnalysis,
    );

    return basePrompt.replace(
      "INSTRUCTIONS:",
      `REGENERATION CONTEXT:
This is a regeneration of an existing persona. Current persona version: ${
        currentPersona.version || 1
      }
${preservationText}

INSTRUCTIONS:`,
    );
  }

  /**
   * Apply preservation options to generated data
   */
  static applyPreservationOptions(
    newData,
    currentPersona,
    preservationOptions,
  ) {
    if (!preservationOptions) {
      return newData;
    }

    const finalData = { ...newData };

    // Preserve tone if requested
    if (
      preservationOptions.preserveTone &&
      currentPersona.communicationStyle?.tone
    ) {
      finalData.communicationStyle = finalData.communicationStyle || {};
      finalData.communicationStyle.tone =
        currentPersona.communicationStyle.tone;
    }

    // Preserve values if requested
    if (
      preservationOptions.preserveValues &&
      currentPersona.psychographics?.values
    ) {
      finalData.psychographics = finalData.psychographics || {};
      finalData.psychographics.values = currentPersona.psychographics.values;
    }

    // Preserve interests if requested
    if (
      preservationOptions.preserveInterests &&
      currentPersona.interests?.length > 0
    ) {
      finalData.interests = currentPersona.interests;
    }

    return finalData;
  }
}

export default PersonaService;
