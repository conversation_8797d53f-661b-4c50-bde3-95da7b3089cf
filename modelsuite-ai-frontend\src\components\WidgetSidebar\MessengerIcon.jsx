import React from "react";

const MessengerIcon = ({ className }) => {
  return (
    <svg
      width="100%"
      height="100%"
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <defs>
        {/* Background gradients to match PNG look */}
        <radialGradient id="messengerBgRadial" cx="66%" cy="22%" r="92%">
          <stop offset="0%" stopColor="#3B1282" />
          <stop offset="48%" stopColor="#18143A" />
          <stop offset="100%" stopColor="#0A0B18" />
        </radialGradient>
        <radialGradient id="messengerVioletHalo" cx="66%" cy="22%" r="70%">
          <stop offset="0%" stopColor="#7E2CFF" stopOpacity="0.35" />
          <stop offset="100%" stopColor="#000000" stopOpacity="0" />
        </radialGradient>

        {/* Stroke gradient (pink -> violet -> blue) */}
        <linearGradient
          id="messengerStrokeGrad"
          x1="15%"
          y1="15%"
          x2="85%"
          y2="85%"
        >
          <stop offset="0%" stopColor="#FF3CF6" />
          <stop offset="55%" stopColor="#A43CFF" />
          <stop offset="100%" stopColor="#0AA3FF" />
        </linearGradient>

        {/* Dots fill gradient (brighter cyan center) */}
        <radialGradient id="messengerDotGrad" cx="50%" cy="50%" r="70%">
          <stop offset="0%" stopColor="#2BE3FF" />
          <stop offset="100%" stopColor="#0AA3FF" />
        </radialGradient>

        {/* Glow filter for the neon effect */}
        <filter id="messengerGlow" x="-60%" y="-60%" width="220%" height="220%">
          <feGaussianBlur stdDeviation="14" result="blur" />
          <feMerge>
            <feMergeNode in="blur" />
            <feMergeNode in="SourceGraphic" />
          </feMerge>
        </filter>
      </defs>

      {/* Rounded square background */}
      <rect
        x="64"
        y="64"
        width="896"
        height="896"
        rx="220"
        fill="url(#messengerBgRadial)"
      />
      <rect
        x="64"
        y="64"
        width="896"
        height="896"
        rx="220"
        fill="url(#messengerVioletHalo)"
      />

      {/* Chat bubble path with continuous rounded-rect + tail */}
      <g fill="none" strokeLinecap="round" strokeLinejoin="round">
        {/* glow pass */}
        <path
          d="
            M 380 260
            H 680
            A 120 120 0 0 1 800 380
            V 540
            A 120 120 0 0 1 680 660
            H 420
            L 380 720
            L 392 660
            H 380
            A 120 120 0 0 1 260 540
            V 380
            A 120 120 0 0 1 380 260
            Z"
          stroke="url(#messengerStrokeGrad)"
          strokeWidth="34"
          filter="url(#messengerGlow)"
        />
        {/* crisp pass */}
        <path
          d="
            M 380 260
            H 680
            A 120 120 0 0 1 800 380
            V 540
            A 120 120 0 0 1 680 660
            H 420
            L 380 720
            L 392 660
            H 380
            A 120 120 0 0 1 260 540
            V 380
            A 120 120 0 0 1 380 260
            Z"
          stroke="url(#messengerStrokeGrad)"
          strokeWidth="18"
        />
      </g>

      {/* Three message dots */}
      <g>
        {/* subtle glow */}
        <g filter="url(#messengerGlow)">
          <circle cx="470" cy="468" r="30" fill="url(#messengerDotGrad)" />
          <circle cx="550" cy="468" r="30" fill="url(#messengerDotGrad)" />
          <circle cx="630" cy="468" r="30" fill="url(#messengerDotGrad)" />
        </g>
        {/* crisp dots */}
        <circle cx="470" cy="468" r="22" fill="url(#messengerDotGrad)" />
        <circle cx="550" cy="468" r="22" fill="url(#messengerDotGrad)" />
        <circle cx="630" cy="468" r="22" fill="url(#messengerDotGrad)" />
      </g>
    </svg>
  );
};

export default MessengerIcon;
