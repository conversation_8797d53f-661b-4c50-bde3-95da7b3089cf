import express from "express";
import upload from "../../middlewares/cloudinaryUpload.js";

import {
  uploadInvoice,
  getInvoices,
  updateSingleInvoiceWithFile,
  mass_payment_status,
  exportInvoices,
  getInvoiceById,
  updateInvoice,
  deleteInvoice,
  getInvoices_dash,
  getModelDashboard,
  getInvoicesByAgency,
  single_payment_status,
} from "../../controllers/billing/billingController.js";
import { verifyToken } from "../../middlewares/authMiddleware.js";
import checkPermission from "../../middlewares/permissionCheck.js";
import { checkAgency } from "../../middlewares/roleCheck.js";
const router = express.Router();

router.post(
  "/invoice",
  verifyToken,
  checkAgency,
  checkPermission("earnings.manage"),
  upload.single("file"),
  uploadInvoice,
);
router.post(
  "/mass-update",
  verifyToken,
  checkAgency,
  checkPermission("earnings.manage"),
  upload.any(),
  mass_payment_status,
);
router.get(
  "/invoices",
  verifyToken,
  checkPermission("earnings.view"),
  getInvoices,
);

router.post(
  "/invoice",
  verifyToken,
  checkAgency,
  upload.single("file"),
  uploadInvoice,
);
router.get("/invoices", verifyToken, getInvoices);

router.get(
  "/invoices/export",
  verifyToken,
  checkPermission("earnings.view"),
  exportInvoices,
);
router.put(
  "/invoices/:id",
  verifyToken,
  checkAgency,
  checkPermission("earnings.manage"),
  updateInvoice,
);
router.delete(
  "/invoices/:id",
  verifyToken,
  checkAgency,
  checkPermission("earnings.manage"),
  deleteInvoice,
);
router.get(
  "/invoices/:id",
  verifyToken,
  checkPermission("earnings.view"),
  getInvoiceById,
);
router.put(
  "/update/:id",
  verifyToken,
  checkAgency,
  checkPermission("earnings.manage"),
  upload.single("screenshot"),
  updateSingleInvoiceWithFile,
);
router.get("/modeldashboard/:modelId", getModelDashboard);
router.get("/dashboard", verifyToken, checkAgency, getInvoices_dash);
router.get("/agency-invoices", verifyToken, getInvoicesByAgency);
router.post(
  "/single-update",
  verifyToken,
  checkAgency,
  checkPermission("earnings.manage"),
  upload.single("screenshot"), // file field name should be 'screenshot'
  single_payment_status,
);

export default router;
