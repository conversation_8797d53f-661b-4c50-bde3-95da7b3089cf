import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { FileText, Clock, CheckCircle, AlertCircle } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { DataTable } from "@/reusable/table/DataTable";
import StatusBadge from "@/reusable/Status/StatusBadge";

import { fetchMyAssignments } from "@/redux/features/questionnaire/questionnaireSlice";

export default function MyAssignmentsPage() {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { myAssignments, assignmentLoading } = useSelector(
    (state) => state.questionnaireReducer
  );

  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });

  useEffect(() => {
    dispatch(fetchMyAssignments());
  }, [dispatch]);

  const handleTakeQuestionnaire = (assignmentId) => {
    navigate(`/model/questionnaires/take/${assignmentId}`);
  };

  const handleViewResponses = (templateId) => {
    navigate(`/model/questionnaires/responses/${templateId}`);
  };

  const columns = [
    {
      accessorKey: "templateId.title",
      header: "Questionnaire",
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <FileText className="h-4 w-4 text-muted-foreground" />
          <span className="font-medium">
            {row.original.templateId?.title || "Unknown Template"}
          </span>
        </div>
      ),
    },
    {
      accessorKey: "templateId.description",
      header: "Description",
      cell: ({ row }) => (
        <span className="text-sm text-muted-foreground">
          {row.original.templateId?.description || "No description"}
        </span>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const statusVariants = {
          pending: "warning",
          in_progress: "default",
          completed: "success",
          overdue: "destructive",
        };
        return (
          <StatusBadge
            status={row.original.status}
            variant={statusVariants[row.original.status] || "secondary"}
          />
        );
      },
    },
    {
      accessorKey: "assignedAt",
      header: "Assigned",
      cell: ({ row }) => (
        <span className="text-sm text-muted-foreground">
          {new Date(row.original.assignedAt).toLocaleDateString()}
        </span>
      ),
    },
    {
      accessorKey: "dueDate",
      header: "Due Date",
      cell: ({ row }) => {
        const dueDate = row.original.dueDate;
        const isOverdue =
          dueDate &&
          new Date(dueDate) < new Date() &&
          row.original.status !== "completed";

        return (
          <span
            className={`text-sm ${
              isOverdue ? "text-red-600" : "text-muted-foreground"
            }`}
          >
            {dueDate ? new Date(dueDate).toLocaleDateString() : "No due date"}
          </span>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          {row.original.status === "completed" ? (
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleViewResponses(row.original.templateId._id)}
            >
              View Responses
            </Button>
          ) : (
            <Button
              size="sm"
              onClick={() => handleTakeQuestionnaire(row.original._id)}
            >
              {row.original.status === "in_progress" ? "Continue" : "Start"}
            </Button>
          )}
        </div>
      ),
    },
  ];

  const pendingCount =
    myAssignments?.filter((a) => a.status === "pending")?.length || 0;
  const inProgressCount =
    myAssignments?.filter((a) => a.status === "in_progress")?.length || 0;
  const completedCount =
    myAssignments?.filter((a) => a.status === "completed")?.length || 0;
  const overdueCount =
    myAssignments?.filter(
      (a) =>
        a.dueDate &&
        new Date(a.dueDate) < new Date() &&
        a.status !== "completed"
    )?.length || 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-semibold tracking-tight">
          My Questionnaires
        </h1>
        <p className="text-muted-foreground">
          Complete your assigned questionnaires and track your progress
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pendingCount}</div>
            <p className="text-xs text-muted-foreground">Waiting to start</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Progress</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{inProgressCount}</div>
            <p className="text-xs text-muted-foreground">
              Currently working on
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{completedCount}</div>
            <p className="text-xs text-muted-foreground">
              Successfully submitted
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Overdue</CardTitle>
            <AlertCircle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {overdueCount}
            </div>
            <p className="text-xs text-muted-foreground">Past due date</p>
          </CardContent>
        </Card>
      </div>

      {/* Assignments Table */}
      <Card>
        <CardHeader>
          <CardTitle>All Assignments</CardTitle>
          <CardDescription>
            Your questionnaire assignments and their current status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={myAssignments || []}
            pagination={pagination}
            setPagination={setPagination}
            loading={assignmentLoading}
          />
        </CardContent>
      </Card>
    </div>
  );
}
