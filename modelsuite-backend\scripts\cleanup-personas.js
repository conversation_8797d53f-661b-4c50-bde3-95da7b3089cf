import mongoose from "mongoose";
import PersonaProfile from "./models/persona/PersonaProfile.js";
import PersonaFeedback from "./models/persona/PersonaFeedback.js";
import ModelUser from "./models/model.js";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

/**
 * <PERSON><PERSON><PERSON> to clean up personas from the database
 * Keeps the 6 most recent personas and removes the rest
 */

async function cleanupPersonas() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI);
    console.log("✅ Connected to MongoDB");

    // Find all personas, sorted by creation date (newest first)
    const allPersonas = await PersonaProfile.find({})
      .sort({ createdAt: -1 })
      .populate("modelId", "username fullName");

    console.log(`📊 Total personas found: ${allPersonas.length}`);

    if (allPersonas.length <= 6) {
      console.log("ℹ️ 6 or fewer personas exist. No cleanup needed.");
      process.exit(0);
    }

    // Keep the 6 newest personas
    const personasToKeep = allPersonas.slice(0, 6);
    const personasToDelete = allPersonas.slice(6);

    console.log(`🔄 Keeping ${personasToKeep.length} newest personas:`);
    personasToKeep.forEach((persona, index) => {
      console.log(
        `  ${index + 1}. ${persona._id} - ${
          persona.modelId?.username || "Unknown"
        } (${persona.createdAt.toLocaleDateString()})`,
      );
    });

    console.log(`\n🗑️ Will delete ${personasToDelete.length} older personas:`);
    personasToDelete.forEach((persona, index) => {
      console.log(
        `  ${index + 1}. ${persona._id} - ${
          persona.modelId?.username || "Unknown"
        } (${persona.createdAt.toLocaleDateString()})`,
      );
    });

    // Get the IDs of personas to delete
    const personaIdsToDelete = personasToDelete.map((p) => p._id);

    // Ask for confirmation
    console.log(
      `\n⚠️ WARNING: This will permanently delete ${personasToDelete.length} personas and their related feedback.`,
    );
    console.log("This action cannot be undone.");

    // In a real scenario, you'd want user confirmation here
    // For now, we'll proceed automatically

    console.log("\n🔄 Starting cleanup...");

    // Delete related feedback first (to maintain referential integrity)
    const feedbackDeleteResult = await PersonaFeedback.deleteMany({
      personaId: { $in: personaIdsToDelete },
    });
    console.log(
      `📝 Deleted ${feedbackDeleteResult.deletedCount} feedback records`,
    );

    // Delete the personas
    const personaDeleteResult = await PersonaProfile.deleteMany({
      _id: { $in: personaIdsToDelete },
    });
    console.log(`👤 Deleted ${personaDeleteResult.deletedCount} personas`);

    // Verify remaining count
    const remainingCount = await PersonaProfile.countDocuments({});
    console.log(`\n✅ Cleanup complete!`);
    console.log(`📊 Remaining personas: ${remainingCount}`);

    // Show the remaining personas
    const remainingPersonas = await PersonaProfile.find({})
      .sort({ createdAt: -1 })
      .populate("modelId", "username fullName");

    console.log("\n📋 Remaining personas:");
    remainingPersonas.forEach((persona, index) => {
      console.log(
        `  ${index + 1}. ${persona._id} - ${
          persona.modelId?.username || "Unknown"
        } (${persona.createdAt.toLocaleDateString()})`,
      );
    });
  } catch (error) {
    console.error("❌ Error during cleanup:", error);
  } finally {
    await mongoose.disconnect();
    console.log("🔌 Disconnected from MongoDB");
    process.exit(0);
  }
}

// Run the cleanup
cleanupPersonas();
