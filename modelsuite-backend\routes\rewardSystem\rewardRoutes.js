import express from "express";

import {
  recordLogin,
  recordPost,
  recordModelSignup,
  getRewardData,
  getAllRewards,
  getLeaderboard,
  getRewardStats,
  getUserBadges,
  manualRecordActivity,
} from "../../controllers/RewardSystem/rewardController.js";
import { verifyToken, verifyRole } from "../../middlewares/authMiddleware.js";

const router = express.Router();

// ===== MODEL ROUTES =====
// Models can only:
// - Check their login streak rewards
// - View leaderboards
router.get(
  "/model/my-rewards",
  verifyToken,
  verifyRole("model"),
  getAllRewards,
);
router.get(
  "/model/my-rewards/:rewardType",
  verifyToken,
  verifyRole("model"),
  getRewardData,
);
router.get("/model/my-badges", verifyToken, verifyRole("model"), getUserBadges);
router.get(
  "/model/leaderboard/:rewardType",
  verifyToken,
  verifyRole("model"),
  getLeaderboard,
);
router.get("/model/stats", verifyToken, verifyRole("model"), getRewardStats);

// Models can only record login (automatically called)
router.post(
  "/model/record-login",
  verifyToken,
  verifyRole("model"),
  recordLogin,
);

// ===== AGENCY ROUTES =====
// Agencies can:
// - Create posts for their models
// - Sign new models
// - View all their rewards
router.get(
  "/agency/my-rewards",
  verifyToken,
  verifyRole("agency"),
  getAllRewards,
);
router.get(
  "/agency/my-rewards/:rewardType",
  verifyToken,
  verifyRole("agency"),
  getRewardData,
);
router.get(
  "/agency/my-badges",
  verifyToken,
  verifyRole("agency"),
  getUserBadges,
);
router.get(
  "/agency/leaderboard/:rewardType",
  verifyToken,
  verifyRole("agency"),
  getLeaderboard,
);
router.get("/agency/stats", verifyToken, verifyRole("agency"), getRewardStats);

// ✅ MISSING AGENCY ACTIVITY ROUTES
router.post(
  "/agency/record-post",
  verifyToken,
  verifyRole("agency"),
  recordPost,
);
router.post(
  "/agency/record-model-signup",
  verifyToken,
  verifyRole("agency"),
  recordModelSignup,
);

// ===== ADMIN/TESTING ROUTES =====
router.post("/admin/manual-record", verifyToken, manualRecordActivity);

// ===== UNIVERSAL ROUTES =====
router.get("/my-rewards", verifyToken, getAllRewards);
router.get("/my-badges", verifyToken, getUserBadges);

export default router;
