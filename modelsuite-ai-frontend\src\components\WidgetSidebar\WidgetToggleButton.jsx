import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { Button } from "@/components/ui/button";
import { Sidebar } from "lucide-react";
import {
  toggleSidebar,
  selectIsWidgetSidebarOpen,
} from "@/redux/features/widgetSidebar/widgetSidebarSlice";

const WidgetToggleButton = ({ className = "" }) => {
  const dispatch = useDispatch();
  const isOpen = useSelector(selectIsWidgetSidebarOpen);

  const handleToggle = () => {
    dispatch(toggleSidebar());
  };

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={handleToggle}
      className={`
        fixed top-17.5 right-11 z-40
        border-gray-700 bg-gray-800/90 backdrop-blur-sm
        text-gray-300 hover:bg-gray-700 hover:text-white hover:border-gray-600 
        transition-all duration-200 shadow-lg
        ${className}
      `}
      title={isOpen ? "Close Widgets" : "Open Widgets"}
    >
      <Sidebar className="h-4 w-4" />
    </Button>
  );
};

export default WidgetToggleButton;
