import TimeSession from "../../models/ShiftReport/TimeSession.js";
import ShiftReport from "../../models/ShiftReport/ShiftReport.js";
import Employee from "../../models/Employee/Employee.js";
import Agency from "../../models/agency.js";

export const startTimeSession = async (req, res) => {
  try {
    const employeeId = req.user?._id;
    const agencyId = req.user?.agencyId || req.user?._id; //remove ._id later

    // For testing: Allow agency users to use timer
    if (req.user.role === "agency") {
      console.log("🧪 TEST MODE: Agency user using employee timer");
    }

    const { taskDescription } = req.body;

    if (!employeeId || !agencyId)
      return res.status(401).json("Unauthorized access.");

    const today = new Date().toISOString().split("T")[0];

    const activeSession = await TimeSession.findOne({
      employeeId,
      status: "active",
      date: today,
    });

    if (activeSession) {
      return res.status(401).json({
        error:
          "You already have an active session. Please end it before creating a new one.",
      });
    }

    const timeSession = new TimeSession({
      employeeId,
      agencyId,
      startTime: new Date(),
      taskDescription,
      date: today,
    });

    await timeSession.save();

    return res.status(201).json({
      message: "Time session started successfully",
      session: timeSession,
    });
  } catch (error) {
    console.error("Start session error:", error);
    res.status(500).json({ error: "Server error" });
  }
};

export const endTimeSession = async (req, res) => {
  try {
    const employeeId = req.user?._id;
    const { sessionId, taskDescription } = req.body;

    const session = await TimeSession.findOne({
      _id: sessionId,
      employeeId,
      status: "active",
    });

    if (!session) {
      return res.status(404).json({ error: "Active session not found" });
    }

    const endTime = new Date();
    const duration = Math.round((endTime - session.startTime) / (1000 * 60)); // in minutes

    session.endTime = endTime;
    session.duration = duration;
    session.status = "completed";

    if (taskDescription) session.taskDescription = taskDescription;

    await session.save();

    return res.status(200).json({
      message: "Time session ended successfully.",
      session: session,
    });
  } catch (error) {
    console.error("End session error:", error);
    res.status(500).json({ error: "Server error" });
  }
};

export const getActiveSessions = async (req, res) => {
  try {
    const employeeId = req.user?._id;
    const today = new Date().toISOString().split("T")[0];

    const activeSessions = await TimeSession.find({
      employeeId,
      status: "active",
      date: today,
    });

    res.json({ sessions: activeSessions });
  } catch (error) {
    console.error("Get active sessions error:", error);
    res.status(500).json({ error: "Server error" });
  }
};

export const submitShiftReport = async (req, res) => {
  try {
    const employeeId = req.user?._id;
    const agencyId = req.user?.agencyId || req.user._id; //change later
    const {
      workSummary,
      tasksCompleted,
      challenges,
      tomorrowPriorities,
      totalHours,
      productivityRating,
    } = req.body;

    const today = new Date().toISOString().split("T")[0]; //returns just the date

    const existingReport = await ShiftReport.findOne({
      employeeId,
      date: today,
    });

    if (existingReport)
      return res
        .status(401)
        .json({ message: "Shift report already submitted for today." });

    const shiftReport = new ShiftReport({
      employeeId,
      agencyId,
      date: today,
      workSummary,
      tasksCompleted,
      challenges,
      tomorrowPriorities,
      totalHours,
      productivityRating,
    });

    await shiftReport.save();

    return res.status(201).json({
      message: "Shift report submitted successfully",
      report: shiftReport,
    });
  } catch (error) {
    console.error("Submit report error:", error);
    res.status(500).json({ error: "Server error" });
  }
};

export const getMyReports = async (req, res) => {
  try {
    const employeeId = req.user?._id;
    const { startDate, endDate, page = 1, limit = 10 } = req.query;

    const query = { employeeId };

    if (startDate && endDate) {
      query.date = { $gte: startDate, $lte: endDate };
    }

    const reports = await ShiftReport.find(query)
      .sort({ date: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await ShiftReport.countDocuments(query);

    res.json({
      reports,
      pagination: {
        current: page,
        pages: Math.ceil(total / limit),
        total,
      },
    });
  } catch (error) {
    console.error("Get reports error:", error);
    res.status(500).json({ error: "Server error" });
  }
};

// 🧪 TEMPORARY: Delete all reports by agency email (NO SECURITY CHECKS)
export const deleteAllReportsByAgencyEmail = async (req, res) => {
  try {
    const { agencyEmail } = req.body;

    if (!agencyEmail) {
      return res.status(400).json({
        error: "agencyEmail is required in request body",
      });
    }

    // Find the agency by email
    const agency = await Agency.findOne({ agencyEmail: agencyEmail });

    if (!agency) {
      return res.status(404).json({
        error: `No agency found with email: ${agencyEmail}`,
      });
    }

    // Delete all shift reports for this agency
    const deleteResult = await ShiftReport.deleteMany({
      agencyId: agency._id,
    });

    console.log(
      `🗑️ TEMP DELETE: Deleted ${deleteResult.deletedCount} reports for agency: ${agencyEmail}`,
    );

    return res.status(200).json({
      message: `Successfully deleted all reports for agency: ${agencyEmail}`,
      agencyId: agency._id,
      agencyName: agency.agencyName || agency.fullName,
      deletedCount: deleteResult.deletedCount,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Temp delete all reports error:", error);
    res.status(500).json({
      error: "Server error",
      details: error.message,
    });
  }
};
