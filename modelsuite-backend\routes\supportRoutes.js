import express from "express";
import multer from "multer";
import axios from "axios";
import fs from "fs";
import FormData from "form-data";

const router = express.Router();
const upload = multer({ dest: "uploads/" });

router.post("/submit", upload.single("attachment"), async (req, res) => {
  const { name, email, subject, category, urgency, message } = req.body;
  const attachment = req.file;

  if (!name || !email || !message) {
    return res.status(400).json({ error: "Missing required fields." });
  }

  const domain = process.env.FRESHDESK_DOMAIN;
  const apiKey = process.env.FRESHDESK_API_KEY;

  const ticketData = {
    email,
    subject: subject || `Support from ${name}`,
    description: `
      <strong>Category:</strong> ${category}<br/>
      <strong>Urgency:</strong> ${urgency}<br/><br/>
      ${message}
    `,
    priority: urgency === "High" ? 4 : urgency === "Normal" ? 3 : 2,
    status: 2,
    tags: category ? [category.toLowerCase()] : [],
  };

  try {
    // ✅ Step 1: Create ticket with JSON
    const ticketResponse = await axios.post(
      `https://${domain}.freshdesk.com/api/v2/tickets`,
      ticketData,
      {
        headers: {
          Authorization: `Basic ${Buffer.from(apiKey + ":X").toString("base64")}`,
          "Content-Type": "application/json", // must be JSON here
        },
      },
    );

    const ticketId = ticketResponse.data.id;

    // ✅ Step 2: Upload attachment separately if exists
    if (attachment) {
      console.log("Uploading file:", attachment.originalname);

      const form = new FormData();
      form.append("attachments[]", fs.createReadStream(attachment.path), {
        filename: attachment.originalname,
      });

      try {
        const uploadRes = await axios.post(
          `https://${domain}.freshdesk.com/api/v2/tickets/${ticketId}/attachments`,
          form,
          {
            headers: {
              Authorization: `Basic ${Buffer.from(apiKey + ":X").toString("base64")}`,
              ...form.getHeaders(),
            },
          },
        );

        console.log("Attachment upload successful:", uploadRes.data);
      } catch (err) {
        console.error(
          "Attachment upload failed:",
          err.response?.data || err.message,
        );
      }

      fs.unlink(attachment.path, (err) => {
        if (err) console.error("Failed to delete uploaded file:", err);
      });
    }

    res.status(200).json({ message: "Ticket created successfully.", ticketId });
  } catch (err) {
    console.error("Failed to create Freshdesk ticket:");
    if (err.response) {
      console.error("Status:", err.response.status);
      console.error("Data:", err.response.data);
    } else {
      console.error("Error:", err.message);
    }

    if (attachment) {
      fs.unlink(attachment.path, (err) => {
        if (err) console.error("Failed to delete uploaded file:", err);
      });
    }

    res.status(500).json({ error: "Failed to create ticket." });
  }
});

export default router;
